-include .env
-include .env.local
export

DOCKER_COMPOSE_PROJECT_NAME := paw-champ
PHP_CONTAINER_NAME := ${DOCKER_COMPOSE_PROJECT_NAME}-php-1
FRONT_CONTAINER_NAME := ${DOCKER_COMPOSE_PROJECT_NAME}-front-1
REDIS_CONTAINER_NAME := ${DOCKER_COMPOSE_PROJECT_NAME}-redis-1

.PHONY: front-install-deps
front-install-deps:
	docker run -e NODE_OPTIONS=--openssl-legacy-provider --rm --name ${FRONT_CONTAINER_NAME}-deps -it -v `pwd`:/app -w /app/front node:20 yarn

composer:
	docker exec -it ${PHP_CONTAINER_NAME} php -r "copy('https://getcomposer.org/installer', 'composer-setup.php');"
	docker exec -it ${PHP_CONTAINER_NAME} php -r "if (hash_file('sha384', 'composer-setup.php') === 'dac665fdc30fdd8ec78b38b9800061b4150413ff2e3b6f88543c636f7cd84f6db9189d43a81e5503cda447da73c7e5b6') { echo 'Installer verified'; } else { echo 'Installer corrupt'; unlink('composer-setup.php'); } echo PHP_EOL;"
	docker exec -it ${PHP_CONTAINER_NAME} php composer-setup.php
	docker exec -it ${PHP_CONTAINER_NAME} php -r "unlink('composer-setup.php');"
	docker exec -it ${PHP_CONTAINER_NAME} mv composer.phar composer

.PHONY: back-install-deps
back-install-deps:
	docker exec -w /app ${PHP_CONTAINER_NAME} /bin/sh -c "COMPOSER_PROCESS_TIMEOUT=1; ./composer install --ignore-platform-reqs"

.PHONY: front-build
front-build:
	docker run -e NODE_OPTIONS=--openssl-legacy-provider --rm --name ${FRONT_CONTAINER_NAME}-build -it -v  `pwd`:/app -w /app/front node:20 yarn run build

.PHONY: update-backend
update-backend:
	make back-install-deps
	make migrate

.PHONY: update-frontend
update-frontend:
	make front-install-deps
	make front-build

.PHONY: update
update:
	make update-backend
	make update-frontend

.PHONY: front-watch
front-watch:
	docker run -e NODE_OPTIONS=--openssl-legacy-provider --rm --name ${FRONT_CONTAINER_NAME}-watch -it -v `pwd`:/app -w /app/front node:20 yarn run watch

.PHONY: migrate
migrate:
	docker exec ${PHP_CONTAINER_NAME} /app/bin/console doctrine:migrations:migrate -n

.PHONY: rollback-1
rollback-1:
	docker exec -it ${PHP_CONTAINER_NAME} /app/bin/console doctrine:migrations:migrate -n current-1

.PHONY: migrate-test-db
migrate-test-db:
	docker exec -it ${PHP_CONTAINER_NAME} /app/bin/console --env=test doctrine:migrations:migrate -n

.PHONY: setup-test-db
setup-test-db:
	docker exec -it -w /app ${PHP_CONTAINER_NAME} /bin/sh -c " \
		/app/bin/console --env=test doctrine:schema:create -n || true \
		&& /app/bin/console --env=test doctrine:fixtures:load -n \
	"

.PHONY: load-dev-fixtures
load-dev-fixtures:
	docker exec -it ${PHP_CONTAINER_NAME} /app/bin/console app:dev:load-fixtures

.PHONY: test
test: migrate-test-db setup-test-db
	docker exec -it -w /app ${PHP_CONTAINER_NAME} /app/vendor/bin/phpunit tests

.PHONY: reset-opcache
reset-opcache:
	bin/console cache:clear
	kill -USR2 1

.PHONY: code-quality
code-quality:
	@printf "\n\n\033[0;32m Running php-cs-fixer... \033[0m\n\n"
	vendor/bin/php-cs-fixer --config=.php-cs-fixer.dist.php fix -v --using-cache=no
	@printf "\n\n\033[0;32m Running phpstan... \033[0m\n\n"
	vendor/bin/phpstan analyse -c phpstan.neon --memory-limit 512M

.PHONY: php-cs-fixer-fix
php-cs-fixer-fix:
	docker exec -it ${PHP_CONTAINER_NAME} vendor/bin/php-cs-fixer --config=.php-cs-fixer.dist.php fix

.PHONY: php-cs-fixer-dry-run
php-cs-fixer-dry-run:
	docker exec -it ${PHP_CONTAINER_NAME} vendor/bin/php-cs-fixer --config=.php-cs-fixer.dist.php fix -v --dry-run --using-cache=no

.PHONY: phpstan
phpstan:
	PHP_CS_FIXER_IGNORE_ENV=true vendor/bin/phpstan analyse -c phpstan.neon --memory-limit 512M

.PHONY: tunnel
tunnel:
	# Required cloudflared to be installed
	cloudflared tunnel run --url ${TUNNEL_URL} ${TUNNEL_NAME}

.PHONY: tunnel-proxy
tunnel-proxy:
	# Required mitmproxy and cloudflared to be installed
	mitmweb -p ${TUNNEL_PROXY_PORT} --mode reverse:http://${APP_DOMAIN}&
	cloudflared tunnel run --url http://localhost:${TUNNEL_PROXY_PORT} ${TUNNEL_NAME}

.PHONY: clear-cache
clear-cache:
	docker exec ${PHP_CONTAINER_NAME} rm -rf var/cache/*
	docker exec ${REDIS_CONTAINER_NAME} redis-cli FLUSHALL

.PHONY: fix-icloud-sync
fix-icloud-sync:
	find . -type f -name '* 1.php' -delete
	find . -type f -name '* 2.php' -delete
	find . -type f -name '* 3.php' -delete
	find . -type f -name '* 1.twig' -delete
	find . -type f -name '* 2.twig' -delete

.PHONY: pull-translations
pull-translations: pull-vagus-translations pull-nutrition-translations pull-love-translations pull-challenge-translations

.PHONY: pull-vagus-translations
pull-vagus-translations:
	docker run --rm -v $$(pwd)/translations:/usr/crowdin-project crowdin/cli:4.6.0 crowdin bundle download -T ${CROWDIN_TOKEN} -i ${CROWDIN_PROJECT_ID} ${CROWDIN_VAGUS_BUNDLE_ID}

.PHONY: pull-challenge-translations
pull-challenge-translations:
	docker run --rm -v $$(pwd)/translations:/usr/crowdin-project crowdin/cli:4.6.0 crowdin bundle download -T ${CROWDIN_TOKEN} -i ${CROWDIN_PROJECT_ID} ${CROWDIN_CHALLENGE_BUNDLE_ID}
	docker build -t quote_yamls -f utils/Dockerfile ./utils  && docker run -it --rm -v `pwd`/translations:/translations quote_yamls

.PHONY: pull-nutrition-translations
pull-nutrition-translations:
	docker run --rm -v $$(pwd)/translations:/usr/crowdin-project crowdin/cli:4.6.0 crowdin bundle download -T ${CROWDIN_TOKEN} -i ${CROWDIN_PROJECT_ID} ${CROWDIN_NUTRITION_BUNDLE_ID}

.PHONY: pull-love-translations
pull-love-translations:
	docker run --rm -v $$(pwd)/translations:/usr/crowdin-project crowdin/cli:4.6.0 crowdin bundle download -T ${CROWDIN_TOKEN} -i ${CROWDIN_PROJECT_ID} ${CROWDIN_LOVE_BUNDLE_ID}

.PHONY: init
init:
	@if [ ! -f .env ]; then \
		echo "Creating .env from .env.dist..."; \
		cp .env.dist .env; \
	else \
		echo ".env file already exists. Skipping creation."; \
	fi
	docker-compose down --remove-orphans
	docker-compose build
	docker-compose up -d
	$(MAKE) composer
	$(MAKE) update-backend
	$(MAKE) setup-test-db
	$(MAKE) update-frontend
	@echo "Project setup and build complete."
