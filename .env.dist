# In all environments, the following files are loaded if they exist,
# the latter taking precedence over the former:
#
#  * .env                contains default values for the environment variables needed by the app
#  * .env.local          uncommitted file with local overrides
#  * .env.$APP_ENV       committed environment-specific defaults
#  * .env.$APP_ENV.local uncommitted environment-specific overrides

###> symfony/framework-bundle ###
APP_ENV=dev
APP_RELEASE=local
APP_SECRET=88d94997fbf693a81b9200d6a3518953
APP_LOGLEVEL=error
APP_DOMAIN=localhost:8080
SUPPORT_EMAIL=<EMAIL>
SUPPORT_PHONE_NUMBER="+****************"
BILLING_SUPPORT_EMAIL=<EMAIL>
FEEDBACK_EMAIL=<EMAIL>
PARTNERSHIPS_EMAIL=<EMAIL>
USER_COUNTRY_COOKIE_EXPIRE_DAYS=1
BOT_USER_AGENTS='["Googlebot", "Bingbot", "Yahoo!"]'
COUNTDOWN_TTL_SEC=259200
INTERNAL_CLUSTER_CIDR=0.0.0.0/0
###< symfony/framework-bundle ###

FUNNEL_MAIN=quizMain

###> symfony/mailer ###
# MAILER_DSN=smtp://localhost
###< symfony/mailer ###

###> doctrine/doctrine-bundle ###
DATABASE_URL="mysql://root:root@mysql:3306/dog-training?serverVersion=5.7"
###< doctrine/doctrine-bundle ###

REDIS_DSN='redis://redis:6379/0'

###> solid payment api ###
SOLID_MAIN_PUBLIC_KEY="api_pk_c43b83312989425ca8b7a8f7c5042acd"
SOLID_USA_PUBLIC_KEY="api_pk_c43b83312989425ca8b7a8f7c5042acd"
SOLID_MAIN_SECRET_KEY="api_sk_8b656b69decc48619978b58193e77be0"
SOLID_USA_SECRET_KEY="api_sk_8b656b69decc48619978b58193e77be0"

SOLID_WEBHOOK_MAIN_PUBLIC_KEY="<wh_pk...>"
SOLID_WEBHOOK_MAIN_SECRET_KEY="<wh_sk...>"
SOLID_WEBHOOK_USA_PUBLIC_KEY="<wh_pk...>"
SOLID_WEBHOOK_USA_SECRET_KEY="<wh_sk...>"

SOLID_GOOGLE_MERCHANT_ID="m_id"
SOLID_ONE_TIME_SETTLEMENT_INTERVAL=0

###< solid payment api ###

###> Amplitude ###
AMPLITUDE_PUBLIC_KEY="********************************"
AMPLITUDE_API_KEY="********************************"
AMPLITUDE_API_SECRET="********************************"
AMPLITUDE_BASE_API_URL="https://api.amplitude.com"
###< Amplitude ###

###> Sentry ###
SENTRY_DSN_FRONTEND=""
SENTRY_DSN_BACKEND=""
SENTRY_TRACES_SAMPLE_RATE=1
SENTRY_SUCCESS_TRACES_SAMPLE_RATE=0.001
SENTRY_SAMPLE_RATE=1
SENTRY_PROFILE_SAMPLE_RATE=0.2
###< Sentry ###

GEOIP_URL="https://geoip.rs-tech.cloud"

FB_PIXEL_ID="583272243854110"

###> LMS ###
LMS_ENROLL_PRODUCT_ID="bundle1"
LMS_ENROLL_PRODUCT_TYPE="type"
LMS_DAYS_UNTIL_UNENROLL=30
LMS_CLIENT_SECRET=""
LMS_CLIENT_ID=""
LMS_BASE_URL_V2="https://www.paw-champ.com"
###< LMS ###

###> symfony/messenger ###
# Choose one of the transports below
MESSENGER_TRANSPORT_DSN=doctrine://default?queue_name=async&auto_setup=false
# MESSENGER_TRANSPORT_DSN=amqp://guest:guest@localhost:5672/%2f/messages
# MESSENGER_TRANSPORT_DSN=redis://localhost:6379/messages
MESSENGER_FAILURE_TRANSPORT_DSN=doctrine://default?queue_name=failed&auto_setup=false
MESSENGER_LMS_TRANSPORT_DSN=doctrine://default?queue_name=lms&auto_setup=false
MESSENGER_MAIL_TRANSPORT_DSN=doctrine://default?queue_name=mail&auto_setup=false
MESSENGER_CPA_TRANSPORT_DSN=doctrine://default?queue_name=cpa&auto_setup=false
MESSENGER_FB_EXPORT_TRANSPORT_DSN=doctrine://default?queue_name=fb_export&auto_setup=false
MESSENGER_FAILURE_JSON_TRANSPORT_DSN=doctrine://default?queue_name=failed_json&auto_setup=false
MESSENGER_PUBSUB_RECEIVE_USER_EVENT_TRANSPORT_DSN=doctrine://default?queue_name=user_event_receive&auto_setup=false
MESSENGER_PUBSUB_SEND_USER_EVENT_TRANSPORT_DSN=doctrine://default?queue_name=user_event_send&auto_setup=false
MESSENGER_PUBSUB_SEND_SUBSCRIPTION_EVENT_TRANSPORT_DSN=doctrine://default?queue_name=subscritpion_event_send&auto_setup=false
MESSENGER_PUBSUB_SEND_USER_QUIZ_EVENT_TRANSPORT_DSN=doctrine://default?queue_name=user_quiz_event_send&auto_setup=false
###< symfony/messenger ###

###> Growth Book ###
GROWTH_BOOK_API_ENDPOINT="https://growthbook-api.private-zoo.com/todo"
GROWTH_BOOK_API_SECRET=""
GROWTH_BOOK_WEBHOOK_SECRET=""
GROWTH_BOOK_CLIENT_KEY=""
GROWTH_BOOK_API_HOST="https://growthbook-api.private-zoo.com"
###< Growth Book ###

###> Listclean ###
LIST_CLEAN_URI="https://api.listclean.xyz"
LIST_CLEAN_TOKEN="token"
###> Listclean ###

GOOGLE_SHEET_REVEAL_BOT_ID=id

PHP_CS_FIXER_IGNORE_ENV=true

###> Postmark ###
POSTMARK_URI='https://postmarkapp.com'
POSTMARK_TOKEN='token'
POSTMARK_TEMPLATES='[]'
###< Postmark ###

###> Cloudflared tunnel ###
TUNNEL_NAME=tunnel-name.local
TUNNEL_URL=http://localhost:8080
# Configurates mitmproxy listen port. Will use localhost:$TUNNEL_PROXY_PORT instead of TUNNEL_URL.
TUNNEL_PROXY_PORT=8080
###< Cloudflared tunnel ###

ZD_WIDGET_AUTH_TOKEN=token

###> Mailkeeper ###
MAILKEEPER_API_URL=https://some.url
MAILKEEPER_PRODUCT_ID=product_id
MAILKEEPER_API_KEY=api_key
MAILKEEPER_CALLBACK_KEY=callback_key
MAILKEEPER_CHANNEL=paw-champ.private-zoo.com
MAILKEEPER_NON_LOGIN_REMINDER_INTERVAL_SECONDS=259200
###< Mailkeeper ###

###> Zendesk ###
ZENDESK_URI=https://zendesk.com
ZENDESK_EMAIL=<EMAIL>
ZENDESK_API_TOKEN=token
###< Zendesk ###

UNSUBSCRIBE_NOTIFICATION_INTERVAL=600

METRICS_BASIC_AUTH_PASSWORD=SuperStrongToken
METRICS_STORAGE_TYPE=in_memory

###> Trustpilot ###
TRUST_PILOT_API_KEY="key"
TRUST_PILOT_API_SECRET="secret"
TRUST_PILOT_API_BUSINESS_UNIT_ID="business_unit_id"
TRUST_PILOT_OAUTH_URL="https://api.trustpilot.com/v1/oauth/oauth-business-users-for-applications/accesstoken"
TRUST_PILOT_API_URL="https://api.trustpilot.com"
TRUST_PILOT_INVITATION_API_URL="https://invitations-api.trustpilot.com"
###< Trustpilot ###

###> Airtable ###
AIRTABLE_API_URL="https://api.airtable.com"
AIRTABLE_API_KEY="key"
AIRTABLE_CACHE_TTL=900
###< Airtable ###

###> Google Air Tag ###
GOOGLE_AIR_TAG_ID="G-GT3BBXMGCX"
###< Google Air Tag ###

###> Google Tag Manager ###
GOOGLE_TAG_MANAGER_ID="GTM-K2T8GW5F"
###< Google Tag Manager ###

###> Squidex ###
SQUIDEX_API_URL="https://cloud.squidex.io"
SQUIDEX_CLIENT_ID="client_id"
SQUIDEX_CLIENT_SECRET="client_secret"
SQUIDEX_ASSET_URL="https://cloud.squidex.io"
###< Squidex ###

###> Google ADS ###
GOOGLE_ADS_CUSTOMER_ID="customer_id"
GOOGLE_ADS_DEVELOPER_TOKEN="developer_token"
###< Google ADS ###

###> Mobile App ###
MOBILE_APP_API_URL="https://api.private-zoo.com"
MOBILE_APP_API_TOKEN="token"
USER_APP_DEEPLINK="https://pawchamp.onelink.me/kbDI/xl9gvqg8"
USER_APP_DEEPLINK_WEB_AND_LMS="https://pawchamp.onelink.me/kbDI/websitelms"
###< Mobile App ###

###> Crowdin ###
CROWDIN_TOKEN=
CROWDIN_PROJECT_ID=762181
CROWDIN_LOVE_BUNDLE_ID=9
CROWDIN_NUTRITION_BUNDLE_ID=11
CROWDIN_VAGUS_BUNDLE_ID=15
CROWDIN_CHALLENGE_BUNDLE_ID=17
###< Crowdin ###

###> Clarity ###
CLARITY_PROJECT_ID=qd8gv3k7pb
###< Clarity ###

###> Canonical Domain ###
CANONICAL_DOMAIN="https://paw-champ.com"
###< Canonical Domain ###
