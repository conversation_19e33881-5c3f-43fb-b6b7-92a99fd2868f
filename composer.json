{"type": "project", "license": "proprietary", "minimum-stability": "stable", "prefer-stable": true, "require": {"php": ">=8.2", "ext-ctype": "*", "ext-iconv": "*", "ext-imap": "*", "ext-json": "*", "ext-openssl": "*", "ext-redis": "*", "ext-zip": "*", "ext-zlib": "*", "artprima/prometheus-metrics-bundle": "^1.19", "composer/package-versions-deprecated": "1.11.99.*", "doctrine/annotations": "^2.0.1", "doctrine/doctrine-bundle": "^2.10.2", "doctrine/doctrine-migrations-bundle": "^3.2.4", "doctrine/orm": "^2.16.2", "easycorp/easyadmin-bundle": "^4.7.6", "facebook/php-business-sdk": "^22.0", "gentenox/sentry-monolog-handler": "^2.0.1", "google/apiclient": "^2.14.0", "google/cloud-pubsub": "^1.46", "googleads/google-ads-php": "^25.0", "growthbook/growthbook": "^1.3", "guzzlehttp/guzzle": "^6.5.8", "maknz/slack": "^1.7", "matomo/device-detector": "^6.1.5", "mlkp/mk-sdk": "^2.0", "nelmio/api-doc-bundle": "^4.23", "openspout/openspout": "^4.28", "phpdocumentor/reflection-docblock": "^5.3", "ramsey/uuid": "4.7.*", "scienta/doctrine-json-functions": "^5.3", "sentry/sentry-symfony": "^4.11", "solidgate/php-sdk": "1.5.*", "symfony/amqp-messenger": "6.3.*", "symfony/asset": "6.3.*", "symfony/console": "6.3.*", "symfony/doctrine-messenger": "6.3.*", "symfony/dotenv": "6.3.*", "symfony/expression-language": "6.3.*", "symfony/flex": "^v2.3.3", "symfony/form": "6.3.*", "symfony/framework-bundle": "6.3.*", "symfony/http-client": "6.3.*", "symfony/http-foundation": "6.3.*", "symfony/intl": "6.3.*", "symfony/mailer": "6.3.*", "symfony/messenger": "6.3.*", "symfony/mime": "6.3.*", "symfony/monolog-bundle": "^3.8", "symfony/notifier": "6.3.*", "symfony/password-hasher": "6.3.*", "symfony/process": "6.3.*", "symfony/property-access": "6.3.*", "symfony/property-info": "6.3.*", "symfony/proxy-manager-bridge": "6.3.*", "symfony/runtime": "6.3.*", "symfony/security-bundle": "6.3.*", "symfony/security-csrf": "6.3.*", "symfony/serializer": "6.3.*", "symfony/string": "6.3.*", "symfony/translation": "6.3.*", "symfony/twig-bundle": "6.3.*", "symfony/uid": "6.3.*", "symfony/validator": "6.3.*", "symfony/web-link": "6.3.*", "symfony/yaml": "6.3.*", "twig/extra-bundle": "^3.7.1", "twig/twig": "^3.7.1"}, "require-dev": {"dama/doctrine-test-bundle": "^v7.2.1", "doctrine/doctrine-fixtures-bundle": "^3.4.4", "friendsofphp/php-cs-fixer": "^3.25.1", "mikey179/vfsstream": "^1.6", "phpstan/extension-installer": "^1.3.1", "phpstan/phpstan": "^1.10.33", "phpstan/phpstan-doctrine": "^1.3.43", "phpstan/phpstan-symfony": "^1.3.2", "phpunit/phpunit": "^10.3.3", "rector/rector": "^0.18.2", "symfony/browser-kit": "6.3.*", "symfony/css-selector": "6.3.*", "symfony/debug-bundle": "6.3.*", "symfony/maker-bundle": "^1.50", "symfony/phpunit-bridge": "6.3.*", "symfony/stopwatch": "6.3.*", "symfony/var-dumper": "6.3.*", "symfony/web-profiler-bundle": "6.3.*"}, "config": {"optimize-autoloader": true, "preferred-install": {"*": "dist"}, "sort-packages": true, "allow-plugins": {"composer/package-versions-deprecated": true, "symfony/flex": true, "symfony/runtime": true, "phpstan/extension-installer": true, "php-http/discovery": false}}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*"}, "repositories": [{"type": "vcs", "url": "https://gitlab.com/mlkp/mk-sdk"}], "scripts": {"auto-scripts": {}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "6.3.*"}}}