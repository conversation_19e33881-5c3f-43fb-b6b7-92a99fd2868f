# Dog Training

#### Initial project setup


```shell
$ make init
```

#### Load dev fixture data from multiple CSV files into the local database
```shell
$ make load-dev-fixtures loads 
```


## Code Quality

---

#### Using MAKEFILE commands

To run code quality tools:
```shell
$ make code-quality
```

### PHP CS Fixer

To keep our code clear and maintainable was added `php-cs-fixer` package. 
It was integrated in our CI pipeline, as part of separate stage, called `code-analysis`.  
This stage will alert about any violation of coding standards. 
Despite that pipeline should succeed even with violations.  
Therefore, manual `fix` action MAY be done before committing code.

#### Using MAKEFILE commands

To run linter without changing files:
```shell
$ make php-cs-fixer-dry-run
```

To apply fix for all files:
```shell
$ make php-cs-fixer-fix
```

### PHPStan

PHPStan scans our code in `src` directory. 
It looks for possible bugs and syntax mistakes before they're reaching running environments.  
It is integrated with CI pipeline, as part of stage `code-analysis`.  
Config file `phpstan.neon` is stored in root of project.

#### Possible issues

If you encountered with PHPStan violation, that is a bug, or should not be fixed at very moment, 
you can take next steps:
 - `/* @phpstan-ignore-next-line */` this annotation helps to ignore line of code, that causes violation
 - add regex to `ignoreErrors` section of config file
 - disable unnecessary inspection in `parameters` section of config file

#### Using MAKEFILE commands

To run locally:
```shell
$ make phpstan
```
## API Documentation

Our API documentation is available at `/api/doc`.

---

## Solid integration

### [Checkout docs](docs/solid.md)

---
## Pulling Translations from Crowdin

To retrieve translations for a specific funnel:
- If you don't have a BUNDLE_ID locally, add the following environment variable to your .env.local  
Example: ```CROWDIN_<FUNNEL_NAME>_BUNDLE_ID=<bundle_id>```  
You can find the appropriate bundle_id on the Crowdin translations page: ```https://crowdin.com/project/<project-name>/translations```
- Run the corresponding Makefile script:  

To pull translations for a specific funnel: 
```shell 
$ make pull-vagus-translations
```
To pull translations for all funnels:
```shell 
$ make pull-translations
```

This will download the latest translation files into your project.

