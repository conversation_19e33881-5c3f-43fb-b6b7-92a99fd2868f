parameters:
    level: 6
    paths:
        - src
        - tests

    checkMissingIterableValueType: false
    checkGenericClassInNonGenericObjectType: false

    ignoreErrors:
        - '#Symfony\\Component\\Console\\Helper\\HelperInterface given#'
        - '#FacebookAds\\Object\\ServerSide#'
        - '#Call to an undefined method Symfony\\Component\\Serializer\\SerializerInterface#'
        - '#Property App\\Infrastructure\\ExternalService\\Solid\\SolidAPI::\$appDomain is never read#'
        - '#Property App\\Infrastructure\\ExternalService\\Solid\\SolidAPI::\$googlePayMerchantId is never read#'
        - '#Method App\\Infrastructure\\ExternalService\\Solid\\SolidAPI::allowApplePay\(\) is unused#'
