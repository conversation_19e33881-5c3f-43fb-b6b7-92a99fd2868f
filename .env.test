# define your env variables for the test env here
KERNEL_CLASS='App\Kernel'
APP_SECRET='$ecretf0rt3st'
SYMFONY_DEPRECATIONS_HELPER=999999
PANTHER_APP_ENV=panther
PANTHER_ERROR_SCREENSHOT_DIR=./var/error-screenshots

###> symfony/framework-bundle ###
APP_ENV=test
SUPPORT_EMAIL=<EMAIL>
SUPPORT_PHONE_NUMBER="+****************"
BILLING_SUPPORT_EMAIL=<EMAIL>
###< symfony/framework-bundle ###

###> doctrine/doctrine-bundle ###
DATABASE_URL="mysql://root:root@mysql:3306/test_dog_training?serverVersion=5.7"
###< doctrine/doctrine-bundle ###

REDIS_DSN=redis://redis:6379/0

###> solid payment api ###
SOLID_MAIN_PUBLIC_KEY=""
SOLID_MAIN_SECRET_KEY=""
SOLID_USA_PUBLIC_KEY=""
SOLID_USA_SECRET_KEY=""
###< solid payment api ###

###> Amplitude ###
AMPLITUDE_PUBLIC_KEY=""
AMPLITUDE_API_KEY=""
AMPLITUDE_API_SECRET=""
###< Amplitude ###

###> Sentry ###
SENTRY_DSN_FRONTEND=""
SENTRY_DSN_BACKEND=""
###< Sentry ###

GEOIP_URL="http://url"

FB_PIXEL_ID=""

###> LMS ###
LMS_ENROLL_PRODUCT_ID=""
LMS_DAYS_UNTIL_UNENROLL=30
###< LMS ###

REMOTE_ADDR="**************"

GROWTH_BOOK_WEBHOOK_SECRET="ababahalamaha"
GROWTH_BOOK_CLIENT_KEY="ababahalamaha"
GROWTH_BOOK_API_HOST="https://growthbook.io"

GOOGLE_SHEET_REVEAL_BOT_ID=id

###> Postmark ###
POSTMARK_URI='https://postmarkapp.com'
POSTMARK_TOKEN='token'
POSTMARK_TEMPLATES='[]'

ZD_WIDGET_AUTH_TOKEN=token

###> Zendesk ###
ZENDESK_URI=https://zendesk.com
ZENDESK_EMAIL=<EMAIL>
ZENDESK_API_TOKEN=token
###< Zendesk ###

UNSUBSCRIBE_NOTIFICATION_INTERVAL=600

###> Mailkeeper ###
MAILKEEPER_API_URL=https://some.url
MAILKEEPER_PRODUCT_ID=product_id
MAILKEEPER_API_KEY=api_key
MAILKEEPER_CALLBACK_KEY=callback_key
MAILKEEPER_NON_LOGIN_REMINDER_INTERVAL_SECONDS=259200
###< Mailkeeper ###

###> Google Air Tag ###
GOOGLE_AIR_TAG_ID="G-GT3BBXMGCX"
###> Google Air Tag ###

###> Google Tag Manager ###
GOOGLE_TAG_MANAGER_ID="GTM-K2T8GW5F"
###< Google Tag Manager ###

###> Google ADS ###
GOOGLE_ADS_CUSTOMER_ID="customer_id"
GOOGLE_ADS_DEVELOPER_TOKEN="developer_token"
###< Google ADS ###

###> Mobile App ###
MOBILE_APP_API_URL="https://api.private-zoo.com"
MOBILE_APP_API_TOKEN="token"
USER_APP_DEEPLINK="https://pawchamp.onelink.me/KXBI/ctiucmpr"
###< Mobile App ###

###> Clarity ###
CLARITY_PROJECT_ID=qd8gv3k7pb
###< Clarity ###
