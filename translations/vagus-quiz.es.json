{"quiz.agreement.template": "By choosing “Reactive girl” or “Reactive boy” and proceeding further you agree with the {termsOfUseLink}, {privacyPolicyLink} and {cookiePolicyLink}", "quiz.agreement.terms_of_use": "Terms of Use", "quiz.agreement.privacy_policy": "Privacy Policy", "quiz.agreement.cookie_policy": "<PERSON><PERSON>", "quiz.vagus_gender_reactive.title": "Personalized vagus reset challenge", "quiz.vagus_gender_reactive.subtitle": "To reduce leash pulling & calm your dog", "quiz.vagus_gender_reactive.your_dog_is": "Your dog is:", "quiz.vagus_gender_reactive.reactive_girl": "Reactive girl", "quiz.vagus_gender_reactive.reactive_boy": "Reactive boy", "quiz.vagus_age.title": "What is your dog's age?", "quiz.vagus_age.puppy.text": "<PERSON><PERSON><PERSON>", "quiz.vagus_age.puppy.description": "Less than 6 months", "quiz.vagus_age.adolescent.text": "Adolescent dog", "quiz.vagus_age.adolescent.description": "6-18 months", "quiz.vagus_age.adult.text": "Adult dog", "quiz.vagus_age.adult.description": "1.5-7 years", "quiz.vagus_age.senior.text": "Senior dog", "quiz.vagus_age.senior.description": "Above 7 years", "quiz.vagus_breed.title": "Choose your {petAge, select, puppy {pup} other {dog} }'s breed", "quiz.vagus_breed.subtitle": "Vagus nerve reset challenge according to your dog's breed", "quiz.vagus_breed.another_breed": "Another breed", "quiz.vagus_breed.placeholder.select_breed": "Select Breed", "quiz.vagus_breed.btn_next_step": "Next step", "quiz.vagus_breed.answers.mixed_breed": "Mixed breed", "quiz.vagus_breed.answers.labrador": "Labrador", "quiz.vagus_breed.answers.american_pit_bull_terrier": "American Pit Bull Terrier", "quiz.vagus_breed.answers.german_shepherd": "German Shepherd", "quiz.vagus_breed.answers.cocker_spaniel": "Cocker Spaniel", "quiz.vagus_breed.answers.french_bulldog": "French Bulldog", "quiz.vagus_breed.answers.chihuahua": "Chihuahua", "quiz.vagus_breed.answers.cockapoo": "Cockapoo", "quiz.vagus_breed.answers.border_collie": "Border Collie", "quiz.vagus_breed.answers.staffordshire_bull_terrier": "Staffordshire Bull Terrier", "quiz.dogs_owners.title": "Over 250,000 dog owners", "quiz.dogs_owners.academic_text": "Latest dog behavior discoveries were presented at **Oxford, Harvard,** and **Cambridge Universities**", "quiz.dogs_owners.subtitle": "choose **PawChamp Dog Vagus Reset Challenge** to help their dog's overcome reactivity!", "quiz.dogs_owners.text": "Reactivity isn't a flaw — it's your dog's vagus nerve asking for help", "quiz.dogs_owners.team": "team", "quiz.dogs_owners.continue": "Continue", "quiz.dog_dragging_down_the_street.title": "My dog is dragging me down the street", "quiz.dog_dragging_down_the_street.subtitle": "Do you relate to this statement?", "quiz.dog_dragging_down_the_street.agree_title": "Strongly agree", "quiz.dog_dragging_down_the_street.disagree_title": "Strongly disagree", "quiz.dog_dragging_down_the_street.recent_studies_title": "Recent studies show that...", "quiz.dog_dragging_down_the_street.recent_studies_text": "86% of dog owners dealing with leash pulling also notice signs of vagus nerve tension in their dogs. The next few questions will help determine if your dog's leash pulling is linked to vagus nerve tension and how much it's affecting them.", "quiz.dog_dragging_down_the_street.next_step": "Next step", "quiz.statement_relation.subtitle": "Do you relate to this statement?", "quiz.statement_relation.agree_title": "Strongly agree", "quiz.statement_relation.disagree_title": "Strongly disagree", "quiz.startles_noise.title": "My dog startles at every little noise", "quiz.startles_noise.subtitle": "Do you relate to this statement?", "quiz.startles_noise.agree_title": "Strongly agree", "quiz.startles_noise.disagree_title": "Strongly disagree", "quiz.barks_everything.title": "My dog is barking at everything in sight", "quiz.barks_everything.subtitle": "Do you relate to this statement?", "quiz.barks_everything.agree_title": "Strongly agree", "quiz.barks_everything.disagree_title": "Strongly disagree", "quiz.barks_everything.cta_title": "You need to know that...", "quiz.barks_everything.cta_text": "Even small hyper-reactions from your dog, such as barking at everything, can be the first warning signs that your dog has a problem with {petGender, select, female {her} other {his} } vagus nerve.", "quiz.barks_everything.next_step": "Next step", "quiz.upon_seeing_other_dogs.title": "My dog is going wild upon seeing other dogs", "quiz.upon_seeing_other_dogs.subtitle": "Do you relate to this statement?", "quiz.upon_seeing_other_dogs.agree_title": "Strongly agree", "quiz.upon_seeing_other_dogs.disagree_title": "Strongly disagree", "quiz.ignoring_commands_reactive.title": "Sometimes my dog ignores commands completely", "quiz.ignoring_commands_reactive.subtitle": "Do you relate to this statement?", "quiz.ignoring_commands_reactive.agree_title": "Strongly agree", "quiz.ignoring_commands_reactive.disagree_title": "Strongly disagree", "quiz.ignoring_commands_reactive.cta_title": "You might wonder why...", "quiz.ignoring_commands_reactive.cta_text": "The difficulty in following the owner's commands might be related to the vagus nerve, which plays a key role in your {petAge, select, puppy {pup} other {dog} }'s focus and command response.", "quiz.ignoring_commands_reactive.next_step_button": "Next step", "quiz.appetite_experience.title": "Do you sometimes experience sudden changes in {petAge, select, puppy {pup} other {dog} }'s appetite?", "quiz.appetite_experience.responses.eats_more": "Yes, {pet<PERSON><PERSON>, select, female {she} other {he} } eats more than usual", "quiz.appetite_experience.responses.no_change": "Haven't noticed anything", "quiz.appetite_experience.responses.loses_appetite": "Yes, {pet<PERSON><PERSON>, select, female {she} other {he} } loses appetite", "quiz.digestion_troubles.title": "Have you ever noticed your {petAge, select, puppy {pup} other {dog} }'s digestion out of balance lately?", "quiz.digestion_troubles.responses.yes_sometimes": "Yes, sometimes", "quiz.digestion_troubles.responses.not_sure": "I'm not sure", "quiz.digestion_troubles.responses.no_havent_noticed": "No, haven't noticed", "quiz.sleepy_changes.title": "Have you ever noticed physical changes in your {petAge, select, puppy {pup} other {dog} }'s behavior, like increased sleeping time?", "quiz.sleepy_changes.responses.sometimes": "Yes, sometimes", "quiz.sleepy_changes.responses.not_sure": "I'm not sure", "quiz.sleepy_changes.responses.no_changes": "No, haven't noticed any changes", "quiz.random_behavior_changes.title": "I feel like my {petAge, select, puppy {pup} other {dog} }'s behavior changes sometimes without any clear reason", "quiz.random_behavior_changes.subtitle": "Do you relate to this statement?", "quiz.random_behavior_changes.agree_title": "Strongly agree", "quiz.random_behavior_changes.disagree_title": "Strongly disagree", "quiz.random_behavior_changes.cta_title": "We know this might feel like...", "quiz.random_behavior_changes.cta_text": "Stress and an imbalanced vagus nerve can affect a dog's behavior in various ways. We've analyzed over 500 scientific studies to identify the most effective methods that specifically target the root cause, not just the symptoms.", "quiz.random_behavior_changes.next_step": "Next step", "quiz.react_come_home.title": "How does your dog react when you come home?", "quiz.react_come_home.subtitle": "Choose all that apply:", "quiz.react_come_home.responses.scratches_the_door": "Scratches the door even before you open it", "quiz.react_come_home.responses.extremely_excited": "Extremely excited, jumping and licking", "quiz.react_come_home.responses.exited_and_pees": "So excited that {pet<PERSON><PERSON>, select, female {she} other {he} } pees", "quiz.react_come_home.responses.barks_loudly": "Barks loudly", "quiz.react_come_home.responses.hides_or_cowers": "Hides or cowers", "quiz.react_come_home.responses.calmly_greets": "<PERSON><PERSON><PERSON> greets you", "quiz.react_come_home.next_step": "Next step", "quiz.behaviors_to_improve.title": "Which of these behavioral issues or tendencies do you observe in your dog?", "quiz.behaviors_to_improve.subtitle": "Choose all that apply:", "quiz.behaviors_to_improve.responses.excessive_energy": "Excessive energy and lack of control", "quiz.behaviors_to_improve.responses.aggression": "Aggression towards people or other animals", "quiz.behaviors_to_improve.responses.leash_pulling": "Leash pulling", "quiz.behaviors_to_improve.responses.separation_anxiety": "Separation anxiety", "quiz.behaviors_to_improve.responses.excessive_barking": "Excessive barking", "quiz.behaviors_to_improve.responses.destructive_behavior": "Destructive behavior", "quiz.behaviors_to_improve.responses.house_soiling": "House soiling", "quiz.behaviors_to_improve.next_step": "Next step", "quiz.react_scared.title": "Does your dog easily gets scared or stressed?", "quiz.react_scared.responses.yes": "Yes", "quiz.react_scared.responses.not_always": "Not always", "quiz.react_scared.responses.no": "No", "quiz.stress_triggers.title": "What triggers your dog's fear or stress?", "quiz.stress_triggers.subtitle": "Choose all that apply:", "quiz.stress_triggers.responses.other_dogs.title": "Other dogs", "quiz.stress_triggers.responses.new_people.title": "New people", "quiz.stress_triggers.responses.thunderstorms_noise.title": "Thunderstorms or loud noises", "quiz.stress_triggers.responses.unexpected_touch.title": "Unexpected touch or handling", "quiz.stress_triggers.responses.being_left_alone.title": "Being left alone", "quiz.stress_triggers.responses.vet_visits.title": "Vet visits", "quiz.stress_triggers.responses.grooming.title": "Grooming", "quiz.stress_triggers.responses.seeing_animals.title": "Seeing animals", "quiz.stress_triggers.responses.car_rides_travel.title": "Car rides or travel", "quiz.stress_triggers.responses.fireworks_celebrations.title": "Fireworks or celebrations", "quiz.stress_triggers.responses.other.title": "Other", "quiz.stress_triggers.next_step": "Next step", "quiz.aggressive.tendency_title": "Does your dog easily gets aggressive?", "quiz.aggressive.responses.yes": "Yes", "quiz.aggressive.responses.not_always": "Not always", "quiz.aggressive.responses.no": "No", "quiz.aggression_triggers.title": "What triggers your dog to aggression?", "quiz.aggression_triggers.choose_all": "Choose all that apply:", "quiz.aggression_triggers.responses.other_dogs": "Other dogs", "quiz.aggression_triggers.responses.new_people": "New people", "quiz.aggression_triggers.responses.thunderstorms": "Thunderstorms or loud noises", "quiz.aggression_triggers.responses.unexpected_touch": "Unexpected touch or handling", "quiz.aggression_triggers.responses.approached_while_eating": "Being approached while eating", "quiz.aggression_triggers.responses.toys_taken_away": "Having their toys taken away", "quiz.aggression_triggers.responses.left_alone": "Being left alone", "quiz.aggression_triggers.responses.vet_visits": "Vet visits", "quiz.aggression_triggers.responses.grooming": "Grooming", "quiz.aggression_triggers.responses.seeing_animals": "Seeing animals", "quiz.aggression_triggers.responses.other": "Other", "quiz.aggression_triggers.next_step": "Next step", "quiz.easily_excited.title": "Does your dog easily gets excited?", "quiz.easily_excited.responses.yes": "Yes", "quiz.easily_excited.responses.not_always": "Not always", "quiz.easily_excited.responses.no": "No", "quiz.vagus_excitement_triggers.title": "What triggers your dog to excitement?", "quiz.vagus_excitement_triggers.subtitle": "Choose all that apply:", "quiz.vagus_excitement_triggers.responses.other_dogs": "Other dogs", "quiz.vagus_excitement_triggers.responses.new_people": "New people", "quiz.vagus_excitement_triggers.responses.toys": "Toys", "quiz.vagus_excitement_triggers.responses.food": "Food", "quiz.vagus_excitement_triggers.responses.outdoor_activities": "Walks or outdoor activities", "quiz.vagus_excitement_triggers.responses.family_members": "Seeing family members", "quiz.vagus_excitement_triggers.responses.small_animals": "Small animals (like squirrels or cats)", "quiz.vagus_excitement_triggers.responses.other": "Other", "quiz.vagus_excitement_triggers.next_step": "Next step", "quiz.behaviors.identification.title": "We identified what influences your dog's behavior!", "quiz.behaviors.triggers_knowledge.title": "Knowing the triggers is crucial", "quiz.behaviors.identification.vagus_subtitle": "They play a significant role in how your dog responds to stimuli. Based on this, we can create an effective training plan to improve behavior and enhance the relationship between you and your dog.", "quiz.continue_button.text": "Continue", "quiz.vagus_nerve.title": "Have you ever heard about dog's vagus nerve?", "quiz.vagus_nerve.question": "Ever heard about dog's vagus nerve", "quiz.vagus_nerve.responses.nothing_at_all": "Nothing at all", "quiz.vagus_nerve.responses.maybe": "Maybe a thing or two", "quiz.vagus_nerve.responses.expert": "I'm an expert", "quiz.vagus_in_dogs_anyway.title": "**What's the Vagus Nerve** in dogs anyway?", "quiz.vagus_in_dogs_anyway.description": "The vagus nerve is a crucial part of your dog's nervous system, responsible for regulating relaxation, digestion, and heart rate. **When balanced**, it helps your **dog stay calm** and resilient to stress.", "quiz.vagus_in_dogs_anyway.subtitle_vagus": "However, an **imbalanced vagus nerve** can lead to **reactivity**, anxiety, digestive issues, and poor recovery from stress.", "quiz.vagus_in_dogs_anyway.subtitle_non_vagus": "However, an **imbalanced vagus nerve** can lead to **anxiety, reactivity,** digestive issues, and poor recovery from stress.", "quiz.vagus_in_dogs_anyway.quote_vagus": "Receive a personalized **Vagus Nerve Reset Challenge** tailored to your dog's breed, age, and behavior tendencies by finishing the quiz", "quiz.vagus_in_dogs_anyway.quote_non_vagus": "Take on customising the **Dog Vagus Reset Challenge** by **discovering your dog's triggers**", "quiz.vagus_in_dogs_anyway.continue_button": "Continue", "quiz.motivation_to_start.title": "What's your motivation to start the journey to reset your {petAge, select, puppy {puppy} other {dog} }'s vagus nerve?", "quiz.motivation_to_start.subtitle": "Choose all that apply:", "quiz.motivation_to_start.responses.motivation_love_for_dog": "My love for my {pet<PERSON>ge, select, puppy {puppy} other {dog} }", "quiz.motivation_to_start.responses.motivation_desire_longer_life": "Desire to make {pet<PERSON><PERSON>, select, puppy {pup} other {dog} }'s life longer and happier", "quiz.motivation_to_start.responses.motivation_easier_life": "Wishing for an easier life with my dog", "quiz.motivation_to_start.responses.motivation_other": "Other", "quiz.motivation_to_start.next_step": "Next step", "quiz.vagus_challenge.title": "Let's set your main goal for starting Vagus Nerve Reset Challenge!", "quiz.vagus_challenge.subtitle": "Choose your priority:", "quiz.vagus_challenge.responses.discover_vagus_nerve": "Get to know more about vagus nerve", "quiz.vagus_challenge.responses.calmer_dog": "Make my reactive dog more calmer", "quiz.vagus_challenge.responses.stronger_bond_with_dog": "Build a much stronger bond with my dog", "quiz.did_you_hear.title": "Did you hear about <PERSON><PERSON><PERSON><PERSON><PERSON> from a dog trainer?", "quiz.did_you_hear.responses.yes": "Yes", "quiz.did_you_hear.responses.no": "No", "quiz.vagus_practicing.title": "Your challenge will be reviewed by **practicing dog expert**", "quiz.vagus_practicing.subtitle": "“PawChamp incorporates positive reinforcement training to deliver personalized content and resources to dog owners for enhanced dog vagus nerve balancing”", "quiz.vagus_practicing.expert_name": "Jordan Zook", "quiz.vagus_practicing.content": "Content reviewed by an expert", "quiz.vagus_practicing.expert": "Certified Professional Dog Trainer", "quiz.vagus_practicing.continue_button": "Continue", "quiz.vagus_name.title": "Your dog's name is...", "quiz.vagus_name.label": "Type your dog's name:", "quiz.vagus_name.placeholder.name": "Name", "quiz.vagus_name.next_step_button": "Next step", "quiz.vagus_profile_react.title": "{petName} is all set for\n start Vagus Nerve Reset\n Challenge for Reactive dog", "quiz.vagus_profile_react.vagal_tension": "{petName}'s Vagal Tension", "quiz.vagus_profile_react.profile_level": "NORMAL - 4,5", "quiz.vagus_profile_react.relaxed": "relaxed", "quiz.vagus_profile_react.moderate": "MODERATE", "quiz.vagus_profile_react.medium": "MEDIUM", "quiz.vagus_profile_react.high": "HIGH", "quiz.vagus_profile_react.svg_text": "{petName} - 11,8", "quiz.vagus_profile_react.high_vagal_tension": "High vagal tension:", "quiz.vagus_profile_react.high_vagal_tension.description": "A dog with a high vagal tension may struggle with relaxation, stress recovery, and overall nervous system balance. This can lead to difficulties in training, reactivity, and a reduced quality of life for the dog and the owner.", "quiz.vagus_profile_react.behavioral_issues.title": "Behavioral Issues", "quiz.vagus_profile_react.reactivity_level.title": "Reactivity level", "quiz.vagus_profile_react.reactivity_level.text": "Intense", "quiz.vagus_profile_react.stress_level.title": "Stress level", "quiz.vagus_profile_react.stress_level.text": "Above average", "quiz.vagus_profile_react.readiness_to_start.title": "Readiness to start", "quiz.vagus_profile_react.readiness_to_start.text": "Perfect", "quiz.vagus_profile_react.continue": "Continue", "quiz.time_spend.title": "How much time are you ready to spend to make {<PERSON><PERSON><PERSON>}'s life better?", "quiz.time_spend.answer.seven_minutes": "7 min/day", "quiz.time_spend.answer.fifteen_minutes": "15 min/day", "quiz.time_spend.answer.thirty_minutes": "30 min/day", "quiz.time_spend.answer.sixty_minutes": "60 min/day", "quiz.vagus_graph_reactive.support_vagus_nerve": "This program is designed to support {pet<PERSON>ame}'s vagus nerve balance and reduce reactivity", "quiz.vagus_graph_reactive.decrease_vagal_tension": "Based on your answers, we expect you'll decrease {petName}'s Vagal Tension by", "quiz.vagus_graph_reactive.progress_chart_description": "This chart shows your potential progress if you follow all the steps listed in our plan", "quiz.vagus_graph_reactive.academic_presentation": "Latest dog training discoveries were presented at **Oxford, Harvard,** and **Cambridge Universities**", "quiz.vagus_graph_reactive.continue": "Continue", "quiz.result.result_step_one.subtitle": "Preparing {petName}'s personalized content for Vagus Nerve Reset Challenge ...", "quiz.result.result_step_one.title_red": "Over 250,000 dog owners", "quiz.result.result_step_one.text": "have chosen Paw<PERSON>ham<PERSON>", "quiz.result.reviews.jessie": "The lessons are short and easy to follow. the homework assignments are helpful.", "quiz.result.reviews.liam": "It is well explained for the people who never had a dog before. With a lot of common sense.", "quiz.result.reviews.sierra": "Immediate, attentive, personalized, relevant, and helpful", "quiz.result.reviews.calming_approach.title": "New approach to calmness", "quiz.result.reviews.calming_approach.text": "This was a new way for me to learn how to help my dog stay calm. We're not finished yet, but I've already seen big improvements with our 8-year-old German shepherd who had some training but struggled with reactivity.", "quiz.result.reviews.structure_brilliant.title": "Structure is brilliant", "quiz.result.reviews.structure_brilliant.text": "The way the everything is broken down is excellent. I've had one on one training at my house and have found it less effective than this course and would highly recommend it.", "quiz.result.reviews.excellent_course.title": "Excellent course", "quiz.result.reviews.excellent_course.text": "Excellent course, very detailed and easy to understand. What I like about this course is they emphasize that a dog requires patience and understanding the process. I'm still on week one, but their approach seems easy to follow. Can't wait to see the end results…", "quiz.result.result_step_two.title": "Enter your email to get {pet<PERSON><PERSON>}'s personalized Vagus Nerve Reset Challenge for reactive dogs", "quiz.result.result_step_two.enter_email_prompt": "Type your email:", "quiz.result.result_step_two.email_placeholder": "Enter your email to get your plan", "quiz.result.result_step_two.join_us": "**250,000** dog owners have **joined us!**", "quiz.result.result_step_two.privacy_note": "We protect your privacy and are committed to protecting your personal data. We never send spam emails, only relevant information.", "quiz.result.continue_button": "Continue", "quiz.result.email_subscription.title": "Do you want to receive emails with **special offers**, dog training tips, advice and **free gifts**?", "quiz.result.email_subscription.yes_button": "Yes, I'm in!", "quiz.result.email_subscription.no_checkbox": "I'm not interested", "quiz.result.result_step_three.title": "{petName}'s vagal tone", "quiz.result.result_step_three.week_1": "WEEK 1", "quiz.result.result_step_three.week_2": "WEEK 2", "quiz.result.result_step_three.week_3": "WEEK 3", "quiz.result.result_step_three.week_4": "WEEK 4", "quiz.result.result_step_three.chart_explanation": "This chart shows your potential progress if you follow all the steps listed in our plan", "quiz.result.result_step_three.personalized_ready": "{petN<PERSON>}'s personalized **Vagus Nerve Reset Challenge** is ready", "quiz.result.discount.title": "**Scratch & Save** on {petName}'s personalized Vagus Nerve Reset Challenge!", "quiz.result.discount.description": "Help your dog to become Calm&Focused today!", "quiz.result.discount.message": "off {<PERSON><PERSON><PERSON>}'s personalized vagus nerve reset challenge", "quiz.result.popup_problem.title": "One more question", "quiz.result.popup_problem.text": "Has {<PERSON><PERSON><PERSON>} ever had desensitisation training?", "quiz.result.popup_problem.no_button": "No", "quiz.result.popup_problem.yes_button": "Yes", "quiz.result.popup_obedience.title": "Finalizing your plan", "quiz.result.popup_obedience.text": "Are you inclined to finish what you start?", "quiz.result.popup_obedience.no_button": "No", "quiz.result.popup_obedience.yes_button": "Yes", "quiz.result.scratch_card_hint": "Scratch your discount", "quiz.result.scratch_card_woohoo": "Woo hoo!", "quiz.result.scratch_card_you_won": "You won a discount", "quiz.result.scratch_card_discount_off": "{discountPercents} off", "quiz.result.scratch_card_disclaimer": "*This discount will be applied automatically", "quiz.result.scratch_card_continue": "Continue"}