{"quiz.agreement.template": "By choosing “<PERSON> Girl” or “<PERSON> Boy” and proceeding further you agree with the {termsOfUseLink}, {privacyPolicyLink} and {cookiePolicyLink}", "quiz.agreement.terms_of_use": "Terms of Use", "quiz.agreement.privacy_policy": "Privacy Policy", "quiz.agreement.cookie_policy": "<PERSON><PERSON>", "quiz.gender.subtitle": "2-minute quiz", "quiz.gender.title": "How much do you love your dog?", "quiz.gender.text": "YOUR DOG IS:", "quiz.gender.responses.girl": "Good Girl", "quiz.gender.responses.boy": "Good Boy", "quiz.pet_age.title": "What is your dog’s age?", "quiz.pet_age.responses.puppy.label": "<PERSON><PERSON><PERSON>", "quiz.pet_age.responses.puppy.age_range": "Less than 6 months", "quiz.pet_age.responses.adolescent.label": "Adolescent dog", "quiz.pet_age.responses.adolescent.age_range": "6-18 months", "quiz.pet_age.responses.adult.label": "Adult dog", "quiz.pet_age.responses.adult.age_range": "1.5-7 years", "quiz.pet_age.responses.senior.label": "Senior dog", "quiz.pet_age.responses.senior.age_range": "Above 7 years", "quiz.snuggle_with_dog.title": "I snuggle with my {pet<PERSON>ge, select, puppy {pup} other {dog} } all the time", "quiz.snuggle_with_dog.subtitle": "Do you relate to this statement?", "quiz.snuggle_with_dog.strongly_agree": "Strongly agree", "quiz.snuggle_with_dog.strongly_disagree": "Strongly disagree", "quiz.photos_of_dog.title": "I take more photos of my {petAge, select, puppy {puppy} other {dog} } than I do of myself", "quiz.photos_of_dog.subtitle": "Do you relate to this statement?", "quiz.photos_of_dog.strongly_agree": "Strongly agree", "quiz.photos_of_dog.strongly_disagree": "Strongly disagree", "quiz.spoil_dog_with_treats.title": "I spoil my {petAge, select, puppy {pup} other {dog} } with new toys and treats every chance I get", "quiz.spoil_dog_with_treats.subtitle": "Do you relate to this statement?", "quiz.spoil_dog_with_treats.strongly_agree": "Strongly agree", "quiz.spoil_dog_with_treats.strongly_disagree": "Strongly disagree", "quiz.dog_sleep_wherever_wants.title": "I allow my {pet<PERSON><PERSON>, select, puppy {pup} other {dog} } to sleep wherever {pet<PERSON><PERSON>, select, female {she} other {he} } wants", "quiz.dog_sleep_wherever_wants.subtitle": "Do you relate to this statement?", "quiz.dog_sleep_wherever_wants.strongly_agree": "Strongly agree", "quiz.dog_sleep_wherever_wants.strongly_disagree": "Strongly disagree", "quiz.jump_into_fire_for_dog.title": "I’d jump into the fire for my {petAge, select, puppy {pup} other {doggo} } without hesitation", "quiz.jump_into_fire_for_dog.subtitle": "Do you relate to this statement?", "quiz.jump_into_fire_for_dog.strongly_agree": "Strongly agree", "quiz.jump_into_fire_for_dog.strongly_disagree": "Strongly disagree", "quiz.love_level.progress_message": "You're making great progress in filling the love level by answering questions!", "quiz.love_level.title": "Love Level", "quiz.love_level.calculating": "Calculating...", "quiz.love_level.dogs_understand": "Dogs don’t understand words, only actions - finishing this quiz is one of them!", "quiz.love_level.continue": "Continue", "quiz.feel_guilty_for_not_spending_time.title": "I feel guilty for not spending enough time with my {petAge, select, puppy {pup} other {dog} }", "quiz.feel_guilty_for_not_spending_time.subtitle": "Do you relate to this statement?", "quiz.feel_guilty_for_not_spending_time.strongly_agree": "Strongly agree", "quiz.feel_guilty_for_not_spending_time.strongly_disagree": "Strongly disagree", "quiz.feel_bad_when_dog_looks_upset.title": "I feel bad when I lose my temper and my {petAge, select, puppy {pup} other {dog} } looks upset", "quiz.feel_bad_when_dog_looks_upset.subtitle": "Do you relate to this statement?", "quiz.feel_bad_when_dog_looks_upset.strongly_agree": "Strongly agree", "quiz.feel_bad_when_dog_looks_upset.strongly_disagree": "Strongly disagree", "quiz.satisfied_with_behavior.title": "Are you satisfied with your {petAge, select, puppy {pup} other {dog} }’s behavior?", "quiz.satisfied_with_behavior.responses.yes": "Perfectly, yes", "quiz.satisfied_with_behavior.responses.could_be_better": "Could be much better", "quiz.satisfied_with_behavior.responses.no": "No, it’s a disaster", "quiz.misbehavior_reaction.title": "How do you react when your {petAge, select, puppy {puppy} other {doggo} } misbehaves?", "quiz.misbehavior_reaction.subtitle": "Choose all that apply:", "quiz.misbehavior_reaction.responses.physical_punishment": "Physical punishment", "quiz.misbehavior_reaction.responses.yelling_shouting": "Yelling or shouting", "quiz.misbehavior_reaction.responses.isolation": "Isolating from a dog", "quiz.misbehavior_reaction.responses.ignoring": "Ignoring this fact and moving forward", "quiz.misbehavior_reaction.responses.calm_redirection": "Trying calmly redirect my dog to me", "quiz.misbehavior_reaction.responses.do_not_remember": "I don't remember", "quiz.misbehavior_reaction.next_step": "Next Step", "quiz.behaviors_to_improve.title": "Which of these misbehavioral tendencies do you observe in your {petAge, select, puppy {pup} other {dog} }?", "quiz.behaviors_to_improve.subtitle": "Choose all that apply:", "quiz.behaviors_to_improve.responses.excessive_energy": "Excessive energy and lack of control", "quiz.behaviors_to_improve.responses.aggression": "Aggression towards people or other animals", "quiz.behaviors_to_improve.responses.leash_pulling": "Leash pulling", "quiz.behaviors_to_improve.responses.separation_anxiety": "Separation anxiety", "quiz.behaviors_to_improve.responses.excessive_barking": "Excessive barking", "quiz.behaviors_to_improve.responses.destructive_behavior": "Destructive behavior (chewing, scratching, digging)", "quiz.behaviors_to_improve.responses.house_soiling": "House soiling", "quiz.behaviors_to_improve.next_step": "Next step", "quiz.love_level_disobedience_barrier.title": "Disobedience is a barrier to love", "quiz.love_level_disobedience_barrier.love_level": "Love Level", "quiz.love_level_disobedience_barrier.calculating": "Calculating...", "quiz.love_level_disobedience_barrier.barrier_message": "When your **dog misbehaves**, frustration can create a **barrier** between you ", "quiz.love_level_disobedience_barrier.encouragement": "**Keep answering to break the barriers and boost love!**", "quiz.love_level_disobedience_barrier.continue_button": "Continue", "quiz.change_dogs_life_for_better.title": "Would you like to change your {petAge, select, puppy {pup} other {dog} }’s life for the better?", "quiz.change_dogs_life_for_better.responses.definitely": "Definitely!", "quiz.change_dogs_life_for_better.responses.no": "No", "quiz.how_important_training.title": "How important do you think dog training is?", "quiz.how_important_training.responses.very_important": "Very important", "quiz.how_important_training.responses.somewhat_important": "Somewhat important", "quiz.how_important_training.responses.not_important": "Not important", "quiz.training_is_reflection_of_love.title": "Dog training is a reflection of your love", "quiz.training_is_reflection_of_love.description": "**A trained dog feels more confident, safe, and\nloved**, knowing they have clear expectations\nand support.", "quiz.training_is_reflection_of_love.continue_button": "Continue", "quiz.how_long_you_walk_with_dog.title": "How long do you walk your {petAge, select, puppy {pup} other {dog} }?", "quiz.how_long_you_walk_with_dog.responses.ten_minutes": "10 min/day", "quiz.how_long_you_walk_with_dog.responses.fifteen_minutes": "15 min/day", "quiz.how_long_you_walk_with_dog.responses.thirty_minutes": "30 min/day", "quiz.how_long_you_walk_with_dog.responses.sixty_minutes": "60 min/day", "quiz.training_during_walk.title": "Did you know you can train your {petAge, select, puppy {pup} other {dog} } effectively during walks?", "quiz.training_during_walk.responses.kind_of": "Kind of", "quiz.training_during_walk.responses.wow": "Wow!", "quiz.structured_plan.title": "Would you like to get a structured dog training plan?", "quiz.structured_plan.responses.sure": "Sure!", "quiz.structured_plan.responses.not_sure": "Not sure", "quiz.breed.title": "Choose your {petAge, select, puppy {pup} other {dog} }’s breed", "quiz.breed.subtitle": "Training plan according to your {petAge, select, puppy {pup’s} other {dog’s} } breed", "quiz.breed.label": "Another breed", "quiz.breed.select_breed": "Select breed", "quiz.breed.responses.mixed_breed": "Mixed breed", "quiz.breed.responses.german_shepherd": "German Shepherd", "quiz.breed.responses.border_collie": "Border Collie", "quiz.breed.responses.american_pit_bull_terrier": "American Pit Bull Terrier", "quiz.breed.responses.labrador": "Labrador", "quiz.breed.responses.golden_retriever": "Golden Retriever", "quiz.breed.responses.australian_shepherd": "Australian Shepherd", "quiz.breed.responses.french_bulldog": "French Bulldog", "quiz.breed.responses.chihuahua": "Chihuahua", "quiz.breed.responses.staffordshire_bull_terrier": "Staffordshire Bull Terrier", "quiz.breed.next_step": "Next step", "quiz.practicing_dog_behaviorist.title": "Your plan will be reviewed by **practicing dog behaviorist**", "quiz.practicing_dog_behaviorist.text": "“PawChamp incorporates positive reinforcement training to deliver personalized content and resources to dog owners for enhanced dog training”", "quiz.practicing_dog_behaviorist.expert_name": "Jordan Zook", "quiz.practicing_dog_behaviorist.reviewed_by_expert": "Content reviewed by an expert", "quiz.practicing_dog_behaviorist.certified_trainer": "Certified Professional Dog Trainer", "quiz.practicing_dog_behaviorist.continue": "Continue", "quiz.how_you_call_your_dog.title": "How do you call your dog?", "quiz.how_you_call_your_dog.responses.cute_nicknames": "Cute nicknames", "quiz.how_you_call_your_dog.responses.by_name": "By {pet<PERSON><PERSON>, select, female {her} other {his} } name", "quiz.how_you_call_your_dog.responses.dog_doggy": "Dog/doggy", "quiz.how_you_call_your_dog.responses.hey_you": "Hey YOU!", "quiz.dog_name.title": "And your {petAge, select, puppy {puppy} other {dog} }’s name is...", "quiz.dog_name.label": "Type your {petAge, select, puppy {pup} other {dog} }’s name:", "quiz.dog_name.input_placeholder": "Name", "quiz.dog_name.next_step": "Next step", "quiz.love_profile.title": "Here’s your summary:", "quiz.love_profile.love_level": "Love Level: ", "quiz.love_profile.readiness_score.title": "Readiness score", "quiz.love_profile.readiness_score.result": "Result: Perfect", "quiz.love_profile.readiness_score.level.extremely_low": "Extremely low", "quiz.love_profile.readiness_score.level.low": "Low", "quiz.love_profile.readiness_score.level.medium": "Medium", "quiz.love_profile.readiness_score.level.high": "High", "quiz.love_profile.alert.impressive_score_title": "Impressive score for a dog owner!", "quiz.love_profile.alert.impressive_score_text": "Such readiness score indicates that you are motivated to start training {petName}. It makes training easier, improves understanding, and brings you closer to your dog.", "quiz.love_profile.list.love_level.title": "Love Level", "quiz.love_profile.list.love_level.text": "High", "quiz.love_profile.list.motivation.title": "Motivation", "quiz.love_profile.list.motivation.text": "Moderate", "quiz.love_profile.list.trainability.title": "Trainability", "quiz.love_profile.list.trainability.text": "Medium", "quiz.love_profile.list.knowledge.title": "Knowledge", "quiz.love_profile.list.knowledge.text": "Low", "quiz.love_profile.continue": "Continue", "quiz.ask_qualified_handler_question.title": "Would you like to ask questions about dog behavior, training, etc. to a qualified dog handler online?", "quiz.ask_qualified_handler_question.responses.yes": "Yes, it would be great", "quiz.ask_qualified_handler_question.responses.no": "No, thanks", "quiz.time_ready_to_spend.title": "How much time are you ready to spend to make {<PERSON><PERSON><PERSON>}’s life better?", "quiz.time_ready_to_spend.responses.5_min": "5 min/day", "quiz.time_ready_to_spend.responses.10_min": "10 min/day", "quiz.time_ready_to_spend.responses.15_min": "15 min/day", "quiz.time_ready_to_spend.responses.30_min": "30 min/day", "quiz.time_ready_to_spend.responses.60_min": "60 min/day", "quiz.journey_chart.title": "Your Pawfect Journey with Personalised Dog Obedience Plan", "quiz.journey_chart.subtitle": "Based on your answers, we expect you’ll increase {petName}’s well-being by", "quiz.journey_chart.chart_tooltip": "{pet<PERSON><PERSON>}'s\nwell-being", "quiz.journey_chart.chart_tooltip.start": "Love Level", "quiz.journey_chart.chart_tooltip.end": "{pet<PERSON><PERSON>}'s\nwell-being", "quiz.journey_chart.today": "Today", "quiz.journey_chart.first_month": "1-st month", "quiz.journey_chart.chart_subtitle": "This chart shows your potential progress if you follow all the steps listed in our plan", "quiz.journey_chart.continue": "Continue", "quiz.result.loading.creating_plan": "Creating {<PERSON><PERSON><PERSON>}'s personalized obedience plan...", "quiz.result.loading.over_100000_owners": "Over 100,000 dogs owners", "quiz.result.loading.over_250000_owners": "Over 250,000 dog owners", "quiz.result.loading.chosen_us": "have chosen Paw<PERSON>ham<PERSON>", "quiz.result.review.reinforcement_techniques.title": "Great positive reinforcement techniques", "quiz.result.review.reinforcement_techniques.author": "Dawg Daddy", "quiz.result.review.reinforcement_techniques.text": "This is a great training program that starts from the very base which is critical. It makes its way up the ladder with positive reinforcement techniques that truly work. I’ve seen great results with my 2 dogs.", "quiz.result.review.training_structure.title": "Training structure is brilliant", "quiz.result.review.training_structure.author": "<PERSON>", "quiz.result.review.training_structure.text": "The way the training is broken down is excellent. I’ve had one on one training at my house and have found it less effective than this course and would highly recommend it.", "quiz.result.review.excellent_course.title": "Excellent training course", "quiz.result.review.excellent_course.author": "<PERSON>", "quiz.result.review.excellent_course.text": "Excellent training course, very detailed and easy to understand. What I like about this course is they emphasize that training a dog requires patience and understanding the process. I’m still on week one, but their approach seems easy to follow. Can’t wait to see the end results…", "quiz.result.enter_email.title": "Enter your email to get {pet<PERSON><PERSON>}’s personalized obedience plan", "quiz.result.enter_email.placeholder": "Enter your email to get your plan", "quiz.result.enter_email.privacy_protection": "We protect your privacy and are committed to protecting your personal data. We never send spam emails, only relevant information.", "quiz.result.enter_email.continue": "Continue", "quiz.result.email_subscription.title": "Do you want to receive emails with **special offers**, dog training tips, advice and **free gifts**?", "quiz.result.email_subscription.yes": "Yes, keep me updated!", "quiz.result.email_subscription.no": "No, thanks", "quiz.result.obedience_level_chart.title": "{pet<PERSON><PERSON>}’s personalized **dog obedience plan** is ready", "quiz.result.obedience_level_chart.start": "Today", "quiz.result.obedience_level_chart.end": "After using\nPawChamp", "quiz.result.obedience_level_chart.week1": "WEEK 1", "quiz.result.obedience_level_chart.week2": "WEEK 2", "quiz.result.obedience_level_chart.week3": "WEEK 3", "quiz.result.obedience_level_chart.week4": "WEEK 4", "quiz.result.obedience_level_chart.chart_explanation": "This chart shows your potential progress if you follow all the steps listed in our plan", "quiz.result.obedience_level_chart.continue": "Continue", "quiz.result.popup.first_popup.title": "One more question", "quiz.result.popup.first_popup.question": "Has {<PERSON><PERSON><PERSON>} ever had obedience training?", "quiz.result.popup.first_popup.responses.yes": "Yes", "quiz.result.popup.first_popup.responses.no": "No", "quiz.result.popup.second_popup.title": "Finalizing your plan", "quiz.result.popup.second_popup.question": "Are you inclined to finish what you start?", "quiz.result.popup.second_popup.responses.yes": "Yes", "quiz.result.popup.second_popup.responses.no": "No"}