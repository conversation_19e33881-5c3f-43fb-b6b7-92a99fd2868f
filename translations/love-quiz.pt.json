{"quiz.agreement.template": "Ao escolher “G<PERSON><PERSON> ou “<PERSON>aroto” e prosseguir, você concorda com os {termsOfUseLink}, {privacyPolicyLink} e {cookiePolicyLink}", "quiz.agreement.terms_of_use": "Termos de uso", "quiz.agreement.privacy_policy": "Política de privacidade", "quiz.agreement.cookie_policy": "Política de cookies", "quiz.gender.subtitle": "Teste de 2 minuto", "quiz.gender.title": "O quanto você ama seu cachorro?", "quiz.gender.text": "O SEU CÃO É:", "quiz.gender.responses.girl": "<PERSON><PERSON> menina", "quiz.gender.responses.boy": "<PERSON><PERSON> garoto", "quiz.pet_age.title": "Qual é a idade do seu cão?", "quiz.pet_age.responses.puppy.label": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "quiz.pet_age.responses.puppy.age_range": "Menos de 6 meses", "quiz.pet_age.responses.adolescent.label": "<PERSON>ão adolescente", "quiz.pet_age.responses.adolescent.age_range": "6-18 meses", "quiz.pet_age.responses.adult.label": "Cão adulto", "quiz.pet_age.responses.adult.age_range": "1,5-7 anos", "quiz.pet_age.responses.senior.label": "Cão sênior", "quiz.pet_age.responses.senior.age_range": "Mais de 7 anos", "quiz.snuggle_with_dog.title": "Eu me aconchego com meu/minha {petAge, select, puppy {cachorro} other {cão} } a toda a hora", "quiz.snuggle_with_dog.subtitle": "Concorda com esta afirmação?", "quiz.snuggle_with_dog.strongly_agree": "Concordo plenamente", "quiz.snuggle_with_dog.strongly_disagree": "Discordo totalmente", "quiz.photos_of_dog.title": "Tiro mais fotografias do meu/minha {petAge, select, puppy {ca<PERSON><PERSON><PERSON>} other {cão} } do que de mim mesmo", "quiz.photos_of_dog.subtitle": "Concorda com esta afirmação?", "quiz.photos_of_dog.strongly_agree": "Concordo plenamente", "quiz.photos_of_dog.strongly_disagree": "Discordo totalmente", "quiz.spoil_dog_with_treats.title": "Mimo o meu/minha {petAge, select, puppy {cachorro} other {cão} } com novos brinquedos e guloseimas sempre que posso", "quiz.spoil_dog_with_treats.subtitle": "Concorda com esta afirmação?", "quiz.spoil_dog_with_treats.strongly_agree": "Concordo plenamente", "quiz.spoil_dog_with_treats.strongly_disagree": "Discordo totalmente", "quiz.dog_sleep_wherever_wants.title": "Permito que o meu/minha {petA<PERSON>, select, puppy {cachorro} other {cão} } durma onde {petGender, select, female {ela} other {ele} } quiser", "quiz.dog_sleep_wherever_wants.subtitle": "Concorda com esta afirmação?", "quiz.dog_sleep_wherever_wants.strongly_agree": "Concordo plenamente", "quiz.dog_sleep_wherever_wants.strongly_disagree": "Discordo totalmente", "quiz.jump_into_fire_for_dog.title": "I’d jump into the fire for my {petAge, select, puppy {pup} other {doggo} } without hesitation", "quiz.jump_into_fire_for_dog.subtitle": "Concorda com esta afirmação?", "quiz.jump_into_fire_for_dog.strongly_agree": "Concordo plenamente", "quiz.jump_into_fire_for_dog.strongly_disagree": "Discordo totalmente", "quiz.love_level.progress_message": "Você está fazendo um ótimo progresso em preencher o nível de amor respondendo às perguntas!", "quiz.love_level.title": "<PERSON><PERSON><PERSON>or", "quiz.love_level.calculating": "Calculando...", "quiz.love_level.dogs_understand": "Dogs don’t understand words, only actions - finishing this quiz is one of them!", "quiz.love_level.continue": "Continue", "quiz.feel_guilty_for_not_spending_time.title": "Me sinto culpado por não passar tempo suficiente com o meu/minha {petAge, select, puppy {cachorro} other {cão} }", "quiz.feel_guilty_for_not_spending_time.subtitle": "Concorda com esta afirmação?", "quiz.feel_guilty_for_not_spending_time.strongly_agree": "Concordo plenamente", "quiz.feel_guilty_for_not_spending_time.strongly_disagree": "Discordo totalmente", "quiz.feel_bad_when_dog_looks_upset.title": "Me sinto mal quando perco a calma e o meu/minha {petAge, select, puppy {cachorro} other {cão} } parece aborrecido", "quiz.feel_bad_when_dog_looks_upset.subtitle": "Concorda com esta afirmação?", "quiz.feel_bad_when_dog_looks_upset.strongly_agree": "Concordo plenamente", "quiz.feel_bad_when_dog_looks_upset.strongly_disagree": "Discordo totalmente", "quiz.satisfied_with_behavior.title": "Você está satisfeito com o comportamento do seu/sua {petAge, select, puppy {cachorro} other {cão} }?", "quiz.satisfied_with_behavior.responses.yes": "Perfeitamente, sim", "quiz.satisfied_with_behavior.responses.could_be_better": "Podia ser muito melhor", "quiz.satisfied_with_behavior.responses.no": "No, it’s a disaster", "quiz.misbehavior_reaction.title": "Como reage quando o seu/sua {petAge, select, puppy {cachorro} other {c<PERSON><PERSON><PERSON>} } se comporta mal?", "quiz.misbehavior_reaction.subtitle": "Se<PERSON><PERSON>e todas as opções aplicáveis:", "quiz.misbehavior_reaction.responses.physical_punishment": "Castigos físicos", "quiz.misbehavior_reaction.responses.yelling_shouting": "<PERSON><PERSON> ou berrar", "quiz.misbehavior_reaction.responses.isolation": "Isolamento do cão", "quiz.misbehavior_reaction.responses.ignoring": "Ignora o fato e segue em frente", "quiz.misbehavior_reaction.responses.calm_redirection": "Tento redirecionar calmamente o meu cão para mim", "quiz.misbehavior_reaction.responses.do_not_remember": "<PERSON><PERSON> me lembro", "quiz.misbehavior_reaction.next_step": "Próxima etapa", "quiz.behaviors_to_improve.title": "Quais destas tendências de comportamento incorreto observa no seu/sua {petAge, select, puppy {cachorro} other {cão} }?", "quiz.behaviors_to_improve.subtitle": "Se<PERSON><PERSON>e todas as opções aplicáveis:", "quiz.behaviors_to_improve.responses.excessive_energy": "Excesso de energia e falta de controle", "quiz.behaviors_to_improve.responses.aggression": "Agressividade contra pessoas ou outros animais", "quiz.behaviors_to_improve.responses.leash_pulling": "Puxões de coleira", "quiz.behaviors_to_improve.responses.separation_anxiety": "Ansiedade de separação", "quiz.behaviors_to_improve.responses.excessive_barking": "Latidos excessivos", "quiz.behaviors_to_improve.responses.destructive_behavior": "Comportamento destrutivo (mastigar, arranhar, cavar)", "quiz.behaviors_to_improve.responses.house_soiling": "Sujeira na casa", "quiz.behaviors_to_improve.next_step": "Próximo passo", "quiz.love_level_disobedience_barrier.title": "A desobediência é um obstáculo ao amor", "quiz.love_level_disobedience_barrier.love_level": "<PERSON><PERSON><PERSON>or", "quiz.love_level_disobedience_barrier.calculating": "Calculando...", "quiz.love_level_disobedience_barrier.barrier_message": "When your **dog misbehaves**, frustration can create a **barrier** between you ", "quiz.love_level_disobedience_barrier.encouragement": "**Keep answering to break the barriers and boost love!**", "quiz.love_level_disobedience_barrier.continue_button": "Continue", "quiz.change_dogs_life_for_better.title": "Would you like to change your {petAge, select, puppy {pup} other {dog} }’s life for the better?", "quiz.change_dogs_life_for_better.responses.definitely": "Sem dúvida!", "quiz.change_dogs_life_for_better.responses.no": "No", "quiz.how_important_training.title": "Na sua opinião, qual é a importância do treinamento para cães?", "quiz.how_important_training.responses.very_important": "<PERSON><PERSON>e", "quiz.how_important_training.responses.somewhat_important": "Um pouco importante", "quiz.how_important_training.responses.not_important": "Não é importante", "quiz.training_is_reflection_of_love.title": "O treinamento de um cão é um reflexo do seu amor", "quiz.training_is_reflection_of_love.description": "**Um cão treinado sente-se mais confiante, seguro e\namado**, sabendo que tem expectativas claras\ne apoio.", "quiz.training_is_reflection_of_love.continue_button": "Continue", "quiz.how_long_you_walk_with_dog.title": "Quanto tempo passeia o seu/sua {petAge, select, puppy {cachorro} other {cão} }?", "quiz.how_long_you_walk_with_dog.responses.ten_minutes": "10 min/dia", "quiz.how_long_you_walk_with_dog.responses.fifteen_minutes": "15 min/dia", "quiz.how_long_you_walk_with_dog.responses.thirty_minutes": "30 min/dia", "quiz.how_long_you_walk_with_dog.responses.sixty_minutes": "60 min/dia", "quiz.training_during_walk.title": "Sabia que pode treinar o seusua {petAge, select, puppy {cachorro} other {cão} } de forma eficaz durante os passeios?", "quiz.training_during_walk.responses.kind_of": "Tipo de", "quiz.training_during_walk.responses.wow": "U<PERSON>!", "quiz.structured_plan.title": "Gostaria de obter um plano de treino estruturado para cães?", "quiz.structured_plan.responses.sure": "<PERSON><PERSON><PERSON>!", "quiz.structured_plan.responses.not_sure": "Não tenho certeza", "quiz.breed.title": "Escolha a raça do seu/sua {petAge, select, puppy {cachorro} other {cão} }", "quiz.breed.subtitle": "Plano de treino de acordo com a raça do seu/sua {petAge, select, puppy {cachorro} other {cão} }", "quiz.breed.label": "Outra raça", "quiz.breed.select_breed": "Selecionar a raça", "quiz.breed.responses.mixed_breed": "<PERSON><PERSON>a", "quiz.breed.responses.german_shepherd": "<PERSON>", "quiz.breed.responses.border_collie": "Border Collie", "quiz.breed.responses.american_pit_bull_terrier": "American Pit Bull Terrier", "quiz.breed.responses.labrador": "Labrador", "quiz.breed.responses.golden_retriever": "Golden Retriever", "quiz.breed.responses.australian_shepherd": "Pastor <PERSON>", "quiz.breed.responses.french_bulldog": "Bulldog francês", "quiz.breed.responses.chihuahua": "Chihuahua", "quiz.breed.responses.staffordshire_bull_terrier": "Staffordshire Bull Terrier", "quiz.breed.next_step": "Próximo passo", "quiz.practicing_dog_behaviorist.title": "O seu plano será revisto por um **comportamentalista de cães**", "quiz.practicing_dog_behaviorist.text": "\"A PawChamp incorpora o treinamento com reforço positivo para oferecer conteúdo e recursos personalizados aos tutores, aprimorando o adestramento dos cães\"", "quiz.practicing_dog_behaviorist.expert_name": "Jordan Zook", "quiz.practicing_dog_behaviorist.reviewed_by_expert": "Conteúdo revisto por um especialista", "quiz.practicing_dog_behaviorist.certified_trainer": "Treinador de cães profissional certificado", "quiz.practicing_dog_behaviorist.continue": "Continue", "quiz.how_you_call_your_dog.title": "Como é que chama o seu cão?", "quiz.how_you_call_your_dog.responses.cute_nicknames": "Apelidos fofos", "quiz.how_you_call_your_dog.responses.by_name": "Por {pet<PERSON><PERSON>, select, female {dela} other {dele} } nome", "quiz.how_you_call_your_dog.responses.dog_doggy": "Cão/cachorro", "quiz.how_you_call_your_dog.responses.hey_you": "Ei, você!", "quiz.dog_name.title": "E o seu/sua {pet<PERSON><PERSON>, select, puppy {ca<PERSON><PERSON><PERSON>} other {cão} }chama-se...", "quiz.dog_name.label": "Escreva o nome do seu/sua {petAge, select, puppy {cachorro} other {cão} }:", "quiz.dog_name.input_placeholder": "Nome", "quiz.dog_name.next_step": "Próximo passo", "quiz.love_profile.title": "Here’s your summary:", "quiz.love_profile.love_level": "Nível de amor: 81%", "quiz.love_profile.readiness_score.title": "Pontuação de prontidão", "quiz.love_profile.readiness_score.result": "Resultado: <PERSON><PERSON><PERSON>", "quiz.love_profile.readiness_score.level.extremely_low": "Extremely low", "quiz.love_profile.readiness_score.level.low": "Low", "quiz.love_profile.readiness_score.level.medium": "Medium", "quiz.love_profile.readiness_score.level.high": "High", "quiz.love_profile.alert.impressive_score_title": "Impressive score for a dog owner!", "quiz.love_profile.alert.impressive_score_text": "Esta pontuação de prontidão indica que está motivado para começar a treinar {petName}. Torna o treino mais fácil, melhora a compreensão e aproxima-o do seu cão.", "quiz.love_profile.list.love_level.title": "<PERSON><PERSON><PERSON>or", "quiz.love_profile.list.love_level.text": "<PERSON><PERSON><PERSON>", "quiz.love_profile.list.motivation.title": "Motivação", "quiz.love_profile.list.motivation.text": "Moderado", "quiz.love_profile.list.trainability.title": "Capacidade de formação", "quiz.love_profile.list.trainability.text": "Médio", "quiz.love_profile.list.knowledge.title": "Conhecimento", "quiz.love_profile.list.knowledge.text": "Baixo", "quiz.love_profile.continue": "Continue", "quiz.ask_qualified_handler_question.title": "Gostaria de fazer perguntas sobre comportamento canino, treinamento, etc., para um adestrador qualificado online?", "quiz.ask_qualified_handler_question.responses.yes": "<PERSON>m, seria ótimo", "quiz.ask_qualified_handler_question.responses.no": "Não, obrigado", "quiz.time_ready_to_spend.title": "Quanto tempo está disposto a gastar para melhorar a vida de {petName}?", "quiz.time_ready_to_spend.responses.5_min": "5 min/dia", "quiz.time_ready_to_spend.responses.10_min": "10 min/dia", "quiz.time_ready_to_spend.responses.15_min": "15 min/dia", "quiz.time_ready_to_spend.responses.30_min": "30 min/dia", "quiz.time_ready_to_spend.responses.60_min": "60 min/dia", "quiz.journey_chart.title": "A sua jornada perfeita com um plano de obediência canina personalizado", "quiz.journey_chart.subtitle": "Com base nas suas respostas, esperamos que aumente o bem-estar de {petName}em", "quiz.journey_chart.chart_tooltip": "bem-estar de {petName}", "quiz.journey_chart.chart_tooltip.start": "Love Level", "quiz.journey_chart.chart_tooltip.end": "{pet<PERSON><PERSON>}'s\nwell-being", "quiz.journey_chart.today": "Hoje", "quiz.journey_chart.first_month": "1.º mês", "quiz.journey_chart.chart_subtitle": "Este gráfico mostra o seu progresso potencial se você seguir todos os passos listados no nosso plano", "quiz.journey_chart.continue": "Continue", "quiz.result.loading.creating_plan": "Criar o plano de obediência personalizado de {petName}...", "quiz.result.loading.over_100000_owners": "Mais de 100.000 proprietários de cães", "quiz.result.loading.over_250000_owners": "Over 250,000 dog owners", "quiz.result.loading.chosen_us": "escolheram PawChamp", "quiz.result.review.reinforcement_techniques.title": "Excelentes técnicas de reforço positivo", "quiz.result.review.reinforcement_techniques.author": "Dawg Daddy", "quiz.result.review.reinforcement_techniques.text": "Este é um ótimo programa de treinamento que começa desde a base, o que é fundamental. Ele avança de forma gradual com técnicas de reforço positivo que realmente funcionam. Eu vi ótimos resultados com meus 2 cães.", "quiz.result.review.training_structure.title": "A estrutura do treinamento é brilhante", "quiz.result.review.training_structure.author": "<PERSON>", "quiz.result.review.training_structure.text": "A forma como o treinamento é dividido é excelente. Já tive um adestrador individual em minha casa e achei menos eficaz do que este curso, e recomendaria altamente.", "quiz.result.review.excellent_course.title": "Excelente curso de treinamento", "quiz.result.review.excellent_course.author": "<PERSON>", "quiz.result.review.excellent_course.text": "Curso de treinamento excelente, muito detalhado e fácil de entender. O que eu gosto neste curso é que eles enfatizam que treinar um cachorro exige paciência e compreensão do processo. Ainda estou na primeira semana, mas a abordagem deles parece ser fácil de seguir. Mal posso esperar para ver os resultados finais…", "quiz.result.enter_email.title": "Insira o seu e-mail para receber o Desafio de Obediência Canina personalizado de {petName}", "quiz.result.enter_email.placeholder": "Insira o seu e-mail para obter o seu plano", "quiz.result.enter_email.privacy_protection": "Nós protegemos sua privacidade e estamos comprometidos em proteger seus dados pessoais. Nunca enviamos e-mails de spam, apenas informações relevantes.", "quiz.result.enter_email.continue": "<PERSON><PERSON><PERSON><PERSON>", "quiz.result.email_subscription.title": "Quer receber e-mails com **ofertas especiais**, dicas de treino de cães, conselhos e **brindes**?", "quiz.result.email_subscription.yes": "Sim, mantenha-me informado!", "quiz.result.email_subscription.no": "Não, obrigado", "quiz.result.obedience_level_chart.title": "O **plano de obediência canina** personalizado de {petName} está pronto", "quiz.result.obedience_level_chart.start": "Today", "quiz.result.obedience_level_chart.end": "After using\nPawChamp", "quiz.result.obedience_level_chart.week1": "SEMANA 1", "quiz.result.obedience_level_chart.week2": "SEMANA 2", "quiz.result.obedience_level_chart.week3": "SEMANA 3", "quiz.result.obedience_level_chart.week4": "SEMANA 4", "quiz.result.obedience_level_chart.chart_explanation": "Este gráfico mostra o seu progresso potencial se você seguir todos os passos listados no nosso plano", "quiz.result.obedience_level_chart.continue": "<PERSON><PERSON><PERSON><PERSON>", "quiz.result.popup.first_popup.title": "<PERSON>s uma pergunta", "quiz.result.popup.first_popup.question": "{petName} já teve algum treino de obediência?", "quiz.result.popup.first_popup.responses.yes": "Yes", "quiz.result.popup.first_popup.responses.no": "No", "quiz.result.popup.second_popup.title": "Finalizar o seu plano", "quiz.result.popup.second_popup.question": "Tem tendência para terminar o que começa?", "quiz.result.popup.second_popup.responses.yes": "Yes", "quiz.result.popup.second_popup.responses.no": "No"}