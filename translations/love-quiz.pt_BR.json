{"quiz.agreement.template": "<PERSON><PERSON> es<PERSON><PERSON><PERSON> \"<PERSON><PERSON> garota\" ou \"Bom garoto\" e prosseguir, você concorda com os {termsOfUseLink}, a {privacyPolicyLink} e a {cookiePolicyLink}", "quiz.agreement.terms_of_use": "Termos de uso", "quiz.agreement.privacy_policy": "Política de privacidade", "quiz.agreement.cookie_policy": "Política de cookies", "quiz.gender.subtitle": "Teste de 2 minutos", "quiz.gender.title": "O quanto você ama seu cachorro?", "quiz.gender.text": "O SEU CÃO É:", "quiz.gender.responses.girl": "Bo<PERSON> garota", "quiz.gender.responses.boy": "<PERSON><PERSON> garoto", "quiz.pet_age.title": "Qual é a idade do seu cão?", "quiz.pet_age.responses.puppy.label": "<PERSON>l<PERSON><PERSON>", "quiz.pet_age.responses.puppy.age_range": "Menos de 6 meses", "quiz.pet_age.responses.adolescent.label": "Adolescente", "quiz.pet_age.responses.adolescent.age_range": "6-18 meses", "quiz.pet_age.responses.adult.label": "Adulto", "quiz.pet_age.responses.adult.age_range": "1,5-7 anos", "quiz.pet_age.responses.senior.label": "Sênior", "quiz.pet_age.responses.senior.age_range": "Mais de 7 anos", "quiz.snuggle_with_dog.title": "Me aconchego com meu {petAge, select, puppy {filhote} other {cão} } o tempo todo", "quiz.snuggle_with_dog.subtitle": "Você concorda com essa afirmação?", "quiz.snuggle_with_dog.strongly_agree": "Concordo totalmente", "quiz.snuggle_with_dog.strongly_disagree": "Discordo totalmente", "quiz.photos_of_dog.title": "Tiro mais fotos do meu {petAge, select, puppy {filhote} other {cão} } que de mim mesmo", "quiz.photos_of_dog.subtitle": "Você concorda com essa afirmação?", "quiz.photos_of_dog.strongly_agree": "Concordo totalmente", "quiz.photos_of_dog.strongly_disagree": "Discordo totalmente", "quiz.spoil_dog_with_treats.title": "Mimo o meu {petAge, select, puppy {filhote} other {cão} } com brinquedos e petiscos novos sempre que posso", "quiz.spoil_dog_with_treats.subtitle": "Você concorda com essa afirmação?", "quiz.spoil_dog_with_treats.strongly_agree": "Concordo totalmente", "quiz.spoil_dog_with_treats.strongly_disagree": "Discordo totalmente", "quiz.dog_sleep_wherever_wants.title": "Permito que meu {pet<PERSON><PERSON>, select, puppy {filhote} other {cão} } durma onde{petG<PERSON>, select, female {} other {} } quiser", "quiz.dog_sleep_wherever_wants.subtitle": "Você concorda com essa afirmação?", "quiz.dog_sleep_wherever_wants.strongly_agree": "Concordo totalmente", "quiz.dog_sleep_wherever_wants.strongly_disagree": "Discordo totalmente", "quiz.jump_into_fire_for_dog.title": "Eu pularia no fogo pelo meu {petAge, select, puppy {filhote} other {c<PERSON><PERSON><PERSON>} } sem hesitar", "quiz.jump_into_fire_for_dog.subtitle": "Você concorda com essa afirmação?", "quiz.jump_into_fire_for_dog.strongly_agree": "Concordo totalmente", "quiz.jump_into_fire_for_dog.strongly_disagree": "Discordo totalmente", "quiz.love_level.progress_message": "Você está fazendo um ótimo trabalho preenchendo o nível de amor respondendo às perguntas!", "quiz.love_level.title": "<PERSON><PERSON><PERSON>or", "quiz.love_level.calculating": "Calculando...", "quiz.love_level.dogs_understand": "Os cães não entendem palavras, apenas ações — terminar este teste é uma delas!", "quiz.love_level.continue": "<PERSON><PERSON><PERSON><PERSON>", "quiz.feel_guilty_for_not_spending_time.title": "Sinto culpa por não passar tempo suficiente com meu {petAge, select, puppy {filhote} other {cão} }", "quiz.feel_guilty_for_not_spending_time.subtitle": "Você concorda com essa afirmação?", "quiz.feel_guilty_for_not_spending_time.strongly_agree": "Concordo totalmente", "quiz.feel_guilty_for_not_spending_time.strongly_disagree": "Discordo totalmente", "quiz.feel_bad_when_dog_looks_upset.title": "Me sinto mal quando perco a calma e meu {petAge, select, puppy {filhote} other {cão} } parece aborrecido", "quiz.feel_bad_when_dog_looks_upset.subtitle": "Você concorda com essa afirmação?", "quiz.feel_bad_when_dog_looks_upset.strongly_agree": "Concordo totalmente", "quiz.feel_bad_when_dog_looks_upset.strongly_disagree": "Discordo totalmente", "quiz.satisfied_with_behavior.title": "Você está satisfeito com o comportamento do seu {petAge, select, puppy {filhote} other {cão} }?", "quiz.satisfied_with_behavior.responses.yes": "Absolutamente, sim", "quiz.satisfied_with_behavior.responses.could_be_better": "Podia ser muito melhor", "quiz.satisfied_with_behavior.responses.no": "<PERSON><PERSON>, é um desastre", "quiz.misbehavior_reaction.title": "Como você reage quando seu {petAge, select, puppy {filhote} other {c<PERSON><PERSON><PERSON>} } se comporta mal?", "quiz.misbehavior_reaction.subtitle": "Se<PERSON><PERSON>e todas as opções aplicáveis:", "quiz.misbehavior_reaction.responses.physical_punishment": "Uso castigos físicos", "quiz.misbehavior_reaction.responses.yelling_shouting": "Grito", "quiz.misbehavior_reaction.responses.isolation": "Me isolo do cão", "quiz.misbehavior_reaction.responses.ignoring": "Ignoro o fato e sigo em frente", "quiz.misbehavior_reaction.responses.calm_redirection": "Tento redirecionar calmamente o cão para mim", "quiz.misbehavior_reaction.responses.do_not_remember": "<PERSON><PERSON> me lembro", "quiz.misbehavior_reaction.next_step": "Próxima etapa", "quiz.behaviors_to_improve.title": "Quais destas tendências de mau comportamento você observa no seu {petAge, select, puppy {filhote} other {cão} }?", "quiz.behaviors_to_improve.subtitle": "Se<PERSON><PERSON>e todas as opções aplicáveis:", "quiz.behaviors_to_improve.responses.excessive_energy": "Excesso de energia e falta de controle", "quiz.behaviors_to_improve.responses.aggression": "Agressividade contra pessoas ou outros animais", "quiz.behaviors_to_improve.responses.leash_pulling": "Puxões de coleira", "quiz.behaviors_to_improve.responses.separation_anxiety": "Ansiedade de separação", "quiz.behaviors_to_improve.responses.excessive_barking": "Latidos excessivos", "quiz.behaviors_to_improve.responses.destructive_behavior": "Comportamento destrutivo (mastigar, arranhar, cavar)", "quiz.behaviors_to_improve.responses.house_soiling": "Sujeira na casa", "quiz.behaviors_to_improve.next_step": "Próxima etapa", "quiz.love_level_disobedience_barrier.title": "A desobediência é um obstáculo ao amor", "quiz.love_level_disobedience_barrier.love_level": "<PERSON><PERSON><PERSON>or", "quiz.love_level_disobedience_barrier.calculating": "Calculando...", "quiz.love_level_disobedience_barrier.barrier_message": "Quando seu **cachorro se comporta mal**, a frustração pode criar uma **barreira** entre vocês ", "quiz.love_level_disobedience_barrier.encouragement": "**Continue respondendo para quebrar as barreiras e impulsionar o amor!**", "quiz.love_level_disobedience_barrier.continue_button": "<PERSON><PERSON><PERSON><PERSON>", "quiz.change_dogs_life_for_better.title": "Você gostaria de mudar a vida do seu {petAge, select, puppy {filhote} other {cão} } para melhor?", "quiz.change_dogs_life_for_better.responses.definitely": "Sem dúvida!", "quiz.change_dogs_life_for_better.responses.no": "Não", "quiz.how_important_training.title": "Na sua opinião, qual é o grau de importância do adestramento canino?", "quiz.how_important_training.responses.very_important": "<PERSON><PERSON>e", "quiz.how_important_training.responses.somewhat_important": "Um pouco importante", "quiz.how_important_training.responses.not_important": "Não é importante", "quiz.training_is_reflection_of_love.title": "O adestramento canino é um reflexo do seu amor", "quiz.training_is_reflection_of_love.description": "**Um cão adestrado se sente mais confiante, seguro e amado**, sabendo que tem expectativas claras e apoio.", "quiz.training_is_reflection_of_love.continue_button": "<PERSON><PERSON><PERSON><PERSON>", "quiz.how_long_you_walk_with_dog.title": "Por quanto tempo você passeia com seu {petAge, select, puppy {filhote} other {cão} }?", "quiz.how_long_you_walk_with_dog.responses.ten_minutes": "10 min/dia", "quiz.how_long_you_walk_with_dog.responses.fifteen_minutes": "15 min/dia", "quiz.how_long_you_walk_with_dog.responses.thirty_minutes": "30 min/dia", "quiz.how_long_you_walk_with_dog.responses.sixty_minutes": "60 min/dia", "quiz.training_during_walk.title": "Você sabia que pode adestrar seu {petAge, select, puppy {filhote} other {cão} } de forma eficaz durante os passeios?", "quiz.training_during_walk.responses.kind_of": "<PERSON><PERSON> ou menos", "quiz.training_during_walk.responses.wow": "U<PERSON>!", "quiz.structured_plan.title": "Gostaria de obter um plano de adestramento canino estruturado?", "quiz.structured_plan.responses.sure": "<PERSON><PERSON><PERSON>!", "quiz.structured_plan.responses.not_sure": "Não tenho certeza", "quiz.breed.title": "Selecione a raça do seu {petAge, select, puppy {filhote} other {c<PERSON>} }", "quiz.breed.subtitle": "Plano de adestramento de acordo com a raça do seu {petAge, select, puppy {filhote} other {cão} }", "quiz.breed.label": "Outra raça", "quiz.breed.select_breed": "Selecione a raça", "quiz.breed.responses.mixed_breed": "<PERSON><PERSON> ra<PERSON> definida", "quiz.breed.responses.german_shepherd": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "quiz.breed.responses.border_collie": "Border collie", "quiz.breed.responses.american_pit_bull_terrier": "American pit bull terrier", "quiz.breed.responses.labrador": "Labrador", "quiz.breed.responses.golden_retriever": "Golden retriever", "quiz.breed.responses.australian_shepherd": "Pastor<PERSON><PERSON><PERSON><PERSON><PERSON>", "quiz.breed.responses.french_bulldog": "Buldogue francês", "quiz.breed.responses.chihuahua": "Chihuahua", "quiz.breed.responses.staffordshire_bull_terrier": "Staffordshire bull terrier", "quiz.breed.next_step": "Próxima etapa", "quiz.practicing_dog_behaviorist.title": "Seu plano será revisado por um **comportamentalista canino**", "quiz.practicing_dog_behaviorist.text": "\"O PawChamp incorpora o treinamento com reforço positivo para oferecer conteúdo e recursos personalizados aos tutores, aprimorando o adestramento canino\"", "quiz.practicing_dog_behaviorist.expert_name": "Jordan Zook", "quiz.practicing_dog_behaviorist.reviewed_by_expert": "Conteúdo revisado por um especialista", "quiz.practicing_dog_behaviorist.certified_trainer": "Adestrador de cães profissional certificado", "quiz.practicing_dog_behaviorist.continue": "<PERSON><PERSON><PERSON><PERSON>", "quiz.how_you_call_your_dog.title": "Como você chama seu cão?", "quiz.how_you_call_your_dog.responses.cute_nicknames": "Apelidos fofos", "quiz.how_you_call_your_dog.responses.by_name": "Pelo nome {pet<PERSON><PERSON>, select, female {} other {} }", "quiz.how_you_call_your_dog.responses.dog_doggy": "Cachorro/cachorrinho", "quiz.how_you_call_your_dog.responses.hey_you": "Ei, você!", "quiz.dog_name.title": "E o nome do seu {pet<PERSON><PERSON>, select, puppy {filhote} other {c<PERSON>} } é&nbsp;...", "quiz.dog_name.label": "Digite o nome do seu {pet<PERSON><PERSON>, select, puppy {filhote} other {cão} }:", "quiz.dog_name.input_placeholder": "Nome", "quiz.dog_name.next_step": "Próxima etapa", "quiz.love_profile.title": "Aqui está seu resumo:", "quiz.love_profile.love_level": "Nível de amor: ", "quiz.love_profile.readiness_score.title": "Pontuação de prontidão", "quiz.love_profile.readiness_score.result": "Resultado: perfeito", "quiz.love_profile.readiness_score.level.extremely_low": "Extremamente baixo", "quiz.love_profile.readiness_score.level.low": "Baixo", "quiz.love_profile.readiness_score.level.medium": "Médio", "quiz.love_profile.readiness_score.level.high": "Alto", "quiz.love_profile.alert.impressive_score_title": "Pontuação impressionante para um tutor de cachorro!", "quiz.love_profile.alert.impressive_score_text": "Essa pontuação de prontidão indica que você está motivado para começar a adestrar {petName}. <PERSON><PERSON> facilita o treinamento, melhora a compreensão e o deixa mais próximo do seu cão.", "quiz.love_profile.list.love_level.title": "<PERSON><PERSON><PERSON>or", "quiz.love_profile.list.love_level.text": "Alto", "quiz.love_profile.list.motivation.title": "Motivação", "quiz.love_profile.list.motivation.text": "Moderado", "quiz.love_profile.list.trainability.title": "Capacidade de treinamento", "quiz.love_profile.list.trainability.text": "Médio", "quiz.love_profile.list.knowledge.title": "Conhecimento", "quiz.love_profile.list.knowledge.text": "Baixo", "quiz.love_profile.continue": "<PERSON><PERSON><PERSON><PERSON>", "quiz.ask_qualified_handler_question.title": "Gostaria de fazer perguntas sobre comportamento canino, adestramento etc. para um adestrador qualificado on-line?", "quiz.ask_qualified_handler_question.responses.yes": "<PERSON>m, seria ótimo", "quiz.ask_qualified_handler_question.responses.no": "Não, obrigado", "quiz.time_ready_to_spend.title": "Quanto tempo está disposto a gastar para melhorar a vida de {petName}?", "quiz.time_ready_to_spend.responses.5_min": "5 min/dia", "quiz.time_ready_to_spend.responses.10_min": "10 min/dia", "quiz.time_ready_to_spend.responses.15_min": "15 min/dia", "quiz.time_ready_to_spend.responses.30_min": "30 min/dia", "quiz.time_ready_to_spend.responses.60_min": "60 min/dia", "quiz.journey_chart.title": "A sua jornada perfeita com um plano de obediência canina personalizado", "quiz.journey_chart.subtitle": "Com base nas suas respostas, o bem-estar de {petName} deve aumentar até", "quiz.journey_chart.chart_tooltip": "Be<PERSON>-estar de\n{petName}", "quiz.journey_chart.chart_tooltip.start": "<PERSON><PERSON><PERSON>or", "quiz.journey_chart.chart_tooltip.end": "Be<PERSON>-estar de\n{petName}", "quiz.journey_chart.today": "Hoje", "quiz.journey_chart.first_month": "1º mês", "quiz.journey_chart.chart_subtitle": "Este gráfico mostra seu progresso em potencial ao seguir todos os passos listados no nosso plano", "quiz.journey_chart.continue": "<PERSON><PERSON><PERSON><PERSON>", "quiz.result.loading.creating_plan": "Criando o plano de obediência personalizado de {petName}...", "quiz.result.loading.over_100000_owners": "Mais de 100.000 tutores de cães", "quiz.result.loading.over_250000_owners": "Mais de 250.000 tutores de cães", "quiz.result.loading.chosen_us": "escolheram o PawChamp", "quiz.result.review.reinforcement_techniques.title": "Excelentes técnicas de reforço positivo", "quiz.result.review.reinforcement_techniques.author": "Dawg Daddy", "quiz.result.review.reinforcement_techniques.text": "Este é um ótimo programa de treinamento que começa desde a base, o que é fundamental. Ele avança de forma gradual com técnicas de reforço positivo que realmente funcionam. Eu vi ótimos resultados com meus 2 cães.", "quiz.result.review.training_structure.title": "A estrutura do adestramento é brilhante", "quiz.result.review.training_structure.author": "<PERSON>", "quiz.result.review.training_structure.text": "A forma como o adestramento é dividido é excelente. <PERSON>á recebi treinamento individual na minha casa e achei menos eficaz do que este curso, que recomendo fortemente.", "quiz.result.review.excellent_course.title": "Excelente curso de adestramento", "quiz.result.review.excellent_course.author": "<PERSON>", "quiz.result.review.excellent_course.text": "Curso de adestramento excelente, muito detalhado e fácil de entender. O que eu gosto neste curso é que eles enfatizam que o adestramento de um cão exige paciência e compreensão do processo. Ainda estou na primeira semana, mas a abordagem deles parece ser fácil de seguir. Mal posso esperar para ver os resultados finais…", "quiz.result.enter_email.title": "Insira seu e-mail para receber o plano de obediência personalizado de {petName}", "quiz.result.enter_email.placeholder": "Insira o seu e-mail para obter o seu plano", "quiz.result.enter_email.privacy_protection": "Protegemos sua privacidade e temos o compromisso de manter a segurança dos seus dados pessoais. Nunca enviamos e-mails de spam, apenas informações relevantes.", "quiz.result.enter_email.continue": "<PERSON><PERSON><PERSON><PERSON>", "quiz.result.email_subscription.title": "Quer receber e-mails com **ofertas especiais**, dicas de adestramento canino, conselhos e **brindes**?", "quiz.result.email_subscription.yes": "Sim, quero receber novidades!", "quiz.result.email_subscription.no": "Não, obrigado", "quiz.result.obedience_level_chart.title": "O **plano de obediência canina** personalizado de {petName} está pronto", "quiz.result.obedience_level_chart.start": "Hoje", "quiz.result.obedience_level_chart.end": "Depois de usar \no PawChamp", "quiz.result.obedience_level_chart.week1": "SEMANA 1", "quiz.result.obedience_level_chart.week2": "SEMANA 2", "quiz.result.obedience_level_chart.week3": "SEMANA 3", "quiz.result.obedience_level_chart.week4": "SEMANA 4", "quiz.result.obedience_level_chart.chart_explanation": "Este gráfico mostra seu progresso em potencial ao seguir todos os passos listados no nosso plano", "quiz.result.obedience_level_chart.continue": "<PERSON><PERSON><PERSON><PERSON>", "quiz.result.popup.first_popup.title": "<PERSON>s uma pergunta", "quiz.result.popup.first_popup.question": "{petName} já teve algum treino de obediência?", "quiz.result.popup.first_popup.responses.yes": "<PERSON>m", "quiz.result.popup.first_popup.responses.no": "Não", "quiz.result.popup.second_popup.title": "Finalizando seu plano", "quiz.result.popup.second_popup.question": "Você tende a terminar o que começa?", "quiz.result.popup.second_popup.responses.yes": "<PERSON>m", "quiz.result.popup.second_popup.responses.no": "Não"}