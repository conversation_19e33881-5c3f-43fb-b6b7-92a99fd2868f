.idea
.env
./tmp
.vscode

###> symfony/framework-bundle ###
/.env.local
/.env.local.php
/.env.*.local
/config/secrets/prod/prod.decrypt.private.php
/public/bundles/
/var/
/vendor/
/public/assets/*
###< symfony/framework-bundle ###

###> symfony/phpunit-bridge ###
.phpunit.result.cache
/phpunit.xml
###< symfony/phpunit-bridge ###

###> phpunit/phpunit ###
/phpunit.xml
.phpunit.result.cache
###< phpunit/phpunit ###

###> rector/rector ###
/rector.php
###< rector/rector ###

###> friendsofphp/php-cs-fixer ###
.php-cs-fixer.cache
###< friendsofphp/php-cs-fixer ###

./composer

.qodo
