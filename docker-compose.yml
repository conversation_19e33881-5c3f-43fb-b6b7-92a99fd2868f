x-php-container: &php
    build: docker/php
    restart: always
    environment:
        PHP_IDE_CONFIG: serverName=localhost:8080
        XDEBUG_SESSION: 0
        REDIS_HOST: "redis"
        METRICS_STORAGE_TYPE: "redis"
    volumes:
        - ./:/app/

name: paw-champ
services:
    nginx:
        build: docker/nginx
        ports:
            - 8080:80
        volumes:
            - ./:/app/
    php:
        <<: *php
        expose:
            - 9000
            - 9003
    async-consumer:
        <<: *php
        command: php bin/console messenger:consume async
    lms-consumer:
        <<: *php
        command: php bin/console messenger:consume lms
    failed-consumer:
        <<: *php
        command: php bin/console messenger:consume failed

    mysql:
        image: bitnami/mysql:5.7
        volumes:
            - ./docker/mysql/db-data:/bitnami/mysql/db-data:rw
            - ./docker/mysql/testing-db.sql:/docker-entrypoint-initdb.d/testing-db.sql
        environment:
            MYSQL_ROOT_USER: root
            MYSQL_ROOT_PASSWORD: root
            MYSQL_DATABASE: dog-training
        ports:
            - 127.0.0.1:3306:3306

    redis:
        image: redis

    front:
        image: node:20
        volumes:
            - ./front:/app/
        working_dir: /app
        command: yarn watch
        environment:
            - NODE_OPTIONS=--openssl-legacy-provider
