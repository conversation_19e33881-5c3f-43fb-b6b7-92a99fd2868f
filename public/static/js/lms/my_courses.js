import {
    addLinkToTheBurgerMenu,
    customHamburgerLogic,
    fetchHtmlAsText,
    loadCss,
    LocalStorageItemManager,
    UserCoursesManager,
    UserCoursesProvider,
    waitForEl,
} from './common.js?v=14';
import {cmsAssetsUrl, cmsContentUrl, scriptUrl} from './lms.js?v=14';
import {getOnboardingHtml, loadOnboardingStyles, onboardingLogic, showOnboarding,} from './onboarding.js?v=14';
import trackAskDogExpertEvent from "./components/ask-dog-expert/track-ask-dog-expert-event.js?v=14";
import {initializeUserAppDeepLinkComponent} from "./components/app-redirect-link/app-redirect-link.js?v=14";
import RetainedOfferController from "./components/common/retainer-offer-banner/retainer-offer-banner.js?v=14";
import { SUBSCRIPTION_PRODUCT_TYPES } from "./constants/product.js?v=14";

let userProblems;
const initProgressBar = async () => {
    const progressBarSelectors = document.querySelectorAll('.courses__element');

    if (!progressBarSelectors) {
        return;
    }

    if (!Array.isArray(me?.courses)) {
        return;
    }

    /**
     * @type {Object[]}
     */
    const coursesList = me.courses;

    coursesList.forEach((course) => {
        const { unitsCompleted } = course.me.courseProgress;
        const { unitsTotal } = course.me.courseProgress;
        const percentage = Math.floor((unitsCompleted / unitsTotal) * 100) + '%';

        progressBarSelectors.forEach((selector) => {
            if (course.titleId === selector.getAttribute('data-course-id')) {
                selector.querySelector('.course__progress-percents-filled').style.width = percentage;
                selector.querySelector('.course__progress-value').innerHTML = percentage + '<span>Complete</span>';
            }
        });
    });
};

const hideMainContent = async () => {
    $('#section_1683034697770_341').hide();
};
const hideDefaultHamburger = async () => {
    $('#component_1593094758262_3').hide();
};
const hideProblemsBlock = () => {
    $('.problems-bar__container').hide();
};
const showPremiumAccountEmail = () => {
    const emailInsertSelector = $('.premium-email__mailto').find('span');
    emailInsertSelector.addClass('notranslate');

    if (userEmail.length > 40) {
        const text = emailInsertSelector.text();

        emailInsertSelector.text(text);
    } else {
        emailInsertSelector.text(userEmail);
    }
};
const loadMyCoursesStyles = async () => {
    loadCss(`${scriptUrl}/components/common/reset-default-styles/reset-default-styles.css`);
    loadCss(`${scriptUrl}/components/common/hamburger-menu/hamburger.css`);
    loadCss(`${scriptUrl}/components/my_courses/premium-email/premium-email.css`);
    loadCss(`${scriptUrl}/components/my_courses/problems-bar/problems-bar.css`);
    loadCss(`${scriptUrl}/components/my_courses/course/course.css`);
    loadCss(`${scriptUrl}/components/my_courses/course-problem/course-problem.css`);
    loadCss(`${scriptUrl}/components/my_courses/main-course/main-course.css`);
    loadCss(`${scriptUrl}/components/my_courses/problem-bar-element/problem-bar-element.css`);
    loadCss(`${scriptUrl}/components/my_courses/special-courses/special-courses.css`);
    loadCss(`${scriptUrl}/components/dog-experts-static-banner/dog-experts-static-banner.css`);
    loadCss(`${scriptUrl}/components/common/retainer-offer-banner/retainer-offer-banner.css`);
    loadCss(`${scriptUrl}/components/dog-experts-dynamic-banner/dog-experts-dynamic-banner.css`);
};

loadMyCoursesStyles();

const loadUserProblems = async () => {
    await fetch(`${baseUrl}/api/v1/user/${userExternalId}`)
        .then((response) => response.json())
        .then((data) => {
            userProblems = data && data?.problems || [];
            showCanceledSubscriptionInfo(data?.subscriptions, data?.lms);
        })
        .catch((e) => {
            console.error(e)
        });
};

const showCanceledSubscriptionInfo = (subscriptions = [], lmsData) => {
    if (subscriptions.length === 0) {
        return;
    }

    const hasActiveSubscription = subscriptions.some(
        subscription => subscription.status === 'active' && !subscription.soft_cancelled
    );

    if (hasActiveSubscription) {
        return;
    }

    const softCancelledSubscriptions = subscriptions.filter(
        subscription => subscription.product.productType !== SUBSCRIPTION_PRODUCT_TYPES.UNDEFINED
            && subscription.status === 'active'
            && subscription.soft_cancelled
    );

    if (softCancelledSubscriptions.length > 0) {
        const productTypeOrder = [
            SUBSCRIPTION_PRODUCT_TYPES.SUBSCRIPTION,
            SUBSCRIPTION_PRODUCT_TYPES.UPSELL,
        ];

        const sortedSubscriptions = softCancelledSubscriptions.sort(
            (a, b) => {
                const typeCompare = productTypeOrder.indexOf(a.product.productType) - productTypeOrder.indexOf(b.product.productType);
                if (typeCompare !== 0) return typeCompare;

                return new Date(b.started_at).getTime() - new Date(a.started_at).getTime()
            }
        );
    
        const latestSubscription = sortedSubscriptions[0];
        const expiredAt = new Date(latestSubscription?.expired_at);
        const expiredAtLabel = getExpiredAtLabel(expiredAt);
    
        setSubscriptionCancelledInfo(expiredAtLabel || null);
        return;
    }

    const hasCancelledSubscriptions = subscriptions.every(
        subscription => subscription.status === 'cancelled'
    );

    if (hasCancelledSubscriptions) {
        const expiredAt = new Date(lmsData?.unenroll_at);
        const expiredAtLabel = getExpiredAtLabel(expiredAt);
        setSubscriptionCancelledInfo(expiredAtLabel || null);
    }
}

const getExpiredAtLabel = (expiredAt) => {
    if (!expiredAt || !isValidDate(expiredAt)) {
        return null;
    }

    const expiredAtDate = expiredAt.getDate();
    const expiredAtMonth = getDateMonthShortName(expiredAt);
    const expiredAtYear = expiredAt.getFullYear();

    return `${ expiredAtDate } ${ expiredAtMonth } ${ expiredAtYear }`;
}

const setSubscriptionCancelledInfo = (expiredAtLabel = null) => {
    const subscriptionInfoContainer = document.getElementById('subscription-info-container');
    const subscriptionExpiredAtField = document.getElementById('subscription-expires-at');

    if (!subscriptionInfoContainer || !subscriptionExpiredAtField) {
        return;
    }

    subscriptionInfoContainer.classList.add('premium-email__container--is-canceled');

    if (expiredAtLabel) {
        subscriptionExpiredAtField.innerText = expiredAtLabel;
    }
}

const getDateMonthShortName = (date = new Date()) => {
    const formatter = new Intl.DateTimeFormat('en', { month: 'short' });

    return formatter.format(date);
}

const isValidDate = (date) => {
    return date instanceof Date && !isNaN(date);
}

const loadProgressBar = async () => {
    if (typeof me.courses === 'string') {
        setTimeout(() => {
            loadProgressBar();
        }, 2000);
    } else {
        initProgressBar();
    }
};

const addHrefValueToProblems = (problemElement) => {
    let problemCourse = '';
    const problemId = problemElement?.getAttribute('data-problem-id');

    if (!problemElement || !problemId) {
        return;
    }

    switch (problemId) {
        case 'Pulling':
        case 'Destruction':
        case 'Leash pulling':
        case 'Chasing': {
            problemCourse = 'dog-obedience-training-plan';
            break;
        }
        case 'Barking':
        case 'Excessive barking':
        case 'Whining': {
            problemCourse = 'barking';
            break;
        }
        case 'Excessive energy':
        case 'Jumping': {
            problemCourse = 'hyperdog';
            break;
        }
        case 'Aggression':
        case 'Biting': {
            problemCourse = 'dog-agression';
            break;
        }
        case 'Separation':
        case 'separation':
        case 'separation-anxiety':
        case 'House soiling':
        case 'Destructive behavior':
        case 'Separation anxiety 2':
        case 'Toilet': {
            problemCourse = 'separation';
            break;
        }
    }
    problemElement.setAttribute('href', `#${problemCourse}`);
};

const loadHamburgerLogic = async () => {
    if (Array.isArray(me.courses)) {
        await handleHamburgerMenu();
    } else {
        setTimeout(() => {
            loadHamburgerLogic();
        }, 2000);
    }
};

// Topbar
waitForEl('#section_1564405797906_0', async (el) => {
    const loadPremiumEmail = async () => {
        el.after(await fetchHtmlAsText(`${scriptUrl}/components/my_courses/premium-email/component.html`));
    };

    const loadProblemsBar = async () => {
        $('.premium-email__container').after(await fetchHtmlAsText(`${scriptUrl}/components/my_courses/problems-bar/component.html`));
    };

    const loadHamburgerMenu = async () => {
        await hideDefaultHamburger();

        $('#el_1595271079236_1').after(await fetchHtmlAsText(`${scriptUrl}/components/common/hamburger-menu/component.html`));
    };

    const initRetaineBlock = async () => {
        RetainedOfferController.init()
        $('.problems-bar__container').after(await fetchHtmlAsText(`${scriptUrl}/components/common/retainer-offer-banner/retainer-offer-banner.html`));
    }

    const loadMainCourse = async () => {
        const { items } = await UserCoursesProvider.getCoursesList()
        const isContainMainCourse = items.some(item => item.data.main.iv);

        if (isContainMainCourse) {
            $('.problems-bar__container').after(await fetchHtmlAsText(`${scriptUrl}/components/my_courses/main-course/component.html`));
        }
    };

    const loadSpecialCourses = async () => {
        $('.problems-bar__container').after(await fetchHtmlAsText(`${scriptUrl}/components/my_courses/special-courses/component.html`));
    };

    hideMainContent()
        .then(() => loadPremiumEmail())
        .then(() => loadProblemsBar())
        .then(() => loadSpecialCourses())
        .then(() => loadMainCourse())
        .then(() => loadUserProblems())
        .then(() => loadHamburgerMenu())
        .then(() => initRetaineBlock())
        .then(() => initializeUserAppDeepLinkComponent())
        .then(() => logic());
});

const loadCourses = async () => {
    const courseProblemHtml = await fetchHtmlAsText(`${scriptUrl}/components/my_courses/course-problem/component.html`);
    const html = await fetchHtmlAsText(`${scriptUrl}/components/my_courses/course/component.html`);

    const data = await UserCoursesProvider.getCoursesList();

    if (!data) {
        return;
    }

    const courseData = data.items.map(item => ({
        courseId: item.data.ppCourseId.iv,
        data: item.data,
        sort: item.data.sort?.iv ?? Infinity
    }));

    courseData.sort((a, b) => a.sort - b.sort);

    for (const course of courseData) {
        let courseImage = course.data.image.iv;

        if (course.data.imageLocalized?.[locale]) {
            courseImage = course.data.imageLocalized[locale];
        }

        appendCourse(course.data.main.iv, course.courseId, html, courseImage, course.data.text.iv);

        const problems = course.data.problems?.iv;
        if (Array.isArray(problems) && problems.length > 0) {
            problems.forEach((problem) => {
                if (userProblems?.includes(problem.text)) {
                    appendCourseProblem(course.courseId, problem, courseProblemHtml);
                }
            });
        }
    }
    await loadProgressBar();
};

const appendCourse = (isMain, course, courseHtml, image, text) => {
    let courseComponent = $('.special-courses__content');
    if (isMain) {
        courseComponent = $('.main-course__content');
    }
    courseComponent.append(`<div class="courses__element" data-course-id="${course}" id="${course}">${courseHtml}</div>`);

    const courseElement = $(`[data-course-id="${course}"]`);
    courseElement.find('.course__image').attr('src', `${cmsAssetsUrl}/${image}`);
    courseElement.find('.course__text').text(text);
    courseElement.find('.course__button-link').attr('href', `/course?courseid=${course}`);
};

const appendCourseProblem = (course, problem, problemHtml) => {
    const courseElement = $(`[data-course-id='${course}']`);

    courseElement
        .find('.course__problems-wrapp')
        .css("display", "flex")
        .append(`<div class="course__problems-list" data-course-problem="${problem.text}">${problemHtml}</div>`);
    const problemContainer = $(`[data-course-problem='${problem.text}']`);
    problemContainer
        .find('.course__problems-item-image')
        .attr('src', `${cmsAssetsUrl}/${problem.image}`);
    problemContainer
        .find('.course__problems-item-text')
        .text(problem.text);
};

const handleUserProblems = () => {
    const hasAnyEmptyProblem = userProblems && userProblems.some((problem) => !problem)

    if (hasAnyEmptyProblem || !userProblems.length || !Array.isArray(userProblems) ) {
        hideProblemsBlock();
        return;
    }

    fetchHtmlAsText(`${scriptUrl}/components/my_courses/problem-bar-element/component.html`)
        .then((html) => {
            userProblems.forEach((problem) => {
                if (problem === 'Separation anxiety') {
                    problem = 'Separation anxiety 2';
                }
                const problemUrl = `${cmsContentUrl}/custom-course-problem/${problem.toLowerCase().replace(/\s/g, "-").trim()}`;
                fetch(problemUrl)
                    .then((response) => response.json())
                    .then((data) => {
                        if (data.data === undefined) {
                            return;
                        }

                        appendProblem(problem, html, data.data.icon.iv, data.data.text.iv);
                    });
            });
        });
};

const handleHamburgerMenu = async () => {
    fetchHtmlAsText(`${scriptUrl}/components/common/hamburger-menu/component.html`)
        .then(() => {
            waitForEl('#hamburger-menu-overlay', async () => {
                await addLinkToTheBurgerMenu('Ask Dog Experts', '/ask-dog-experts');
            });
        })
        .then(() => {
            if (!Array.isArray(me?.courses)) {
                return;
            }

            /**
             * @type {Object[]}
             */
            const coursesList = me.courses;

            coursesList.forEach((course) => {
                $('.hamburger__overlay-sub-list').append(`<li class="hamburger__overlay-sub-item"><a class="hamburger__overlay-sub-link clickBtn" href="/course/${course.titleId}">${course.title}</a></li>`);
                $('.clickBtn').on('click', (e) => {
                    window.location.href = e.currentTarget.href;
                });
            });
        })
        .then(() => customHamburgerLogic());
};

const fixContentlocalisation = () => {
    const removeIconsSubtext = () => {
        const iconSubtext = document.querySelectorAll('.problems-bar-element__text');

        if (!iconSubtext) {
            return;
        }

        iconSubtext.forEach((el) => {
            el.remove();
        });
    };
    const fixSpacingUserProblems = () => {
        const userProblemsWrapp = document.querySelector('.problems-bar__list');

        if (!userProblemsWrapp) {
            return;
        }

        userProblemsWrapp.style.setProperty('margin-bottom', '-16px');
    };

    if (!me) {
        return;
    }

    const locales = ["locale_de", "locale_es", "locale_pt", "locale_fr", "locale_it"];

    me.tags.forEach((tag) => {
        if (locales.includes(tag)) {
            removeIconsSubtext();
            fixSpacingUserProblems();
        }
    });
};

const addStaticBannerDogExperts = async () => {
    $('#section_123412341234_0').before(await fetchHtmlAsText(`${scriptUrl}/components/dog-experts-static-banner/component.html`));

    document.querySelector('.dog-experts-banner__button')
        ?.addEventListener('click', () => {
            trackAskDogExpertEvent("banner_click");
        })
}

const dynamicWidgetDataManager = new LocalStorageItemManager('DynamicWidgetData', {
    bannedShownCounter: 0,
    lastShownTimestamp: null,
});

const addDynamicBannerDogExperts = async () => {
    $('#section_123412341234_0')
        .before(await fetchHtmlAsText(`${scriptUrl}/components/dog-experts-dynamic-banner/component.html`));

    const dynamicBanner = document.getElementById('dog-experts-dynamic-banner-root');
    const closeDynamicBanner = document.getElementById('close-dog-experts-dynamic-banner');

    closeDynamicBanner.addEventListener('click', () => {
        dynamicBanner.classList.remove('active');
        setTimeout(() => {
            dynamicBanner.remove();
        }, 300);
    });

    const showBanner = () => {
        dynamicBanner.classList.add('active');
        dynamicWidgetDataManager.set('bannedShownCounter', dynamicWidgetDataManager.get('bannedShownCounter') + 1);
        dynamicWidgetDataManager.set('lastShownTimestamp', Date.now());

        document.querySelector('.dog-experts-dynamic-banner__button')
            ?.addEventListener('click', () => {
                trackAskDogExpertEvent("popup_click");
            });
    }

    const startShowBannerLogic = () => {
        const pageContent = document.querySelector('#pageContent');

        const handlePageContentScroll = () => {
            const scrollHeight = pageContent.scrollHeight - window.innerHeight;

            if (pageContent.scrollTop / scrollHeight >= .5) {
                showBanner();
                pageContent?.removeEventListener('scroll', handlePageContentScroll);
            }
        }

        pageContent?.addEventListener('scroll', handlePageContentScroll);
    }

    const h24 = 1000 * 60 * 60 * 24; // 24 hours

    const lastShownTimestamp = dynamicWidgetDataManager.get('lastShownTimestamp');
    const bannedShownCounter = dynamicWidgetDataManager.get('bannedShownCounter');

    if ((lastShownTimestamp === null || lastShownTimestamp <= Date.now() - h24) && bannedShownCounter < 2) {
        startShowBannerLogic();
    }
}

const appendProblem = async (problem, html, image, text) => {
    const problemElement = $(`
        <a href="#" class="problems-bar-element__item" data-problem-id="${problem}">
            ${html}
        </a>`);

    $('.problems-bar__list').append(problemElement);

    problemElement.find('.problems-bar-element__image').attr('src', `${cmsAssetsUrl}/${image}`);
    problemElement.find('.problems-bar-element__text').text(text);

    addHrefValueToProblems(problemElement[0]);
    fixContentlocalisation(me.tags);
};

const logic = async () => {
    // Main section
    $('#section_1644504870673_288').hide();

    const userCoursesList = UserCoursesManager.getUserCourses();
    await loadHamburgerLogic();

    showPremiumAccountEmail();
    handleUserProblems();
    loadCourses()
        .then(() => {
            if (!userOnboarded && userCoursesList.includes('dog-obedience-training-plan')) {
                getOnboardingHtml()
                    .then((html) => $('body').append(html))
                    .then(() => loadOnboardingStyles())
                    .then(() => onboardingLogic())
                    .then(() => $('.lw-topbar.sticky-topbar').css('position', 'relative'))
                    .then(() => showOnboarding());
            }
        })
        .then(() => {
            addStaticBannerDogExperts();
            addDynamicBannerDogExperts();
        });
};
