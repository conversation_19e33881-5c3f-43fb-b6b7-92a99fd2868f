import {
    addLinkToTheBurgerMenu,
    customHamburgerLogic,
    fetchHtmlAsText,
    loadCss,
    waitForEl,
} from './common.js?v=14';
import { scriptUrl } from './lms.js?v=14';

const hideDefaultHamburger = async () => {
    $('#component_1593094758262_3').hide();
};

const loadHeroBlockStyles = async () => {
    loadCss(`${scriptUrl}/components/common/hamburger-menu/hamburger.css`);
};

const getHamburgerHTML = async () => {
    $('#el_1595271079236_1').after(await fetchHtmlAsText(`${scriptUrl}/components/common/hamburger-menu/component.html`));
}

const loadHamburgerLogic = async () => {
    if (typeof me.courses === 'string') {
        setTimeout(() => {
            loadHamburgerLogic();
        }, 2000);
    } else {
        await handleHamburgerMenu();
    }
};

const handleHamburgerMenu = async () => {
    fetchHtmlAsText(`${scriptUrl}/components/common/hamburger-menu/component.html`)
        .then(() => {
            if (!Array.isArray(me?.courses)) {
                return;
            }

            /**
             * @type {Object[]}
             */
            const coursesList = me.courses;

            coursesList.forEach((course) => {
                $('.hamburger__overlay-sub-list').append(`<li class="hamburger__overlay-sub-item"><a class="hamburger__overlay-sub-link clickBtn" href="/course/${course.titleId}">${course.title}</a></li>`);
                $('.clickBtn').on('click', (e) => {
                    window.location.href = e.currentTarget.href;
                })
            });
        })
        .then(() => customHamburgerLogic())

};


loadHeroBlockStyles();

waitForEl('#component_1593094758262_3', async () => {
    await hideDefaultHamburger();
    await getHamburgerHTML();
    await loadHamburgerLogic();
});

waitForEl('#hamburger-menu-overlay', async () => {
    await addLinkToTheBurgerMenu('Ask Dog Experts', '/ask-dog-experts');
});
