/**
 * # AmplitudeClient
 *
 * * The AmplitudeClient class is a wrapper for the amplitude.js library
 *   It provides a simple interface to initialize the amplitude instance and track events
 *
 * * This class is a singleton, so it can be used in multiple places in the codebase
 *
 * * Call AmplitudeClient.init(amplitudeApiKey) to initialize the client
 *   This will load the amplitude script and initialize the amplitude instance
 *
 * * Call AmplitudeClient.trackEvent(eventName, eventProperties) to track an event
 *   This will track the event using the amplitude instance
 *   To track an event, the client must be initialized first
 *
 * * Call AmplitudeClient.identify(identifyData) to identify a user
 *   This will identify the user using the amplitude instance
 *   To identify a user, the client must be initialized first
 *
 */

/**
 *
 * @typedef AmplitudeResult
 * Result of calling amplitude's methods track(), identify(), etc.
 * Docs - https://amplitude.github.io/Amplitude-TypeScript/interfaces/_amplitude_analytics_browser.Types.Result.html
 *
 * @property {number} code - The status code of the request
 * @property {string} message - The message of the request
 * @property {object} event - (Details about the event)[https://amplitude.github.io/Amplitude-TypeScript/modules/_amplitude_analytics_browser.Types.html#Event]
 */

import { loadJs } from "../lms.js?v=14";

export default class AmplitudeClient {
    static _instance = null;

    /**
     *
     * @param {string} amplitudeApiKey - The API key for the amplitude project
     * @param {string} userId - The user id
     * @param {Object} initialIdentifyData - The initial identify data
     * @param {Object} amplitudeOptions - The amplitude options
     *
     * @return {Promise<Object>}
     */
    static async init(amplitudeApiKey, userId, initialIdentifyData = null, amplitudeOptions = null) {
        if (!amplitudeApiKey || !userId) {
            throw new Error('Amplitude API key or userId is not provided');
        }

        // If amplitude is already initialized, return the instance
        if (this._instance) {
            return this;
        }

        await this._loadScript(amplitudeApiKey);

        if (!window.amplitude) {
            throw new Error('Something went wrong while loading amplitude script');
        }

        this._instance = window.amplitude;

        await this._initInstance(amplitudeApiKey, userId, amplitudeOptions);

        if (initialIdentifyData) {
            await this.identify(initialIdentifyData);
        }

        return this;
    }

    /**
     *
     * @param {string} amplitudeApiKey
     * @param {string} userId
     * @param {Object} amplitudeOptions
     *
     * @return {Promise<void>}
     * @private
     */
    static _initInstance(amplitudeApiKey, userId, amplitudeOptions) {
        if (!this._instance) {
            throw new Error('Amplitude client is not initialized');
        }

        const defaultAmplitudeOptions = {
            defaultTracking: false,
        };

        return this._instance.init(
            amplitudeApiKey,
            userId,
            Object.assign(defaultAmplitudeOptions, amplitudeOptions),
        ).promise;
    }

    /**
     *
     * @param {string} amplitudeApiKey - The API key for the amplitude project
     *
     * @return {Promise<unknown>}
     * @private
     */
    static _loadScript(amplitudeApiKey) {
        const scriptUrl = this._getScriptSrc(amplitudeApiKey);
        const existingScript = document.querySelector(`script[src="${scriptUrl}"]`);

        if (existingScript) {
            return Promise.resolve();
        }

        return new Promise((resolve, reject) => {
            loadJs(
                scriptUrl,
                resolve,
                reject,
            );
        });
    }

    /**
     * Returns the script src for the amplitude script
     * The script src is defined in amplitude docs - https://amplitude.com/docs/sdks/analytics/browser/browser-sdk-2#install-the-sdk
     *
     * @param amplitudeApiKey - The API key for the amplitude project
     *
     * @return {string} - The script src for the amplitude script
     * @private
     */
    static _getScriptSrc(amplitudeApiKey) {
        return `https://cdn.amplitude.com/script/${ amplitudeApiKey }.js`;
    }

    /**
     * This method calls [amplitude's identify method](https://amplitude.github.io/Amplitude-TypeScript/modules/_amplitude_analytics_browser.html#identify)
     *
     * @param {Record<string, number | string | boolean | (string | number)[] | {}>} identifyData
     *
     * @return {Promise<AmplitudeResult>} - Returns a promise property of the amplitude identify method
     */
    static identify(identifyData) {
        if (!this._instance) {
            throw new Error('Amplitude client is not initialized');
        }

        const identify = new this._instance.Identify();

        Object
            .entries(identifyData)
            .forEach(([key, value]) => {
                identify.set(key, value);
            });

        return this._instance.identify(identify).promise;
    }

    /**
     * This method calls [amplitude's track method](https://amplitude.github.io/Amplitude-TypeScript/modules/_amplitude_analytics_browser.html#track)
     *
     * @param {string} eventName - The name of the event
     * @param {Object} [eventProperties] - The properties of the event
     * @param {Object} [eventOptions] - The options for the event
     *
     * @return {Promise<AmplitudeResult>} - Returns a promise property of the amplitude track method
     */
    static trackEvent(eventName, eventProperties, eventOptions) {
        if (!this._instance) {
            throw new Error('Amplitude client is not initialized');
        }

        if (!eventName || typeof eventName !== 'string') {
            throw new Error('Event name is required and must be a string');
        }

        return this._instance.track(eventName, eventProperties, eventOptions).promise;
    }
}
