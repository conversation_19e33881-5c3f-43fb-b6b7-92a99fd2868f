.app-redirect-link {
    width: 100%;
    padding: 16px;
    display: flex;
    justify-content: center;

    visibility: hidden;
    opacity: 0;
    transition: visibility .0s .3s, opacity .3s;
}

.app-redirect-link:hover {
    text-decoration: none;
}

.app-redirect-link.active {
    visibility: visible;
    opacity: 1;
    transition: visibility 0s, opacity .3s;
}

.app-redirect-link__container {
    width: 100%;
    max-width: 343px;
    height: 100%;
    max-height: 65px;
    font-family: "Nunito Sans";
    display: flex;
    padding: 12px;
    align-items: center;
    justify-content: space-between;
    border-radius: 8px;
    border: 1px solid #848484;
    background: #141414;
}

.app-redirect-link__text {
    display: flex;
    flex-direction: column;
}

.app-redirect-link__header {
    color: #FFF;
    font-size: 22px;
    font-style: normal;
    font-weight: 800;
    line-height: 100%;
}

.app-redirect-link__subheader {
    color: #FFF;
    font-size: 14px;
    font-style: normal;
    font-weight: 400;
    line-height: 120%;
    letter-spacing: -0.42px;
    opacity: 0.7;
}

.app-redirect-link__icon-box {
    display: flex;
    gap: 8px;
}

.app-redirect-link__icon {
    width: 30px;
    height: 30px;
}

.app-redirect-link__line {
    width: 1px;
    background: #EFEFEF;
}
