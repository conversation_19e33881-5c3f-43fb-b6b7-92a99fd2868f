html {
    scroll-behavior: smooth;
}
.onboarding__container {
    display: none;
    width: 100%;
    height: 100%;
    position: fixed;
    background: rgba(0, 0, 0, 0.77);
    top: 0;
    left: 0;
    z-index: 11;
    font-family: "<PERSON><PERSON><PERSON>", sans-serif;
}
.onboarding__courses-container {
    background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1678361234/dog-training/lms/courses/main-course-bg.jpg") top center no-repeat;
    background-size: cover;
    width: 100%;
    border: 4px solid #FFD84F;
    border-radius: 16px 16px 0 0;
}
.onboarding__get-container {
    background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1678361234/dog-training/lms/courses/main-course-bg.jpg") top center no-repeat;
    background-size: cover;
    width: 100%;
    border: 4px solid #FFD84F;
    border-radius: 16px 16px 0 0;
}
.onboarding__problems-container {
    background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1678361234/dog-training/lms/courses/main-course-bg.jpg") top center no-repeat;
    background-size: cover;
    width: 100%;
    border: 4px solid #FFD84F;
    border-radius: 16px 16px 0 0;
    display: flex;
    align-items: center;
    justify-content: center;
}
.onboarding__problems-container .course__problems-wrapp {
    margin-top: 16px;
}
.onboarding__courses-image {
    width: 100%;
    max-width: 300px;
    margin: 0 auto;
}
.onboarding__inner {
    height: 100vh;
}
.onboarding__container.active {
    display: block;
}
.onboarding__list {
    display: flex;
    flex-direction: column;
    align-items: center;
    height: 100%;
    position: relative;
}
.onboarding__item {
    display: none;
    max-width: 567px;
    width: calc(100% - 16px);
    flex-direction: column;
    align-items: center;
    margin: 0;
    height: max-content;
}
.onboarding__item.active {
    margin: 0 8px;
    display: flex;
}
.onboarding__item .problems-bar-element__item {
    pointer-events: none;
}
.onboarding__item .problems-bar__container {
    width: 100%;
    border: 4px solid #FFD84F;
    border-radius: 16px 16px 0 0;
}
.onboarding__item .problems-bar__list {
    justify-content: flex-start;
    max-width: none;
}
.onboarding__counter-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
}
.onboarding__item-info-box {
    background: #ffffff;
    padding: 16px;
    width: 100%;
    border-radius: 0 0 16px 16px;
}
.onboarding__counter-pagination span {
    background: #16191E;
    width: 6px;
    height: 6px;
    opacity: 0.3;
    border-radius: 100%;
    margin-right: 8px;
}
.onboarding__counter-pagination span.active {
    opacity: 1;
}
.onboarding__counter-pagination span:last-of-type {
    margin-right: 0;
}
.onboarding__counter-pagination {
    padding-bottom: 8px;
}
.onboarding__counter {
    text-align: center;
    padding-bottom: 8px;
}
.onboarding__title {
    color: #ffffff;
    font-size: 24px;
    font-weight: 800;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 8px;
}
.onboarding__courses-title {
    font-weight: 800;
    font-size: 24px;
    line-height: 100%;
    text-align: center;
    color: #FFFFFF;
    padding: 16px 0 8px;
}
.onboarding__text {
    text-align: center;
    padding: 0 8px 16px;
    font-size: 20px;
    font-weight: 500;
    line-height: 120%;
}
.onboarding__skip {
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: flex-end;
}
.onboarding__header {
    padding: 24px 24px 16px;
    display: flex;
    justify-content: flex-end;
}
.onboarding__btn {
    font-family: "Nunito", sans-serif;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 8px;
    width: 100%;
    max-width: 414px;
    margin: 0 auto;
    height: 56px;
    color: #ffffff;
    font-weight: 600;
    background: #16191E;
    border-radius: 64px;
    font-size: 20px;
    cursor: pointer;
    transition: 0.1s ease-in-out;
}
.onboarding__btn:active {
    transition: 0.1s ease-in-out;
    background: #1f1f1f;
}
.onboarding__get-button {
    background: #FFFFFF;
    border-radius: 64px;
    padding: 16px 0;
    max-width: 300px;
    width: 100%;
    height: 56px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #16191E!important;
    font-weight: 600;
    margin: 16px auto;
    transition: 0.1s ease-in-out;
}
.onboarding__get-button-link {

}
.arrow-big, .arrow-small {
    position: relative;
}
.arrow-small:before {
    content: url('https://res.cloudinary.com/dr0cx27xo/image/upload/v1680095351/dog-training/lms/onboarding-arrow-small.svg');
    position: absolute;
    bottom: -32px;
    right: 24px;
}
.arrow-big:before {
   content: url('https://res.cloudinary.com/dr0cx27xo/image/upload/v1680095351/dog-training/lms/onboarding-arrow-big.svg');
    position: absolute;
    bottom: -32px;
    right: 24px;
}
