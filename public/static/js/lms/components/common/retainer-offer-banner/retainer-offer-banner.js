import { waitForUserDiscoutProductChange } from '../../../helpers/user.js?v=14';
import { getQuizResponses } from "../../user-guide/user-guide.js?v=14";
import AmplitudeClient from "../../../plugins/amplitude-client.js?v=14";

const DEFAULT_DOG_NAME = 'your dog';

const retainedPopUpOrder = {
  initial: 'confirm_discounted_subscription_popup',
  rejection: 'unlimited_access_popup',
  discount: 'one_time_payment_popup',
  confirm: 'subscription_change_confirmation',
  error: 'error_popup',
}

function applyContent(selector, content) {
  document.querySelectorAll(selector).forEach(el => {
    if (el) el.textContent = String(content);
  });
}

function applyCurrencyContent(selector, content) {
  document.querySelectorAll(selector).forEach(targetElement => {
    const newTextNode = document.createTextNode(String(content));
    const parentNode = targetElement.parentNode;
    targetElement.replaceWith(newTextNode);
    const preparedParentTextContent = parentNode.textContent.trim();
    parentNode.textContent = preparedParentTextContent;
  });
}

function getFormattedAmount(amount) {
  return +(amount / 100).toFixed(2)
}

const formattedBillingPeriods = {
  '3 days': '3 days',
  '7 days': '1 week',
  '30 days': '1 month',
  '31 days': '1 month',
  '1 week': '1 week',
  '3 month': '3 months',
  '6 month': '6 months',
  '1 year': '1 year'
};

export default class RetainedOfferController {
  static retainedPopUpStep = ""; // initial | rejection | discount | confirm
  static subscriptionExternalId = "";
  static discount75Product = null;
  static discountUnlimitedProduct = null;
  static hasOpenedUnlimitedCase = false;
  static dogName = DEFAULT_DOG_NAME;
  static offerPeriod = "";
  static currency = "";

  static async init() {
    this.dogName = await this.getDogName();
    this.getUserSubscriptionDataAndInitFlow();
  }

  static async getDogName() {
    const quizResponses = await getQuizResponses();

    return quizResponses?.dogName?.length ? quizResponses.dogName : DEFAULT_DOG_NAME;
  }

  static async getUserSubscriptionDataAndInitFlow() {
    try {
      const response = await fetch(`${baseUrl}/api/v1/user/${userExternalId}`)

      if (!response.ok) {
        throw new Error(`Failed to fetch user data: ${response.status}`);
      }

      const res = await response.json();

      const {subscriptions} = res;
      const firstSoftCancelled = subscriptions.find(({soft_cancelled}) => soft_cancelled);

      if (!firstSoftCancelled) {
        return;
      }

      this.subscriptionExternalId = firstSoftCancelled.external_id;
      const restSubscriptions = subscriptions.filter(({id}) => id !== firstSoftCancelled.id);
      const hasNoActiveSubscriptions = restSubscriptions.every(
        ({status, soft_cancelled}) =>
          status === "cancelled" || (status === "active" && soft_cancelled)
      );

      const { product, expired_at, currency } = firstSoftCancelled;
      const {discount75Product, discountUnlimitedProduct} = product;

      if (!hasNoActiveSubscriptions || !Boolean(discount75Product) || !Boolean(discountUnlimitedProduct)) {
        return;
      }

      this.currency = currency;
      this.discount75Product = discount75Product;
      this.discountUnlimitedProduct = discountUnlimitedProduct;
      this.offerPeriod = formattedBillingPeriods[product.billingPeriod] || product.billingPeriod;

      const isCountryUS = localStorage.getItem('userCountry') === 'US';
      const formatedDogName = this.dogName.charAt(0).toUpperCase() + this.dogName.slice(1);

      document.querySelector('.retaine-container')?.classList.toggle(locale, locale !== 'en');
      const formattedDiscountPrice = `${currency} ${getFormattedAmount(discount75Product.amount)}`;
      const formattedUnlimitedPrice = `${currency} ${getFormattedAmount(discountUnlimitedProduct.amount)}`;

      applyCurrencyContent('.discount-75-insert-selector', formattedDiscountPrice);
      applyCurrencyContent('.discount-unlimited-insert-selector', formattedUnlimitedPrice);
      applyContent('.user-dog-name-insert-selector', formatedDogName);
      applyContent('.period-selector', this.offerPeriod);

      const initialPop = document.getElementById("confirm_discounted_subscription_popup");
      const initialPopupDescription = initialPop.querySelector('.popup_description.main');
      const initialPopupDescriptionUSA = initialPop.querySelector('.popup_description.usa');

      initialPopupDescription?.classList.toggle("active", !isCountryUS);
      initialPopupDescriptionUSA?.classList.toggle("active", isCountryUS);

      this.blockActivityControl(true);
      this.initTimer(expired_at);
      this.initRetainedFlow();

    } catch (error) {
      console.error("Failed to init subscription discount flow:", error);
    }
  }

  static initTimer(finalPoint) {
    let timerInterval;

    const MS_IN_SECOND = 1000;
    const MS_IN_MINUTE = MS_IN_SECOND * 60;
    const MS_IN_HOUR = MS_IN_MINUTE * 60;
    const MS_IN_DAY = MS_IN_HOUR * 24;
    
    const endDate = new Date(finalPoint).getTime();
    const timer = document.querySelector('.timer_container');
    const daysEl = timer.querySelector('.days');
    const hoursEl = timer.querySelector('.hours');
    const minutesEl = timer.querySelector('.minutes');
    const secondsEl = timer.querySelector('.seconds');

    if (!daysEl || !hoursEl || !minutesEl || !secondsEl) {
      return;
    }

    const updateTimer = () => {
      const now = new Date().getTime();
      const distance = endDate - now;

      if (distance <= 0) {
        clearInterval(timerInterval);
        daysEl.textContent = '00';
        hoursEl.textContent = '00';
        minutesEl.textContent = '00';
        secondsEl.textContent = '00';
        return;
      }

      const days = Math.floor(distance / MS_IN_DAY);
      const hours = Math.floor((distance % MS_IN_DAY) / MS_IN_HOUR);
      const minutes = Math.floor((distance % MS_IN_HOUR) / MS_IN_MINUTE);
      const seconds = Math.floor((distance % MS_IN_MINUTE) / MS_IN_SECOND);

      daysEl.textContent = String(Math.min(days, 99)).padStart(2, '0');
      hoursEl.textContent = String(hours).padStart(2, '0');
      minutesEl.textContent = String(minutes).padStart(2, '0');
      secondsEl.textContent = String(seconds).padStart(2, '0');
    };

    updateTimer();
    timerInterval = setInterval(updateTimer, 1000);
  }

  static popupButtonHandler = ({ currentTarget }) => {
    const target = currentTarget.getAttribute("data-popup-target");
    const amplitudeAction = currentTarget.getAttribute("data-amplitude-event");

    if (target) this.handlePopupAction(target);
    if (amplitudeAction) this.amplitudeActionHandler(amplitudeAction, currentTarget);
  };

  static initRetainedFlow() {
    const popupActionButtons = document.querySelectorAll('.popup_button');

    if (!popupActionButtons.length) return;

    popupActionButtons.forEach((btn) => {
      btn.removeEventListener("click", this.popupButtonHandler);
      btn.addEventListener("click", this.popupButtonHandler);
    });
  }

  static handlePopupAction(action) {
    const isInitial = action === 'initial';
    const isConfirm = action === 'confirm';
    const isClose = action === 'close';
    const shouldRetainStep = !isConfirm && !isClose;
    const shouldClosePrevious = this.retainedPopUpStep && !isInitial && !isConfirm;

    if (shouldClosePrevious) {
      this.closePopup();
    }

    const actionMap = {
      initial: () => this.popupContainerActivityControl(true),
      confirm: () => this.renewSubscription(),
      discount: () => {
        this.hasOpenedUnlimitedCase = true
      },
      close: () => {
        this.popupContainerActivityControl(false);
        this.retainedPopUpStep = '';
        this.hasOpenedUnlimitedCase = false;
      },
      finally: () => {
        this.hasOpenedUnlimitedCase = false;
        this.blockActivityControl(false);
        this.popupContainerActivityControl(false);
        window.location.reload();
      },
    };

    if (actionMap[action]) {
      actionMap[action]();
    }

    if (shouldRetainStep) {
      this.retainedPopUpStep = action;
      this.openPopup();
    }
  };

  static amplitudeActionHandler (action, currentTarget) {
    const buttonValue = currentTarget.textContent.trim().toLowerCase();

    const eventsWithOfferType = [
      'confirmDiscount',
      'confirmUnlimited',
    ];

    const eventNameMap = {
      initial: 'retainer_offer_tap',
      confirmDiscount: 'retainer_offer_confirmation_tap',
      confirmUnlimited: 'unlimited_offer_confirmation_tap',
      offer: 'unlimited_offer_paywall',
    };

    const eventName = eventNameMap[action];

    if (!eventName) return;

    const payload = eventsWithOfferType.includes(action)
      ? { offer_type: buttonValue }
      : null;

    AmplitudeClient.trackEvent(eventName, payload);
  };

  static openPopup() {
    const id = retainedPopUpOrder?.[this.retainedPopUpStep];
    const popup = document.getElementById(id);
    popup?.classList.add('active');
  }

  static closePopup() {
    const id = retainedPopUpOrder?.[this.retainedPopUpStep];
    const popup = document.getElementById(id);
    popup?.classList.remove('active');
  }

  static blockActivityControl(isActive) {
    document.querySelector(".offer-banner")?.classList.toggle("active", isActive);
  }

  static popupContainerActivityControl(isActive) {
    document.getElementById('popup_container')?.classList.toggle('active', isActive);
  }

  static async renewSubscription() {
    const productToUse = this.hasOpenedUnlimitedCase ? this.discountUnlimitedProduct : this.discount75Product;
    
    applyCurrencyContent('.discount-result-selector', `${this.currency} ${getFormattedAmount(productToUse.amount)}`);

    applyContent('.period-popup-selector', this.hasOpenedUnlimitedCase
      ? `, Unlimited Access.`
      : ` for ${this.offerPeriod}`);

    try {
      this.toggleLoader(true);
      const response = await fetch(`${baseUrl}/api/v1/subscription/${this.subscriptionExternalId}/discount/${productToUse.id}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`Request failed with status ${response.status}: ${JSON.stringify(errorData)}`);
      }

      const isProductChanged = await waitForUserDiscoutProductChange({
        subscriptionId: this.subscriptionExternalId, 
        discountProductId: productToUse.id, 
        userId: userExternalId
      });

      if (!isProductChanged) {
        throw new Error(`Discount product was not applied`);
      }

      this.closePopup();
      this.retainedPopUpStep = 'confirm';
      this.openPopup();

      AmplitudeClient.trackEvent("retainer_offer_success", {
        offer_type: this.hasOpenedUnlimitedCase ? "unlimited" : "discount"
      });

    } catch (err) {
      console.error(err);
      this.handlePopupAction('error');
    } finally {
      this.toggleLoader(false);
    }
  }

  static toggleLoader(isActive) {
    const loader = document.querySelector(".offer_loader");

    loader?.classList.toggle("active", isActive);
  }
}
