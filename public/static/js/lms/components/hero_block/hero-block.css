::-webkit-scrollbar {
    display: none;
}

#section_1643807965473_267 {
    padding: 16px 0 0;
}
#section_1594746894968_63 {
    padding-top: 0!important;
}
#el_1683029125103_369 {
    display: none;
}
#el_1683027871707_398 {
    margin-bottom: 0;
}
.hero-block__wrapp {
    display: flex;
    font-family: "Nunito", sans-serif;
    width: 100%;
}
.hero-block__img-wrapp {
    display: flex;
    align-items: flex-end;
    width: 35%;
}
.hero-block__img {
    width: 100%;
    max-width: 130px;
    margin-right: 8px;
}
.hero-block__title {
    font-style: normal;
    font-weight: 800;
    font-size: 42px;
    line-height: 150%;
    text-align: center;
    background: linear-gradient(159.44deg, #FF765A 5.8%, #E93737 90.92%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
}
.hero-block__title-text {
    font-weight: 500;
    font-size: 16px;
    line-height: 110%;
    color: #ffffff;
}
.hero-block__subtitle {
    color: #ffffff;
    font-weight: 800;
    font-size: 20px;
    line-height: 110%;
}
.hero-block__box {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    width: 65%;
    padding-bottom: 8px;
}
.hero-block__video-svg-text {
    background: #ffffff;
    color: #16191E;
    font-size: 12px;
    line-height: 100%;
    font-weight: bold;
    padding: 4px 8px 4px 32px;
    border-radius: 8px;
    width: max-content;
    margin-left: -34px;
    margin-top: -2px;
    position: relative;
    z-index: 0;
}
.hero-block__video-svg-wrapp {
    display: flex;
    align-items: center;
    margin-left: -8px;
}
.hero-block__video-svg {
    position: relative;
    z-index: 1;
}

@media (max-width: 375px) {
    .hero-block__img {
        max-width: 100px;
    }
}
@media (min-width: 375px) {
    .hero-block__img {
        max-width: 120px;
    }
}
@media (min-width: 568px) {
    .hero-block__img {
        max-width: 200px;
    }
}


@media (min-width: 1040px) {
    .lw-cols {
        justify-content: left;
    }
    .hero-block__title {
        font-size: 64px;
        line-height: 120%;
    }
    .hero-block__box {
        padding-left: 64px;
    }
    .hero-block__subtitle {
        font-size: 32px;
    }
    .hero-block__title-text {
        font-size: 24px;
    }
    .hero-block__img {
        max-width: 300px;
    }
    .hero-block__video-svg {
        height: 64px;
        width: 64px;
    }
    .hero-block__video-svg-text {
        font-size: 20px;
        padding: 4px 8px 4px 32px;
        margin-left: -36px;
        margin-top: -2px;
    }
}

