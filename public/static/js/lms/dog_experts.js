import {
    fetchHtmlAsText, loadCss, waitForEl
} from './common.js?v=14';
import { loadJs, scriptUrl } from './lms.js?v=14';

loadJs(`${scriptUrl}/hamburger.js?v=14`);

const loadDogExpertsStyles = async () => {
    loadCss(`${scriptUrl}/components/dog-experts/dog-experts.css`);
    loadCss(`${scriptUrl}/components/common/reset-default-styles/reset-default-styles.css`);
};

/**
 *
 * @param {string} eventType
 * @param {Object} [options]
 * @param {"like"|"dislike"|"none"} [options.reation]
 * @param {string} [options.feedback]
 */
waitForEl('#component_1593094758262_3', async (el) => {
    el.hide();

    await loadDogExpertsStyles()
});
