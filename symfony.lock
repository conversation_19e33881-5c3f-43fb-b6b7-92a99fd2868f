{"artprima/prometheus-metrics-bundle": {"version": "1.19", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "1.14", "ref": "9522bce04594caddf6c61ec78c88fea5f5ffa121"}}, "brick/math": {"version": "0.9.2"}, "clue/stream-filter": {"version": "v1.5.0"}, "composer/package-versions-deprecated": {"version": "*********"}, "composer/pcre": {"version": "3.0.0"}, "composer/semver": {"version": "3.3.2"}, "composer/xdebug-handler": {"version": "3.0.3"}, "dama/doctrine-test-bundle": {"version": "6.7", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "master", "version": "4.0", "ref": "56eaa387b5e48ebcc7c95a893b47dfa1ad51449c"}, "files": ["config/packages/test/dama_doctrine_test_bundle.yaml"]}, "doctrine/annotations": {"version": "1.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.0", "ref": "a2759dd6123694c8d901d0ec80006e044c2e6457"}, "files": ["config/routes/annotations.yaml"]}, "doctrine/cache": {"version": "2.0.3"}, "doctrine/collections": {"version": "1.6.7"}, "doctrine/common": {"version": "3.1.2"}, "doctrine/data-fixtures": {"version": "1.5.2"}, "doctrine/dbal": {"version": "2.13.2"}, "doctrine/deprecations": {"version": "v0.5.3"}, "doctrine/doctrine-bundle": {"version": "2.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "2.4", "ref": "dda18c8830b143bc31c0e0457fb13b9029614d76"}, "files": ["config/packages/doctrine.yaml", "config/packages/prod/doctrine.yaml", "config/packages/test/doctrine.yaml", "src/Entity/.gitignore", "src/Repository/.gitignore"]}, "doctrine/doctrine-fixtures-bundle": {"version": "3.4", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.0", "ref": "1f5514cfa15b947298df4d771e694e578d4c204d"}, "files": ["src/DataFixtures/AppFixtures.php"]}, "doctrine/doctrine-migrations-bundle": {"version": "3.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.1", "ref": "ee609429c9ee23e22d6fa5728211768f51ed2818"}, "files": ["config/packages/doctrine_migrations.yaml", "migrations/.gitignore"]}, "doctrine/event-manager": {"version": "1.1.1"}, "doctrine/inflector": {"version": "2.0.3"}, "doctrine/instantiator": {"version": "1.4.0"}, "doctrine/lexer": {"version": "1.2.1"}, "doctrine/migrations": {"version": "3.1.4"}, "doctrine/orm": {"version": "2.9.3"}, "doctrine/persistence": {"version": "2.2.1"}, "doctrine/sql-formatter": {"version": "1.1.1"}, "easycorp/easyadmin-bundle": {"version": "4.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.0", "ref": "b131e6cbfe1b898a508987851963fff485986285"}}, "egulias/email-validator": {"version": "3.1.1"}, "facebook/php-business-sdk": {"version": "10.0.1"}, "friendsofphp/php-cs-fixer": {"version": "3.8", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "3.0", "ref": "be2103eb4a20942e28a6dd87736669b757132435"}, "files": [".php-cs-fixer.dist.php"]}, "friendsofphp/proxy-manager-lts": {"version": "v1.0.5"}, "gentenox/sentry-monolog-handler": {"version": "v1.0.4"}, "google/apiclient": {"version": "2.12", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "2.0", "ref": "49c5d7a66d863d28513979bf0666dc77599100c0"}, "files": ["config/packages/google_apiclient.yaml"]}, "guzzlehttp/guzzle": {"version": "6.5.5"}, "guzzlehttp/promises": {"version": "1.4.1"}, "guzzlehttp/psr7": {"version": "1.8.2"}, "http-interop/http-factory-guzzle": {"version": "1.2.0"}, "jean85/pretty-package-versions": {"version": "2.0.4"}, "laminas/laminas-code": {"version": "4.4.0"}, "maknz/slack": {"version": "1.7.0"}, "monolog/monolog": {"version": "2.2.0"}, "myclabs/deep-copy": {"version": "1.10.2"}, "nelmio/api-doc-bundle": {"version": "4.23", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "main", "version": "3.0", "ref": "c8e0c38e1a280ab9e37587a8fa32b251d5bc1c94"}}, "nikic/php-parser": {"version": "v4.10.5"}, "phar-io/manifest": {"version": "2.0.1"}, "phar-io/version": {"version": "3.1.0"}, "php-cs-fixer/diff": {"version": "v2.0.2"}, "php-http/client-common": {"version": "2.4.0"}, "php-http/discovery": {"version": "1.14.0"}, "php-http/httplug": {"version": "2.2.0"}, "php-http/message": {"version": "1.12.0"}, "php-http/message-factory": {"version": "v1.0.2"}, "php-http/promise": {"version": "1.1.0"}, "phpdocumentor/reflection-common": {"version": "2.2.0"}, "phpdocumentor/reflection-docblock": {"version": "5.2.2"}, "phpdocumentor/type-resolver": {"version": "1.4.0"}, "phpspec/prophecy": {"version": "1.13.0"}, "phpstan/extension-installer": {"version": "1.1.0"}, "phpstan/phpstan": {"version": "1.6.8"}, "phpstan/phpstan-doctrine": {"version": "1.3.9"}, "phpstan/phpstan-symfony": {"version": "1.2.2"}, "phpunit/php-code-coverage": {"version": "9.2.6"}, "phpunit/php-file-iterator": {"version": "3.0.5"}, "phpunit/php-invoker": {"version": "3.1.1"}, "phpunit/php-text-template": {"version": "2.0.4"}, "phpunit/php-timer": {"version": "5.0.3"}, "phpunit/phpunit": {"version": "9.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "9.3", "ref": "68450b8fef6804047d9424c55d758c28b4340791"}, "files": [".env.test", "phpunit.xml.dist", "tests/bootstrap.php"]}, "psr/cache": {"version": "1.0.1"}, "psr/container": {"version": "1.1.1"}, "psr/event-dispatcher": {"version": "1.0.0"}, "psr/http-client": {"version": "1.0.1"}, "psr/http-factory": {"version": "1.0.1"}, "psr/http-message": {"version": "1.0.1"}, "psr/link": {"version": "1.0.0"}, "psr/log": {"version": "1.1.4"}, "ralouphie/getallheaders": {"version": "3.0.3"}, "ramsey/collection": {"version": "1.1.3"}, "ramsey/uuid": {"version": "4.1.1"}, "rector/rector": {"version": "0.12.23"}, "sebastian/cli-parser": {"version": "1.0.1"}, "sebastian/code-unit": {"version": "1.0.8"}, "sebastian/code-unit-reverse-lookup": {"version": "2.0.3"}, "sebastian/comparator": {"version": "4.0.6"}, "sebastian/complexity": {"version": "2.0.2"}, "sebastian/diff": {"version": "4.0.4"}, "sebastian/environment": {"version": "5.1.3"}, "sebastian/exporter": {"version": "4.0.3"}, "sebastian/global-state": {"version": "5.0.3"}, "sebastian/lines-of-code": {"version": "1.0.3"}, "sebastian/object-enumerator": {"version": "4.0.4"}, "sebastian/object-reflector": {"version": "2.0.4"}, "sebastian/recursion-context": {"version": "4.0.4"}, "sebastian/resource-operations": {"version": "3.0.3"}, "sebastian/type": {"version": "2.3.4"}, "sebastian/version": {"version": "3.0.2"}, "sensio/framework-extra-bundle": {"version": "5.2", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.2", "ref": "fb7e19da7f013d0d422fa9bce16f5c510e27609b"}, "files": ["config/packages/sensio_framework_extra.yaml"]}, "sentry/sdk": {"version": "3.1.0"}, "sentry/sentry": {"version": "3.3.2"}, "sentry/sentry-symfony": {"version": "3.0", "recipe": {"repo": "github.com/symfony/recipes-contrib", "branch": "master", "version": "3.0", "ref": "9746f0823302d7980e5273ef7a69ef3f5ac80914"}}, "solidgate/php-sdk": {"version": "v1.4.1"}, "symfony/amqp-messenger": {"version": "v5.3.14"}, "symfony/asset": {"version": "v5.3.2"}, "symfony/browser-kit": {"version": "v5.3.0"}, "symfony/cache": {"version": "v5.3.0"}, "symfony/cache-contracts": {"version": "v2.4.0"}, "symfony/config": {"version": "v5.3.2"}, "symfony/console": {"version": "5.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.3", "ref": "da0c8be8157600ad34f10ff0c9cc91232522e047"}, "files": ["bin/console"]}, "symfony/css-selector": {"version": "v5.3.0"}, "symfony/debug-bundle": {"version": "4.1", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.1", "ref": "0ce7a032d344fb7b661cd25d31914cd703ad445b"}, "files": ["config/packages/dev/debug.yaml"]}, "symfony/dependency-injection": {"version": "v5.3.2"}, "symfony/deprecation-contracts": {"version": "v2.4.0"}, "symfony/doctrine-bridge": {"version": "v5.3.1"}, "symfony/doctrine-messenger": {"version": "v5.3.14"}, "symfony/dom-crawler": {"version": "v5.3.0"}, "symfony/dotenv": {"version": "v5.3.0"}, "symfony/error-handler": {"version": "v5.3.0"}, "symfony/event-dispatcher": {"version": "v5.3.0"}, "symfony/event-dispatcher-contracts": {"version": "v2.4.0"}, "symfony/expression-language": {"version": "v5.3.0"}, "symfony/filesystem": {"version": "v5.3.0"}, "symfony/finder": {"version": "v5.3.0"}, "symfony/flex": {"version": "1.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.0", "ref": "c0eeb50665f0f77226616b6038a9b06c03752d8e"}, "files": [".env"]}, "symfony/form": {"version": "v5.3.2"}, "symfony/framework-bundle": {"version": "5.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.3", "ref": "772b77cfb5017644547ef7f9364c54e4eb9a6c61"}, "files": ["config/packages/cache.yaml", "config/packages/framework.yaml", "config/preload.php", "config/routes/framework.yaml", "config/services.yaml", "public/index.php", "src/Controller/.gitignore", "src/Kernel.php"]}, "symfony/http-client": {"version": "v5.3.2"}, "symfony/http-client-contracts": {"version": "v2.4.0"}, "symfony/http-foundation": {"version": "v5.3.2"}, "symfony/http-kernel": {"version": "v5.3.2"}, "symfony/intl": {"version": "v5.3.0"}, "symfony/mailer": {"version": "4.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.3", "ref": "15658c2a0176cda2e7dba66276a2030b52bd81b2"}, "files": ["config/packages/mailer.yaml"]}, "symfony/maker-bundle": {"version": "1.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "1.0", "ref": "fadbfe33303a76e25cb63401050439aa9b1a9c7f"}}, "symfony/messenger": {"version": "5.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "main", "version": "4.3", "ref": "25e3c964d3aee480b3acc3114ffb7940c89edfed"}, "files": ["config/packages/messenger.yaml"]}, "symfony/mime": {"version": "v5.3.2"}, "symfony/monolog-bridge": {"version": "v5.3.0"}, "symfony/monolog-bundle": {"version": "3.7", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.7", "ref": "329f6a5ef2e7aa033f802be833ef8d1268dd0848"}, "files": ["config/packages/dev/monolog.yaml", "config/packages/prod/deprecations.yaml", "config/packages/prod/monolog.yaml", "config/packages/test/monolog.yaml"]}, "symfony/notifier": {"version": "5.0", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.0", "ref": "c31585e252b32fe0e1f30b1f256af553f4a06eb9"}, "files": ["config/packages/notifier.yaml"]}, "symfony/options-resolver": {"version": "v5.3.0"}, "symfony/password-hasher": {"version": "v5.3.2"}, "symfony/phpunit-bridge": {"version": "5.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.3", "ref": "0a3b084605bd384975df457a20e9f41ca09fd4ed"}, "files": [".env.test", "bin/phpunit", "phpunit.xml.dist", "tests/bootstrap.php"]}, "symfony/polyfill-intl-grapheme": {"version": "v1.23.0"}, "symfony/polyfill-intl-icu": {"version": "v1.23.0"}, "symfony/polyfill-intl-idn": {"version": "v1.23.0"}, "symfony/polyfill-intl-normalizer": {"version": "v1.23.0"}, "symfony/polyfill-mbstring": {"version": "v1.23.0"}, "symfony/polyfill-php73": {"version": "v1.23.0"}, "symfony/polyfill-php80": {"version": "v1.23.0"}, "symfony/polyfill-php81": {"version": "v1.23.0"}, "symfony/polyfill-uuid": {"version": "v1.23.0"}, "symfony/process": {"version": "v5.3.2"}, "symfony/property-access": {"version": "v5.3.0"}, "symfony/property-info": {"version": "v5.3.1"}, "symfony/proxy-manager-bridge": {"version": "v5.3.0"}, "symfony/psr-http-message-bridge": {"version": "v2.1.1"}, "symfony/redis-messenger": {"version": "v5.3.14"}, "symfony/routing": {"version": "5.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.3", "ref": "2d706bd8c6a9e6730343bb22092dabba1f42f4f3"}, "files": ["config/packages/routing.yaml", "config/routes.yaml"]}, "symfony/runtime": {"version": "v5.3.2"}, "symfony/security-bundle": {"version": "5.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.3", "ref": "8b35bfc00a7716db4ca5a764a4b338476ca3a704"}, "files": ["config/packages/security.yaml"]}, "symfony/security-core": {"version": "v5.3.2"}, "symfony/security-csrf": {"version": "v5.3.0"}, "symfony/security-guard": {"version": "v5.3.0"}, "symfony/security-http": {"version": "v5.3.2"}, "symfony/serializer": {"version": "v5.3.2"}, "symfony/service-contracts": {"version": "v2.4.0"}, "symfony/stopwatch": {"version": "v5.3.0"}, "symfony/string": {"version": "v5.3.2"}, "symfony/translation": {"version": "5.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.3", "ref": "da64f5a2b6d96f5dc24914517c0350a5f91dee43"}, "files": ["config/packages/translation.yaml", "translations/.gitignore"]}, "symfony/translation-contracts": {"version": "v2.4.0"}, "symfony/twig-bridge": {"version": "v5.3.0"}, "symfony/twig-bundle": {"version": "5.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "5.3", "ref": "b416645602504d22d15912e0918001e6d71bb9fa"}, "files": ["config/packages/twig.yaml", "templates/base.html.twig"]}, "symfony/uid": {"version": "v5.3.14"}, "symfony/validator": {"version": "4.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "4.3", "ref": "d902da3e4952f18d3bf05aab29512eb61cabd869"}, "files": ["config/packages/test/validator.yaml", "config/packages/validator.yaml"]}, "symfony/var-dumper": {"version": "v5.3.2"}, "symfony/var-exporter": {"version": "v5.3.2"}, "symfony/web-link": {"version": "v5.3.0"}, "symfony/web-profiler-bundle": {"version": "3.3", "recipe": {"repo": "github.com/symfony/recipes", "branch": "master", "version": "3.3", "ref": "6bdfa1a95f6b2e677ab985cd1af2eae35d62e0f6"}, "files": ["config/packages/dev/web_profiler.yaml", "config/packages/test/web_profiler.yaml", "config/routes/dev/web_profiler.yaml"]}, "symfony/yaml": {"version": "v5.3.2"}, "theseer/tokenizer": {"version": "1.2.0"}, "twig/extra-bundle": {"version": "v3.3.1"}, "twig/twig": {"version": "v3.3.2"}, "webmozart/assert": {"version": "1.10.0"}}