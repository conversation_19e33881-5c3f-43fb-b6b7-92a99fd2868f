<!doctype html>
<html lang="en">
<head>
    {% include 'subscription/components/head.html.twig' with {
        title: "PawChamp Dog Training - humane and effective methods based on science!",
        description: "PawChamp published materials and courses are validated by certified dog trainers and behavior specialists, ensuring that you receive the most relevant guidance, proven positive reinforcement methods, and consistent instructions backed by experts and universities research.",
        canonical_href: "https://paw-champ.com/pawchamp-methods"
    } %}
    {% block additionalStyles %}
        <link rel="stylesheet" href="{{ asset("/assets/subscription/css/pawchamp-methods.css") }}">
    {% endblock %}

    {% block additionalScripts %}
        {% include 'compliance/components/google-air-tag.html.twig' %}
        <!-- TrustBox script -->
        <script type="text/javascript" src="//widget.trustpilot.com/bootstrap/v5/tp.widget.bootstrap.min.js" async></script>
        <!-- End TrustBox script -->
    {% endblock %}
</head>
<body class="service" id="scrollBar" data-page="contacts">

{% set lang = app.request.query.get('lang') %}

{% include 'common/header-main.html.twig' %}

<main class="" itemscope itemtype="https://schema.org/WebPage">
    <div class="container">
        <section class="pawchamp-methods_section">
            <h1 class="pawchamp-methods_header" itemprop="headline">
                {{ ('paw_champ_methods_page.science_based_training')|trans }}
            </h1>

            <div class="pawchamp-methods_img-container">

                <picture>
                    <source srcset="https://images.paw-champ.com/pc/images/common/dog-studying-with-glasses.webp"
                        type="image/webp"
                    >
                    <img src="https://images.paw-champ.com/pc/images/common/dog-studying-with-glasses.png"
                        alt="Dog studying with glasses"
                        title="Dog studying with glasses"
                        class="pawchamp-methods_img"
                    >
                </picture>
            </div>

                <div class="pawchamp-methods_container">
                    {% for i in range(0, 1) %}
                        <p class="pawchamp-methods_subtitle">
                            {{ ('paw_champ_methods_page.science_based_training_subtitle.'~i)|trans }}
                        </p>
                    {% endfor %}
                </div>
        </section>

        <section class="pawchamp-methods_section">
            <h2 class="pawchamp-methods_subheader" itemprop="headline">
                <span class="pawchamp-methods_subheader-main">
                    {{ ('paw_champ_methods_page.positive_reinforcement_proven_main')|trans }}
                </span>
                {{ ('paw_champ_methods_page.positive_reinforcement_proven')|trans }}
            </h2>

            <p class="pawchamp-methods_subtitle">
                {{ ('paw_champ_methods_page.positive_reinforcement_proven_subtitle')|trans }}
            </p>

            <div class="pawchamp-methods_card">
                {% for i in range(0, 2) %}
                    <div class="pawchamp-methods_card_item">
                        <div class="pawchamp-methods_card_item_box">
                            <img class="pawchamp-methods_card_item_icon" src="https://images.paw-champ.com/pc/emoji/graduation-cap.png" alt="Graduation cap emoji">
                            <p class="pawchamp-methods_card_item_header">
                                {{ ("paw_champ_methods_page.universities_research_list."~i~'.title')|trans }}
                            </p>
                        </div>

                        <p class="pawchamp-methods_card_item_description">
                            {{ ("paw_champ_methods_page.universities_research_list."~i~'.description.0')|trans }}
                            <i>{{ ("paw_champ_methods_page.universities_research_list."~i~".description.1")|trans }}</i>
                            {{ ("paw_champ_methods_page.universities_research_list."~i~".description.2")|trans }}
                        </p>
                    </div>
                {% endfor %}
            </div>
        </section>

        <section class="pawchamp-methods_section">
            <h2 class="pawchamp-methods_subheader" itemprop="headline">
                <span class="pawchamp-methods_subheader-main">
                    {{ ('paw_champ_methods_page.life_approach_main')|trans }}
                </span>
                {{ ('paw_champ_methods_page.life_approach')|trans }}
            </h2>

            <div class="pawchamp-methods_container">
                {% for i in range(0, 2) %}
                    <p class="pawchamp-methods_text">
                        {{ ('paw_champ_methods_page.life_approach_subtitle.'~i)|trans }}
                    </p>
                {% endfor %}
            </div>
        </section>

        <section class="pawchamp-methods_section">
            <h2 class="pawchamp-methods_subheader" itemprop="headline">
                {{ ('paw_champ_methods_page.positive_reinforcement_advantages')|trans }}
            </h2>

            <div class="pawchamp-methods_card">
                {% for i in range(0, 4) %}
                    <div class="pawchamp-methods_card_item">
                        <div class="pawchamp-methods_card_item_box">
                            <img class="pawchamp-methods_card_item_icon" src="https://images.paw-champ.com/pc/icons/check-circle-light-blue.svg" alt="check circle light blue">
                        </div>

                        <p class="pawchamp-methods_card_item_description">
                            {{ ('paw_champ_methods_page.positive_reinforcement_advantages_subtitle.' ~i)|trans }}
                        </p>
                    </div>
                {% endfor %}
            </div>
        </section>

        <section class="pawchamp-methods_section">
            <h2 class="pawchamp-methods_subheader" itemprop="headline">
                <span class="pawchamp-methods_subheader-main">
                    {{ ('paw_champ_methods_page.unique_program_research-main')|trans }}
                </span>
                {{ ('paw_champ_methods_page.unique_program_research')|trans }}
            </h2>
            <p class="pawchamp-methods_text">
                {{ ('paw_champ_methods_page.unique_program_subtitle')|trans }}
            </p>

            <div class="pawchamp-methods_courses">
                <h3 class="pawchamp-methods_courses_header">
                    {{ ('paw_champ_methods_page.courses_offer')|trans }}
                </h3>

                <div class="pawchamp-methods_courses_container">
                    {% for i in range(0, 2) %}
                        <div class="pawchamp-methods_courses_item">
                            <img class="pawchamp-methods_courses_item_icon" src="{{ ('paw_champ_methods_page.courses_offer_list.'~i~'.icon')|trans }}" alt="Training program courses icon">
                            <div>
                                <h3 class="pawchamp-methods_courses_item_header">
                                    {{ ('paw_champ_methods_page.courses_offer_list.'~i~'.title')|trans }}
                                </h3>
                                <p class="pawchamp-methods_courses_item_description">
                                    {{ ('paw_champ_methods_page.courses_offer_list.'~i~'.description')|trans }}
                                </p>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            </div>

            <p class="pawchamp-methods_text">
                {{ ('paw_champ_methods_page.end_page_text')|trans }}
            </p>
        </section>
    </div>
</main>

{% include 'subscription/components/footer-main.html.twig' %}

<script src="{{ asset("/assets/subscription/js/pawchamp_methods.js") }}"></script>

</body>
</html>
