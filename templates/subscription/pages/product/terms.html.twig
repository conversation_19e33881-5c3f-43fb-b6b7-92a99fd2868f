<!doctype html>
<html lang="en">
<head>
    {% include 'subscription/components/head.html.twig' with { canonical_href: "https://paw-champ.com/terms" } %}
    {% block additionalStyles %}
        <link rel="stylesheet" href="{{ asset("/assets/subscription/css/service.css") }}">
    {% endblock %}
    {% block additionalScripts %}
        {% include 'compliance/components/google-air-tag.html.twig' %}
    {% endblock %}
</head>
<body class="service" id="scrollBar" data-page="terms">

{% include 'common/header-main.html.twig' %}

{% set clientCountry = getCountry()|upper %}
{% set countryCodesEEA = [
    "AT", "BE", "BG", "HR", "CY", "CZ", "DK", "EE", "FI",
    "FR", "DE", "GR", "HU", "IS", "IE", "IT", "LV", "LI",
    "LT", "LU", "MT", "NL", "NO", "PL", "PT", "RO", "SK",
    "SI", "ES", "SE", "GB"
] %}

<main class="main">
    <section>
        <div class="container">
            <h1 class="service__title">
                {% trans %}terms_page.title{% endtrans %}
            </h1>
            <p class="service__text">
                {% trans %}terms_page.last_update_date{% endtrans %}
            </p>
            <b class="service__text">
                {% trans %}terms_page.subtitle{% endtrans %}
            </b>
            <h2 class="service__subtitle">
                {% trans %}terms_page.definitions.title{% endtrans %}
            </h2>
            <p class="service__text">
                {% set definitionsText = 'terms_page.definitions.text.0.other' %}

                {% if isOrganicMobileAppUser %}
                    {% set definitionsText = 'terms_page.definitions.text.0.other' %}
                {% elseif clientCountry == 'US' %}
                    {% set definitionsText = 'terms_page.definitions.text.0.us' %}
                {% elseif clientCountry == 'HK' or clientCountry == 'CN' %}
                    {% set definitionsText = 'terms_page.definitions.text.0.hk' %}
                {% elseif clientCountry in countryCodesEEA %}
                    {% set definitionsText = 'terms_page.definitions.text.0.eea' %}
                {% endif %}

                {{ definitionsText|trans|replace({
                    '%host_path%': app.request.host|trim,
                })|raw }}
            </p>
            <p class="service__text">{% trans %}terms_page.definitions.text.1{% endtrans %}</p>

            <h2 class="service__subtitle">{% trans %}terms_page.license.title{% endtrans %}</h2>
            <p class="service__text">{% trans %}terms_page.license.text.0{% endtrans %}</p>
            <p class="service__text">{% trans %}terms_page.license.text.1{% endtrans %}</p>

            <h3 class="service__subtitle">{% trans %}terms_page.intellectual_property_rights.title{% endtrans %}</h3>
            <h4 class="service__subtitle-small">{% trans %}terms_page.intellectual_property_rights.paragraphs.ownership.title{% endtrans %}</h4>
            <p class="service__text">{% trans %}terms_page.intellectual_property_rights.paragraphs.ownership.text.0{% endtrans %}</p>
            <p class="service__text">{% trans %}terms_page.intellectual_property_rights.paragraphs.ownership.text.1{% endtrans %}</p>

            <h4 class="service__subtitle-small">{% trans %}terms_page.intellectual_property_rights.paragraphs.content.title{% endtrans %}</h4>
            <p class="service__text">{% trans %}terms_page.intellectual_property_rights.paragraphs.content.text{% endtrans %}</p>

            <h4 class="service__subtitle-small">{% trans %}terms_page.intellectual_property_rights.paragraphs.use_of_content.title{% endtrans %}</h4>
            <p class="service__text">{% trans %}terms_page.intellectual_property_rights.paragraphs.use_of_content.text{% endtrans %}</p>

            <h4 class="service__subtitle-small">{% trans %}terms_page.intellectual_property_rights.paragraphs.third_party_software.title{% endtrans %}</h4>
            <p class="service__text">{% trans %}terms_page.intellectual_property_rights.paragraphs.third_party_software.text{% endtrans %}</p>

            <h3 class="service__subtitle">{% trans %}terms_page.user_submissions.title{% endtrans %}</h3>
            <h4 class="service__subtitle-small">{% trans %}terms_page.user_submissions.paragraphs.user_submissions.title{% endtrans %}</h4>
            <p class="service__text">{% trans %}terms_page.user_submissions.paragraphs.user_submissions.text{% endtrans %}</p>

            <h4 class="service__subtitle-small">{% trans %}terms_page.user_submissions.paragraphs.licence_to_user_submissions.title{% endtrans %}</h4>
            <p class="service__text">{% trans %}terms_page.user_submissions.paragraphs.licence_to_user_submissions.text{% endtrans %}</p>

            <h3 class="service__subtitle">{% trans %}terms_page.third_party_sources.title{% endtrans %}</h3>
            <p class="service__text">{% trans %}terms_page.third_party_sources.text.0{% endtrans %}</p>
            <p class="service__text">{% trans %}terms_page.third_party_sources.text.1{% endtrans %}</p>
            <p class="service__text">{% trans %}terms_page.third_party_sources.text.2{% endtrans %}</p>
            <p class="service__text">{% trans %}terms_page.third_party_sources.text.3{% endtrans %}</p>
            <p class="service__text">{% trans %}terms_page.third_party_sources.text.4{% endtrans %}</p>

            <h3 class="service__subtitle">{% trans %}terms_page.privacy.title{% endtrans %}</h3>
            <p class="service__text">{% trans %}terms_page.privacy.text{% endtrans %}</p>

            <h3 class="service__subtitle">{% trans %}terms_page.warranties_disclaimer.title{% endtrans %}</h3>
            <p class="service__text">{% trans %}terms_page.warranties_disclaimer.text.0{% endtrans %}</p>
            <p class="service__text">{% trans %}terms_page.warranties_disclaimer.text.1{% endtrans %}</p>

            <h3 class="service__subtitle">
                {% trans %}terms_page.limitations_of_liability.title{% endtrans %}
            </h3>
            <br>
            <p class="service__text">{% trans %}terms_page.limitations_of_liability.list_title{% endtrans %}</p>
            <ul class="service__list">
                <li class="service__list-item">
                    {% trans %}terms_page.limitations_of_liability.options.0{% endtrans %}
                </li>
                <li class="service__list-item">
                    {% trans %}terms_page.limitations_of_liability.options.1{% endtrans %}
                </li>
                <li class="service__list-item">
                    {% trans %}terms_page.limitations_of_liability.options.2{% endtrans %}
                </li>
                <li class="service__list-item">
                    {% trans %}terms_page.limitations_of_liability.options.3{% endtrans %}
                </li>
            </ul>
            <p class="service__text">{% trans %}terms_page.limitations_of_liability.text.0{% endtrans %}</p>
            <p class="service__text">{% trans %}terms_page.limitations_of_liability.text.1{% endtrans %}</p>
            <p class="service__text">{% trans %}terms_page.limitations_of_liability.text.2{% endtrans %}</p>

            <h3 class="service__subtitle">
                {% trans %}terms_page.indemnity.title{% endtrans %}
            </h3>
            <p class="service__text">
                {% trans %}terms_page.indemnity.articles.0.text{% endtrans %}
            </p>
            <ul class="service__list">
                <li class="service__list-item">
                    {% trans %}terms_page.indemnity.articles.0.options.0{% endtrans %}
                </li>
                <li class="service__list-item">
                    {% trans %}terms_page.indemnity.articles.0.options.1{% endtrans %}
                </li>
                <li class="service__list-item">
                    {% trans %}terms_page.indemnity.articles.0.options.2{% endtrans %}
                </li>
                <li class="service__list-item">
                    {% trans %}terms_page.indemnity.articles.0.options.3{% endtrans %}
                </li>
            </ul>
            <p class="service__text">
                {% trans %}terms_page.indemnity.articles.1.text{% endtrans %}
            </p>
            <ul class="service__list">
                <li class="service__list-item">
                    {% trans %}terms_page.indemnity.articles.1.options.0{% endtrans %}
                </li>
                <li class="service__list-item">
                    {% trans %}terms_page.indemnity.articles.1.options.1{% endtrans %}
                </li>
            </ul>
            <p class="service__text">
                {% trans %}terms_page.indemnity.articles.2.text{% endtrans %}
            </p>
            <ul class="service__list">
                <li class="service__list-item">
                    {% trans %}terms_page.indemnity.articles.2.options.0{% endtrans %}
                </li>
                <li class="service__list-item">
                    {% trans %}terms_page.indemnity.articles.2.options.1{% endtrans %}
                </li>
            </ul>

            <h3 class="service__subtitle">{% trans %}terms_page.term_and_terminations.title{% endtrans %}</h3>
            <p class="service__text">{% trans %}terms_page.term_and_terminations.text{% endtrans %}</p>

            <h3 class="service__subtitle">{% trans %}terms_page.surviving_provisions.title{% endtrans %}</h3>
            <p class="service__text">{% trans %}terms_page.surviving_provisions.text{% endtrans %}</p>

            <h3 class="service__subtitle">{% trans %}terms_page.assignment.title{% endtrans %}</h3>
            <p class="service__text">{% trans %}terms_page.assignment.text{% endtrans %}</p>

            <h3 class="service__subtitle">{% trans %}terms_page.modification.title{% endtrans %}</h3>
            <p class="service__text">{% trans %}terms_page.modification.text{% endtrans %}</p>

            <h3 class="service__subtitle">
                {% trans %}terms_page.governing_law.title{% endtrans %}
            </h3>
            <p class="service__text">
                {% if isOrganicMobileAppUser %}
                    {% trans %}terms_page.governing_law.text.0.other{% endtrans %}
                {% elseif clientCountry == 'US' %}
                    {% trans %}terms_page.governing_law.text.0.us{% endtrans %}
                {% elseif clientCountry == 'HK' or clientCountry == 'CN' %}
                    {% trans %}terms_page.governing_law.text.0.hk{% endtrans %}
                {% else %}
                    {% trans %}terms_page.governing_law.text.0.other{% endtrans %}
                {% endif %}
            </p>
            <p class="service__text">{% trans %}terms_page.governing_law.text.1{% endtrans %}</p>

            <h3 class="service__subtitle">
                {% trans %}terms_page.money_back.title{% endtrans %}
            </h3>
            <p class="service__text">{% trans %}terms_page.money_back.articles.0.text.0{% endtrans %}</p>
            <ul class="service__list">
                <li class="service__list-item">
                    {% trans %}terms_page.money_back.articles.0.options.0{% endtrans %}
                </li>
                <li class="service__list-item">
                    {% trans %}terms_page.money_back.articles.0.options.1{% endtrans %}
                </li>
                <li class="service__list-item">
                    {% trans %}terms_page.money_back.articles.0.options.2{% endtrans %}
                </li>
            </ul>
            <p class="service__text">{% trans %}terms_page.money_back.articles.0.text.1{% endtrans %}</p>

            <h3 class="service__subtitle">{% trans %}terms_page.money_back.articles.1.title{% endtrans %}</h3>
            <p class="service__text">{% trans %}terms_page.money_back.articles.1.text{% endtrans %}</p>
            <ul class="service__list">
                <li class="service__list-item">
                    {% trans %}terms_page.money_back.articles.1.options.0{% endtrans %}
                </li>
            </ul>

            <h3 class="service__subtitle">{% trans %}terms_page.subscription_policy.title{% endtrans %}</h3>

            <h3 class="service__subtitle-small">{% trans %}terms_page.subscription_policy.paragraphs.types_of_subscription.title{% endtrans %}</h3>
            <p class="service__text">{% trans %}terms_page.subscription_policy.paragraphs.types_of_subscription.text{% endtrans %}</p>
            <ul class="service__list">
                <li class="service__list-item service__list-item--is-alphabetic">{% trans %}terms_page.subscription_policy.paragraphs.types_of_subscription.options.0{% endtrans %}</li>
                <li class="service__list-item service__list-item--is-alphabetic">{% trans %}terms_page.subscription_policy.paragraphs.types_of_subscription.options.1{% endtrans %}</li>
            </ul>

            <h3 class="service__subtitle-small">{% trans %}terms_page.subscription_policy.paragraphs.free_or_paid_trial.title{% endtrans %}</h3>
            <p class="service__text">{% trans %}terms_page.subscription_policy.paragraphs.free_or_paid_trial.text.0{% endtrans %}</p>
            <p class="service__text">{% trans %}terms_page.subscription_policy.paragraphs.free_or_paid_trial.text.1{% endtrans %}</p>
            <p class="service__text">{% trans %}terms_page.subscription_policy.paragraphs.free_or_paid_trial.text.2{% endtrans %}</p>

            <h3 class="service__subtitle-small">{% trans %}terms_page.subscription_policy.paragraphs.subscription.title{% endtrans %}</h3>
            <p class="service__text">{% trans %}terms_page.subscription_policy.paragraphs.subscription.text{% endtrans %}</p>

            <h3 class="service__subtitle-small">{% trans %}terms_page.subscription_policy.paragraphs.payment_method.title{% endtrans %}</h3>
            <p class="service__text">{% trans %}terms_page.subscription_policy.paragraphs.payment_method.text.0{% endtrans %}</p>
            <p class="service__text">{% trans %}terms_page.subscription_policy.paragraphs.payment_method.text.1{% endtrans %}</p>
            <p class="service__text">{% trans %}terms_page.subscription_policy.paragraphs.payment_method.text.2{% endtrans %}</p>

            <h3 class="service__subtitle-small">{% trans %}terms_page.subscription_policy.paragraphs.cancellation.title{% endtrans %}</h3>
            <p class="service__text">{% trans %}terms_page.subscription_policy.paragraphs.cancellation.text.0{% endtrans %}</p>
            <p class="service__text">{% trans %}terms_page.subscription_policy.paragraphs.cancellation.text.1{% endtrans %}</p>
            <p class="service__text">
                {% set support_email_link %}
                    <a href="mailto:{{ support_email }}" class="service__text_link" target="_blank">{{ support_email }}</a>
                {% endset %}

                <b>{% trans %}terms_page.subscription_policy.paragraphs.cancellation.text.2.title{% endtrans %}</b>
                {{ 'terms_page.subscription_policy.paragraphs.cancellation.text.2.text'|trans|replace({
                    '%support_email_link%': support_email_link|trim,
                })|raw }}
            </p>
            <p class="service__text">
                {% set apple_support_link %}
                    <a href="https://support.apple.com/en-us/HT202039"
                       class="service__text_link"
                       target="_blank"
                    >
                        {% trans %}terms_page.subscription_policy.paragraphs.cancellation.text.3.text_parts.apple_support_page{% endtrans %}
                    </a>
                {% endset %}

                <b>{% trans %}terms_page.subscription_policy.paragraphs.cancellation.text.3.title{% endtrans %}</b>
                {{ 'terms_page.subscription_policy.paragraphs.cancellation.text.3.text'|trans|replace({
                    '%apple_support_page_link%': apple_support_link|trim,
                })|raw }}
            </p>
            <p class="service__text">
                {% set google_support_link %}
                    <a href="https://support.google.com/googleplay/answer/7018481?co=GENIE.Platform%253DAndroid&amp;hl=en"
                       class="service__text_link"
                       target="_blank"
                    >
                        {% trans %}terms_page.subscription_policy.paragraphs.cancellation.text.4.text_parts.google_support_page{% endtrans %}
                    </a>
                {% endset %}

                <b>{% trans %}terms_page.subscription_policy.paragraphs.cancellation.text.4.title{% endtrans %}</b>
                {{ 'terms_page.subscription_policy.paragraphs.cancellation.text.4.text'|trans|replace({
                    '%google_support_page_link%': google_support_link|trim,
                })|raw }}

            </p>
            <p class="service__text">{% trans %}terms_page.subscription_policy.paragraphs.cancellation.text.5{% endtrans %}</p>

            <h3 class="service__subtitle-small">{% trans %}terms_page.subscription_policy.paragraphs.refunds.title{% endtrans %}</h3>
            <p class="service__text">{% trans %}terms_page.subscription_policy.paragraphs.refunds.text.0{% endtrans %}</p>
            <p class="service__text">{% trans %}terms_page.subscription_policy.paragraphs.refunds.text.1{% endtrans %}</p>
            <p class="service__text">{% trans %}terms_page.subscription_policy.paragraphs.refunds.text.2{% endtrans %}</p>
            {% if clientCountry == 'BR' %}
                <p class="service__text">{% trans %}terms_page.subscription_policy.paragraphs.refunds.brasil_users_text{% endtrans %}</p>
            {% endif %}
            <p class="service__text">{% trans %}terms_page.subscription_policy.paragraphs.refunds.text.3{% endtrans %}</p>

            <h3 class="service__subtitle-small">
                {% trans %}terms_page.subscription_policy.paragraphs.changes.title{% endtrans %}
            </h3>
            <p class="service__text">{% trans %}terms_page.subscription_policy.paragraphs.changes.text.0{% endtrans %}</p>
            <p class="service__text">
                {% set support_email_link %}
                    <a href="mailto:{{ support_email }}" class="service__text_link" target="_blank">{{ support_email }}</a>
                {% endset %}

                {{ 'terms_page.subscription_policy.paragraphs.changes.text.1'|trans|replace({
                    '%support_email_link%': support_email_link|trim,
                })|raw }}
            </p>

            <h3 class="service__subtitle">{% trans %}terms_page.subscription_policy.paragraphs.translations.title{% endtrans %}</h3>
            <p class="service__text">{% trans %}terms_page.subscription_policy.paragraphs.translations.text{% endtrans %}</p>
        </div>
    </section>
</main>

{% include 'subscription/components/footer-main.html.twig' with {
    hiddenLegal: isOrganicMobileAppUser,
    hiddenLinks: isOrganicMobileAppUser,
} %}

<script src="{{ asset("/assets/subscription/js/service.js") }}"></script>

</body>
</html>
