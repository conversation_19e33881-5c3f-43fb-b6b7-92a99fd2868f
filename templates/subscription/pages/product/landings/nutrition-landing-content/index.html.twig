{% set clientCountry = getCountry()|upper %}
{% set isCountryUS = clientCountry == 'US' %}
{% set isCountryGB = clientCountry == 'GB' %}
{% set isCountryBR = clientCountry == 'BR' %}

{% set discountPercents = '60%' %}
{% set previousDiscountPercents = null %}

{% if isCountryGB %}
    {% set discountPercents = '50%' %}
{% endif %}

{% if isDiscountLanding %}
    {% set discountPercents = '64%' %}
    {% set previousDiscountPercents = '60%' %}

    {% if isCountryGB %}
        {% set discountPercents = '54%' %}
        {% set previousDiscountPercents = '50%' %}
    {% elseif isCountryUS %}
        {% set discountPercents = '70%' %}
    {% endif %}
{% endif %}

{% set localeLanguageClassModifierMap = {
    'en': 'english',
    'de': 'deutch',
    'fr': 'french',
    'es': 'spanish',
    'es_419': 'spanish',
    'pt': 'portugal',
    'pt_BR': 'portugal',
    'it': 'italian',
} %}
{% set localeLanguageClassModifier = localeLanguageClassModifierMap[locale]|default('') %}

{#  Nutrition Goal #}
{% set nutritionGoalMap = {
    improve_health_wellness: "landing.problems.goal.improve_health_wellness"|resp_i11n_trans(domain=translations_domain),
    fix_tummy_issues: "landing.problems.goal.fix_tummy_issues"|resp_i11n_trans(domain=translations_domain),
    stop_overfeeding: "landing.problems.goal.stop_overfeeding"|resp_i11n_trans(domain=translations_domain),
    natural_home_cooked: "landing.problems.goal.natural_home_cooked"|resp_i11n_trans(domain=translations_domain),
}
%}
{% set nutritionGoal = nutritionGoalMap['improve_health_wellness'] %}
{% if app.user.lastQuiz.responses.nutritionGoal is defined %}
   {% set nutritionGoal = nutritionGoalMap[app.user.lastQuiz.responses.nutritionGoal|first|default('improve_health_wellness')] %}
{% endif %}

{# Health Tendencies #}
{% set healthTendsMap = {
    allergies: "landing.problems.allergies"|resp_i11n_trans(domain=translations_domain),
    digestive_problem: "landing.problems.digestive_problem"|resp_i11n_trans(domain=translations_domain),
    mobility_concerns: "landing.problems.joint_or_mobility_concerns"|resp_i11n_trans(domain=translations_domain),
    skin_issues: "landing.problems.skin_or_coat_issues"|resp_i11n_trans(domain=translations_domain),
    watery_eyes: "landing.problems.watery_eyes"|resp_i11n_trans(domain=translations_domain),
    ear_issues: "landing.problems.ear_issues"|resp_i11n_trans(domain=translations_domain),
    excessive_scratching: "landing.problems.excessive_scratching"|resp_i11n_trans(domain=translations_domain),
    runny_nose: "landing.problems.runny_nose"|resp_i11n_trans(domain=translations_domain),
    bad_breath: "landing.problems.bad_breath"|resp_i11n_trans(domain=translations_domain),
    none_of_the_above: "landing.problems.none_of_the_above"|resp_i11n_trans(domain=translations_domain)
} %}

{% set isEmptyHealthTendencies = true %}
{% set healthTendency = [] %}
{% if app.user.lastQuiz.responses.healthTend is defined%}
    {% set isEmptyHealthTendencies = 'none_of_the_above' in app.user.lastQuiz.responses.healthTend %}

    {% if not isEmptyHealthTendencies %}
        {% for tend in app.user.lastQuiz.responses.healthTend %}
            {% set healthTendency = healthTendency | merge([healthTendsMap[tend]|default('')]) %}
        {% endfor %}
    {% endif %}
{% endif %}

{# Activity Level #}
{% set activityLevelMap = {
    passive: "landing.problems.activity.passive"|resp_i11n_trans(domain=translations_domain),
    normal: "landing.problems.activity.normal"|resp_i11n_trans(domain=translations_domain),
    active: "landing.problems.activity.active"|resp_i11n_trans(domain=translations_domain),
    hyperactive: "landing.problems.activity.hyperactive"|resp_i11n_trans(domain=translations_domain),
} %}
{% if app.user.lastQuiz.responses.activityLevel is defined %}
    {% set activityLevel = activityLevelMap[app.user.lastQuiz.responses.activityLevel|first|default('normal')] %}
{% else %}
    {% set activityLevel = activityLevelMap["normal"] %}
{% endif %}

{# Behavior Problems #}
{% if app.user.lastQuiz.responses.problems is defined %}
    {% set behaviorProblems = [] | merge(app.user.lastQuiz.responses.problems) %}
{% else %}
    {% set behaviorProblems = [] %}
{% endif %}

<main class="main landing nutrition {{ localeLanguageClassModifier }}">
    <section class="landing__header">
        <div class="container">
            <div class="landing__header-logo-box">
                <img src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1720101060/paw-champ/logo/pawChamp.png" alt="logo" class="landing__header-logo">
            </div>
        </div>
    </section>
    <section class="landing__timer" id="timerView">
        <div class="landing__timer-text">
            {{ 'landing.timer.reserved_price'|resp_i11n_trans(domain=translations_domain) }}
            <span id="countdown1">{{ getFormattedTimeToCountdownFinish() }}</span>
        </div>
    </section>

    {% if isDiscountLanding %}
        <section class="landing__discount" id="discountBlockView">
            <div class="container">
                <div class="landing__discount-wrapp">
                    <div class="landing__discount-title-box">
                        <p class="landing__discount-prev previous-discount-label">
                            {{ 'landing.discount_block.previous'|resp_i11n_trans(domain=translations_domain) }}
                            <span class="discount-label">{{ previousDiscountPercents }}</span>
                        </p>
                        <h2 class="landing__discount-title">
                            {{ 'landing.discount_block.get_discount'|resp_i11n_trans({'{discountPercents}': discountPercents}, domain=translations_domain) }}
                        </h2>
                    </div>
                    <div class="landing__discount-img-box">
                        <img src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1685119143/dog-training/img/better/gift.png" alt="" class="landing__discount-img">
                    </div>
                </div>
            </div>
        </section>
    {% else %}

    {% endif %}

    {% include 'subscription/components/localized/landing-content-sections/nutrition/goals.twig' %}

    {% include 'subscription/components/localized/landing-content-sections/nutrition/problems.twig' %}

    <section class="landing__offer">
       <div class="container">
           <p class="landing__subtitle">
               {{ 'landing.community.join_community.subtitle'|resp_i11n_trans(domain=translations_domain) }}
           </p>

            {% include 'subscription/components/localized/landing-content-sections/nutrition/users.twig' %}

            <h3 class="landing__offer-title mb-32">
                {{ 'landing.offer.choose_options'|resp_i11n_trans(domain=translations_domain) }}
            </h3>

            {% if isDiscountLanding %}
                {% include 'subscription/components/localized/promocode-discount.html.twig' with {
                    discountPercents: discountPercents,
                    previousDiscountPercents: previousDiscountPercents,
                } %}
            {% else %}
                {% include 'subscription/components/localized/promocode-main.html.twig' %}
            {% endif %}

            {% include 'subscription/components/localized/landing-content-sections/nutrition/offers.twig' %}
       </div>
    </section>

    {% include 'subscription/components/localized/landing-content-sections/nutrition/what-you-get.twig' %}

    {% include 'subscription/components/localized/landing-content-sections/nutrition/featured.twig' %}

    {% include 'subscription/components/localized/landing-content-sections/landing-faq.twig' %}

    {% include 'subscription/components/localized/feedbacks.html.twig' %}

    <section class="landing__section">
        {% include 'subscription/components/localized/landing-content-sections/nutrition/problems.twig' %}
    </section>

    <section class="landing__offer">
        <div class="container">
            {% if isDiscountLanding %}
                {% include 'subscription/components/localized/promocode-discount.html.twig' with {
                    discountPercents: discountPercents,
                    previousDiscountPercents: previousDiscountPercents,
                } %}
            {% else %}
                {% include 'subscription/components/localized/promocode-main.html.twig' %}
            {% endif %}

            {% include 'subscription/components/localized/landing-content-sections/nutrition/offers.twig' with {
                ctaButtonId: 'secondOffer',
                ctaButtonClickEvent: 'Get my plan 4',
            } %}
        </div>
    </section>


    {% include 'subscription/components/localized/landing-content-sections/landing-money-back.twig' %}

    {% include 'subscription/components/localized/landing-content-sections/landing-footer.twig' %}
</main>
