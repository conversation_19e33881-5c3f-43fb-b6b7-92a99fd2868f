<!DOCTYPE html>
<html lang="en">
<head>
    {% include 'subscription/components/head.html.twig' %}

    {% set countryNamesList = {
        'AU' : 'Australia',
        'CA' : 'Canada',
        'DE' : 'Germany',
        'CN' : 'Hong Kong',
        'NL' : 'Netherlands',
        'NZ' : 'New Zealand',
        'SG' : 'Singapore',
        'SE' : 'Sweden',
        'GB' : 'United Kingdom',
        'US': 'United States',
    } %}

    {% set deviceInfo = getDeviceNameAndModel() %}

    {% block additionalStyles %}
        <link rel="stylesheet" href="{{ asset("/assets/subscription/css/better-land.css") }}">
    {% endblock %}

    {% block additionalScripts %}
        {% include 'common/fbPixels/fbPixelMultioffer.html.twig' %}

        {% include 'common/hotjar.html.twig' %}

        <script defer src="{{ asset("/assets/subscription/js/landing.js") }}"></script>
    {% endblock %}
</head>
<body class="{% if deviceInfo.os %} device-{{ deviceInfo.os.name | default('unknown') | lower }} {% endif %} {% if getCountry() == 'MX' %}three-letter-currency{% endif %}">

{% include 'subscription/components/google-tag-manager/google-tag-manager-body.html.twig' %}
{% include 'subscription/pages/product/landings/sub-10-discount-content/default.html.twig' %}

{% include 'subscription/components/quiz-responses.html.twig' %}

<script>
    window.language = 'es';
    window.paymentFormDiscount = '75';
    window.currentPaymentPlanInfo = '';
    window.currencySign = '{{ userCurrencySign() }}';
    window.paymentInfoId = '6';
    window.currencyPayment = '{{ getUserCurrencyCode() }}';
    window.userId = '{{ app.user.externalId }}';
    window.getDeviceName = {{ getDeviceNameAndModel()|json_encode()|raw }};
</script>

{% include 'subscription/components/ES/payment-popup.html.twig' with {discountPercent: '40'} %}
{% include 'subscription/components/ES/thank-you-popup.html.twig' %}
{% include 'subscription/components/ES/password.html.twig' %}
{% include 'subscription/components/ES/upsale-advanced.html.twig' %}
{% include 'subscription/components/ES/sticky-offer.html.twig' %}

</body>
</html>
