<!doctype html>
<html lang="en">
<head>
    {% embed 'subscription/components/head.html.twig' with {
        canonical_href: "https://paw-champ.com/membership-info",
        title: "",
        description: "",
    } %}
        {% block viewportMetaTags %}
            <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no, shrink-to-fit=no">
        {% endblock %}
    {% endembed %}

    {% block additionalStyles %}
        <link rel="stylesheet" href="{{ asset("/assets/subscription/css/membership-info.css") }}">
    {% endblock %}
</head>
<body>
{% set hasActiveSubscription = false %}
{% set hasSoftCancelledSubscription = false %}
{% set hasProductDiscount75 = false %}

{% for subscription in subscriptionInfo %}
    {% if subscription.status == "active" %}
        {% set hasActiveSubscription = true %}
    {% endif %}

    {% if subscription.isSoftCancelled %}
        {% set hasSoftCancelledSubscription = true %}
    {% endif %}

    {% if subscription.subscriptionProduct75Discount != null %}
        {% set hasProductDiscount75 = true %}
        {% set discountPercents = '75' %}
    {% endif %}
{% endfor %}

{% set isSoftCancelledWithDiscount = hasSoftCancelledSubscription and not hasActiveSubscription and hasProductDiscount75 %}
{% set filteredSubscription = subscriptionInfo|filter(s => s.subscriptionProduct75Discount is not null and s.isSoftCancelled) %}
{% set latestSubscription = subscriptionInfo|sort((a, b) => b.startedAt|date('U') <=> a.startedAt|date('U'))|first %}

<main class="membership-info">
    <section class="membership-info_inner">
        <div class="container">
            <h1 class="membership-info_header">{{ ("membership_info.header")|trans }}</h1>

            <div class="membership-info_avatar">
                <div class="membership-info_avatar_img">
                    <img src="https://images.paw-champ.com/pc/images/common/membership-info-avatar.svg">
                </div>
                <p class="membership-info_avatar_email">{{ app.user.email }}</p>
            </div>

            <div class="membership-info_card">
                <div class="membership-info_card_title">
                    {{ ("membership_info.dog_profile_card.title")|trans }}
                </div>

                <div class="membership-info_card_list">
                    {% if dogProfile.dogName is defined and dogProfile.dogName != null %}
                        <p class="membership-info_card_list_text">
                            {{ ("membership_info.dog_profile_card.list.name")|trans }}
                            <b class="capitalize">{{ dogProfile.dogName }}</b>
                        </p>
                    {% endif %}
                    {% if dogProfile.age is defined and dogProfile.age != null %}
                        <p class="membership-info_card_list_text">
                            {{ ("membership_info.dog_profile_card.list.age")|trans }}
                            <b>
                                {% set age = dogProfile.age == 'Adolescent' ? 'Teen paws' : dogProfile.age %}
                                {{ ("age." ~ age|lower)|trans|raw }}
                            </b>
                        </p>
                    {% endif %}
                    {% if dogProfile.breed is defined and dogProfile.breed != null %}
                        <p class="membership-info_card_list_text">
                            {{ ("membership_info.dog_profile_card.list.breed")|trans }}
                            <b>{{ dogProfile.breed }}</b>
                        </p>
                    {% endif %}
                    {% if dogProfile.gender is defined and dogProfile.gender != null %}
                        {% set genderMap = {
                            "he": 'Boy',
                            "his": 'Boy',
                            "him": 'Boy',
                            "she": 'Girl',
                            "her": 'Girl',
                        } %}

                        {% set dogGender = genderMap[dogProfile.gender|join(', ')|lower] ?? dogProfile.gender|join(', ') %}

                        <p class="membership-info_card_list_text">
                            {{ ("membership_info.dog_profile_card.list.gender")|trans }}
                            <b>{{ ("gender." ~ dogGender)|trans }}</b>
                        </p>
                    {% endif %}
                    {% if dogProfile.goal is defined and dogProfile.dogName != null %}
                        <p class="membership-info_card_list_text">
                            {{ ("membership_info.dog_profile_card.list.goal")|trans }}
                            <b>{{ ("goals." ~ dogProfile.goal)|trans }}</b>
                        </p>
                    {% endif %}
                    {% if dogProfile.problems is defined and dogProfile.problems != null %}
                        <p class="membership-info_card_list_text">
                            {{ "membership_info.dog_profile_card.list.problems"|trans }}
                            <b class="membership-info_card_list_text-problems">
                                {{ dogProfile.problems|map(i => ('problems.' ~ i)|trans)|join(', ') }}
                            </b>
                        </p>
                    {% endif %}
                </div>
            </div>

            {% if isSoftCancelledWithDiscount and latestSubscription.subscriptionProduct75Discount is not null and latestSubscription.isSoftCancelled %}
                <div class="membership-discount__banner">
                    <img class="membership-discount__banner-img" src="https://images.paw-champ.com/pc/images/common/dog-with-smile-and-discount-75.png" alt="dog with discount">
                    <div class="membership-discount__banner-box">
                        <h4 class="membership-discount__banner-title">
                            {{ ("membership_info.discount.banner.title")|trans|raw }}
                        </h4>
                        <button class="membership-discount__banner-btn" data-reset-action="discount75">
                            {{ ("membership_info.discount.banner.btn_text")|trans }}
                        </button>
                    </div>
                </div>
            {% endif %}

            {% include 'subscription/components/membership-status-card.html.twig' %}

            <div class="membership-info_support">
                <div class="membership-info_support_btn btn-black" data-reset-action="support">
                    {{ ("membership_info.contact_support")|trans }}
                </div>

                <p class="membership-info_support_title">
                    {% if hasActiveSubscription %}
                        {{ ("membership_info.support_help_active")|trans }}
                    {% else %}
                        {{ ("membership_info.support_help_inactive")|trans }}
                    {% endif %}
                    <br>
                    <span class="membership-info_support_title-bold">{{ ("membership_info.support_mail")|trans }}</span>
                </p>
            </div>

            <p class="membership__footer">
                <span class="membership__footer-item">
                    <a class="membership__footer-item-link" href="{{ path('static_page_terms_organic_mobile_app_users', app.request.query.all | merge({'page': null})) }}">
                        {% trans %}footer.links.terms_of_use{% endtrans %}
                    </a>
                </span>

                <span class="membership__footer-item">
                    <a class="membership__footer-item-link" href="{{ path('static_page_policy_organic_mobile_app_users', app.request.query.all | merge({'page': null})) }}">
                        {% trans %}footer.links.privacy_policy{% endtrans %}
                    </a>
                </span>
            </p>
        </div>
    </section>
</main>

{% include 'subscription/components/membership-feedback-request.html.twig' %}
{% include 'subscription/components/membership-cancellation-reason.html.twig' %}
{% include 'subscription/components/membership-feedback-submission.html.twig' %}
{% include 'subscription/components/membership_benefits_loss.html.twig' %}

{% if isSoftCancelledWithDiscount and latestSubscription.subscriptionProduct75Discount is not null and latestSubscription.isSoftCancelled %}
    {% include 'subscription/components/membership_discount.html.twig' with {
        subscriptionData: latestSubscription,
        subscriptionDiscountData: latestSubscription.subscriptionProduct75Discount,
        subscriptionDiscountPercents: '75',
        previousPlanTitle: "cancelled",
        buttonActionType: "buy_now",
    } %}
{% endif %}

<aside class="membership__loader" id="membership-loader">
    <div class="preload"></div>
</aside>

<aside class="membership__loader card">
    <div class="preload"></div>
</aside>

<script>
    window.userId = '{{ app.user.externalId }}';
    window.subscriptionInfo = {{ subscriptionInfo|json_encode|raw }};
    window.latestSubscription = {{ latestSubscription|json_encode|raw }};
    window.dogProfile = {{ dogProfile|json_encode|raw }};
    window.supportEmail = '{{ support_email }}';
    window.userEmail = '{{ app.user.email }}';
</script>

<script src="{{ asset("/assets/subscription/js/membership_info.js") }}"></script>

</body>
</html>
