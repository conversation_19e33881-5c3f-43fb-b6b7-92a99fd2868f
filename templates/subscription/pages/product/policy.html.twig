<!doctype html>
<html lang="en">
<head>
    {% include 'subscription/components/head.html.twig' with { canonical_href: "https://paw-champ.com/policy" } %}

    {% block additionalStyles %}
        <link rel="stylesheet" href="{{ asset("/assets/subscription/css/service.css") }}">
    {% endblock %}
    {% block additionalScripts %}
        {% include 'compliance/components/google-air-tag.html.twig' %}
    {% endblock %}
</head>
<body class="service" id="scrollBar" data-page="policy">

{% include 'common/header-main.html.twig' %}

{% set clientCountry = getCountry()|upper %}
{% set countryCodesEEA = [
    "AT", "BE", "BG", "HR", "CY", "CZ", "DK", "EE", "FI",
    "FR", "DE", "GR", "HU", "IS", "IE", "IT", "LV", "LI",
    "LT", "LU", "MT", "NL", "NO", "PL", "PT", "RO", "SK",
    "SI", "ES", "SE", "GB"
] %}

<main class="main">
    <section>
        <div class="container">
            <h1 class="service__title">
                {% trans %}policy_page.title{% endtrans %}
            </h1>
            <p class="service__text">
                {% trans %}policy_page.updated_at{% endtrans %}
            </p>
            <p class="service__text">
                {% trans %}policy_page.subtitle{% endtrans %}
            </p>
            <h2 class="service__subtitle">
                {% trans %}policy_page.application_of_privacy_policy.title{% endtrans %}
            </h2>
            <p class="service__text">
                {{ 'policy_page.application_of_privacy_policy.text'|trans|replace({
                    '%we%': '<b>'~'policy_page.application_of_privacy_policy.text_parts.we'|trans~'</b>',
                    '%us%': '<b>'~'policy_page.application_of_privacy_policy.text_parts.us'|trans~'</b>',
                    '%services%': '<b>'~'policy_page.application_of_privacy_policy.text_parts.services'|trans~'</b>',
                    '%terms%': '<b>'~'policy_page.application_of_privacy_policy.text_parts.terms'|trans~'</b>',
                })|raw }}
            </p>
            <h2 class="service__subtitle">
                {% trans %}policy_page.kind_of_data_we_collect.title{% endtrans %}
            </h2>
            <p class="service__text">
                {% trans %}policy_page.kind_of_data_we_collect.text{% endtrans %}
            </p>
            <h3 class="service__subtitle">
                {% trans %}policy_page.data_we_collect.title{% endtrans %}
            </h3>
            <p class="service__text">
                {% trans %}policy_page.data_we_collect.text{% endtrans %}
            </p>
            <h3 class="service__subtitle">
                {% trans %}policy_page.navigation_data.title{% endtrans %}
            </h3>
            <p class="service__text">
                {% trans %}policy_page.navigation_data.text{% endtrans %}
            </p>
            <h3 class="service__subtitle" id="cookies">
                {% trans %}policy_page.cookies_and_similar.title{% endtrans %}
            </h3>
            <p class="service__text">
                {% trans %}policy_page.cookies_and_similar.text{% endtrans %}
            </p>
            <h4 class="service__subtitle">
                {% trans %}policy_page.browser_and_device_data.title{% endtrans %}
            </h4>
            <p class="service__text">
                {% trans %}policy_page.browser_and_device_data.text{% endtrans %}
            </p>
            <h3 class="service__subtitle">
                {% trans %}policy_page.advertising_data.title{% endtrans %}
            </h3>
            <p class="service__text">
                {% trans %}policy_page.advertising_data.text{% endtrans %}
            </p>
            <h3 class="service__subtitle">
                {% trans %}policy_page.data_about_children.title{% endtrans %}
            </h3>
            <p class="service__text">
                {% trans %}policy_page.data_about_children.text{% endtrans %}
            </p>
            <h4 class="service__subtitle">
                {% trans %}policy_page.how_we_use_data.title{% endtrans %}
            </h4>
            <p class="service__text">
                {% trans %}policy_page.how_we_use_data.text{% endtrans %}
            </p>
            <ul class="service__list">
                <li class="service__list-item">
                    <b>{% trans %}policy_page.how_we_use_data.options.0.0{% endtrans %}</b>{% trans %}policy_page.how_we_use_data.options.0.1{% endtrans %}
                </li>
                <li class="service__list-item">
                    <b>{% trans %}policy_page.how_we_use_data.options.1.0{% endtrans %}</b>{% trans %}policy_page.how_we_use_data.options.1.1{% endtrans %}
                </li>
                <li class="service__list-item">
                    <b>{% trans %}policy_page.how_we_use_data.options.2.0{% endtrans %}</b>{% trans %}policy_page.how_we_use_data.options.2.1{% endtrans %}
                </li>
                <li class="service__list-item">
                    <b>{% trans %}policy_page.how_we_use_data.options.3.0{% endtrans %}</b>{% trans %}policy_page.how_we_use_data.options.3.1{% endtrans %}
                </li>
                <li class="service__list-item">
                    <b>{% trans %}policy_page.how_we_use_data.options.4.0{% endtrans %}</b>{% trans %}policy_page.how_we_use_data.options.4.1{% endtrans %}
                </li>
                <li class="service__list-item">
                    <b>{% trans %}policy_page.how_we_use_data.options.5.0{% endtrans %}</b>{% trans %}policy_page.how_we_use_data.options.5.1{% endtrans %}
                </li>
            </ul>
            <p class="service__text">
                {% trans %}policy_page.how_we_use_data.subtext{% endtrans %}
            </p>
            <h4 class="service__subtitle">
                {% trans %}policy_page.legal_basis_for_processing.title{% endtrans %}
            </h4>
            <p class="service__text">
                {% trans %}policy_page.legal_basis_for_processing.text{% endtrans %}
            </p>
            <h4 class="service__subtitle">
                {% trans %}policy_page.advertising.title{% endtrans %}
            </h4>
            <h4 class="service__subtitle">
                {% trans %}policy_page.advertising.articles.other_services_on_the_website.title{% endtrans %}
            </h4>
            <p class="service__text">
                {% trans %}policy_page.advertising.articles.other_services_on_the_website.text{% endtrans %}
            </p>
            <h4 class="service__subtitle">
                {% trans %}policy_page.advertising.articles.the_website_on_other_websites.title{% endtrans %}
            </h4>
            <p class="service__text">
                {% set digital_advertising_alliance_link %}
                    <a href="https://optout.aboutads.info/?c=2&lang=EN" target="_blank">
                        {% trans %}policy_page.advertising.articles.the_website_on_other_websites.text_parts.digital_advertising_alliance{% endtrans %}
                    </a>
                {% endset %}
                {% set network_advertising_initiative_link %}
                    <a href="https://optout.networkadvertising.org" target="_blank">
                        {% trans %}policy_page.advertising.articles.the_website_on_other_websites.text_parts.network_advertising_initiative{% endtrans %}
                    </a>
                {% endset %}

                {{ 'policy_page.advertising.articles.the_website_on_other_websites.text'|trans|replace({
                    '%digital_advertising_alliance%': digital_advertising_alliance_link,
                    '%network_advertising_initiative%': network_advertising_initiative_link,
                })|raw }}
            </p>
            <h5 class="service__subtitle">
                {% trans %}policy_page.advertising_choices.title{% endtrans %}
            </h5>
            <ul class="service__list">
                <li class="service__list-item">
                    {% trans %}policy_page.advertising_choices.options.0{% endtrans %}
                </li>
                <li class="service__list-item">
                    {% set nai_website_link %}
                        <a href="https://optout.networkadvertising.org/" target="_blank">
                            {% trans %}policy_page.advertising_choices.options.1.text_parts.nai_website{% endtrans %}
                        </a>
                    {% endset %}
                    {% set daa_website_link %}
                        <a href="https://www.aboutads.info/choices/" target="_blank">
                            {% trans %}policy_page.advertising_choices.options.1.text_parts.daa_website{% endtrans %}
                        </a>
                    {% endset %}

                    {{ 'policy_page.advertising_choices.options.1.text'|trans|replace({
                        '%nai_website%': nai_website_link,
                        '%daa_website%': daa_website_link,
                    })|raw }}
                </li>
            </ul>
            <p class="service__text">
                {% trans %}policy_page.advertising_choices.text{% endtrans %}
            </p>
            <h5 class="service__subtitle">
                {% trans %}policy_page.retention_period.title{% endtrans %}
            </h5>
            <p class="service__text">
                {% trans %}policy_page.retention_period.text{% endtrans %}
            </p>
            <h5 class="service__subtitle">
                {% trans %}policy_page.with_whom_we_share_data.title{% endtrans %}
            </h5>
            <p class="service__text">
                {% trans %}policy_page.with_whom_we_share_data.text.0{% endtrans %}
                <br>
                {% trans %}policy_page.with_whom_we_share_data.text.1{% endtrans %}
            </p>
            <h6 class="service__subtitle">
                {% trans %}policy_page.with_your_consent.title{% endtrans %}
            </h6>
            <p class="service__text">
                {% trans %}policy_page.with_your_consent.text{% endtrans %}
            </p>
            <h6 class="service__subtitle">{% trans %}policy_page.service_providers.title{% endtrans %}</h6>
            <p class="service__text">{% trans %}policy_page.service_providers.text{% endtrans %}</p>
            <h6 class="service__subtitle">
                {% trans %}policy_page.other_situations.title{% endtrans %}
            </h6>
            <p class="service__text">
                {% trans %}policy_page.other_situations.text{% endtrans %}
            </p>
            <ul class="service__list">
                <li class="service__list-item">
                    {% trans %}policy_page.other_situations.options.0{% endtrans %}
                </li>
                <li class="service__list-item">
                    {% trans %}policy_page.other_situations.options.1{% endtrans %}
                </li>
                <li class="service__list-item">
                    {% trans %}policy_page.other_situations.options.2{% endtrans %}
                </li>
            </ul>
            <h6 class="service__subtitle">
                {% trans %}policy_page.international_data_transfers.title{% endtrans %}
            </h6>
            <p class="service__text">
                {% trans %}policy_page.international_data_transfers.text{% endtrans %}
            </p>
            <h6 class="service__subtitle">
                {% trans %}policy_page.we_process_data_outside_eu_and_eea.title{% endtrans %}
            </h6>
            <p class="service__text">
                {% trans %}policy_page.we_process_data_outside_eu_and_eea.text.0{% endtrans %}
            </p>
            <p class="service__text">
                {% trans %}policy_page.we_process_data_outside_eu_and_eea.text.1{% endtrans %}
            </p>
            <h6 class="service__subtitle">
                {% trans %}policy_page.third_party_websites.title{% endtrans %}
            </h6>
            <p class="service__text">
                {% trans %}policy_page.third_party_websites.text{% endtrans %}
            </p>
            <h6 class="service__subtitle">
                {% trans %}policy_page.protect_personal_data.title{% endtrans %}
            </h6>
            <p class="service__text">
                {% trans %}policy_page.protect_personal_data.text{% endtrans %}
            </p>
            <h6 class="service__subtitle">
                {% trans %}policy_page.changes_to_privacy_police.title{% endtrans %}
            </h6>
            <p class="service__text">
                {% trans %}policy_page.changes_to_privacy_police.text{% endtrans %}
            </p>
            <h6 class="service__subtitle">
                {% trans %}policy_page.privacy_rights.title{% endtrans %}
            </h6>
            <p class="service__text">
                {% set privacy_rights_email_link %}
                    <a href="mailto:{{ support_email }}" class="service__text_link" target="_blank">{{ support_email }}</a>
                {% endset %}

                {{ 'policy_page.privacy_rights.text'|trans|replace({
                    '%email_link%': privacy_rights_email_link,
                })|raw }}
            </p>
            <h6 class="service__subtitle">
                {% trans %}policy_page.personal_data_controller.title{% endtrans %}
            </h6>

            <div class="service__text">
                {% if isOrganicMobileAppUser %}
                    {% trans %}policy_page.personal_data_controller.text.other{% endtrans %}
                {% elseif clientCountry == 'US' %}
                    {% trans %}policy_page.personal_data_controller.text.us{% endtrans %}
                {% elseif clientCountry == 'HK' or clientCountry == 'CN' %}
                    {% trans %}policy_page.personal_data_controller.text.hk{% endtrans %}
                {% elseif clientCountry in countryCodesEEA %}
                    {% trans %}policy_page.personal_data_controller.text.eea{% endtrans %}
                {% else %}
                    {% trans %}policy_page.personal_data_controller.text.other{% endtrans %}
                {% endif %}
            </div>

            <h6 class="service__subtitle">
                {% trans %}policy_page.contact_us.title{% endtrans %}
            </h6>
            <p class="service__text">
                {% set contact_us_email_link %}
                    <a href="mailto:{{ support_email }}" class="service__text_link" target="_blank">{{ support_email }}</a>
                {% endset %}

                {{ 'policy_page.contact_us.text'|trans|replace({
                    '%email_link%': contact_us_email_link,
                })|raw }}
            </p>

            <h6 class="service__subtitle">
                {% trans %}policy_page.translations.title{% endtrans %}
            </h6>
            <p class="service__text">
                {% trans %}policy_page.translations.text{% endtrans %}
            </p>
        </div>
    </section>
</main>

{% include 'subscription/components/footer-main.html.twig' with {
    hiddenLegal: isOrganicMobileAppUser,
    hiddenLinks: isOrganicMobileAppUser,
} %}

<script src="{{ asset("/assets/subscription/js/service.js") }}"></script>

</body>
</html>
