<aside class="unsubscribe__cancelled" id="cancelled-popup">
    <div class="container">
        <div class="unsubscribe__cancelled-inn">
            {% include('subscription/unsubscribe/components/header.html.twig') %}
            {% include('subscription/unsubscribe/components/cancel-subscription.html.twig') with {button_trigger_class: 'cancelled-popup-close-btn'} %}
            {% include('subscription/unsubscribe/components/title-with-img.html.twig') with { title: "Your subscription has been cancelled" } %}
            {% include('subscription/unsubscribe/components/subtitle.html.twig') with { subtitle: "As a thank you for being loyal customer. we would like to offer you a no-commitment, cancel anytime PawChamp at <b>a special discount</b>." } %}
            {% include('subscription/unsubscribe/components/plan-current.html.twig') with {
                bg_title_class: "black",
                bg_body_class: "dark-grey",
                title: "CANCELLED PLAN",
                plan_name: planName,
                price: userCurrencySign() ~ currentDayPrice,
                total_price: userCurrencySign() ~ currentTotal,
            } %}
            {% include('subscription/unsubscribe/components/plan-new.html.twig') with {
                bg_body_class: "",
                bg_title_class: "green",
                title: "NEW PRICE - SAVE 75%",
                plan_name: planName,
                price_old: userCurrencySign() ~ currentDayPrice,
                price: userCurrencySign() ~ newDiscountDayPrice,
                total_price: userCurrencySign() ~ newDiscountTotal,
                btn_text: "CHANGE PLAN",
                btn_trigger_class: "staying-popup-open-btn",
                btn_addiction_class: "green",
                disclamer_text: "You will be charged the discounted price each period after the expiration of current subscription period. <a href=" ~ termsLink ~ ">Terms of use</a>",
                amplitude_event: "discount_75",
                unsubscribe_status: "discount_75",
            } %}

            <a class="unsubscribe__btn-link" data-event="{{ amplitude_event_feed }}">
                {% include('subscription/unsubscribe/components/cancel-button.html.twig') with {
                    btn_text: "GOT IT",
                    btn_trigger_class: "got-it-btn",
                    unsubscribe_status: "unsubscribed",
                } %}
            </a>
        </div>
    </div>
</aside>
