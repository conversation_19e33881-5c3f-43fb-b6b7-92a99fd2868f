<div class="unsubscribe__plan {{ bg_body_class }}">
    <div class="unsubscribe__plan-title {{ bg_title_class }}">
        {{ title }}
    </div>
    <div class="unsubscribe__plan-wrapp">
        {% if plan_name != "" %}
            <p class="unsubscribe__plan-name">
                {{ plan_name }}
            </p>
        {% endif %}
        {% if price_old != "" %}
            <p class="unsubscribe__plan-price-old">
                {{ price_old }}<span>/day</span>
            </p>
        {% endif %}
        {% if price != "" %}
            <p class="unsubscribe__plan-price">
                {{ price }}
            </p>
        {% else %}
            <p class="unsubscribe__plan-price">
                {{ price }} <span>/ day</span>
            </p>
        {% endif %}
        {% if total_price != "" %}
            <p class="unsubscribe__plan-price-total">
                Total {{ total_price }}
            </p>
        {% endif %}
        <button class="unsubscribe__plan-btn {{ btn_trigger_class }} {{ btn_addiction_class }}" data-event="{{ amplitude_event }}" data-status="{{ unsubscribe_status }}">
            {{ btn_text }}
        </button>
        <p class="unsubscribe__plan-disclamer">
            {{ disclamer_text | raw }}
        </p>
    </div>
</div>
