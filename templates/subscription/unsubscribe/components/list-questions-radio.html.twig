<ul class="unsubscribe__form-list" data-type="question" data-event="uns_reas_dnt_like">
    {% for question in questions %}
        <li class="unsubscribe__form-item">
            <label class="unsubscribe__form-label" for="question{{ question.id }}">
                <input class="unsubscribe__form-input" type="radio" name="reason" value="{{ question.id }}" id="question{{ question.id }}" data-question="{{ question.name }}">
                {{ question.name }}
            </label>
        </li>
    {% endfor %}
</ul>
<div class="unsubscribe__btn-link">
    {% include('subscription/unsubscribe/components/red-bottom-button.html.twig') with {
        btn_trigger_class: "cancelled-popup-open-btn",
        btn_text: "Submit",
        btn_addictive_class: "submit unsubscribe",
        unsubscribe_status: "unsubscribed",
    } %}
</div>
