<aside class="unsubscribe__cancelled" id="cancelled-popup">
    <div class="container">
        {% include('subscription/unsubscribe/components/header.html.twig') %}
        <div class="unsubscribe__cancelled-wrapp">
            {% include('subscription/unsubscribe/components/cancel-subscription.html.twig') with {button_trigger_class: 'cancelled-popup-close-btn'} %}
            <div class="unsubscribe__cancelled-title">
                {{ title }}
            </div>
            <div class="unsubscribe__cancelled-img" id="cancelled-popup-open-btn">
                <svg width="135" height="107" viewBox="0 0 135 107" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M67.5004 106.461C38.3281 106.461 14.2949 82.4281 14.2949 53.2054C14.2949 24.0331 38.2777 0 67.5004 0C96.6727 0 120.706 24.0331 120.706 53.2054C120.706 82.4281 96.7231 106.461 67.5004 106.461ZM61.9581 78.4982C64.1246 78.4982 66.0896 77.3897 67.3996 75.4752L90.7777 39.7025C91.6847 38.3926 92.1885 37.0322 92.1885 35.7726C92.1885 32.6488 89.4174 30.4319 86.3943 30.4319C84.4294 30.4319 82.7667 31.49 81.4567 33.6061L61.807 64.8441L53.0402 54.0116C51.6798 52.3993 50.2691 51.6939 48.4552 51.6939C45.3818 51.6939 42.8626 54.1627 42.8626 57.2865C42.8626 58.7477 43.3161 60.0073 44.4749 61.418L56.3151 75.5255C57.9274 77.4905 59.6909 78.4982 61.9581 78.4982Z"
                          fill="#40D351"></path>
                </svg>
            </div>
            <p class="unsubscribe__cancelled-text">
                {{ text }}
            </p>
            <div class="unsubscribe__textarea-wrapp">
                <label for="cancelledTextarea"></label>
                <textarea class="unsubscribe__textarea {% if addictional_class is defined %}{{ addictional_class }}{% endif %}" name="" id="cancelledTextarea" placeholder="Please leave your feedback so we can improve!"></textarea>
            </div>
            <a class="unsubscribe__btn-link fixed" data-event="{{ amplitude_event }}">
                {% include('subscription/unsubscribe/components/red-bottom-button.html.twig') with {
                    btn_trigger_class: "got-it",
                    btn_text: "GOT IT",
                    btn_addictive_class: "got-it-btn",
                    unsubscribe_status: "unsubscribed",
                } %}
            </a>
        </div>
    </div>
</aside>
