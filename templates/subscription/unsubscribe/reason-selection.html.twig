{% extends 'subscription/unsubscribe/unsubscribe.html.twig' %}

    {% block additionalScripts %}
        <script defer src="{{ asset("/assets/unsubscribe/js/choice.js") }}"></script>
    {% endblock %}

{% block content %}
    <section class="unsubscribe">
        <div class="container">
            <div class="unsubscribe__wrapp">
                <div class="unsubscribe__cancel-box">
                    <div class="unsubscribe__cancel-btn-back"></div>
                </div>
                {% include('subscription/unsubscribe/components/title.html.twig') with { title: 'What is the main reason you decided not to continue with the PawChamp?' } %}
                <div class="unsubscribe__box">
                    <form class="unsubscribe__form" action="{{ path('unsubscribe') }}">
                        {% for param, value in app.request.query.all %}
                            <input type="hidden" name="{{ param }}" value="{{ value }}">
                        {% endfor %}
                        <ul class="unsubscribe__form-list" data-type="reason" data-event="unsubscribe_reason">
                            {% for reason in reasons %}
                                <li class="unsubscribe__form-item">
                                    <label class="unsubscribe__form-label" for="reason{{ reason.id }}">
                                        <input class="unsubscribe__form-input" type="radio" name="reason" value="{{ reason.id }}" id="reason{{ reason.id }}" data-question="{{ reason.name }}">
                                        {{ reason.name }}
                                    </label>
                                </li>
                            {% endfor %}
                        </ul>
                        <div class="unsubscribe__btn-link fixed">
                            <button class="unsubscribe__btn submit" type="submit">Submit</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>
{% endblock %}
