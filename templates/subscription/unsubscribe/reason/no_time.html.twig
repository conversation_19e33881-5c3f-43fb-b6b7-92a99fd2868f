{% extends 'subscription/unsubscribe/reason.html.twig' %}

{% block additionalScripts %}
    <script defer src="{{ asset("/assets/unsubscribe/js/choice.js") }}"></script>
{% endblock %}

{% block content %}
    <div class="container">
        {% include('subscription/unsubscribe/components/cancel-subscription.html.twig') with {button_trigger_class: 'backBtn'} %}
        {% include('subscription/unsubscribe/components/title.html.twig') with { title: "If not today, then when?" } %}
        {% include('subscription/unsubscribe/components/subtitle.html.twig') with { subtitle: "Invest in your dog's well-being with <b>a generous 50% discount</b> to create a stronger bond and a harmonious environment with your furry friend." } %}

        {% include('subscription/unsubscribe/components/plan-current.html.twig') with {
            bg_title_class: "",
            bg_body_class: "",
            title: "CANCELLED PLAN",
            plan_name: planName,
            price: userCurrencySign() ~ currentDayPrice,
            total_price: userCurrencySign() ~ currentTotal,
        } %}
        {% include('subscription/unsubscribe/components/plan-new.html.twig') with {
            bg_body_class: "",
            bg_title_class: "yellow",
            title: "NEW PRICE - SAVE 50%",
            plan_name: planName,
            price_old: userCurrencySign() ~ currentDayPrice,
            price: userCurrencySign() ~ newDayPrice,
            total_price: userCurrencySign() ~ newTotal,
            btn_text: "CHANGE PLAN",
            btn_trigger_class: "staying-popup-open-btn",
            btn_addiction_class: "",
            disclamer_text: "You will be charged the discounted price each period after the expiration of current subscription period. <a href=" ~ termsLink ~ ">Terms of use</a>",
            amplitude_event: "discount_50",
            unsubscribe_status: "discount_50",
        } %}

        {% include('subscription/unsubscribe/components/cancel-button.html.twig') with {
            btn_text: "CONTINUE",
            btn_trigger_class: "cancelled-popup-open-btn",
            amplitude_event: "unsubscribed",
            amplitude_event_feed: "none",
            unsubscribe_status: "unsubscribed",
        } %}
    </div>

    {% include('subscription/unsubscribe/components/thank-you-popup.html.twig') with {
        title: "Thanks for staying with us!",
        img: "https://res.cloudinary.com/dr0cx27xo/image/upload/v1713346388/dog-training/img/unsubscribe/stayng.png",
        text: "Your discounted plan is active now.
                <br>
                In case you need any other help, do not hesitate to contact our support team at
                <br>
                 <b class='unsubscribe__staying-text-link'>%s</b>" | format(support_email)
    } %}
    {% include('subscription/unsubscribe/components/cancelled-popup.html.twig') with {
        title: "Your subscription has been cancelled",
        text: "Your will not be charged in the future. You can continue using the PawChamp until the next billing period.",
        amplitude_event: "unsubscribed",
        addictional_class: "cancelled-textarea-trigger",
    } %}
{% endblock %}
