{% extends 'subscription/unsubscribe/reason.html.twig' %}

{% block additionalScripts %}
    <script defer src="{{ asset("/assets/unsubscribe/js/choice.js") }}"></script>
    <script>
        document.addEventListener("DOMContentLoaded", () => {
            const buttonElement = document.querySelector(".unsubscribe__btn.staying-popup-open-btn");
            const stayingTextElement = document.querySelector(".unsubscribe__staying-text");

            const newText = `Your plan will remain active. <br>
            In case you need any other help, do not hesitate to contact our support team at <br>
            <b>{{ support_email }}</b>`;

            buttonElement.addEventListener("click", () => {
                stayingTextElement.innerHTML = newText
            })
        })
    </script>
{% endblock %}

{% block content %}
    {% set questions = [
        {
            "id": 1,
            "icon": "https://res.cloudinary.com/dr0cx27xo/image/upload/v1713441162/dog-training/img/unsubscribe/icons/01.svg",
            "text": "Maintain high obedience level"
        },
        {
            "id": 2,
            "icon": "https://res.cloudinary.com/dr0cx27xo/image/upload/v1713441162/dog-training/img/unsubscribe/icons/02.svg",
            "text": "Use 50+ games to boost  your doggo’s mental health"
        },
        {
            "id": 3,
            "icon": "https://res.cloudinary.com/dr0cx27xo/image/upload/v1713441162/dog-training/img/unsubscribe/icons/03.svg",
            "text": "Learn new training techniques"
        },
        {
            "id": 4,
            "icon": "https://res.cloudinary.com/dr0cx27xo/image/upload/v1713441162/dog-training/img/unsubscribe/icons/04.svg",
            "text": "Reach your training goals"
        }
    ] %}

    <div class="container">
        {% include('subscription/unsubscribe/components/cancel-subscription.html.twig') with {button_trigger_class: 'backBtn'} %}
        {% include('subscription/unsubscribe/components/title.html.twig') with { title: "Please tell us a bit more about your particular case." } %}
        {% include('subscription/unsubscribe/components/subtitle.html.twig') with { subtitle: "Your feedback is crucial in helping us tailor our services to better suit our users' needs. Would you mind elaborating on what aspects of our plan didn't align with your needs or preferences?" } %}
        {% include('subscription/unsubscribe/components/textarea-border.html.twig') with {
            addictional_class: "other-textarea",
        } %}
        {% include('subscription/unsubscribe/components/cancel-button.html.twig') with {
            btn_text: "CONTINUE",
            btn_trigger_class: "cancel-plan-popup-open-btn textarea-submit-trigger",
            amplitude_event: "unsubscribed",
            unsubscribe_status: "unsubscribed",
        } %}
    </div>

    {% include('subscription/unsubscribe/components/cancel-plan-popup.html.twig') %}
    {% include('subscription/unsubscribe/components/cancelled-popup-offer.html.twig') with {
        amplitude_event: "unsubscribed",
        amplitude_event_feed: "none",
    } %}
    {% include('subscription/unsubscribe/components/thank-you-popup.html.twig') with {
        title: "Thanks for staying with us!",
        img: "https://res.cloudinary.com/dr0cx27xo/image/upload/v1713346388/dog-training/img/unsubscribe/stayng.png",
        text: "Your discounted plan is active now.
                <br>
                In case you need any other help, do not hesitate to contact our support team at
                <br>
                 <b class='unsubscribe__staying-text-link'>%s</b>" | format(support_email)
    } %}
{% endblock %}
