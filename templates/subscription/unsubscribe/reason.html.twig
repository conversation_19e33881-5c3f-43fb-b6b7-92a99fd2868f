{% extends 'subscription/unsubscribe/unsubscribe.html.twig' %}

{% set hardcodedProductsMap = {
    '1_month_discount_80': {
        plan: '30-DAY PLAN',
        current_day_price: '1.66',
        curent_total: '49.99',
        new_day_price: '0.83',
        new_total: '24.99',
        new_discount_day_price: '0.42',
        new_discount_total: '12.49',
    },
    '3_month_discount_80': {
        plan: '90-DAY PLAN',
        current_day_price: '0.88',
        curent_total: '79.99',
        new_day_price: '0.44',
        new_total: '39.99',
        new_discount_day_price: '0.22',
        new_discount_total: '12.49',
    },
    '3_month_80discount': {
        plan: '90-DAY PLAN',
        current_day_price: '0.88',
        curent_total: '79.99',
        new_day_price: '0.44',
        new_total: '39.99',
        new_discount_day_price: '0.22',
        new_discount_total: '12.49',
    },
    '6_month_discount_80': {
        plan: '180-DAY PLAN',
        current_day_price: '0.66',
        curent_total: '119.99',
        new_day_price: '0.33',
        new_total: '59.99',
        new_discount_day_price: '0.16',
        new_discount_total: '29.99',
    },
    '1m_lower_intro_full_monthly': {
        plan: '30-DAY PLAN',
        current_day_price: '1.66',
        curent_total: '49.99',
        new_day_price: '0.83',
        new_total: '24.99',
        new_discount_day_price: '0.42',
        new_discount_total: '12.49',
    },
    '3m_lower_intro_full_quarterly': {
        plan: '90-DAY PLAN',
        current_day_price: '0.88',
        curent_total: '79.99',
        new_day_price: '0.44',
        new_total: '39.99',
        new_discount_day_price: '0.22',
        new_discount_total: '12.49',
    },
    '6m_lower_intro_full_halfyear': {
        plan: '180-DAY PLAN',
        current_day_price: '0.66',
        curent_total: '119.99',
        new_day_price: '0.33',
        new_total: '59.99',
        new_discount_day_price: '0.16',
        new_discount_total: '29.99',
    },
    '1m_lower_intro_full_monthly_discount': {
        plan: '30-DAY PLAN',
        current_day_price: '1.66',
        curent_total: '49.99',
        new_day_price: '0.83',
        new_total: '24.99',
        new_discount_day_price: '0.42',
        new_discount_total: '12.49',
    },
    '3m_lower_intro_full_quarterly_discount': {
        plan: '90-DAY PLAN',
        current_day_price: '0.88',
        curent_total: '79.99',
        new_day_price: '0.44',
        new_total: '39.99',
        new_discount_day_price: '0.22',
        new_discount_total: '12.49',
    },
    '6m_lower_intro_full_halfyear_discount': {
        plan: '180-DAY PLAN',
        current_day_price: '0.66',
        curent_total: '119.99',
        new_day_price: '0.33',
        new_total: '59.99',
        new_discount_day_price: '0.16',
        new_discount_total: '29.99',
    },
    '1month_NEW_USA': {
        plan: '30-DAY PLAN',
        current_day_price: '1.30',
        curent_total: '38.95',
        new_day_price: '0.51',
        new_total: '15.19',
        new_discount_day_price: '0.39',
        new_discount_total: '11.69',
    },
    '3month_NEW_USA': {
        plan: '90-DAY PLAN',
        current_day_price: '0.74',
        curent_total: '66.65',
        new_day_price: '0.29',
        new_total: '25.99',
        new_discount_day_price: '0.22',
        new_discount_total: '19.99',
    },
    '6month_NEW_USA': {
        plan: '180-DAY PLAN',
        current_day_price: '0.53',
        curent_total: '95.99',
        new_day_price: '0.21',
        new_total: '37.99',
        new_discount_day_price: '0.16',
        new_discount_total: '28.79',
    },
    '1month_NEW_USA_discount': {
        plan: '30-DAY PLAN',
        current_day_price: '1.30',
        curent_total: '38.95',
        new_day_price: '0.51',
        new_total: '15.19',
        new_discount_day_price: '0.39',
        new_discount_total: '11.69',
    },
    '3month_NEW_USA_discount': {
        plan: '90-DAY PLAN',
        current_day_price: '0.74',
        curent_total: '66.65',
        new_day_price: '0.29',
        new_total: '25.99',
        new_discount_day_price: '0.22',
        new_discount_total: '19.99',
    },
    '6month_NEW_USA_discount': {
        plan: '180-DAY PLAN',
        current_day_price: '0.53',
        curent_total: '95.99',
        new_day_price: '0.21',
        new_total: '37.99',
        new_discount_day_price: '0.16',
        new_discount_total: '28.79',
    },
    '1_month_VAT_upd': {
        plan: '30-DAY PLAN',
        current_day_price: '1.66',
        curent_total: '49.99',
        new_day_price: '0.83',
        new_total: '24.99',
        new_discount_day_price: '0.42',
        new_discount_total: '12.49',
    },
    '3_month_VAT_upd': {
        plan: '90-DAY PLAN',
        current_day_price: '0.88',
        curent_total: '79.99',
        new_day_price: '0.44',
        new_total: '39.99',
        new_discount_day_price: '0.22',
        new_discount_total: '12.49',
    },
    '6_month_VAT_upd': {
        plan: '180-DAY PLAN',
        current_day_price: '0.66',
        curent_total: '119.99',
        new_day_price: '0.33',
        new_total: '59.99',
        new_discount_day_price: '0.16',
        new_discount_total: '29.99',
    },
    '1_month_VAT_discount_upd': {
        plan: '30-DAY PLAN',
        current_day_price: '1.66',
        curent_total: '49.99',
        new_day_price: '0.83',
        new_total: '24.99',
        new_discount_day_price: '0.42',
        new_discount_total: '12.49',
    },
    '3_month_VAT_discount_upd': {
        plan: '90-DAY PLAN',
        current_day_price: '0.88',
        curent_total: '79.99',
        new_day_price: '0.44',
        new_total: '39.99',
        new_discount_day_price: '0.22',
        new_discount_total: '12.49',
    },
    '6_month_VAT_discount_upd': {
        plan: '180-DAY PLAN',
        current_day_price: '0.66',
        curent_total: '119.99',
        new_day_price: '0.33',
        new_total: '59.99',
        new_discount_day_price: '0.16',
        new_discount_total: '29.99',
    }
} %}

{% set billingPeriodPlanConfigMap = {
    "3 days": {
        name: "3-DAY PLAN",
        daysAmount: 3,
    },
    "7 days": {
        name: "7-DAY PLAN",
        daysAmount: 7,
    },
    "1 week": {
        name: "1-WEEK PLAN",
        daysAmount: 7,
    },
    "30 days": {
        name: "30-DAY PLAN",
        daysAmount: 30,
    },
    "3 month": {
        name: "90-DAY PLAN",
        daysAmount: 90,
    },
    "6 month": {
        name: "180-DAY PLAN",
        daysAmount: 180,
    },
    "1 year": {
        name: "1-YEAR PLAN",
        daysAmount: 365,
    },
    "lifetime": {
        name: "LIFETIME PLAN",
        daysAmount: 999999999999,
    },
} %}

{# Get subscription and product information #}
{% set activeSubscription = app.user.getActiveSubscription() %}
{% set activeProduct = activeSubscription.product ?? null %}
{% set termsLink = path('static_page_terms') %}

{# Extract product details with null safety #}
{% set productName = activeProduct.name ?? null %}
{% set discount50Product = activeProduct.discount50Product ?? null %}
{% set discount75Product = activeProduct.discount75Product ?? null %}

{% set hasHardcodedProductData = hardcodedProductsMap[productName] is defined %}
{% set hardcodedProductData = hasHardcodedProductData ? hardcodedProductsMap[productName] : null %}

{# Validate active product data #}
{% set activeProductBillingPeriod = activeProduct.getBillingPeriod() ?? null %}
{% set activeProductBillingPeriodValue = activeProductBillingPeriod.value ?? null %}
{% set hasActiveProductAllValidData =
    activeProduct is not null and
    productName is not null and
    activeProductBillingPeriodValue is not null and
    billingPeriodPlanConfigMap[activeProductBillingPeriodValue] is defined and
    activeProduct.amount is defined
%}

{# Validate 50% discount product data #}
{% set discount50BillingPeriod = discount50Product.getBillingPeriod() ?? null %}
{% set discount50BillingPeriodValue = discount50BillingPeriod.value ?? null %}
{% set hasDiscount50ProductAllValidData =
    discount50Product is not null and
    discount50Product.name is defined and
    discount50BillingPeriodValue is not null and
    billingPeriodPlanConfigMap[discount50BillingPeriodValue] is defined and
    discount50Product.amount is defined
%}

{# Validate 75% discount product data #}
{% set discount75ProductBillingPeriod = discount75Product.getBillingPeriod() ?? null %}
{% set discount75BillingPeriodValue = discount75ProductBillingPeriod.value ?? null %}
{% set hasDiscount75ProductAllValidData =
    discount75Product is not null and
    discount75Product.name is defined and
    discount75BillingPeriodValue is not null and
    billingPeriodPlanConfigMap[discount75BillingPeriodValue] is defined and
    discount75Product.amount is defined
%}

{# Calculate current plan prices #}
{% if hasActiveProductAllValidData %}
    {% set product = activeProduct.name %}
    {% set planConfig = billingPeriodPlanConfigMap[activeProductBillingPeriodValue] %}
    {% set planName = planConfig.name %}
    {% set currentTotal = activeProduct.amount / 100 %}
    {% set currentDayPrice = (currentTotal / planConfig.daysAmount)|number_format(2) %}
{% elseif hasHardcodedProductData %}
    {% set product = productName %}
    {% set planName = hardcodedProductData.plan %}
    {% set currentDayPrice = hardcodedProductData.current_day_price %}
    {% set currentTotal = hardcodedProductData.curent_total %}
{% else %}
    {% set product = '1m_lower_intro_full_monthly' %}
    {% set planName = '30-DAY PLAN' %}
    {% set currentDayPrice = '1.66' %}
    {% set currentTotal = '49.99' %}
{% endif %}

{# Calculate 50% discount prices #}
{% if hasDiscount50ProductAllValidData %}
    {% set planConfig = billingPeriodPlanConfigMap[discount50BillingPeriodValue] %}
    {% set newTotal = discount50Product.amount / 100 %}
    {% set newDayPrice = (newTotal / planConfig.daysAmount)|number_format(2) %}
{% elseif hasHardcodedProductData %}
    {% set newDayPrice = hardcodedProductData.new_day_price %}
    {% set newTotal = hardcodedProductData.new_total %}
{% else %}
    {% set newDayPrice = '0.83' %}
    {% set newTotal = '24.99' %}
{% endif %}

{# Calculate 75% discount prices #}
{% if hasDiscount75ProductAllValidData %}
    {% set planConfig = billingPeriodPlanConfigMap[discount75BillingPeriodValue] %}
    {% set newDiscountTotal = discount75Product.amount / 100 %}
    {% set newDiscountDayPrice = (newDiscountTotal / planConfig.daysAmount)|number_format(2) %}
{% elseif hasHardcodedProductData %}
    {% set newDiscountDayPrice = hardcodedProductData.new_discount_day_price %}
    {% set newDiscountTotal = hardcodedProductData.new_discount_total %}
{% else %}
    {% set newDiscountDayPrice = '0.42' %}
    {% set newDiscountTotal = '12.49' %}
{% endif %}
