<aside class="password__container" id="password-container">
    <div class="password__inner">
        <div class="password__logo-wrapp">
            <img src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1718272258/paw-champ/logo/square-without-shadow.svg" alt="" class="password__logo">
        </div>
        <h4 class="password__title">
            <img class="password__title-icon" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1671198919/dog-training/icons/good.png" alt="">
            Criar uma conta
        </h4>
        <p class="password__subtitle">
            Vamos criar uma conta para a sua adesão`
        </p>
        <div class="password__email-wrapp">
            <p class="password__email-title">
                Seu E-mail (Login):
            </p>
            <p class="password__email">
                <EMAIL>
            </p>
        </div>
        <form class="password__form" name="password-form" id="password-form">
            <div class="password__field">
                <label class="password__field-label" for="name">Criar uma senha</label>
                <div class="password__field-input-wrapp">
                    <input class="password__field-input" type="password" id="password" name="password" placeholder="Digite uma nova senha" required/>
                    <img class="password__field-input-toggle-button" src="https://images.paw-champ.com/pc/icons/eye-slash-icon-gray.svg" alt="">
                </div>
            </div>
            <div class="password__field">
                <label class="password__field-label" for="email">Repita sua senha</label>
                <div class="password__field-input-wrapp">
                    <input class="password__field-input" type="password" id="password-confirm" name="password-confirm" placeholder="Repetir a senha" required/>
                    <img class="password__field-input-toggle-button" src="https://images.paw-champ.com/pc/icons/eye-slash-icon-gray.svg" alt="">
                </div>
            </div>

            <div class="password__error-message">
                <span id="password__error-text"></span>
            </div>
            <div class="password__button-wrapp">
                <p class="password__error-text" id="submit-error-text">
                    Em caso de qualquer dúvida ou problema, por favor, entre em contato conosco enviando um e-mail para <b>{{ support_email }}</b>
                </p>
                <p class="password__error-info" id="submit-info-text">
                    Você pode alterar sua senha a qualquer momento.
                </p>
                <button class="password__button" type="submit">Confirmar</button>
            </div>
        </form>
    </div>

</aside>
<script>
    window.setPasswordUrl = '{{ path('user_api_set_password', {'externalId': app.user.externalId}) }}'
    window.checkPasswordUrl = '{{ path('user_api_check_password', {'externalId': app.user.externalId}) }}'
    window.userEmail = '{{ app.user.email }}'
    window.userHasPassword = {{ app.user.hasPassword ? 'true' : 'false' }}
</script>

{% include 'subscription/components/success.html.twig' %}
