<div class="container product-onboarding__sign-up-form" id="onboardingSignUpForm">
    <div class="product-onboarding_box product-onboarding_box-block">
        <div class="product-onboarding_box">
            <div class="product-onboarding_title">
                {% if pc_flowtolms_split == 3 %}
                    <svg width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12.8772 22.7858C7.20005 22.7858 2.59619 18.1798 2.59619 12.5001C2.59619 6.82032 7.20005 2.21436 12.8772 2.21436C18.5638 2.21436 23.1676 6.82032 23.1676 12.5001C23.1676 18.1798 18.5638 22.7858 12.8772 22.7858ZM11.851 17.3604C12.35 17.3604 12.8019 17.0872 13.1032 16.6728L17.4152 10.2866C17.6317 9.98516 17.707 9.69316 17.707 9.42943C17.707 8.70415 17.0668 8.15784 16.3419 8.15784C15.8523 8.15784 15.4851 8.41216 15.165 8.91137L11.851 14.1202L10.4764 12.5566C10.2034 12.2458 9.89269 12.0762 9.47844 12.0762C8.7535 12.0762 8.14153 12.6508 8.14153 13.3855C8.14153 13.7246 8.23568 13.9883 8.52754 14.3274L10.58 16.7104C10.9283 17.106 11.3426 17.3604 11.851 17.3604Z" fill="#1998CD"/>
                    </svg>

                    Payment succeed
                {% else %}
                    Create your account
                {% endif %}
            </div>
            <div class="product-onboarding_subtitle">
                {% if pc_flowtolms_split == 3 %}
                    Create your account to save your
                {% else %}
                    To save your
                {% endif %}
                progress and access
                <span class="is-capitalized">
                    {{ 'shared.pet_name'|resp_i11n_trans(domain='shared') }}'s
                </span>
                training plan
            </div>

            <span class="nameInsertSelector"></span>
        </div>

        <div class="product-onboarding_box product-onboarding_box-form">
            <div class="product-onboarding_box">
                <label for="userEmailInput" class="product-onboarding_label">Your email</label>
                <input
                        class="product-onboarding_input"
                        value="<EMAIL>"
                        type="text"
                        placeholder="Email"
                        id="userEmailInput"
                >
                <p class="product-onboarding_input-error" id="emailError"></p>
            </div>

            <div class="product-onboarding_box">
                <label for="userPasswordInput" class="product-onboarding_label">Your password</label>
                <div class="product-onboarding_input-wrapper">
                    <input
                            class="product-onboarding_input"
                            type="password"
                            placeholder="Password"
                            id="userPasswordInput"
                    >
                    <img src="https://images.paw-champ.com/pc/icons/eye-slash-icon-gray.svg"
                         alt="eye slash icon"
                         class="product-onboarding_input-button"
                         id="showPasswordIcon"/>
                </div>
                <p class="product-onboarding_input-error" id="passwordError"></p>
            </div>

            <div class="product-onboarding_box">
                <label for="userPasswordConfirmInput" class="product-onboarding_label">Confirm password</label>
                <div class="product-onboarding_input-wrapper">
                    <input
                            class="product-onboarding_input"
                            type="password"
                            placeholder="Confirm Password"
                            id="userPasswordConfirmInput"
                    >
                    <img src="https://images.paw-champ.com/pc/icons/eye-slash-icon-gray.svg"
                         alt="eye slash icon"
                         class="product-onboarding_input-button"
                         id="showPasswordIconConfirm"/>
                </div>
                <p class="product-onboarding_input-error" id="confirmPasswordError"></p>
            </div>
        </div>

        <div class="product-onboarding_box">
            <div class="product-onboarding_note">
                <img src="https://images.paw-champ.com/pc/icons/info-icon-blue.svg"
                     alt="info icon"
                     class="product-onboarding_note_icon"
                />
                <p class="product-onboarding_note_text">
                    You can update your email and password by contacting
                    {% if pc_flowtolms_split == 3 %}
                        <b class="product-onboarding_note_text-link">{{ support_email }}</b>
                    {% else %}
                        <a class="product-onboarding_note_text-link is-underlined"
                           href="mailto:{{ support_email }}"
                        >
                            {{ support_email }}
                        </a>
                    {% endif %}
                    at any time.
                </p>
            </div>
        </div>

        <p class="product-onboarding_input-error" id="commonError"></p>
    </div>

    <div class="product-onboarding_box-button">
        <button type="submit" class="product-onboarding_button" id="createAccountBtn" disabled>
                <span class="product-onboarding_button-text">
                    Create account
                </span>
        </button>
    </div>
</div>
