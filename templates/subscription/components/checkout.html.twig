<aside class="checkout">
    <div class="checkout__wrapp">
        <div class="checkout__inner">
            <div class="checkout__box">
                <div class="checkout__top">
                <svg class="checkout__logo " width="24" height="20" viewBox="0 0 24 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path fill-rule="evenodd" clip-rule="evenodd"
                          d="M10.6645 2.07798C8.94338 1.99316 6.67518 2.79867 4.46815 6.44506C1.83489 10.7956 1.78227 13.7802 2.50502 15.5379C3.23339 17.3092 4.79898 18.0266 5.95954 17.9169C7.28962 17.7913 8.20802 17.1476 8.75377 16.2251C9.31588 15.2748 9.522 13.9524 9.21566 12.4731C9.10798 11.9532 8.85811 11.2729 8.52922 10.619C8.19523 9.95482 7.82882 9.41566 7.53499 9.12656C7.12378 8.72199 7.12378 8.06604 7.53499 7.66146C7.9462 7.25689 8.6129 7.25689 9.0241 7.66146C9.55022 8.17909 10.0371 8.94521 10.4167 9.7001C10.8015 10.4651 11.1256 11.3177 11.2792 12.0595C11.6671 13.9323 11.4458 15.7933 10.5739 17.2673C9.68559 18.7689 8.16327 19.7902 6.16087 19.9794C4.01887 20.1818 1.61015 18.8863 0.552803 16.3149C-0.51015 13.7299 -0.159871 10.0418 2.65875 5.38494C5.14985 1.26922 8.06835 -0.124518 10.7699 0.00860894C13.3477 0.13564 15.4284 1.64589 16.3821 2.75345C17.0311 3.50703 17.416 4.33273 17.6671 4.9238C17.7233 5.05613 17.7683 5.16539 17.807 5.25941C17.8253 5.30371 17.8422 5.34462 17.8581 5.38296C17.9018 5.3841 17.9485 5.38524 17.9985 5.38641C18.0236 5.387 18.0493 5.3876 18.0756 5.38821C18.4374 5.39659 18.9091 5.40753 19.392 5.44126C19.9036 5.477 20.4775 5.54119 20.9869 5.67245C21.434 5.78766 22.1062 6.01898 22.4967 6.57993C22.6862 6.85209 22.8065 7.21217 22.8845 7.53132C22.9701 7.88193 23.0322 8.30046 23.0526 8.75764C23.0931 9.66648 22.9711 10.796 22.4725 11.8878C21.9657 12.9975 21.0791 14.0431 19.6515 14.745C18.2398 15.439 16.3711 15.7624 13.9424 15.5734C13.3627 15.5283 12.9299 15.0294 12.9757 14.459C13.0216 13.8886 13.5287 13.4628 14.1084 13.5079C16.2762 13.6766 17.7353 13.3708 18.7101 12.8915C19.669 12.4201 20.2275 11.7481 20.5516 11.0385C20.8839 10.311 20.9786 9.51965 20.9487 8.84837C20.9339 8.51551 20.889 8.22919 20.8368 8.01547C20.8093 7.90291 20.7843 7.8286 20.7682 7.78542C20.7251 7.76414 20.6307 7.72251 20.4533 7.6768C20.131 7.59375 19.7083 7.54053 19.2428 7.50802C18.8116 7.4779 18.3862 7.46799 18.0187 7.45943C17.9949 7.45888 17.9714 7.45833 17.9481 7.45778C17.7635 7.45344 17.5826 7.44886 17.4361 7.44063C17.363 7.43652 17.2824 7.43067 17.2052 7.42113C17.1532 7.41469 17.0067 7.39654 16.8537 7.33695C16.514 7.20467 16.3263 6.95303 16.2535 6.84866C16.1628 6.71868 16.0928 6.5795 16.0416 6.47039C15.9772 6.3328 15.901 6.14854 15.8275 5.97087C15.7918 5.8846 15.7568 5.79988 15.724 5.7228C15.4954 5.18462 15.2102 4.59778 14.7754 4.09284C14.0872 3.29376 12.5094 2.16889 10.6645 2.07798ZM17.4679 5.36535C17.4558 5.36385 17.4584 5.36357 17.4719 5.36581C17.4703 5.36563 17.469 5.36548 17.4679 5.36535Z"
                          fill="#16191E"/>
                    <path d="M13.6812 6.8893C13.6812 7.46055 13.2106 7.92364 12.6299 7.92364C12.0493 7.92364 11.5786 7.46055 11.5786 6.8893C11.5786 6.31805 12.0493 5.85496 12.6299 5.85496C13.2106 5.85496 13.6812 6.31805 13.6812 6.8893Z"
                          fill="#16191E"/>
                </svg>
                </div>
                <div class="checkout__top">
                    <div class="checkout__close" id="closeFirstPopup">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M0.292893 0.292893C0.683417 -0.0976311 1.31658 -0.0976311 1.70711 0.292893L15.1421 13.7279C15.5327 14.1184 15.5327 14.7516 15.1421 15.1421C14.7516 15.5327 14.1184 15.5327 13.7279 15.1421L0.292893 1.70711C-0.0976311 1.31658 -0.097631 0.683417 0.292893 0.292893Z"
                                  fill="#16191E"/>
                            <path d="M15.1421 0.292907C15.5327 0.683431 15.5327 1.3166 15.1421 1.70712L1.70711 15.1421C1.31658 15.5327 0.683419 15.5327 0.292894 15.1421C-0.09763 14.7516 -0.09763 14.1185 0.292894 13.7279L13.7279 0.292907C14.1184 -0.0976172 14.7516 -0.0976171 15.1421 0.292907Z"
                                  fill="#16191E"/>
                        </svg>
                    </div>
                </div>
            </div>
            <div class="checkout__box">
                <svg width="96" height="29" viewBox="0 0 96 29" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12.665 19.14C12.2544 19.14 11.8437 19.1073 11.433 19.042C11.0224 18.986 10.635 18.8973 10.271 18.776C9.90704 18.6453 9.58037 18.4867 9.29104 18.3C9.12304 18.188 9.00637 18.0527 8.94104 17.894C8.87571 17.7353 8.85237 17.5813 8.87104 17.432C8.89904 17.2733 8.95971 17.138 9.05304 17.026C9.15571 16.9047 9.28171 16.83 9.43104 16.802C9.58037 16.774 9.74371 16.816 9.92104 16.928C10.3317 17.1707 10.7657 17.348 11.223 17.46C11.6804 17.572 12.161 17.628 12.665 17.628C13.4024 17.628 13.939 17.5067 14.275 17.264C14.611 17.012 14.779 16.69 14.779 16.298C14.779 15.9713 14.6577 15.7147 14.415 15.528C14.1817 15.3413 13.7757 15.1873 13.197 15.066L11.657 14.744C10.7704 14.5573 10.1077 14.2447 9.66904 13.806C9.23971 13.358 9.02504 12.77 9.02504 12.042C9.02504 11.5847 9.11837 11.1693 9.30504 10.796C9.49171 10.4227 9.75304 10.1007 10.089 9.83C10.4344 9.55933 10.8404 9.354 11.307 9.214C11.783 9.06467 12.3057 8.99 12.875 8.99C13.435 8.99 13.967 9.06 14.471 9.2C14.975 9.34 15.4277 9.54533 15.829 9.816C15.9784 9.91867 16.0764 10.0447 16.123 10.194C16.179 10.3433 16.193 10.4927 16.165 10.642C16.137 10.782 16.0717 10.9033 15.969 11.006C15.8664 11.1087 15.7357 11.1693 15.577 11.188C15.4277 11.2067 15.255 11.16 15.059 11.048C14.7137 10.852 14.3637 10.712 14.009 10.628C13.6544 10.544 13.2717 10.502 12.861 10.502C12.4317 10.502 12.063 10.5627 11.755 10.684C11.447 10.8053 11.209 10.978 11.041 11.202C10.8824 11.4167 10.803 11.6733 10.803 11.972C10.803 12.308 10.915 12.5787 11.139 12.784C11.363 12.98 11.7457 13.134 12.287 13.246L13.813 13.568C14.737 13.764 15.423 14.072 15.871 14.492C16.3284 14.912 16.557 15.472 16.557 16.172C16.557 16.62 16.4637 17.026 16.277 17.39C16.0997 17.754 15.8384 18.0667 15.493 18.328C15.157 18.5893 14.751 18.79 14.275 18.93C13.799 19.07 13.2624 19.14 12.665 19.14ZM18.3751 21.632C18.0951 21.632 17.8804 21.5573 17.7311 21.408C17.5817 21.2587 17.5071 21.0393 17.5071 20.75V12.924C17.5071 12.6347 17.5817 12.4153 17.7311 12.266C17.8804 12.1167 18.0904 12.042 18.3611 12.042C18.6411 12.042 18.8557 12.1167 19.0051 12.266C19.1544 12.4153 19.2291 12.6347 19.2291 12.924V14.016L19.0751 13.596C19.2151 13.12 19.4997 12.7373 19.9291 12.448C20.3677 12.1587 20.8764 12.014 21.4551 12.014C22.0524 12.014 22.5751 12.1587 23.0231 12.448C23.4804 12.7373 23.8351 13.148 24.0871 13.68C24.3391 14.2027 24.4651 14.8327 24.4651 15.57C24.4651 16.298 24.3391 16.9327 24.0871 17.474C23.8351 18.006 23.4851 18.4167 23.0371 18.706C22.5891 18.9953 22.0617 19.14 21.4551 19.14C20.8857 19.14 20.3864 19 19.9571 18.72C19.5277 18.4307 19.2384 18.0573 19.0891 17.6H19.2571V20.75C19.2571 21.0393 19.1777 21.2587 19.0191 21.408C18.8697 21.5573 18.6551 21.632 18.3751 21.632ZM20.9651 17.81C21.3104 17.81 21.6137 17.726 21.8751 17.558C22.1364 17.39 22.3371 17.1427 22.4771 16.816C22.6264 16.48 22.7011 16.0647 22.7011 15.57C22.7011 14.8233 22.5424 14.268 22.2251 13.904C21.9077 13.5307 21.4877 13.344 20.9651 13.344C20.6197 13.344 20.3164 13.428 20.0551 13.596C19.7937 13.7547 19.5884 14.002 19.4391 14.338C19.2991 14.6647 19.2291 15.0753 19.2291 15.57C19.2291 16.3073 19.3877 16.8673 19.7051 17.25C20.0224 17.6233 20.4424 17.81 20.9651 17.81ZM28.8556 19.14C28.0809 19.14 27.4136 18.9953 26.8536 18.706C26.2936 18.4167 25.8596 18.006 25.5516 17.474C25.2529 16.942 25.1036 16.312 25.1036 15.584C25.1036 14.8747 25.2483 14.254 25.5376 13.722C25.8363 13.19 26.2423 12.7747 26.7556 12.476C27.2783 12.168 27.8709 12.014 28.5336 12.014C29.0189 12.014 29.4529 12.0933 29.8356 12.252C30.2276 12.4107 30.5589 12.6393 30.8296 12.938C31.1096 13.2367 31.3196 13.6007 31.4596 14.03C31.6089 14.45 31.6836 14.926 31.6836 15.458C31.6836 15.626 31.6229 15.7567 31.5016 15.85C31.3896 15.934 31.2263 15.976 31.0116 15.976H26.5176V14.968H30.4516L30.2276 15.178C30.2276 14.7487 30.1623 14.3893 30.0316 14.1C29.9103 13.8107 29.7283 13.5913 29.4856 13.442C29.2523 13.2833 28.9583 13.204 28.6036 13.204C28.2116 13.204 27.8756 13.2973 27.5956 13.484C27.3249 13.6613 27.1149 13.918 26.9656 14.254C26.8256 14.5807 26.7556 14.9727 26.7556 15.43V15.528C26.7556 16.2933 26.9329 16.8673 27.2876 17.25C27.6516 17.6233 28.1836 17.81 28.8836 17.81C29.1263 17.81 29.3969 17.782 29.6956 17.726C30.0036 17.6607 30.2929 17.5533 30.5636 17.404C30.7596 17.292 30.9323 17.2453 31.0816 17.264C31.2309 17.2733 31.3476 17.3247 31.4316 17.418C31.5249 17.5113 31.5809 17.628 31.5996 17.768C31.6183 17.8987 31.5903 18.034 31.5156 18.174C31.4503 18.314 31.3336 18.4353 31.1656 18.538C30.8389 18.7433 30.4609 18.8973 30.0316 19C29.6116 19.0933 29.2196 19.14 28.8556 19.14ZM35.8135 19.14C35.0948 19.14 34.4695 18.9953 33.9375 18.706C33.4055 18.4073 32.9948 17.9873 32.7055 17.446C32.4162 16.9047 32.2715 16.27 32.2715 15.542C32.2715 14.9913 32.3508 14.5013 32.5095 14.072C32.6775 13.6333 32.9155 13.2647 33.2235 12.966C33.5315 12.658 33.9048 12.4247 34.3435 12.266C34.7822 12.098 35.2722 12.014 35.8135 12.014C36.1215 12.014 36.4528 12.056 36.8075 12.14C37.1715 12.224 37.5122 12.364 37.8295 12.56C37.9788 12.6533 38.0768 12.7653 38.1235 12.896C38.1702 13.0267 38.1795 13.162 38.1515 13.302C38.1235 13.4327 38.0628 13.5493 37.9695 13.652C37.8855 13.7453 37.7782 13.806 37.6475 13.834C37.5168 13.8527 37.3722 13.82 37.2135 13.736C37.0082 13.6147 36.7982 13.526 36.5835 13.47C36.3688 13.4047 36.1635 13.372 35.9675 13.372C35.6595 13.372 35.3888 13.4233 35.1555 13.526C34.9222 13.6193 34.7215 13.7593 34.5535 13.946C34.3948 14.1233 34.2735 14.3473 34.1895 14.618C34.1055 14.8887 34.0635 15.2013 34.0635 15.556C34.0635 16.2467 34.2268 16.7927 34.5535 17.194C34.8895 17.586 35.3608 17.782 35.9675 17.782C36.1635 17.782 36.3642 17.754 36.5695 17.698C36.7842 17.642 36.9988 17.5533 37.2135 17.432C37.3722 17.348 37.5122 17.32 37.6335 17.348C37.7642 17.376 37.8715 17.4413 37.9555 17.544C38.0395 17.6373 38.0908 17.754 38.1095 17.894C38.1282 18.0247 38.1095 18.1553 38.0535 18.286C38.0068 18.4167 37.9135 18.524 37.7735 18.608C37.4655 18.7947 37.1388 18.93 36.7935 19.014C36.4482 19.098 36.1215 19.14 35.8135 19.14ZM39.701 19.098C39.421 19.098 39.2063 19.014 39.057 18.846C38.9077 18.678 38.833 18.4447 38.833 18.146V13.008C38.833 12.7 38.9077 12.4667 39.057 12.308C39.2063 12.14 39.421 12.056 39.701 12.056C39.981 12.056 40.1957 12.14 40.345 12.308C40.5037 12.4667 40.583 12.7 40.583 13.008V18.146C40.583 18.4447 40.5083 18.678 40.359 18.846C40.2097 19.014 39.9903 19.098 39.701 19.098ZM39.701 10.754C39.3743 10.754 39.1177 10.6747 38.931 10.516C38.7537 10.348 38.665 10.1193 38.665 9.83C38.665 9.53133 38.7537 9.30267 38.931 9.144C39.1177 8.98533 39.3743 8.906 39.701 8.906C40.037 8.906 40.2937 8.98533 40.471 9.144C40.6483 9.30267 40.737 9.53133 40.737 9.83C40.737 10.1193 40.6483 10.348 40.471 10.516C40.2937 10.6747 40.037 10.754 39.701 10.754ZM44.2354 19.14C43.7407 19.14 43.2974 19.0467 42.9054 18.86C42.5227 18.664 42.2194 18.4027 41.9954 18.076C41.7807 17.7493 41.6734 17.3807 41.6734 16.97C41.6734 16.466 41.804 16.0693 42.0654 15.78C42.3267 15.4813 42.7514 15.2667 43.3394 15.136C43.9274 15.0053 44.716 14.94 45.7054 14.94H46.4054V15.948H45.7194C45.1407 15.948 44.6787 15.976 44.3334 16.032C43.988 16.088 43.7407 16.186 43.5914 16.326C43.4514 16.4567 43.3814 16.6433 43.3814 16.886C43.3814 17.194 43.4887 17.446 43.7034 17.642C43.918 17.838 44.2167 17.936 44.5994 17.936C44.9074 17.936 45.178 17.866 45.4114 17.726C45.654 17.5767 45.8454 17.376 45.9854 17.124C46.1254 16.872 46.1954 16.5827 46.1954 16.256V14.646C46.1954 14.1793 46.0927 13.8433 45.8874 13.638C45.682 13.4327 45.3367 13.33 44.8514 13.33C44.5807 13.33 44.2867 13.3627 43.9694 13.428C43.6614 13.4933 43.3347 13.6053 42.9894 13.764C42.812 13.848 42.6534 13.8713 42.5134 13.834C42.3827 13.7967 42.28 13.722 42.2054 13.61C42.1307 13.4887 42.0934 13.358 42.0934 13.218C42.0934 13.078 42.1307 12.9427 42.2054 12.812C42.28 12.672 42.406 12.5693 42.5834 12.504C43.0127 12.3267 43.4234 12.2007 43.8154 12.126C44.2167 12.0513 44.5807 12.014 44.9074 12.014C45.5794 12.014 46.13 12.1167 46.5594 12.322C46.998 12.5273 47.3247 12.84 47.5394 13.26C47.754 13.6707 47.8614 14.2027 47.8614 14.856V18.216C47.8614 18.5053 47.7914 18.7293 47.6514 18.888C47.5114 19.0373 47.3107 19.112 47.0494 19.112C46.788 19.112 46.5827 19.0373 46.4334 18.888C46.2934 18.7293 46.2234 18.5053 46.2234 18.216V17.544H46.3354C46.27 17.8707 46.1394 18.1553 45.9434 18.398C45.7567 18.6313 45.5187 18.8133 45.2294 18.944C44.94 19.0747 44.6087 19.14 44.2354 19.14ZM51.5556 19.14C50.781 19.14 50.1976 18.9207 49.8056 18.482C49.4136 18.034 49.2176 17.3807 49.2176 16.522V9.9C49.2176 9.61067 49.2923 9.39133 49.4416 9.242C49.591 9.09267 49.8056 9.018 50.0856 9.018C50.3656 9.018 50.5803 9.09267 50.7296 9.242C50.8883 9.39133 50.9676 9.61067 50.9676 9.9V16.438C50.9676 16.8673 51.0563 17.1847 51.2336 17.39C51.4203 17.5953 51.6816 17.698 52.0176 17.698C52.0923 17.698 52.1623 17.698 52.2276 17.698C52.293 17.6887 52.3583 17.6793 52.4236 17.67C52.5543 17.6513 52.643 17.6887 52.6896 17.782C52.7363 17.866 52.7596 18.0433 52.7596 18.314C52.7596 18.5473 52.713 18.7293 52.6196 18.86C52.5263 18.9907 52.3723 19.07 52.1576 19.098C52.0643 19.1073 51.9663 19.1167 51.8636 19.126C51.761 19.1353 51.6583 19.14 51.5556 19.14ZM61.2411 19.14C60.2891 19.14 59.4538 18.93 58.7351 18.51C58.0258 18.09 57.4751 17.502 57.0831 16.746C56.6911 15.9807 56.4951 15.0847 56.4951 14.058C56.4951 13.2833 56.6071 12.588 56.8311 11.972C57.0551 11.3467 57.3724 10.8147 57.7831 10.376C58.2031 9.928 58.7024 9.58733 59.2811 9.354C59.8691 9.11133 60.5224 8.99 61.2411 8.99C62.2024 8.99 63.0378 9.2 63.7471 9.62C64.4564 10.0307 65.0071 10.614 65.3991 11.37C65.7911 12.126 65.9871 13.0173 65.9871 14.044C65.9871 14.8187 65.8751 15.5187 65.6511 16.144C65.4271 16.7693 65.1051 17.306 64.6851 17.754C64.2744 18.202 63.7751 18.5473 63.1871 18.79C62.6084 19.0233 61.9598 19.14 61.2411 19.14ZM61.2411 17.6C61.8478 17.6 62.3611 17.46 62.7811 17.18C63.2104 16.9 63.5371 16.494 63.7611 15.962C63.9944 15.43 64.1111 14.7953 64.1111 14.058C64.1111 12.938 63.8591 12.07 63.3551 11.454C62.8604 10.838 62.1558 10.53 61.2411 10.53C60.6438 10.53 60.1304 10.67 59.7011 10.95C59.2718 11.2207 58.9404 11.622 58.7071 12.154C58.4831 12.6767 58.3711 13.3113 58.3711 14.058C58.3711 15.1687 58.6231 16.0367 59.1271 16.662C59.6311 17.2873 60.3358 17.6 61.2411 17.6ZM68.5653 19.112C68.2853 19.112 68.066 19.0373 67.9073 18.888C67.758 18.7293 67.6833 18.5053 67.6833 18.216V13.47H67.0393C66.8153 13.47 66.6426 13.414 66.5213 13.302C66.4 13.1807 66.3393 13.0173 66.3393 12.812C66.3393 12.5973 66.4 12.434 66.5213 12.322C66.6426 12.21 66.8153 12.154 67.0393 12.154H68.1873L67.6833 12.616V12.14C67.6833 11.1787 67.926 10.46 68.4113 9.984C68.8966 9.49867 69.5966 9.214 70.5113 9.13L70.9873 9.088C71.174 9.06933 71.3233 9.102 71.4353 9.186C71.5473 9.26067 71.622 9.36333 71.6593 9.494C71.6966 9.61533 71.7013 9.74133 71.6733 9.872C71.6453 10.0027 71.5846 10.1193 71.4913 10.222C71.4073 10.3153 71.2953 10.3667 71.1553 10.376L70.9593 10.39C70.4273 10.4273 70.04 10.5627 69.7973 10.796C69.5546 11.0293 69.4333 11.384 69.4333 11.86V12.378L69.2093 12.154H70.6233C70.8473 12.154 71.02 12.21 71.1413 12.322C71.2626 12.434 71.3233 12.5973 71.3233 12.812C71.3233 13.0173 71.2626 13.1807 71.1413 13.302C71.02 13.414 70.8473 13.47 70.6233 13.47H69.4333V18.216C69.4333 18.8133 69.144 19.112 68.5653 19.112ZM72.9168 19.112C72.6368 19.112 72.4174 19.0373 72.2588 18.888C72.1094 18.7293 72.0348 18.5053 72.0348 18.216V13.47H71.3908C71.1668 13.47 70.9941 13.414 70.8728 13.302C70.7514 13.1807 70.6908 13.0173 70.6908 12.812C70.6908 12.5973 70.7514 12.434 70.8728 12.322C70.9941 12.21 71.1668 12.154 71.3908 12.154H72.5388L72.0348 12.616V12.14C72.0348 11.1787 72.2774 10.46 72.7628 9.984C73.2481 9.49867 73.9481 9.214 74.8628 9.13L75.3388 9.088C75.5254 9.06933 75.6748 9.102 75.7868 9.186C75.8988 9.26067 75.9734 9.36333 76.0108 9.494C76.0481 9.61533 76.0528 9.74133 76.0248 9.872C75.9968 10.0027 75.9361 10.1193 75.8428 10.222C75.7588 10.3153 75.6468 10.3667 75.5068 10.376L75.3108 10.39C74.7788 10.4273 74.3914 10.5627 74.1488 10.796C73.9061 11.0293 73.7848 11.384 73.7848 11.86V12.378L73.5608 12.154H74.9748C75.1988 12.154 75.3714 12.21 75.4928 12.322C75.6141 12.434 75.6748 12.5973 75.6748 12.812C75.6748 13.0173 75.6141 13.1807 75.4928 13.302C75.3714 13.414 75.1988 13.47 74.9748 13.47H73.7848V18.216C73.7848 18.8133 73.4954 19.112 72.9168 19.112ZM79.4486 19.14C78.674 19.14 78.0066 18.9953 77.4466 18.706C76.8866 18.4167 76.4526 18.006 76.1446 17.474C75.846 16.942 75.6966 16.312 75.6966 15.584C75.6966 14.8747 75.8413 14.254 76.1306 13.722C76.4293 13.19 76.8353 12.7747 77.3486 12.476C77.8713 12.168 78.464 12.014 79.1266 12.014C79.612 12.014 80.046 12.0933 80.4286 12.252C80.8206 12.4107 81.152 12.6393 81.4226 12.938C81.7026 13.2367 81.9126 13.6007 82.0526 14.03C82.202 14.45 82.2766 14.926 82.2766 15.458C82.2766 15.626 82.216 15.7567 82.0946 15.85C81.9826 15.934 81.8193 15.976 81.6046 15.976H77.1106V14.968H81.0446L80.8206 15.178C80.8206 14.7487 80.7553 14.3893 80.6246 14.1C80.5033 13.8107 80.3213 13.5913 80.0786 13.442C79.8453 13.2833 79.5513 13.204 79.1966 13.204C78.8046 13.204 78.4686 13.2973 78.1886 13.484C77.918 13.6613 77.708 13.918 77.5586 14.254C77.4186 14.5807 77.3486 14.9727 77.3486 15.43V15.528C77.3486 16.2933 77.526 16.8673 77.8806 17.25C78.2446 17.6233 78.7766 17.81 79.4766 17.81C79.7193 17.81 79.99 17.782 80.2886 17.726C80.5966 17.6607 80.886 17.5533 81.1566 17.404C81.3526 17.292 81.5253 17.2453 81.6746 17.264C81.824 17.2733 81.9406 17.3247 82.0246 17.418C82.118 17.5113 82.174 17.628 82.1926 17.768C82.2113 17.8987 82.1833 18.034 82.1086 18.174C82.0433 18.314 81.9266 18.4353 81.7586 18.538C81.432 18.7433 81.054 18.8973 80.6246 19C80.2046 19.0933 79.8126 19.14 79.4486 19.14ZM84.1385 19.112C83.8492 19.112 83.6252 19.0373 83.4665 18.888C83.3172 18.7293 83.2425 18.5053 83.2425 18.216V12.924C83.2425 12.6347 83.3172 12.4153 83.4665 12.266C83.6159 12.1167 83.8259 12.042 84.0965 12.042C84.3672 12.042 84.5772 12.1167 84.7265 12.266C84.8759 12.4153 84.9505 12.6347 84.9505 12.924V13.806H84.8105C84.9412 13.246 85.1979 12.8213 85.5805 12.532C85.9632 12.2427 86.4719 12.07 87.1065 12.014C87.3025 11.9953 87.4565 12.0467 87.5685 12.168C87.6899 12.28 87.7599 12.4573 87.7785 12.7C87.7972 12.9333 87.7412 13.1247 87.6105 13.274C87.4892 13.414 87.3025 13.498 87.0505 13.526L86.7425 13.554C86.1732 13.61 85.7439 13.7873 85.4545 14.086C85.1652 14.3753 85.0205 14.786 85.0205 15.318V18.216C85.0205 18.5053 84.9459 18.7293 84.7965 18.888C84.6472 19.0373 84.4279 19.112 84.1385 19.112Z"
                          fill="#55AB68"/>
                    <rect x="0.5" y="0.5" width="95" height="28" rx="8.5" stroke="#55AB68"/>
                </svg>
            </div>
            <h4 class="checkout__title">Start your 7-day trial</h4>
            <p class="checkout__text">
                No pressure. Cancel anytime
            </p>
            <div class="checkout__line">
                <p class="checkout__line-total">
                    Total today:
                </p>
                <p class="checkout__line-total-value" id="popup-total-price"></p>
            </div>
            <hr class="checkout__hr">
            <div class="checkout__line">
                <b class="checkout__line-applied">
                    Code PawChamp Plan applied
                </b>
                <div class="checkout__line-timer-box">
                    <p class="checkout__line-timer-text">
                        Reserved for
                    </p>
                    <p class="checkout__line-timer" id="checkoutTimer"></p>
                </div>
            </div>
            <div class="checkout__line checkout__line-cost">
                <b class="checkout__line-cost-title">
                    You cost per month after trial
                </b>
                <div class="checkout__line-cost-box">
                    <p class="checkout__line-cost-old">{{ userCurrencySign() }}40</p>
                    <p class="checkout__line-cost-total">{{ userCurrencySign() }}24.99</p>
                </div>
            </div>
            <p class="checkout__line-save">
                Save {{ userCurrencySign() }}15 every period
            </p>
            <hr class="checkout__hr">
            <div class="checkout__charged">
                You will be charged only <span>{{ userCurrencySign() }}24.99 for your 7-day trial</span>.
                <br>
                <span>
                Cancel anytime.
            </span>
            </div>
            <div class="checkout__btns-wrapp">
                <div class="checkout__btns">
                    <div class="checkout__card" id="cardBtn">
                        <svg width="28" height="21" viewBox="0 0 28 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M2.43658 0.561604C2.28616 0.590998 1.96486 0.714372 1.72255 0.835756C0.889945 1.25295 0.220883 2.15603 0.0610016 3.07861C-0.0203339 3.54771 -0.0203339 17.456 0.0610016 17.9251C0.272791 19.1473 1.34071 20.2446 2.51862 20.4503C2.76979 20.4942 6.20563 20.5076 14.2239 20.4961L25.5737 20.4798L25.9358 20.3332C26.4617 20.1203 26.7705 19.9162 27.1109 19.5567C27.4553 19.193 27.6384 18.8907 27.8393 18.3546L27.9804 17.9782L27.9963 10.7293C28.0074 5.66304 27.9941 3.36908 27.9522 3.11079C27.7529 1.88233 26.7016 0.776969 25.5226 0.55626C25.1039 0.477857 2.83888 0.483031 2.43658 0.561604ZM25.3486 2.38396C25.649 2.47624 26.1021 2.94722 26.1909 3.25952C26.2321 3.40444 26.2574 3.87514 26.2574 4.4961V5.49868H14.0051H1.75286V4.4961C1.75286 3.87514 1.77818 3.40444 1.81937 3.25952C1.90169 2.9699 2.35956 2.47709 2.62802 2.38913C2.91753 2.2943 25.0402 2.28924 25.3486 2.38396ZM26.2574 12.4141C26.2574 16.2491 26.2409 17.5681 26.1909 17.7442C26.1021 18.0565 25.649 18.5275 25.3486 18.6198C25.0212 18.7203 2.98902 18.7203 2.66171 18.6198C2.36126 18.5275 1.90814 18.0565 1.81937 17.7442C1.76932 17.5681 1.75286 16.2491 1.75286 12.4141V7.31802H14.0051H26.2574V12.4141ZM4.89524 13.3085C4.67207 13.4127 4.44529 13.7022 4.40285 13.9372C4.38387 14.0424 4.37676 14.4108 4.38704 14.7559C4.40722 15.4341 4.45405 15.5595 4.78229 15.8142C4.91783 15.9194 5.01109 15.9315 5.69109 15.9315C6.51839 15.9315 6.60273 15.9033 6.8637 15.54C6.96489 15.3991 6.97648 15.3022 6.97648 14.5954C6.97648 13.8886 6.96489 13.7916 6.8637 13.6508C6.61006 13.2976 6.50871 13.2606 5.75569 13.245C5.26078 13.2349 5.01426 13.2531 4.89524 13.3085Z"
                                  fill="white"/>
                        </svg>
                        Credit / Debit Card
                    </div>
                    <div class="checkout__card-content" id="cardContent">
                        <span class="checkout__error-message"></span>
                        <div id="solid-payment-form-container"></div>
                        <div class="payment-popup__loader-box" id="paymentPopupLoader">
                            <div class="payment-popup__loader">
                                <div class="preload"></div>
                            </div>
                        </div>
                    </div>
                    <div class="checkout__btns-seperator">
                        <svg width="76" height="1" viewBox="0 0 76 1" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <line x1="-4.37114e-08" y1="0.5" x2="76" y2="0.499993" stroke="#B9B9B9"/>
                        </svg>
                        <p>or</p>
                        <svg width="76" height="1" viewBox="0 0 76 1" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <line x1="-4.37114e-08" y1="0.5" x2="76" y2="0.499993" stroke="#B9B9B9"/>
                        </svg>
                    </div>
                    <div class="checkout__paypal-error-wrapper">
                        <div id="paypal-button" class="checkout__paypal"></div>
                        <div class="checkout__paypal-error-loader-box" id="paypal-loader">
                            <div class="checkout__paypal-error-loader">
                                <div class="preload"></div>
                            </div>
                        </div>
                        <span class="email-popup__paypal-error" id="paypal-error-message"></span>
                    </div>
                </div>
            </div>
            <p class="checkout__text-bottom">
                By continuing you agree that if you don’t cancel prior the end of the 7-day trial you will automatically be charged {{ userCurrencySign() }}24.99 every month until you cancel. Learn more about cancellation and
                refund policy in
                <a class="checkout__text-bottom-link" href="{{ path('static_page_terms') }}">Subscription terms</a>
            </p>
        </div>
    </div>
    <div class="checkout__knows">
        <a class="checkout__knows-close" href="{{ path('subscription_product_landing_3', {'externalId': user.externalId}) }}">
            <div class="checkout__close closePopUp">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M0.292893 0.292893C0.683417 -0.0976311 1.31658 -0.0976311 1.70711 0.292893L15.1421 13.7279C15.5327 14.1184 15.5327 14.7516 15.1421 15.1421C14.7516 15.5327 14.1184 15.5327 13.7279 15.1421L0.292893 1.70711C-0.0976311 1.31658 -0.097631 0.683417 0.292893 0.292893Z"
                          fill="#16191E"/>
                    <path d="M15.1421 0.292907C15.5327 0.683431 15.5327 1.3166 15.1421 1.70712L1.70711 15.1421C1.31658 15.5327 0.683419 15.5327 0.292894 15.1421C-0.09763 14.7516 -0.09763 14.1185 0.292894 13.7279L13.7279 0.292907C14.1184 -0.0976172 14.7516 -0.0976171 15.1421 0.292907Z"
                          fill="#16191E"/>
                </svg>
            </div>
        </a>
        <h3 class="payment-popup__content-second-title">
            Did you know?
        </h3>
        <p class="payment-popup__content-second-subtitle">
            <span>65%</span> of users started their <b>dog’s with PawChamp Plan</b> advanced in their goals within the <b>first month*</b>
        </p>
        <div class="payment-popup__content-second-graph-box">
            <img class="payment-popup__content-second-graph" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1647957591/dog-training/img/graphic.png" alt="">
        </div>
        <p class="payment-popup__content-second-subtitle">
            *based on the information of users who have shared their progress
        </p>
        <a href="{{ path('subscription_product_landing_3', {'externalId': user.externalId}) }}">
            <p class="payment-popup__content-second-btn closePopUp">
                <span></span>
                Got it
            </p>
        </a>
    </div>
</aside>


{% block additionalScripts %}
    {% include 'subscription/components/solid-payment-form-data.html.twig' %}

    <script>
        window.genMerchantData = '{{ path('subscription_payment_api_generate_merchant_data') }}';
        window.getPaypalTokenUrl = '{{ path('subscription_payment_api_get_paypal_script') }}';
        window.paymentErrorMessageBlockSelector = '.checkout__error-message';
    </script>

{% endblock %}
