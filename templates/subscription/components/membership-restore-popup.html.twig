{% if subscription is defined and subscription is not null and subscription.subscriptionPeriod is not null %}
    {% set price = (subscription.subscriptionCurrency ~ ' ' ~ (subscription.subscriptionPrice / 100))|default('') %}
    {% set month = ('membership_info.period_value.' ~ subscription.subscriptionPeriod.value)|trans %}
    {% set description = 'membership_info.restore_description_text'|trans({
        '%price%': price,
        '%period%': month,
        '%support_email%': support_email,
        '%termsLink%': path('static_page_terms')
    }) %}

    <button class="membership-info_card_restore-btn" data-subscription-id="{{ subscription.externalId }}">
        {{ ("membership_info.subscription_restore")|trans }}
    </button>

    <div class="membership-info_wrapp">
        <div class="membership-info_wrapp-popup-bg"></div>

        <div class="container membership-info_wrapp-container">
            <div class="membership-info_wrapp-popup">
                <div class="membership-info_wrapp-content">
                    <h1 class="membership-info_wrapp-popup-header restore">{{ 'membership_info.confirm_restore_plan'|trans }}</h1>
                    <p class="membership-info_wrapp-popup-description restore">
                        {{ description | raw }}
                    </p>
                </div>

                <div class="membership-info_wrapp-popup-actions">
                    <button class="membership-info_wrapp-popup-btn reset" data-action="cancel">
                        {{ 'membership_info.no'|trans }}
                    </button>
                    <button class="membership-info_wrapp-popup-btn success" data-action="restore">
                        {{ 'membership_info.yes'|trans }}
                    </button>
                </div>
            </div>
        </div>
    </div>
{% endif %}
