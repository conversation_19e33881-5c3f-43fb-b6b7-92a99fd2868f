{% include 'subscription/components/countdown-options.html.twig' %}

<aside class="sticky-offer__wrapper better" id="getBtnAnchor">
    <div class="container">
        <div class="sticky-offer">
            <div class="sticky-offer__logo">
                <img src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1706198790/paw-champ/logo/logo.svg" alt="">
            </div>
            <div class="sticky-offer__timer-wrapper">
                <div>
                    <p class="sticky-offer__timer" id="countdown2">{{ getFormattedTimeToCountdownFinish() }}</p>
                    <div class="sticky-offer__timer-box">
                        <span class="sticky-offer__timer-text" id="lastChance">minutes</span>
                        <span class="sticky-offer__timer-text">seconds</span>
                    </div>
                </div>
            </div>

            <button class="sticky-offer__btn smooth-scroll" id="stickyBtn">
                <a href="#promocode" class="js-scroll"></a>
                <span></span>
                <p class="landing__offer-btn-text">
                    Get my plan
                </p>
            </button>
        </div>
    </div>
</aside>


<script>
    window.finishCountdownTimestamp = {{ getCountdown() }};
    window.getFormattedTimeToCountdownFinish = '{{ getFormattedTimeToCountdownFinish() }}';
</script>
