<script>
    window.productName = '{{ productName }}';
    window.genMerchantData = '{{ path('payment_generate_merchant_data', {'externalId': 'UUID', 'productName': productName} ) }}';
    window.paymentUpdateOrder = '{{ path('payment_update_order', {'externalId': 'UUID', 'productName': productName}) }}';

    window.getPaypalTokenUrl = '{{ path('payment_generate_paypal_url', {'externalId': 'UUID', 'productName': productName}) }}';
    window.updatePaypalOrderStatusUrl = '{{ path('payment_update_order', {'externalId': 'UUID', 'productName': productName, paymentType: 'paypal'}) }}';

    window.createUserUrl = '{{ path('main_create_user_for_upsell') }}';
    window.paymentErrorMessageBlockSelector = '.email-popup__error-message';
    window.logFbEvents = true;
</script>

<aside class="email-popup">
    <div class="email-popup__inner">
        <div class="email-popup__bg" id="paymentBg"></div>
        <div class="email-popup__content-email">
            <div class="email-popup__close  close-popup" id="close-email-popup">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M0.292893 0.292893C0.683417 -0.0976311 1.31658 -0.0976311 1.70711 0.292893L15.1421 13.7279C15.5327 14.1184 15.5327 14.7516 15.1421 15.1421C14.7516 15.5327 14.1184 15.5327 13.7279 15.1421L0.292893 1.70711C-0.0976311 1.31658 -0.097631 0.683417 0.292893 0.292893Z"
                          fill="white"/>
                    <path d="M15.1421 0.292907C15.5327 0.683431 15.5327 1.3166 15.1421 1.70712L1.70711 15.1421C1.31658 15.5327 0.683419 15.5327 0.292894 15.1421C-0.09763 14.7516 -0.09763 14.1185 0.292894 13.7279L13.7279 0.292907C14.1184 -0.0976172 14.7516 -0.0976171 15.1421 0.292907Z"
                          fill="white"/>
                </svg>
            </div>
            <div class="email-popup__content-email-wrapp">
                <h2 class="email-popup__content-email-title">
                    Enter your e-mail address to complete the purchase
                </h2>
                <div class="email-popup__content-email-box">
                    <div class="email-popup__email-error-wrapp">
                        <label class="email-popup__content-email-label" id="emailText" for="email">
                            Email
                        </label>
                        <span class="email-popup__email-error">Unsuccessful user create</span>
                    </div>
                    <ul id="autoComplete" class="email-autocomplete email-autocomplete-scroll"></ul>
                    <input class="email-popup__content-email-input" name="text" type="text" placeholder="<EMAIL>" id="email" autocomplete="off">
                </div>
                <div class="email-popup__content-email-loader-box">
                    <button class="email-popup__content-email-btn" id="email-submit" data-landing-url="better-dog">
                        <span></span>
                        Submit
                    </button>
                    <div class="email-popup__content-email-loader" id="emailLoader">
                        <div class="preload"></div>
                    </div>
                </div>
                <div class="email-popup__content-email-text-box">
                    <svg class="email-popup__content-email-text-icon" width="21" height="26" viewBox="0 0 21 26" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path opacity="0.45"
                              d="M20.1944 9.75H18.5278V7.58332C18.5278 3.40184 15.0387 0 10.75 0C6.46125 0 2.97224 3.40184 2.97224 7.58332V9.75H1.30557C0.99849 9.75 0.75 9.99228 0.75 10.2917V23.8334C0.75 25.0283 1.74661 26 2.97224 26H18.5278C19.7534 26 20.75 25.0283 20.75 23.8333V10.2917C20.75 9.99228 20.5015 9.75 20.1944 9.75ZM12.4134 21.0652C12.4308 21.2181 12.3804 21.3715 12.2751 21.4863C12.1698 21.6011 12.019 21.6667 11.8611 21.6667H9.63891C9.48104 21.6667 9.33021 21.6011 9.22495 21.4863C9.11969 21.3715 9.06922 21.2181 9.08661 21.0652L9.43708 17.9925C8.86797 17.5888 8.52781 16.9504 8.52781 16.25C8.52781 15.0551 9.52443 14.0833 10.7501 14.0833C11.9757 14.0833 12.9723 15.055 12.9723 16.25C12.9723 16.9504 12.6321 17.5888 12.063 17.9925L12.4134 21.0652ZM15.1944 9.75H6.30557V7.58332C6.30557 5.19396 8.29938 3.25 10.75 3.25C13.2006 3.25 15.1944 5.19396 15.1944 7.58332V9.75Z"
                              fill="#16191E"/>
                    </svg>
                    <p class="email-popup__content-email-text">
                        We protect your privacy and are committed to protecting your personal data. We’ll email you a copy of your results for convenient access.
                    </p>
                </div>

            </div>
        </div>
        <div class="email-popup__content-payment">
            <div class="email-popup__close" id="close-payment-popup">
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M0.292893 0.292893C0.683417 -0.0976311 1.31658 -0.0976311 1.70711 0.292893L15.1421 13.7279C15.5327 14.1184 15.5327 14.7516 15.1421 15.1421C14.7516 15.5327 14.1184 15.5327 13.7279 15.1421L0.292893 1.70711C-0.0976311 1.31658 -0.097631 0.683417 0.292893 0.292893Z"
                          fill="white"/>
                    <path d="M15.1421 0.292907C15.5327 0.683431 15.5327 1.3166 15.1421 1.70712L1.70711 15.1421C1.31658 15.5327 0.683419 15.5327 0.292894 15.1421C-0.09763 14.7516 -0.09763 14.1185 0.292894 13.7279L13.7279 0.292907C14.1184 -0.0976172 14.7516 -0.0976171 15.1421 0.292907Z"
                          fill="white"/>
                </svg>
            </div>
            <div class="email-popup__content-wrapper">
                <h3 class="email-popup__content-payment-title">
                    Select payment method
                </h3>
                <ul class="email-popup__content-payment-select">
                    <li class="select-active email-popup__content-payment-paypal email-popup__content-payment-select-item" data-payment-method="paypal">
                        <img src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1633944292/dog-training/logo/paypal.svg" alt="">
                    </li>
                    <li class="email-popup__content-payment-debit email-popup__content-payment-select-item" data-payment-method="debit">
                        <p class="email-popup__content-payment-debit-title">
                            Credit Card
                        </p>
                        <div class="email-popup__content-payment-debit-logos">
                            <img src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1633427297/dog-training/logo/visa-secure.svg" alt="">
                            <img src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1626782173/dog-training/logo/mastercard.svg" alt="">
                            <img src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1626782173/dog-training/logo/discover.svg" alt="">
                        </div>
                    </li>
                </ul>
                <div class="email-popup__content-payment-inner">
                    <div class="email-popup__content-payment-box">
                        <p class="email-popup__content-payment-text">
                            {{ planName }}
                        </p>
                        <p class="email-popup__content-payment-text" id="popup-full-price">
                            {{ userCurrencySign() }}{{ fullPrice }}
                        </p>
                    </div>
                    <div class="email-popup__content-payment-box">
                        <p class="email-popup__content-payment-text">
                            50% Introductory offer discount
                        </p>
                        <p class="email-popup__content-payment-text" id="popup-discount-price">
                            -{{ userCurrencySign() }}{{ economy }}
                        </p>
                    </div>
                </div>
                <div class="email-popup__content-payment-conclution">
                    <b class="email-popup__content-payment-total">
                        Total
                    </b>
                    <p class="email-popup__content-payment-price" id="popup-total-price">
                        {{ userCurrencySign() }}{{ totalPrice }}
                    </p>
                </div>

                <div class="email-popup__method">
                    <div class="email-popup__paypal form-active" data-payment-method="paypal">
                        <div class="email-popup__paypal-error-wrapper">
                            <div id="paypal-button"></div>
                            <div class="email-popup__paypal-error-loader-box" id="paypal-loader">
                                <div class="email-popup__paypal-error-loader">
                                    <div class="preload"></div>
                                </div>
                            </div>
                        </div>
                        <span class="email-popup__paypal-error" id="paypal-error-message"></span>
                        <p class="email-popup__paypal-text" id="paypal-error-message">
                            THE SAFER, EASIER WAY TO PAY
                        </p>
                    </div>
                    {# Solid iframe container #}
                    <div class="email-popup__solid" data-payment-method="debit">
                        <div class="email-popup__error-box">
                            <p class="email-popup__content-payment-info">
                                Card info
                            </p>
                            <span class="email-popup__error-message"></span>
                        </div>
                        <div id="solid-payment-form-container"></div>
                        <div class="email-popup__loader-box" id="paymentPopupLoader">
                            <div class="email-popup__loader">
                                <div class="preload"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</aside>


