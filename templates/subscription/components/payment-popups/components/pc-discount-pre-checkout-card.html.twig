{# for split pc_disc_dokrut
if split to be closed to control group - delet this file.
if split to be closed to split group = delete comment #}
{% set trainerSupportPrice = '129.99' %}
{% set trainerSupportDiscountPrice = '0' %}

<div class="pc-discount-dokrut">
    <div class="payment-popup__content-zero-hr"></div>

    <div class="pc-discount-dokrut-container">
        <div class="pc-discount-dokrut-top">
            <div class="pc-discount-dokrut-line bottom"></div>
            <h3 class="payment-popup__content-zero-title p-0">
                Based on
                <span class="nameInsertSelector">
                    {% if app.user.lastQuiz.responses.dogName is defined %}
                        {{ app.user.lastQuiz.responses.dogName }}’s
                    {% else %}
                        your dog’s
                    {% endif %}
                </span>
                profile, we added these features to your discount to ensure success in achieving your goal:
            </h3>

            <ul class="payment-popup__content-zero-list mt-8">
                <li class="payment-popup__content-zero-list-item">
                    <p>Personal dog trainer support</p>
                    <div>
                        <span>{{ userCurrencySign() }} {{ trainerSupportPrice }}</span>
                        <b>{{ userCurrencySign() }} {{ trainerSupportDiscountPrice }}</b>
                    </div>
                </li>
                <li class="payment-popup__content-zero-list-item submenu">
                    <p>Special modules:</p>
                    <ul class="list-group">
                        {% for checkoutProduct in checkoutPriceData %}
                            <li class="list-group__item">
                                <p>{{ checkoutProduct.text }}</p>
                                <div>
                                    <span>{{ userCurrencySign() }} {{ checkoutProduct.price }}</span>
                                    <b>{{ userCurrencySign() }}  {{ checkoutProduct.discountPrice }}</b>
                                </div>
                            </li>
                        {% endfor %}
                    </ul>
                </li>
            </ul>
        </div>
    </div>

    <div class="pc-discount-dokrut-bottom">
        <div class="pc-discount-dokrut-line top"></div>
        <div class="landing__promocode-wrapp pc-discount-dokrut-promocode">
            <div class="landing__promocode-name-box">
                <h5 class="landing__promocode-name">
                    <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M1.71436 5.51106L6.12679 10.5538C6.22217 10.6628 6.3982 10.6375 6.45889 10.5059L10.2858 2.21436"
                              stroke="#61C27D" stroke-width="1.5" stroke-linecap="round"
                        />
                    </svg>
                    <span class="promocode-name-selector">promocode</span>
                </h5>
            </div>
            <div>
                <p class="landing__promocode-timer" id="countdown6">{{ getFormattedTimeToCountdownFinish() }}</p>
                <div class="landing__promocode-timer-box">
                    <span class="landing__promocode-timer-text">minutes</span>
                    <span class="landing__promocode-timer-text">seconds</span>
                </div>
            </div>
        </div>
    </div>
</div>
