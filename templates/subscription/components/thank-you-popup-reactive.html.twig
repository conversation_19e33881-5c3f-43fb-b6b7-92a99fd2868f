{% set pc_flowtolms_split = getSplitValue('pc_flowtolms') %}

<aside class="thank-you-popup">
    <div class="thank-you-popup__bg" id="thank-youBg"></div>
    <div class="thank-you-popup__inner">
        <div class="thank-you-popup__content {% if pc_flowtolms_split > 1 %}container{% endif %}">
            <h3 class="thank-you-popup__title">
                {% if pc_flowtolms_split > 1 %}
                    Welcome to PawChamp!
                {% else %}
                    Thank you for your purchase!
                {% endif %}
            </h3>
            <p class="thank-you-popup__subtitle">
                Your account has been successfully created
            </p>
            <div class="thank-you-popup__frame">
                <div class="back-0">
                    <div class="thank-you-popup__container">
                        <picture>
                            <source srcset="https://res.cloudinary.com/dr0cx27xo/image/upload/v1719499935/dog-training/img/reactivity/webp/thank-you-reactivity.webp">
                            <img class="thank-you-popup__frame-img" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1719499932/dog-training/img/reactivity/thank-you-reactivity.jpg" alt="thank you image" id="thankYouPopupImg">
                        </picture>
                    </div>
                </div>
                <div class="back-1"></div>
                <div class="back-2"></div>
                <div class="back-3"></div>
            </div>
            {% if pc_flowtolms_split < 2 %}
                <ul class="thank-you-popup__list">
                    <li class="thank-you-popup__list-item active">
                        <p class="thank-you-popup__list-item-text bold">
                            PawChamp. Overcome Reactivity
                        </p>
                        <img class="thank-you-popup__list-item-img" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1672220859/dog-training/img/thank-you-popup/checked.png" alt="">
                    </li>
                    <li class="thank-you-popup__list-item" id="upsell_games">
                        <p class="thank-you-popup__list-item-text">
                            Doggy Games
                        </p>
                        <img class="thank-you-popup__list-item-img" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1672220859/dog-training/img/thank-you-popup/checked.png" alt="">
                    </li>
                    <li class="thank-you-popup__list-item" id="upsell_home">
                        <p class="thank-you-popup__list-item-text">
                            Home Alone
                        </p>
                        <img class="thank-you-popup__list-item-img" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1672220859/dog-training/img/thank-you-popup/checked.png" alt="">
                    </li>
                    <li class="thank-you-popup__list-item" id="upsell_aggression">
                        <p class="thank-you-popup__list-item-text">
                            Dog Aggression & Triggers
                        </p>
                        <img class="thank-you-popup__list-item-img" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1672220859/dog-training/img/thank-you-popup/checked.png" alt="">
                    </li>
                    <li class="thank-you-popup__list-item" id="upsell_hyperdog">
                        <p class="thank-you-popup__list-item-text">
                            HYPER DOG
                        </p>
                        <img class="thank-you-popup__list-item-img" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1672220859/dog-training/img/thank-you-popup/checked.png" alt="">
                    </li>
                    <li class="thank-you-popup__list-item" id="upsell_barking">
                        <p class="thank-you-popup__list-item-text">
                            Why is your dog Barking?
                        </p>
                        <img class="thank-you-popup__list-item-img" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1672220859/dog-training/img/thank-you-popup/checked.png" alt="">
                    </li>
                    <li class="thank-you-popup__list-item" id="upsell_separation">
                        <p class="thank-you-popup__list-item-text">
                            Separation anxiety in Dogs
                        </p>
                        <img class="thank-you-popup__list-item-img" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1672220859/dog-training/img/thank-you-popup/checked.png" alt="">
                    </li>
                </ul>
            {% endif %}

            {% if pc_flowtolms_split > 1 %}
                <div class="product-onboarding_note">
                    <img src="https://images.paw-champ.com/pc/icons/info-icon-blue.svg"
                         alt="info icon"
                         class="product-onboarding_note_icon"
                    />
                    <p class="product-onboarding_note_text">
                        In case of any questions or troubles, please contact us by sending email to
                        <b><EMAIL></b>.
                    </p>
                </div>
            {% else %}
                <p class="thank-you-popup__text-small">
                    In case of any questions or troubles, please contact us by sending email to
                    <span class="thank-you-popup__text-link">{{ support_email }}</span>
                </p>
            {% endif %}

            {% if pc_flowtolms_split > 1 %}
                <div class="product-onboarding_box-button">
                    <a href="{{ path('login') }}" class="product-onboarding_button" id="thankYouPopupBtn">
                        <span class="product-onboarding_button-text">
                            Start training
                        </span>
                    </a>
                </div>
            {% else %}
                <div class="thank-you-popup__btn-wrapp">
                    <a href="{{ path('login') }}" class="thank-you-popup__btn" id="thankYouPopupBtn">
                        Start learning
                    </a>
                </div>
            {% endif %}
        </div>
    </div>
</aside>
