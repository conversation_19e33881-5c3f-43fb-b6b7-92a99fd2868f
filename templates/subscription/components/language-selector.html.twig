{% set languages = {
    'en': {
        'iconAlt': 'PawChamp English language dropdown icon for dog training app',
        'iconTitle': 'PawChamp English language dropdown icon',
        'label': 'English',
        'iconUrl': 'https://res.cloudinary.com/dr0cx27xo/image/upload/v1722435923/paw-champ/icons/country-flags-round/us-flag--round.png',
    },
    'de': {
        'iconAlt': 'PawChamp Deutsch language dropdown icon for dog training app',
        'iconTitle': 'PawChamp Deutsch language dropdown icon',
        'label': 'Deutsch',
        'iconUrl': 'https://res.cloudinary.com/dr0cx27xo/image/upload/v1722435923/paw-champ/icons/country-flags-round/de-flag--round.png',
    },
    'es': {
        'iconAlt': 'PawChamp Español language dropdown icon for dog training app',
        'iconTitle': 'PawChamp Español language dropdown icon',
        'label': 'Español',
        'iconUrl': 'https://res.cloudinary.com/dr0cx27xo/image/upload/v1722435923/paw-champ/icons/country-flags-round/es-flag--round.png',
    },
    'fr': {
        'iconAlt': 'PawChamp Français language dropdown icon for dog training app',
        'iconTitle': 'PawChamp Français language dropdown icon',
        'label': 'Français',
        'iconUrl': 'https://res.cloudinary.com/dr0cx27xo/image/upload/v1722435923/paw-champ/icons/country-flags-round/fr-flag--round.png',
    },
    'pt': {
        'iconAlt': 'PawChamp Português language dropdown icon for dog training app',
        'iconTitle': 'PawChamp Português language dropdown icon',
        'label': 'Português',
        'iconUrl': 'https://res.cloudinary.com/dr0cx27xo/image/upload/v1722435923/paw-champ/icons/country-flags-round/pt-flag--round.png',
    },
    'it': {
        'iconAlt': 'PawChamp Italiana language dropdown icon for dog training app',
        'iconTitle': 'PawChamp Italiana language dropdown icon',
        'label': 'Italiano',
        'iconUrl': 'https://res.cloudinary.com/dr0cx27xo/image/upload/v1727704019/paw-champ/icons/country-flags-round/it-flag--round.png',
    },
} %}

{% set currentLangKey = app.request.query.get('lang') ?? 'en' %}
{% set currentLang = languages[currentLangKey] ?? languages['en'] %}

{% set languagesListWithoutCurrentLang = languages|filter((value, key) => key != currentLangKey) %}
{% set sortedLanguagesList = {(currentLangKey): currentLang} | merge(languagesListWithoutCurrentLang) %}
{% set attrParams = app.request.attributes.all | filter((v,k) => k|first != '_')  %}

{% if app.request.attributes.get('_route') != null %}
    <div class="language-selector">
        <button class="language-selector__label"
                tabindex="0"
        >
            <img src="{{ currentLang.iconUrl }}"
                 alt="{{ currentLang.iconAlt }}"
                 title="{{ currentLang.iconTitle }}"
                 class="language-selector__label-icon"
            >
        </button>

        <div class="language-selector__dropdown">
            <ul class="language-selector__dropdown-list">
                {% for langKey, lang in sortedLanguagesList %}
                    <li class="language-selector__dropdown-item">


                        <a href="{{ path(app.request.attributes.get('_route'), app.request.query.all | merge({'lang': langKey}) | merge(attrParams)) }}"
                           class="language-selector__dropdown-item-content"
                           tabindex="0"
                        >
                            <span>
                                {{ lang.label }}
                            </span>

                            <img src="{{ lang.iconUrl }}"
                                 alt="{{ lang.iconAlt }}"
                                 title="{{ lang.iconTitle }}"
                                 class="language-selector__dropdown-item-icon"
                            >
                        </a>
                    </li>
                {% endfor %}
            </ul>
        </div>
    </div>
{% endif %}
