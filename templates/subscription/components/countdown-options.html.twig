{% set userCountdownStarted = getCountdown() / 1000 | round %}
{% set currentTimestamp = date() | date('U') %}
{% set currentTimestamp = currentTimestamp | replace({'[^0-9]': ''}) | round %}
{% set secondsRemained = userCountdownStarted - currentTimestamp | round %}
{% set minutes = (secondsRemained / 60) %}
{% set seconds = secondsRemained % 60 %}
{% set roundedMinutes = "%d"|format(minutes) %}
{% set roundedSeconds = "%d"|format(seconds) %}

{% if secondsRemained >= 0 %}
{% set formattedCountdown = "%02d:%02d"|format(roundedMinutes, roundedSeconds) %}
{% else %}
{% set formattedCountdown = "00:00" %}
{% endif %}
