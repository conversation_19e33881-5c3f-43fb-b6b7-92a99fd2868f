{% set OFFER_ITEM_VALUES_MAIN =  {
    "one_month": {
        "type": "1m_lower_intro_full_monthly",
        "full_price": "49.99",
        "total_price": "19.99",
        "discount": "251.99",
        "day_price": "1.67",
        "discount_day_price": "67",
    },
    "three_month": {
        "type": "3m_lower_intro_full_quarterly",
        "full_price": "79.99",
        "total_price": "31.99",
        "discount": "269.99",
        "day_price": "0.89",
        "discount_day_price": "36",
    },
    "six_month": {
        "type": "6m_lower_intro_full_halfyear",
        "full_price": "119.99",
        "total_price": "47.99",
        "discount": "293.99",
        "day_price": "0.67",
        "discount_day_price": "27",
    }
} %}

{% set OFFER_ITEM_VALUES_MAIN_VAT_UK =  {
    "one_month": {
        "type": "1month_LP_UK",
        "full_price": "39.99",
        "total_price": "19.99",
        "discount": "241.99",
        "day_price": "1.11",
        "discount_day_price": "56",
    },
    "three_month": {
        "type": "3month_LP_UK",
        "full_price": "66.65",
        "total_price": "31.99",
        "discount": "255.32",
        "day_price": "0.62",
        "discount_day_price": "30",
    },
    "six_month": {
        "type": "6month_LP_UK",
        "full_price": "95.99",
        "total_price": "47.99",
        "discount": "269.99",
        "day_price": "0.44",
        "discount_day_price": "22",
    }
} %}

{% set isGB = getCountry()|upper == 'GB' %}
{% set offer_values = isGB ? OFFER_ITEM_VALUES_MAIN_VAT_UK : OFFER_ITEM_VALUES_MAIN %}
{% set isEnglishChallengeFunnel = funnelName == 'challenge' and locale == 'en' %}
{% set isVagusFunnel = funnelName == 'vagus' %}
{% set pc_disc_dokrut_love_split = getSplitValue('pc_disc_dokrut_love') %}
{% set isPcDiscountPercent = pc_disc_dokrut_love_split > 1 or isEnglishChallengeFunnel or isVagusFunnel %}

<ul>
    <li class="landing__offer-item active most-popular" data-offer-value="4-week plan">
        <div class="landing__offer-item-inner">
            <div class="landing__offer-item-box">
                <input class="paymentInfo"
                       type="radio"
                       checked
                       id="dog-breed-radio-1"
                       name="dog-breed-radio"
                       data-payment-info='{"type": "{{ offer_values.one_month.type }}", "full_price": "{{ offer_values.one_month.full_price }}", "total_price": "{{ offer_values.one_month.total_price }}", "discount": "{{ offer_values.one_month.discount }}"}'
                >
                <label for="dog-breed-radio-1"></label>
                <h4 class="landing__offer-item-title">
                    4-WEEK PLAN
                </h4>
            </div>
            <div class="landing__offer-item-price-wrapper">
                <p class="landing__offer-item-price-full">
                    <span>{{ userCurrencySign() }}{{ offer_values.one_month.day_price }}</span>
                </p>
                <div class="landing__offer-item-price-box">
                    <div class="landing__offer-item-price">
                        <div class="landing__offer-item-price-sign">
                            <span>{{ userCurrencySign() }}</span>
                            <p>0</p>
                        </div>
                        <div class="landing__offer-item-price-value">
                            <span>{{ offer_values.one_month.discount_day_price }}</span>
                            <p class="landing__offer-item-price-text">per day</p>
                        </div>
                    </div>
                </div>
            </div>

            {% if isPcDiscountPercent %}
                <div class="pcdiscount_plan-percent">-{{ discountPercents }}</div>
            {% endif %}
        </div>
    </li>
    <li class="landing__offer-item" data-offer-value="12-week plan">
        <div class="landing__offer-item-inner">
            <div class="landing__offer-item-box">
                <input class="paymentInfo"
                       type="radio"
                       id="dog-breed-radio-2"
                       name="dog-breed-radio"
                       data-payment-info='{"type": "{{ offer_values.three_month.type }}", "full_price": "{{ offer_values.three_month.full_price }}", "total_price": "{{ offer_values.three_month.total_price }}", "discount": "{{ offer_values.three_month.discount }}"}'
                >
                <label for="dog-breed-radio-2"></label>
                <h4 class="landing__offer-item-title">
                    12-WEEK PLAN
                </h4>
            </div>
            <div class="landing__offer-item-price-wrapper">
                <p class="landing__offer-item-price-full">
                    <span>{{ userCurrencySign() }}{{ offer_values.three_month.day_price }}</span>
                </p>
                <div class="landing__offer-item-price-box">
                    <div class="landing__offer-item-price">
                        <div class="landing__offer-item-price-sign">
                            <span>{{ userCurrencySign() }}</span>
                            <p>0</p>
                        </div>
                        <div class="landing__offer-item-price-value">
                            <span>{{ offer_values.three_month.discount_day_price }}</span>
                            <p class="landing__offer-item-price-text">per day</p>
                        </div>
                    </div>
                </div>
            </div>

            {% if isPcDiscountPercent %}
                <div class="pcdiscount_plan-percent">-{{ discountPercents }}</div>
            {% endif %}
        </div>
    </li>
    <li class="landing__offer-item" data-offer-value="24-week plan">
        <div class="landing__offer-item-inner">
            <div class="landing__offer-item-box">
                <input class="paymentInfo"
                       type="radio"
                       id="dog-breed-radio-3"
                       name="dog-breed-radio"
                       data-payment-info='{"type": "{{ offer_values.six_month.type }}", "full_price": "{{ offer_values.six_month.full_price }}", "total_price": "{{ offer_values.six_month.total_price }}", "discount": "{{ offer_values.six_month.discount }}"}'
                >
                <label for="dog-breed-radio-3"></label>
                <h4 class="landing__offer-item-title">
                    24-WEEK PLAN
                </h4>
            </div>
            <div class="landing__offer-item-price-wrapper">
                <p class="landing__offer-item-price-full">
                    <span>{{ userCurrencySign() }}{{ offer_values.six_month.day_price }}</span>
                </p>
                <div class="landing__offer-item-price-box">
                    <div class="landing__offer-item-price">
                        <div class="landing__offer-item-price-sign">
                            <span>{{ userCurrencySign() }}</span>
                            <p>0</p>
                        </div>
                        <div class="landing__offer-item-price-value">
                            <span>{{ offer_values.six_month.discount_day_price }}</span>
                            <p class="landing__offer-item-price-text">per day</p>
                        </div>
                    </div>
                </div>
            </div>

            {% if isPcDiscountPercent %}
                <div class="pcdiscount_plan-percent">-{{ discountPercents }}</div>
            {% endif %}
        </div>
    </li>
</ul>
