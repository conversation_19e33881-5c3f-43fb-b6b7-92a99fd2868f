{% set reactivityFunnel = app.request.attributes.get('slug') in ['quiz20', 'quiz20-puppy-es'] %}

{% if reactivityFunnel %}
    {% set quoteText = 'Plan personal de gestión de la reactividad' %}
{% else %}
    {% set quoteText = 'Plan de Obediencia Personal' %}
{% endif %}

<div class="container quiz__index" id="block{{ blockId }}" data-id="{{ blockId }}">
    <div class="quiz__box amplitude-analytic" data-question="Gender" data-type="one-answer" data-anal-key="Gender">
        <p class="quiz__plan-subtitle">
            Cuestionario de 1 minuto
        </p>
        <h2 class="quiz__plan-title">
            {{ quoteText|raw }}
        </h2>
        <b class="quiz__plan-text">
            Tu perro es:
        </b>
        <ul class="quiz__dog quiz__dog-two text-center" data-selector="one-choice" data-response-type="gender">
            <li class="quiz__dog-item js-btn quiz__active-answer gender-submit" id="{{ blockId }}" data-one-choice="Girl" data-question-value="She">
                <svg class="quiz__dog-item-img" width="49" height="48" viewBox="0 0 49 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M10.3496 15.9087C10.3521 7.78287 16.9266 1.20173 25.0442 1.19922C33.1593 1.20173 39.7363 7.78287 39.7388 15.9087C39.7363 24.0319 33.1618 30.6156 25.0442 30.6156C16.9266 30.6156 10.3521 24.0319 10.3496 15.9087ZM17.4142 8.27092C15.4589 10.2307 14.2551 12.92 14.2551 15.9087C14.2551 18.8973 15.4589 21.5841 17.4142 23.5464C19.3719 25.5036 22.0585 26.7087 25.0442 26.7087C28.0299 26.7087 30.714 25.5036 32.6742 23.5464C34.6295 21.5867 35.8333 18.8973 35.8333 15.9087C35.8333 12.92 34.6295 10.2332 32.6742 8.27092C30.7165 6.31369 28.0299 5.10865 25.0442 5.10865C22.0585 5.10865 19.3719 6.31369 17.4142 8.27092Z" fill="#EF8BA0"/>
                    <path d="M23.0915 46.7993V28.6634H26.9945V46.7993H23.0915Z" fill="#EF8BA0"/>
                    <path d="M17.3463 41.647V37.7401H32.3751V41.647H17.3463Z" fill="#EF8BA0"/>
                </svg>

                <p class="quiz__dog-item-text">Chica</p>
            </li>
            <li class="quiz__dog-item js-btn quiz__active-answer gender-submit" id="{{ blockId }}" data-one-choice="Boy" data-question-value="He">
                <svg class="quiz__dog-item-img" width="49" height="48" viewBox="0 0 49 48" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M19.2244 42.7291C15.5177 42.7316 11.8013 41.2655 9.02557 38.37C6.37345 35.6087 5.04738 32.0278 5.04981 28.4688C5.04738 24.739 6.50193 20.9971 9.38193 18.204C12.1286 15.5354 15.685 14.2035 19.2219 14.206C22.9286 14.2035 26.645 15.6671 29.4232 18.5651C32.0753 21.3288 33.3989 24.9073 33.3965 28.4663C33.3989 32.1936 31.9419 35.9356 29.0644 38.7311C26.3201 41.3973 22.7662 42.7291 19.2316 42.7291C19.2292 42.7291 19.2268 42.7291 19.2244 42.7291ZM11.9977 20.9337C9.87648 22.9925 8.81951 25.7221 8.81708 28.4712C8.81951 31.0935 9.78193 33.7036 11.7359 35.7429C13.7819 37.8749 16.4922 38.9408 19.2268 38.9433C21.8353 38.9408 24.4268 37.9724 26.451 36.0088C28.5698 33.95 29.6292 31.2203 29.6316 28.4712C29.6292 25.8465 28.6668 23.2388 26.7128 21.1995C24.6644 19.0651 21.9541 18.0016 19.2219 17.9991C16.6159 17.9991 14.0219 18.9675 11.9977 20.9337Z" fill="#549CCA"/>
                    <path d="M26.7613 18.5187L39.3383 6.30492L41.9516 9.0321L29.3747 21.2459L26.7613 18.5187Z" fill="#549CCA"/>
                    <path d="M30.6886 9.78829L30.7783 6L42.2498 6.2854L39.1565 9.9932L30.6886 9.78829Z" fill="#549CCA"/>
                    <path d="M38.0656 17.1356L38.2692 8.61499L42.2498 6.2854L41.8329 17.2259L38.0656 17.1356Z" fill="#549CCA"/>
                </svg>

                <p class="quiz__dog-item-text">Chico</p>
            </li>
        </ul>
    </div>
    {% embed 'subscription/components/legal-footer.html.twig' with {addictionClass: 'top-position'} %}
        {% block content %}
            Al elegir «Chica» o «Chico» y seguir adelante aceptas las
            <a href="{{ path('static_page_terms', app.request.query.all | merge({'lang': 'es'})) }}">Términos&nbsp;de&nbsp;Uso</a>,
            <a href="{{ path('static_page_policy', app.request.query.all | merge({'lang': 'es'})) }}">Política&nbsp;de&nbsp;Privacidad</a> y
            <a href="{{ path('static_page_policy', app.request.query.all | merge({'lang': 'es'})) }}#cookies">Política&nbsp;de&nbsp;Cookies</a>
        {% endblock %}
    {% endembed %}
</div>
