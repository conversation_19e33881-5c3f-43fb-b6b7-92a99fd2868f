<div class="quiz__index profile" id="block{{ blockId }}" data-id="{{ blockId }}" data-header-hide="Profile" data-animation="Profile Animation">
    <div class="container">
        <h2 class="quiz__title">
            Aquí está el perfil de reactividad de <span class="nameInsertSelector">tu perro</span>
        </h2>
        <div class="quiz__profile" id="quizProfile">
            <div class="quiz__profile-title-wrapp">
                <h3 class="quiz__profile-title">
                    El nivel de reactividad del <span class="nameInsertSelector">Perro</span>
                </h3>
                <p class="quiz__profile-level">
                    Normal - 2
                </p>
            </div>
            <div class="quiz__profile-svg-box">
                <svg class="quiz__profile-graph" width="311" height="40" viewBox="0 0 311 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g clip-path="url(#clip0_13_41)">
                        <mask id="mask0_13_41" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="311" height="40">
                            <path d="M311 0H0V40H311V0Z" fill="white"/>
                        </mask>
                        <g mask="url(#mask0_13_41)">
                            <path d="M-3.0598e-07 15.5C-1.36992e-07 17.433 1.567 19 3.5 19L307.5 19C309.433 19 311 17.433 311 15.5C311 13.567 309.433 12 307.5 12L3.5 12C1.567 12 -4.74968e-07 13.567 -3.0598e-07 15.5Z" fill="url(#paint0_linear_13_41)"/>

                        </g>
                        <path d="M137.568 37V31.36H140.152C140.755 31.36 141.219 31.4907 141.544 31.752C141.875 32.0133 142.04 32.3707 142.04 32.824C142.04 33.1227 141.965 33.3813 141.816 33.6C141.667 33.8133 141.459 33.9733 141.192 34.08C141.512 34.1707 141.757 34.3307 141.928 34.56C142.104 34.7893 142.192 35.072 142.192 35.408C142.192 35.9093 142.016 36.3013 141.664 36.584C141.317 36.8613 140.843 37 140.24 37H137.568ZM138.752 33.68H139.936C140.544 33.68 140.848 33.4453 140.848 32.976C140.848 32.512 140.544 32.28 139.936 32.28H138.752V33.68ZM138.752 36.08H140.072C140.392 36.08 140.627 36.0187 140.776 35.896C140.925 35.7733 141 35.5867 141 35.336C141 35.0907 140.925 34.9067 140.776 34.784C140.627 34.6613 140.392 34.6 140.072 34.6H138.752V36.08ZM142.459 37L145.051 31.36H146.051L148.643 37H147.395L146.867 35.776H144.235L143.715 37H142.459ZM145.539 32.68L144.651 34.808H146.459L145.555 32.68H145.539ZM148.727 37.08L148.639 36.072L149.255 36.04C149.681 36.0133 149.895 35.7893 149.895 35.368V31.36H151.135V35.352C151.135 36.408 150.575 36.9733 149.455 37.048L148.727 37.08ZM154.887 37.088C154.332 37.088 153.844 36.968 153.423 36.728C153.007 36.4827 152.682 36.144 152.447 35.712C152.218 35.2747 152.103 34.7627 152.103 34.176C152.103 33.5893 152.218 33.08 152.447 32.648C152.682 32.2107 153.007 31.872 153.423 31.632C153.839 31.392 154.327 31.272 154.887 31.272C155.447 31.272 155.935 31.392 156.351 31.632C156.772 31.872 157.098 32.2107 157.327 32.648C157.562 33.08 157.679 33.5867 157.679 34.168C157.679 34.7547 157.562 35.2667 157.327 35.704C157.098 36.1413 156.772 36.4827 156.351 36.728C155.935 36.968 155.447 37.088 154.887 37.088ZM154.887 36.032C155.356 36.032 155.722 35.8693 155.983 35.544C156.25 35.2133 156.383 34.7573 156.383 34.176C156.383 33.5893 156.252 33.136 155.991 32.816C155.73 32.4907 155.362 32.328 154.887 32.328C154.423 32.328 154.058 32.4907 153.791 32.816C153.53 33.136 153.399 33.5893 153.399 34.176C153.399 34.7573 153.53 35.2133 153.791 35.544C154.058 35.8693 154.423 36.032 154.887 36.032Z" fill="#CCCCCC"/>
                        <path d="M289.92 37L292.512 31.36H293.512L296.104 37H294.856L294.328 35.776H291.696L291.176 37H289.92ZM293 32.68L292.112 34.808H293.92L293.016 32.68H293ZM296.591 37V31.36H297.831V35.952H300.439V37H296.591ZM301.467 37V32.384H299.603V31.36H304.571V32.384H302.707V37H301.467ZM307.457 37.088C306.903 37.088 306.415 36.968 305.993 36.728C305.577 36.4827 305.252 36.144 305.017 35.712C304.788 35.2747 304.673 34.7627 304.673 34.176C304.673 33.5893 304.788 33.08 305.017 32.648C305.252 32.2107 305.577 31.872 305.993 31.632C306.409 31.392 306.897 31.272 307.457 31.272C308.017 31.272 308.505 31.392 308.921 31.632C309.343 31.872 309.668 32.2107 309.897 32.648C310.132 33.08 310.249 33.5867 310.249 34.168C310.249 34.7547 310.132 35.2667 309.897 35.704C309.668 36.1413 309.343 36.4827 308.921 36.728C308.505 36.968 308.017 37.088 307.457 37.088ZM307.457 36.032C307.927 36.032 308.292 35.8693 308.553 35.544C308.82 35.2133 308.953 34.7573 308.953 34.176C308.953 33.5893 308.823 33.136 308.561 32.816C308.3 32.4907 307.932 32.328 307.457 32.328C306.993 32.328 306.628 32.4907 306.361 32.816C306.1 33.136 305.969 33.5893 305.969 34.176C305.969 34.7573 306.1 35.2133 306.361 35.544C306.628 35.8693 306.993 36.032 307.457 36.032Z" fill="#CCCCCC"/>
                        <path d="M0.568 37V31.36H4.432V32.328H1.752V33.648H4.256V34.624H1.752V36.032H4.432V37H0.568ZM4.70031 37L6.85231 34.144L4.76431 31.36H6.20431L7.56431 33.24L8.91631 31.36H10.3643L8.26831 34.144L10.4203 37H8.97231L7.55631 35.056L6.15631 37H4.70031ZM12.2089 37V32.384H10.3449V31.36H15.3129V32.384H13.4489V37H12.2089ZM15.9196 37V31.36H18.4956C19.1249 31.36 19.6102 31.512 19.9516 31.816C20.2929 32.12 20.4636 32.544 20.4636 33.088C20.4636 33.5093 20.3542 33.8613 20.1356 34.144C19.9222 34.4213 19.6156 34.6107 19.2156 34.712C19.4982 34.7973 19.7302 35.0027 19.9116 35.328L20.8236 37H19.4556L18.4796 35.2C18.3996 35.056 18.3009 34.9573 18.1836 34.904C18.0662 34.8453 17.9276 34.816 17.7676 34.816H17.1596V37H15.9196ZM17.1596 33.92H18.2716C18.9382 33.92 19.2716 33.6533 19.2716 33.12C19.2716 32.592 18.9382 32.328 18.2716 32.328H17.1596V33.92ZM21.4899 37V31.36H25.3539V32.328H22.6739V33.648H25.1779V34.624H22.6739V36.032H25.3539V37H21.4899ZM26.3496 37V31.36H27.3816L29.2856 34.824L31.1816 31.36H32.1976V37H31.0696V33.56L29.6136 36.152H28.9176L27.4696 33.584V37H26.3496ZM32.6934 37L35.2854 31.36H36.2854L38.8774 37H37.6294L37.1014 35.776H34.4694L33.9494 37H32.6934ZM35.7734 32.68L34.8854 34.808H36.6934L35.7894 32.68H35.7734ZM39.3649 37V31.36H41.5729C42.5329 31.36 43.2769 31.6053 43.8049 32.096C44.3329 32.5813 44.5969 33.2747 44.5969 34.176C44.5969 35.0773 44.3329 35.7733 43.8049 36.264C43.2769 36.7547 42.5329 37 41.5729 37H39.3649ZM40.6049 35.984H41.5009C42.7009 35.984 43.3009 35.3813 43.3009 34.176C43.3009 32.976 42.7009 32.376 41.5009 32.376H40.6049V35.984ZM44.7794 37L47.3714 31.36H48.3714L50.9634 37H49.7154L49.1874 35.776H46.5554L46.0354 37H44.7794ZM47.8594 32.68L46.9714 34.808H48.7794L47.8754 32.68H47.8594ZM51.4668 37V31.36H52.4988L54.4028 34.824L56.2988 31.36H57.3148V37H56.1868V33.56L54.7308 36.152H54.0348L52.5868 33.584V37H51.4668ZM58.4586 37V31.36H62.3226V32.328H59.6426V33.648H62.1466V34.624H59.6426V36.032H62.3226V37H58.4586ZM63.3024 37V31.36H64.2224L67.0384 34.952V31.36H68.1904V37H67.2784L64.4544 33.4V37H63.3024ZM70.6698 37V32.384H68.8058V31.36H73.7738V32.384H71.9098V37H70.6698ZM74.3805 37V31.36H78.2445V32.328H75.5645V33.648H78.0685V34.624H75.5645V36.032H78.2445V37H74.3805ZM81.4508 37V31.36H84.0348C84.6375 31.36 85.1015 31.4907 85.4268 31.752C85.7575 32.0133 85.9228 32.3707 85.9228 32.824C85.9228 33.1227 85.8481 33.3813 85.6988 33.6C85.5495 33.8133 85.3415 33.9733 85.0748 34.08C85.3948 34.1707 85.6401 34.3307 85.8108 34.56C85.9868 34.7893 86.0748 35.072 86.0748 35.408C86.0748 35.9093 85.8988 36.3013 85.5468 36.584C85.2001 36.8613 84.7255 37 84.1228 37H81.4508ZM82.6348 33.68H83.8188C84.4268 33.68 84.7308 33.4453 84.7308 32.976C84.7308 32.512 84.4268 32.28 83.8188 32.28H82.6348V33.68ZM82.6348 36.08H83.9548C84.2748 36.08 84.5095 36.0187 84.6588 35.896C84.8081 35.7733 84.8828 35.5867 84.8828 35.336C84.8828 35.0907 84.8081 34.9067 84.6588 34.784C84.5095 34.6613 84.2748 34.6 83.9548 34.6H82.6348V36.08ZM86.3419 37L88.9339 31.36H89.9339L92.5259 37H91.2779L90.7499 35.776H88.1179L87.5979 37H86.3419ZM89.4219 32.68L88.5339 34.808H90.3419L89.4379 32.68H89.4219ZM92.6096 37.08L92.5216 36.072L93.1376 36.04C93.5642 36.0133 93.7776 35.7893 93.7776 35.368V31.36H95.0176V35.352C95.0176 36.408 94.4576 36.9733 93.3376 37.048L92.6096 37.08ZM98.7699 37.088C98.2153 37.088 97.7273 36.968 97.3059 36.728C96.8899 36.4827 96.5646 36.144 96.3299 35.712C96.1006 35.2747 95.9859 34.7627 95.9859 34.176C95.9859 33.5893 96.1006 33.08 96.3299 32.648C96.5646 32.2107 96.8899 31.872 97.3059 31.632C97.7219 31.392 98.2099 31.272 98.7699 31.272C99.3299 31.272 99.8179 31.392 100.234 31.632C100.655 31.872 100.981 32.2107 101.21 32.648C101.445 33.08 101.562 33.5867 101.562 34.168C101.562 34.7547 101.445 35.2667 101.21 35.704C100.981 36.1413 100.655 36.4827 100.234 36.728C99.8179 36.968 99.3299 37.088 98.7699 37.088ZM98.7699 36.032C99.2393 36.032 99.6046 35.8693 99.8659 35.544C100.133 35.2133 100.266 34.7573 100.266 34.176C100.266 33.5893 100.135 33.136 99.8739 32.816C99.6126 32.4907 99.2446 32.328 98.7699 32.328C98.3059 32.328 97.9406 32.4907 97.6739 32.816C97.4126 33.136 97.2819 33.5893 97.2819 34.176C97.2819 34.7573 97.4126 35.2133 97.6739 35.544C97.9406 35.8693 98.3059 36.032 98.7699 36.032Z" fill="#CCCCCC"/>
                        <path d="M201.584 37V31.36H202.616L204.52 34.824L206.416 31.36H207.432V37H206.304V33.56L204.848 36.152H204.152L202.704 33.584V37H201.584ZM208.576 37V31.36H212.44V32.328H209.76V33.648H212.264V34.624H209.76V36.032H212.44V37H208.576ZM213.42 37V31.36H215.628C216.588 31.36 217.332 31.6053 217.86 32.096C218.388 32.5813 218.652 33.2747 218.652 34.176C218.652 35.0773 218.388 35.7733 217.86 36.264C217.332 36.7547 216.588 37 215.628 37H213.42ZM214.66 35.984H215.556C216.756 35.984 217.356 35.3813 217.356 34.176C217.356 32.976 216.756 32.376 215.556 32.376H214.66V35.984ZM219.615 37V31.36H220.855V37H219.615ZM224.598 37.088C224.043 37.088 223.555 36.968 223.134 36.728C222.718 36.4827 222.393 36.144 222.158 35.712C221.929 35.2747 221.814 34.7627 221.814 34.176C221.814 33.5893 221.929 33.08 222.158 32.648C222.393 32.2107 222.718 31.872 223.134 31.632C223.55 31.392 224.038 31.272 224.598 31.272C225.158 31.272 225.646 31.392 226.062 31.632C226.483 31.872 226.809 32.2107 227.038 32.648C227.273 33.08 227.39 33.5867 227.39 34.168C227.39 34.7547 227.273 35.2667 227.038 35.704C226.809 36.1413 226.483 36.4827 226.062 36.728C225.646 36.968 225.158 37.088 224.598 37.088ZM224.598 36.032C225.067 36.032 225.433 35.8693 225.694 35.544C225.961 35.2133 226.094 34.7573 226.094 34.176C226.094 33.5893 225.963 33.136 225.702 32.816C225.441 32.4907 225.073 32.328 224.598 32.328C224.134 32.328 223.769 32.4907 223.502 32.816C223.241 33.136 223.11 33.5893 223.11 34.176C223.11 34.7573 223.241 35.2133 223.502 35.544C223.769 35.8693 224.134 36.032 224.598 36.032Z" fill="black"/>
                    </g>
                    <g class="range-dot" id="rangeDot">
                        <path d="M239 22C235.134 22 232 18.866 232 15C232 11.134 235.134 8 239 8C242.866 8 246 11.134 246 15C246 18.866 242.866 22 239 22Z" fill="white"/>
                        <path d="M239 23C234.582 23 231 19.4183 231 15C231 10.5817 234.582 7 239 7C243.418 7 247 10.5817 247 15C247 19.4183 243.418 23 239 23Z" stroke="#E6E6E6" stroke-width="2"/>
                    </g>
                    <defs>
                        <linearGradient id="paint0_linear_13_41" x1="-6.11959e-07" y1="12" x2="311" y2="12" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#1998CD"/>
                            <stop offset="0.549638" stop-color="#F5E088"/>
                            <stop offset="1" stop-color="#FF574C"/>
                        </linearGradient>
                        <clipPath id="clip0_13_41">
                            <rect width="311" height="40" fill="white"/>
                        </clipPath>
                    </defs>
                </svg>
                <svg id="obedienceTail" class="tail tail--is-reactivity spanish" width="12" height="7" viewBox="0 0 12 7" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M4.95698 6.31047L0.75 0L11.25 0L6.59544 6.34713C6.18473 6.90719 5.34222 6.88833 4.95698 6.31047Z" fill="#16191E"/>
                </svg>
                <div class="quiz__profile-svg-text-box quiz__profile-svg-text-box--is-reactivity spanish" id="obediencePopup">
                    <p class="quiz__profile-svg-text">
                        <span class="nameInsertSelector">NOMBRE</span> - 8
                    </p>
                </div>
            </div>
            <div class="quiz__profile-alert">
                <div class="quiz__profile-alert-icon-wrapp">
                    <img loading="lazy" class="quiz__profile-alert-icon" src="https://images.paw-champ.com/landings/behavioral_problems.png" alt="">
                </div>
                <div class="quiz__profile-alert-text-wrapp">
                    <h4 class="quiz__profile-alert-title">
                        Nivel de Alta Reactividad
                    </h4>
                    <p class="quiz__profile-alert-text">
                        Un nivel de alta reactividad puede llevar a dificultades en el entrenamiento, mala socialización, mayor riesgo de problemas de comportamiento y una calidad de vida reducida tanto para el perro como para el
                        dueño.
                    </p>
                </div>
            </div>
            <div class="quiz__profile-list-item">
                <div class="quiz__profile-list-icon-wrapp">
                    <img loading="lazy" class="quiz__profile-list-icon" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1684941074/dog-training/icons/better/77.png" alt="">
                </div>
                <div class="quiz__profile-list-text-wrapp">
                    <h4 class="quiz__profile-list-title">
                        Problemas de Comportamiento
                    </h4>
                    <div class="problemsInsertSelector">
                    </div>
                </div>
            </div>
            <div class="quiz__profile-list-wrapp">
                <ul class="quiz__profile-list">
                    <li class="quiz__profile-list-item">
                        <div class="quiz__profile-list-icon-wrapp">
                            <img loading="lazy" class="quiz__profile-alert-icon" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1719506802/dog-training/icons/better/reactivity-profile.png" alt="">
                        </div>
                        <div class="quiz__profile-list-text-wrapp">
                            <h4 class="quiz__profile-list-title">
                                Tipo de reactividad
                            </h4>
                            <p class="quiz__profile-list-text">
                                Respuesta al estrés y la ansiedad
                            </p>
                        </div>
                    </li>
                    <li class="quiz__profile-list-item">
                        <div class="quiz__profile-list-icon-wrapp">
                            <img loading="lazy" class="quiz__profile-alert-icon" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1684941074/dog-training/icons/better/78.png" alt="">
                        </div>
                        <div class="quiz__profile-list-text-wrapp">
                            <h4 class="quiz__profile-list-title">
                                Capacidad de respuesta
                            </h4>
                            <p class="quiz__profile-list-text">
                                Bajo
                            </p>
                        </div>
                    </li>
                    <li class="quiz__profile-list-item">
                        <div class="quiz__profile-list-icon-wrapp">
                            <img loading="lazy" class="quiz__profile-alert-icon" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1684941074/dog-training/icons/better/79.png" alt="">
                        </div>
                        <div class="quiz__profile-list-text-wrapp">
                            <h4 class="quiz__profile-list-title">
                                Capacidad de adiestramiento
                            </h4>
                            <p class="quiz__profile-list-text">
                                Medio
                            </p>
                        </div>
                    </li>
                    <li class="quiz__profile-list-item">
                        <div class="quiz__profile-list-icon-wrapp">
                            <img loading="lazy" class="quiz__profile-alert-icon" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1686214728/dog-training/icons/better/111.png" alt="">
                        </div>
                        <div class="quiz__profile-list-text-wrapp">
                            <h4 class="quiz__profile-list-title">
                                Raza
                            </h4>
                            <div class="quiz__profile-list-text" id="mixedBreedInsertSelector">
                                <span class="breedInsertSelector"></span> <span class="puppyInsertSelector" data-puppy="de cachorro"></span>
                            </div>
                        </div>
                    </li>
                </ul>
                {% if app.request.query.has('ml_img') %}
                    <div class="quiz__profile-man-img-wrapp" id="profileImg">
                        <img loading="lazy" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1689334575/dog-training/img/man-quiz-profile.png" alt="" class="quiz__profile-man-img">
                    </div>
                {% else %}
                    <div class="quiz__profile-girl-img-wrapp" id="profileImg">
                        <img loading="lazy" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1685013727/dog-training/img/better/girl.png" alt="" class="quiz__profile-girl-img">
                    </div>
                {% endif %}
                <div class="quiz__profile-arrow-box" id="profileArrow">
                    <svg width="77" height="185" viewBox="0 0 77 185" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path class="quiz__profile-arrow" d="M74.5 183L30.8244 104.031C27.1952 97.4691 27.1582 89.5107 30.7261 82.9153L74.5 2" stroke="#1998CD" stroke-opacity="0.13" stroke-width="4" stroke-linecap="round"/>
                        <path class="quiz__profile-arrow" d="M62 183L18.3244 104.031C14.6952 97.4691 14.6582 89.5107 18.2261 82.9153L62 2" stroke="#1998CD" stroke-opacity="0.13" stroke-width="4" stroke-linecap="round"/>
                        <path class="quiz__profile-arrow" d="M49.5 183L5.82436 104.031C2.19519 97.4691 2.15817 89.5107 5.72613 82.9153L49.5 2" stroke="#1998CD" stroke-opacity="0.13" stroke-width="4" stroke-linecap="round"/>
                    </svg>
                </div>
            </div>
        </div>
        <div class="quiz__btn-box">
            <button class="quiz__btn js-btn" id="{{ blockId }}">
                <span></span>
                Continuar
            </button>
        </div>
    </div>
</div>
