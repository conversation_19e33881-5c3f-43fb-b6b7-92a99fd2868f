{# "pc_reactpun" split file #}
{# In case of split applying, please, remove the comment #}
{# In case of split refusing, please, double-check if it isn't used in any other places and delete this file #}
{# If you using this file in any other places but splits, please, remove the comment #}
<div class="quiz__index" id="block{{ blockId }}" data-id="{{ blockId }}">
    <div class="quiz__box quiz__mid">
        <div class="quiz__mid-inner">
            <div class="container">
                <div class="adultPuppyContainer">
                    <p class="quiz__mid-puppy-title">
                        Being a reactive <span class="puppyInsertSelector" data-puppy="puppy">dog</span> owner
                        can be tough
                    </p>
                    <div class="quiz__mid-puppy">
                        <picture>
                            <source srcset="https://res.cloudinary.com/dr0cx27xo/image/upload/v1718643334/dog-training/img/quiz/common/tough.webp" type="image/webp">
                            <img loading="lazy" class="quiz__mid-puppy-img" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1706021172/dog-training/img/tough.png" alt="girl hugs dog">
                        </picture>
                    </div>
                    <div class="quiz__mid-subtitle">
                        So, your feelings are totally normal.
                        <br>
                        Now, we have a few more questions about your
                        <span class="puppyInsertSelector" data-puppy="pup">dog</span>’s behavior to better adjust your
                        dog training plan.
                    </div>
                </div>
                <button class="quiz__mid-btn quiz__btn js-btn" id="{{ blockId }}">
                    <span></span>
                    Continue
                </button>
            </div>
        </div>
    </div>
</div>
