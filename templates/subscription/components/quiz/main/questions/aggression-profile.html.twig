<div class="quiz__index profile" id="block{{ blockId }}" data-id="{{ blockId }}" data-header-hide="Profile" data-animation="Profile Animation">
    <div class="container">
        <br>
        <div class="quiz__profile" id="quizProfile">
            <h2 class="quiz__title">
                Here’s <span class="nameInsertSelector">your dog</span>’s <br>
                aggression profile
            </h2>
            <span class="quiz__profile-level quiz__profile-level--is-aggression">
                NORMAL - 2
            </span>
            <div class="quiz__profile-svg-box quiz__profile-svg-box--is-aggression">
                <svg class="quiz__profile-graph" width="311" height="40" viewBox="0 0 311 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M0 15.5C0 13.567 1.567 12 3.5 12H307.5C309.433 12 311 13.567 311 15.5C311 17.433 309.433 19 307.5 19H3.5C1.567 19 0 17.433 0 15.5Z" fill="url(#paint0_linear_2001_41)"/>
                    <g id="rangeDot" filter="url(#filter0_d_2001_41)" class="range-dot">
                        <circle cx="5" cy="7" r="7" transform="matrix(-1 0 0 1 218 8)" fill="white"/>
                        <circle cx="5" cy="7" r="8" transform="matrix(-1 0 0 1 218 8)" stroke="#E6E6E6" stroke-width="2"/>
                    </g>
                    <path d="M0.568 38V32.36H1.808V36.952H4.416V38H0.568ZM7.45744 38.088C6.90277 38.088 6.41477 37.968 5.99344 37.728C5.57744 37.4827 5.2521 37.144 5.01744 36.712C4.7881 36.2747 4.67344 35.7627 4.67344 35.176C4.67344 34.5893 4.7881 34.08 5.01744 33.648C5.2521 33.2107 5.57744 32.872 5.99344 32.632C6.40944 32.392 6.89744 32.272 7.45744 32.272C8.01744 32.272 8.50544 32.392 8.92144 32.632C9.34277 32.872 9.6681 33.2107 9.89744 33.648C10.1321 34.08 10.2494 34.5867 10.2494 35.168C10.2494 35.7547 10.1321 36.2667 9.89744 36.704C9.6681 37.1413 9.34277 37.4827 8.92144 37.728C8.50544 37.968 8.01744 38.088 7.45744 38.088ZM7.45744 37.032C7.92677 37.032 8.2921 36.8693 8.55344 36.544C8.8201 36.2133 8.95344 35.7573 8.95344 35.176C8.95344 34.5893 8.82277 34.136 8.56144 33.816C8.3001 33.4907 7.9321 33.328 7.45744 33.328C6.99344 33.328 6.6281 33.4907 6.36144 33.816C6.1001 34.136 5.96944 34.5893 5.96944 35.176C5.96944 35.7573 6.1001 36.2133 6.36144 36.544C6.6281 36.8693 6.99344 37.032 7.45744 37.032ZM12.6362 38L10.6282 32.36H11.9002L13.1962 36.216L14.5642 32.36H15.4602L16.7882 36.28L18.1322 32.36H19.3322L17.3082 38H16.3002L14.9802 34.256L13.6442 38H12.6362Z" fill="#CCCCCC"/>
                    <path d="M75.9175 38V32.36H76.9495L78.8535 35.824L80.7495 32.36H81.7655V38H80.6375V34.56L79.1815 37.152H78.4855L77.0375 34.584V38H75.9175ZM82.9093 38V32.36H86.7733V33.328H84.0933V34.648H86.5973V35.624H84.0933V37.032H86.7733V38H82.9093ZM87.7531 38V32.36H89.9611C90.9211 32.36 91.6651 32.6053 92.1931 33.096C92.7211 33.5813 92.9851 34.2747 92.9851 35.176C92.9851 36.0773 92.7211 36.7733 92.1931 37.264C91.6651 37.7547 90.9211 38 89.9611 38H87.7531ZM88.9931 36.984H89.8891C91.0891 36.984 91.6891 36.3813 91.6891 35.176C91.6891 33.976 91.0891 33.376 89.8891 33.376H88.9931V36.984ZM93.9484 38V32.36H95.1884V38H93.9484ZM98.7316 38.088C97.9316 38.088 97.3209 37.8827 96.8996 37.472C96.4836 37.0613 96.2756 36.4507 96.2756 35.64V32.36H97.5156V35.648C97.5156 36.1067 97.6196 36.4533 97.8276 36.688C98.0356 36.9173 98.3369 37.032 98.7316 37.032C99.5369 37.032 99.9396 36.5707 99.9396 35.648V32.36H101.164V35.64C101.164 36.4507 100.958 37.0613 100.548 37.472C100.137 37.8827 99.5316 38.088 98.7316 38.088ZM102.277 38V32.36H103.309L105.213 35.824L107.109 32.36H108.125V38H106.997V34.56L105.541 37.152H104.845L103.397 34.584V38H102.277Z" fill="#CCCCCC"/>
                    <path d="M165.235 38V32.36H166.475V34.624H169.099V32.36H170.339V38H169.099V35.656H166.475V38H165.235ZM171.477 38V32.36H172.717V38H171.477ZM176.636 38.088C176.001 38.088 175.463 37.968 175.02 37.728C174.583 37.488 174.249 37.152 174.02 36.72C173.791 36.288 173.676 35.7813 173.676 35.2C173.676 34.6027 173.793 34.0853 174.028 33.648C174.268 33.2107 174.612 32.872 175.06 32.632C175.508 32.392 176.047 32.272 176.676 32.272C177.071 32.272 177.444 32.328 177.796 32.44C178.153 32.552 178.444 32.7013 178.668 32.888L178.268 33.856C178.023 33.6747 177.772 33.5467 177.516 33.472C177.26 33.392 176.983 33.352 176.684 33.352C176.113 33.352 175.684 33.512 175.396 33.832C175.113 34.1467 174.972 34.6027 174.972 35.2C174.972 35.8027 175.116 36.2587 175.404 36.568C175.697 36.8773 176.129 37.032 176.7 37.032C177.004 37.032 177.324 36.984 177.66 36.888V35.784H176.444V34.888H178.684V37.656C178.407 37.7947 178.087 37.9013 177.724 37.976C177.361 38.0507 176.999 38.088 176.636 38.088ZM179.781 38V32.36H181.021V34.624H183.645V32.36H184.885V38H183.645V35.656H181.021V38H179.781Z" fill="#16191E"/>
                    <path d="M241.568 38V32.36H245.432V33.328H242.752V34.648H245.256V35.624H242.752V37.032H245.432V38H241.568ZM245.7 38L247.852 35.144L245.764 32.36H247.204L248.564 34.24L249.916 32.36H251.364L249.268 35.144L251.42 38H249.972L248.556 36.056L247.156 38H245.7ZM253.209 38V33.384H251.345V32.36H256.313V33.384H254.449V38H253.209ZM256.92 38V32.36H259.496C260.125 32.36 260.61 32.512 260.952 32.816C261.293 33.12 261.464 33.544 261.464 34.088C261.464 34.5093 261.354 34.8613 261.136 35.144C260.922 35.4213 260.616 35.6107 260.216 35.712C260.498 35.7973 260.73 36.0027 260.912 36.328L261.824 38H260.456L259.48 36.2C259.4 36.056 259.301 35.9573 259.184 35.904C259.066 35.8453 258.928 35.816 258.768 35.816H258.16V38H256.92ZM258.16 34.92H259.272C259.938 34.92 260.272 34.6533 260.272 34.12C260.272 33.592 259.938 33.328 259.272 33.328H258.16V34.92ZM262.49 38V32.36H266.354V33.328H263.674V34.648H266.178V35.624H263.674V37.032H266.354V38H262.49ZM267.35 38V32.36H268.382L270.286 35.824L272.182 32.36H273.198V38H272.07V34.56L270.614 37.152H269.918L268.47 34.584V38H267.35ZM274.341 38V32.36H278.205V33.328H275.525V34.648H278.029V35.624H275.525V37.032H278.205V38H274.341ZM279.185 38V32.36H280.425V36.952H283.033V38H279.185ZM284.154 38V35.456L281.978 32.36H283.418L284.794 34.344L286.17 32.36H287.554L285.394 35.464V38H284.154ZM290.091 38V32.36H291.331V34.624H293.955V32.36H295.195V38H293.955V35.656H291.331V38H290.091ZM296.334 38V32.36H297.574V38H296.334ZM301.493 38.088C300.858 38.088 300.319 37.968 299.877 37.728C299.439 37.488 299.106 37.152 298.877 36.72C298.647 36.288 298.533 35.7813 298.533 35.2C298.533 34.6027 298.65 34.0853 298.885 33.648C299.125 33.2107 299.469 32.872 299.917 32.632C300.365 32.392 300.903 32.272 301.533 32.272C301.927 32.272 302.301 32.328 302.653 32.44C303.01 32.552 303.301 32.7013 303.525 32.888L303.125 33.856C302.879 33.6747 302.629 33.5467 302.373 33.472C302.117 33.392 301.839 33.352 301.541 33.352C300.97 33.352 300.541 33.512 300.253 33.832C299.97 34.1467 299.829 34.6027 299.829 35.2C299.829 35.8027 299.973 36.2587 300.261 36.568C300.554 36.8773 300.986 37.032 301.557 37.032C301.861 37.032 302.181 36.984 302.517 36.888V35.784H301.301V34.888H303.541V37.656C303.263 37.7947 302.943 37.9013 302.581 37.976C302.218 38.0507 301.855 38.088 301.493 38.088ZM304.638 38V32.36H305.878V34.624H308.502V32.36H309.742V38H308.502V35.656H305.878V38H304.638Z" fill="#CCCCCC"/>
                    <defs>
                        <filter id="filter0_d_2001_41" x="192" y="0" width="38" height="38" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                            <feOffset dy="4"/>
                            <feGaussianBlur stdDeviation="5"/>
                            <feComposite in2="hardAlpha" operator="out"/>
                            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
                            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2001_41"/>
                            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2001_41" result="shape"/>
                        </filter>
                        <linearGradient id="paint0_linear_2001_41" x1="0" y1="19" x2="311" y2="19" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#1998CD"/>
                            <stop offset="0.549638" stop-color="#F5E088"/>
                            <stop offset="1" stop-color="#FF574C"/>
                        </linearGradient>
                    </defs>
                </svg>
                <svg id="obedienceTail" class="tail tail--is-reactivity" width="12" height="7" viewBox="0 0 12 7" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M4.95698 6.31047L0.75 0L11.25 0L6.59544 6.34713C6.18473 6.90719 5.34222 6.88833 4.95698 6.31047Z" fill="#16191E"/>
                </svg>
                <div class="quiz__profile-svg-text-box quiz__profile-svg-text-box--is-reactivity" id="obediencePopup">
                    <p class="quiz__profile-svg-text">
                        <span class="nameInsertSelector">obedience</span> - 8
                    </p>
                </div>
            </div>
            <div class="quiz__profile-alert">
                <div class="quiz__profile-alert-icon-wrapp">
                    <img loading="lazy" class="quiz__profile-alert-icon" src="https://images.paw-champ.com/landings/behavioral_problems.png" alt="">
                </div>
                <div class="quiz__profile-alert-text-wrapp">
                    <h4 class="quiz__profile-alert-title">
                        High Aggression Level:
                    </h4>
                    <p class="quiz__profile-alert-text">
                        A high aggression level can lead to difficulties in training, poor socialization, increased risk of behavior problems, and a reduced quality of life for the dog and the owner.
                    </p>
                </div>
            </div>
            <div class="quiz__profile-list-item">
                <div class="quiz__profile-list-icon-wrapp">
                    <img loading="lazy" class="quiz__profile-list-icon" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1684941074/dog-training/icons/better/77.png" alt="">
                </div>
                <div class="quiz__profile-list-text-wrapp">
                    <h4 class="quiz__profile-list-title">
                        Behavioral Issues
                    </h4>
                    <div class="problemsInsertSelector">
                    </div>
                </div>
            </div>
            <div class="quiz__profile-list-wrapp">
                <ul class="quiz__profile-list">
                    <li class="quiz__profile-list-item">
                        <div class="quiz__profile-list-icon-wrapp">
                            <img loading="lazy" class="quiz__profile-alert-icon" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1719506802/dog-training/icons/better/reactivity-profile.png" alt="">
                        </div>
                        <div class="quiz__profile-list-text-wrapp">
                            <h4 class="quiz__profile-list-title">
                                Aggression type
                            </h4>
                            <p class="quiz__profile-list-text">
                                Stress & fear response
                            </p>
                        </div>
                    </li>
                    <li class="quiz__profile-list-item">
                        <div class="quiz__profile-list-icon-wrapp">
                            <img loading="lazy" class="quiz__profile-alert-icon" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1684941074/dog-training/icons/better/78.png" alt="">
                        </div>
                        <div class="quiz__profile-list-text-wrapp">
                            <h4 class="quiz__profile-list-title">
                                Responsiveness
                            </h4>
                            <p class="quiz__profile-list-text">
                                Low
                            </p>
                        </div>
                    </li>
                    <li class="quiz__profile-list-item">
                        <div class="quiz__profile-list-icon-wrapp">
                            <img loading="lazy" class="quiz__profile-alert-icon" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1684941074/dog-training/icons/better/79.png" alt="">
                        </div>
                        <div class="quiz__profile-list-text-wrapp">
                            <h4 class="quiz__profile-list-title">
                                Trainability
                            </h4>
                            <p class="quiz__profile-list-text">
                                Medium
                            </p>
                        </div>
                    </li>
                    <li class="quiz__profile-list-item">
                        <div class="quiz__profile-list-icon-wrapp">
                            <img loading="lazy" class="quiz__profile-alert-icon" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1686214728/dog-training/icons/better/111.png" alt="">
                        </div>
                        <div class="quiz__profile-list-text-wrapp">
                            <h4 class="quiz__profile-list-title">
                                Breed
                            </h4>
                            <div class="quiz__profile-list-text" id="mixedBreedInsertSelector">
                                <span class="breedInsertSelector">Your</span> <span class="puppyInsertSelector" data-puppy="puppy"></span>
                            </div>
                        </div>
                    </li>
                </ul>
                {% if app.request.query.has('ml_img') %}
                    <div class="quiz__profile-man-img-wrapp" id="profileImg">
                        <img loading="lazy" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1689334575/dog-training/img/man-quiz-profile.png" alt="" class="quiz__profile-man-img">
                    </div>
                {% else %}
                    <div class="quiz__profile-girl-img-wrapp" id="profileImg">
                        <img loading="lazy" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1685013727/dog-training/img/better/girl.png" alt="" class="quiz__profile-girl-img">
                    </div>
                {% endif %}
                <div class="quiz__profile-arrow-box" id="profileArrow">
                    <svg width="77" height="185" viewBox="0 0 77 185" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path class="quiz__profile-arrow" d="M74.5 183L30.8244 104.031C27.1952 97.4691 27.1582 89.5107 30.7261 82.9153L74.5 2" stroke="#1998CD" stroke-opacity="0.13" stroke-width="4" stroke-linecap="round"/>
                        <path class="quiz__profile-arrow" d="M62 183L18.3244 104.031C14.6952 97.4691 14.6582 89.5107 18.2261 82.9153L62 2" stroke="#1998CD" stroke-opacity="0.13" stroke-width="4" stroke-linecap="round"/>
                        <path class="quiz__profile-arrow" d="M49.5 183L5.82436 104.031C2.19519 97.4691 2.15817 89.5107 5.72613 82.9153L49.5 2" stroke="#1998CD" stroke-opacity="0.13" stroke-width="4" stroke-linecap="round"/>
                    </svg>
                </div>
            </div>
        </div>
        <div class="quiz__btn-box">
            <button class="quiz__btn js-btn" id="{{ blockId }}">
                <span></span>
                Continue
            </button>
        </div>
    </div>
</div>
