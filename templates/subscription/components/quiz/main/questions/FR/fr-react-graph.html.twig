<div class="quiz__index android-btn-position graphic" id="block{{ blockId }}" data-id="{{ blockId }}" data-header-hide="Graph" data-animation="Graph Animation">
    <div class="container">
        <h2 class="quiz__title">
            Le dernier plan dont vous aurez besoin pour corriger la réactivité de <span class="nameInsertSelector">votre chien</span>
        </h2>
        <h3 class="quiz__subtitle">
            Nous prévoyons que vous diminuerez le niveau de réactivité de <span class="nameInsertSelector">votre chien</span> de
        </h3>
        <div class="quiz__graph">
            <div class="quiz__graph-title-wrapp">
                <p id="target-date-placeholder" class="quiz__graph-date">Date</p>
                <p class="quiz__graph-text" id="tooltip-subtitle-selector">
                    Juste à temps pour <span class="important-event-placeholder">EVENT</span>
                </p>
            </div>
            <div class="quiz__graph-svg-wrapp">
                <div class="quiz__graph-svg-inner">
                    <svg class="quiz__graph-svg" width="327" height="216" viewBox="0 0 327 216" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path class="graphBg" id="graph-bg" fill-rule="evenodd" clip-rule="evenodd" d="M222.278 124.934C233.506 126.358 243.346 127.607 251.41 129.445C274.076 134.614 287.381 143.18 295.703 151.396C302.118 157.73 305.578 163.799 308.244 168.476L308.245 168.477C308.755 169.372 309.235 170.215 309.702 171H314.391V197H17.4497V19.8222C89.8124 108.13 170.69 118.39 222.278 124.934ZM314.391 169.63V169.496L314.45 169.588L314.391 169.63Z" fill="#F2F3F4"/>
                        <path d="M5.78165 175V173.74H2.81165V172.63L6.04165 167.95H7.30165V172.52H8.26165V173.74H7.30165V175H5.78165ZM5.78165 172.52V170.22L4.22165 172.52H5.78165Z" fill="#D7D7D7"/>
                        <path d="M5.72167 103.11C4.82167 103.11 4.125 102.803 3.63167 102.19C3.145 101.57 2.90167 100.697 2.90167 99.57C2.90167 98.39 3.17834 97.473 3.73167 96.82C4.285 96.167 5.05167 95.84 6.03167 95.84C6.41167 95.84 6.785 95.907 7.15167 96.04C7.525 96.173 7.85167 96.36 8.13167 96.6L7.63167 97.75C7.39167 97.543 7.13167 97.39 6.85167 97.29C6.57834 97.183 6.29834 97.13 6.01167 97.13C4.91834 97.13 4.37167 97.847 4.37167 99.28V99.47C4.51167 99.137 4.72834 98.88 5.02167 98.7C5.315 98.513 5.65167 98.42 6.03167 98.42C6.43167 98.42 6.785 98.52 7.09167 98.72C7.405 98.913 7.65167 99.183 7.83167 99.53C8.01167 99.87 8.10167 100.26 8.10167 100.7C8.10167 101.167 7.99834 101.583 7.79167 101.95C7.59167 102.31 7.31167 102.593 6.95167 102.8C6.59834 103.007 6.18834 103.11 5.72167 103.11ZM5.64167 101.9C5.94834 101.9 6.195 101.797 6.38167 101.59C6.575 101.377 6.67167 101.1 6.67167 100.76C6.67167 100.427 6.575 100.153 6.38167 99.94C6.195 99.727 5.94834 99.62 5.64167 99.62C5.32834 99.62 5.075 99.727 4.88167 99.94C4.695 100.153 4.60167 100.427 4.60167 100.76C4.60167 101.1 4.695 101.377 4.88167 101.59C5.075 101.797 5.32834 101.9 5.64167 101.9Z" fill="#D7D7D7"/>
                        <path d="M6 18.1C5.18 18.1 4.53667 17.9233 4.07 17.57C3.61 17.2167 3.38 16.7267 3.38 16.1C3.38 15.6733 3.49 15.31 3.71 15.01C3.93667 14.71 4.23 14.5 4.59 14.38C4.27 14.24 4.01667 14.0267 3.83 13.74C3.65 13.4533 3.56 13.1233 3.56 12.75C3.56 12.1567 3.78 11.6933 4.22 11.36C4.66 11.02 5.25333 10.85 6 10.85C6.75333 10.85 7.35 11.02 7.79 11.36C8.23 11.6933 8.45 12.1567 8.45 12.75C8.45 13.1233 8.35667 13.4567 8.17 13.75C7.99 14.0367 7.74333 14.2467 7.43 14.38C7.79 14.5 8.07667 14.7133 8.29 15.02C8.51 15.32 8.62 15.68 8.62 16.1C8.62 16.7267 8.38667 17.2167 7.92 17.57C7.46 17.9233 6.82 18.1 6 18.1ZM6 13.93C6.39333 13.93 6.7 13.8367 6.92 13.65C7.14667 13.4633 7.26 13.2067 7.26 12.88C7.26 12.56 7.14667 12.31 6.92 12.13C6.7 11.9433 6.39333 11.85 6 11.85C5.60667 11.85 5.3 11.9433 5.08 12.13C4.86 12.31 4.75 12.56 4.75 12.88C4.75 13.2067 4.86 13.4633 5.08 13.65C5.3 13.8367 5.60667 13.93 6 13.93ZM6 17.1C6.95333 17.1 7.43 16.7367 7.43 16.01C7.43 15.29 6.95333 14.93 6 14.93C5.53333 14.93 5.17667 15.02 4.93 15.2C4.69 15.38 4.57 15.65 4.57 16.01C4.57 16.37 4.69 16.6433 4.93 16.83C5.17667 17.01 5.53333 17.1 6 17.1Z" fill="white"/>
                        <path d="M6 18.1C5.18 18.1 4.53667 17.9233 4.07 17.57C3.61 17.2167 3.38 16.7267 3.38 16.1C3.38 15.6733 3.49 15.31 3.71 15.01C3.93667 14.71 4.23 14.5 4.59 14.38C4.27 14.24 4.01667 14.0267 3.83 13.74C3.65 13.4533 3.56 13.1233 3.56 12.75C3.56 12.1567 3.78 11.6933 4.22 11.36C4.66 11.02 5.25333 10.85 6 10.85C6.75333 10.85 7.35 11.02 7.79 11.36C8.23 11.6933 8.45 12.1567 8.45 12.75C8.45 13.1233 8.35667 13.4567 8.17 13.75C7.99 14.0367 7.74333 14.2467 7.43 14.38C7.79 14.5 8.07667 14.7133 8.29 15.02C8.51 15.32 8.62 15.68 8.62 16.1C8.62 16.7267 8.38667 17.2167 7.92 17.57C7.46 17.9233 6.82 18.1 6 18.1ZM6 13.93C6.39333 13.93 6.7 13.8367 6.92 13.65C7.14667 13.4633 7.26 13.2067 7.26 12.88C7.26 12.56 7.14667 12.31 6.92 12.13C6.7 11.9433 6.39333 11.85 6 11.85C5.60667 11.85 5.3 11.9433 5.08 12.13C4.86 12.31 4.75 12.56 4.75 12.88C4.75 13.2067 4.86 13.4633 5.08 13.65C5.3 13.8367 5.60667 13.93 6 13.93ZM6 17.1C6.95333 17.1 7.43 16.7367 7.43 16.01C7.43 15.29 6.95333 14.93 6 14.93C5.53333 14.93 5.17667 15.02 4.93 15.2C4.69 15.38 4.57 15.65 4.57 16.01C4.57 16.37 4.69 16.6433 4.93 16.83C5.17667 17.01 5.53333 17.1 6 17.1Z" fill="#D7D7D7"/>
                        <line x1="17.4497" y1="15.5" x2="313.57" y2="15.5" stroke="#E8E8E8"/>
                        <line x1="17.4497" y1="41.5" x2="313.57" y2="41.5" stroke="#E8E8E8"/>
                        <line x1="17.4497" y1="67.5" x2="313.57" y2="67.5" stroke="#E8E8E8"/>
                        <line x1="17.4497" y1="93.5" x2="313.57" y2="93.5" stroke="#E8E8E8"/>
                        <line x1="17.4497" y1="119.5" x2="313.57" y2="119.5" stroke="#E8E8E8"/>
                        <line x1="17.4497" y1="145.5" x2="313.57" y2="145.5" stroke="#E8E8E8"/>
                        <line x1="17.4497" y1="171.5" x2="313.57" y2="171.5" stroke="#E8E8E8"/>
                        <line x1="17.4497" y1="197.5" x2="314.51" y2="197.5" stroke="#E8E8E8"/>
                        <line x1="17.8896" y1="204" x2="17.8896" y2="198" stroke="#E8E8E8"/>
                        <line x1="314.01" y1="204" x2="314.01" y2="198" stroke="#E8E8E8"/>
                        <line x1="212.482" y1="204" x2="212.482" y2="198" stroke="#E8E8E8"/>
                        <line class="goalSelector" x1="124.5" y1="204" x2="124.5" y2="100" stroke="#E8E8E8"/>
                        <g id="graph-line" class="quiz__based-graph-line" opacity="0.3" filter="url(#filter0_f_402_3261)">
                            <path d="M18.4497,24C106.292,133 206.818,124.5 252.853,135C298.888,145.5 307.656,170 313.45,179" stroke="url(#paint0_linear_402_3261)" stroke-width="5"/>
                        </g>
                        <path id="graph-line" class="quiz__based-graph-line" d="M18.4497,17C106.292,126 206.818,117.5 252.853,128C298.888,138.5 307.656,163 313.45,172" stroke="url(#paint1_linear_402_3261)" stroke-width="5"/>
                        <g class="goalSelector" id="goal-dot" filter="url(#filter1_d_402_3261)">
                            <circle cx="311" cy="169.55" r="7.55031" fill="white"/>
                            <circle cx="311" cy="169.55" r="6.55031" stroke="#2D2D2D" stroke-opacity="0.12" stroke-width="2"/>
                        </g>
                        <g filter="url(#filter2_d_402_3261)">
                            <circle cx="18" cy="18.5503" r="7.55031" fill="white"/>
                            <circle cx="18" cy="18.5503" r="6.55031" stroke="#2D2D2D" stroke-opacity="0.12" stroke-width="2"/>
                        </g>
                        <g class="tooltip-dot-selector" id="tooltip-dot" filter="url(#filter3_d_402_3261)">
                            <circle cx="124.55" cy="99.5503" r="7.55031" fill="white"/>
                            <circle cx="124.55" cy="99.5503" r="6.55031" stroke="#2D2D2D" stroke-opacity="0.12" stroke-width="2"/>
                        </g>
                        <path class="goalSelector goal-dot-selector" d="M299.31 168.793L293 173V162.5L299.347 167.155C299.907 167.565 299.888 168.408 299.31 168.793Z" fill="#1998CD"/>
                        <rect class="goalSelector goal-dot-selector" x="164.55" y="152" width="129" height="30" rx="8" fill="#1998CD"/>
                        <path class="goalSelector goal-dot-selector" d="M177.285 172L172.847 162.13H175.171L178.573 170.012H177.873L181.275 162.13H183.501L179.063 172H177.285ZM186.471 172.154C185.734 172.154 185.09 172.009 184.539 171.72C183.998 171.431 183.578 171.015 183.279 170.474C182.98 169.933 182.831 169.293 182.831 168.556C182.831 167.819 182.98 167.184 183.279 166.652C183.578 166.12 183.998 165.709 184.539 165.42C185.09 165.131 185.734 164.986 186.471 164.986C187.208 164.986 187.848 165.131 188.389 165.42C188.94 165.709 189.364 166.125 189.663 166.666C189.971 167.198 190.125 167.828 190.125 168.556C190.125 169.293 189.971 169.933 189.663 170.474C189.364 171.015 188.94 171.431 188.389 171.72C187.848 172.009 187.208 172.154 186.471 172.154ZM186.471 170.572C186.928 170.572 187.297 170.409 187.577 170.082C187.857 169.746 187.997 169.237 187.997 168.556C187.997 167.875 187.857 167.375 187.577 167.058C187.297 166.731 186.928 166.568 186.471 166.568C186.023 166.568 185.659 166.731 185.379 167.058C185.099 167.375 184.959 167.875 184.959 168.556C184.959 169.237 185.099 169.746 185.379 170.082C185.659 170.409 186.023 170.572 186.471 170.572ZM194.717 172.154C193.737 172.154 193.009 171.916 192.533 171.44C192.057 170.964 191.819 170.264 191.819 169.34V166.708H190.517V165.126H191.819V163.11H193.933V165.126H195.963V166.708H193.933V169.256C193.933 169.648 194.021 169.942 194.199 170.138C194.385 170.334 194.679 170.432 195.081 170.432C195.202 170.432 195.328 170.418 195.459 170.39C195.599 170.362 195.748 170.325 195.907 170.278L196.215 171.818C196.019 171.921 195.785 172 195.515 172.056C195.244 172.121 194.978 172.154 194.717 172.154ZM197.003 172V165.126H199.075V166.806H198.935C199.047 166.246 199.29 165.817 199.663 165.518C200.046 165.21 200.55 165.033 201.175 164.986L201.791 164.944L201.917 166.722L200.727 166.848C200.195 166.895 199.798 167.049 199.537 167.31C199.285 167.571 199.159 167.949 199.159 168.444V172H197.003ZM206.219 172.154C205.407 172.154 204.707 172.009 204.119 171.72C203.541 171.421 203.093 171.006 202.775 170.474C202.467 169.933 202.313 169.298 202.313 168.57C202.313 167.861 202.463 167.24 202.761 166.708C203.06 166.167 203.475 165.747 204.007 165.448C204.549 165.14 205.16 164.986 205.841 164.986C206.513 164.986 207.092 165.131 207.577 165.42C208.063 165.7 208.436 166.101 208.697 166.624C208.968 167.147 209.103 167.767 209.103 168.486V169.018H204.049V167.926H207.577L207.353 168.122C207.353 167.553 207.227 167.123 206.975 166.834C206.733 166.535 206.383 166.386 205.925 166.386C205.58 166.386 205.286 166.465 205.043 166.624C204.801 166.783 204.614 167.011 204.483 167.31C204.353 167.609 204.287 167.968 204.287 168.388V168.5C204.287 168.976 204.357 169.368 204.497 169.676C204.647 169.975 204.866 170.199 205.155 170.348C205.454 170.497 205.823 170.572 206.261 170.572C206.635 170.572 207.013 170.516 207.395 170.404C207.778 170.292 208.123 170.119 208.431 169.886L208.991 171.3C208.646 171.561 208.221 171.771 207.717 171.93C207.223 172.079 206.723 172.154 206.219 172.154ZM217.533 172.154C216.796 172.154 216.152 172.009 215.601 171.72C215.06 171.431 214.64 171.015 214.341 170.474C214.043 169.933 213.893 169.293 213.893 168.556C213.893 167.819 214.043 167.184 214.341 166.652C214.64 166.12 215.06 165.709 215.601 165.42C216.152 165.131 216.796 164.986 217.533 164.986C218.271 164.986 218.91 165.131 219.451 165.42C220.002 165.709 220.427 166.125 220.725 166.666C221.033 167.198 221.187 167.828 221.187 168.556C221.187 169.293 221.033 169.933 220.725 170.474C220.427 171.015 220.002 171.431 219.451 171.72C218.91 172.009 218.271 172.154 217.533 172.154ZM217.533 170.572C217.991 170.572 218.359 170.409 218.639 170.082C218.919 169.746 219.059 169.237 219.059 168.556C219.059 167.875 218.919 167.375 218.639 167.058C218.359 166.731 217.991 166.568 217.533 166.568C217.085 166.568 216.721 166.731 216.441 167.058C216.161 167.375 216.021 167.875 216.021 168.556C216.021 169.237 216.161 169.746 216.441 170.082C216.721 170.409 217.085 170.572 217.533 170.572ZM226.716 172.154C226.165 172.154 225.675 172.019 225.246 171.748C224.826 171.468 224.546 171.099 224.406 170.642H224.546V172H222.474V162.13H224.588V166.4H224.42C224.569 165.98 224.854 165.639 225.274 165.378C225.694 165.117 226.174 164.986 226.716 164.986C227.322 164.986 227.85 165.131 228.298 165.42C228.746 165.709 229.096 166.12 229.348 166.652C229.6 167.184 229.726 167.819 229.726 168.556C229.726 169.293 229.6 169.933 229.348 170.474C229.096 171.015 228.741 171.431 228.284 171.72C227.836 172.009 227.313 172.154 226.716 172.154ZM226.086 170.572C226.534 170.572 226.898 170.409 227.178 170.082C227.458 169.746 227.598 169.237 227.598 168.556C227.598 167.875 227.458 167.375 227.178 167.058C226.898 166.731 226.534 166.568 226.086 166.568C225.628 166.568 225.26 166.731 224.98 167.058C224.7 167.375 224.56 167.875 224.56 168.556C224.56 169.237 224.7 169.746 224.98 170.082C225.26 170.409 225.628 170.572 226.086 170.572ZM230.991 163.796V161.822H233.273V163.796H230.991ZM229.619 174.702L229.479 173.19L230.137 173.12C230.463 173.073 230.701 172.966 230.851 172.798C231.009 172.63 231.089 172.373 231.089 172.028V165.126H233.203V171.748C233.203 172.233 233.147 172.649 233.035 172.994C232.932 173.349 232.764 173.643 232.531 173.876C232.307 174.109 232.003 174.287 231.621 174.408C231.238 174.539 230.771 174.623 230.221 174.66L229.619 174.702ZM238.417 172.154C237.605 172.154 236.905 172.009 236.317 171.72C235.738 171.421 235.29 171.006 234.973 170.474C234.665 169.933 234.511 169.298 234.511 168.57C234.511 167.861 234.66 167.24 234.959 166.708C235.257 166.167 235.673 165.747 236.205 165.448C236.746 165.14 237.357 164.986 238.039 164.986C238.711 164.986 239.289 165.131 239.775 165.42C240.26 165.7 240.633 166.101 240.895 166.624C241.165 167.147 241.301 167.767 241.301 168.486V169.018H236.247V167.926H239.775L239.551 168.122C239.551 167.553 239.425 167.123 239.173 166.834C238.93 166.535 238.58 166.386 238.123 166.386C237.777 166.386 237.483 166.465 237.241 166.624C236.998 166.783 236.811 167.011 236.681 167.31C236.55 167.609 236.485 167.968 236.485 168.388V168.5C236.485 168.976 236.555 169.368 236.695 169.676C236.844 169.975 237.063 170.199 237.353 170.348C237.651 170.497 238.02 170.572 238.459 170.572C238.832 170.572 239.21 170.516 239.593 170.404C239.975 170.292 240.321 170.119 240.629 169.886L241.189 171.3C240.843 171.561 240.419 171.771 239.915 171.93C239.42 172.079 238.921 172.154 238.417 172.154ZM245.876 172.154C245.13 172.154 244.476 172.009 243.916 171.72C243.366 171.421 242.941 171.001 242.642 170.46C242.344 169.919 242.194 169.279 242.194 168.542C242.194 167.805 242.344 167.17 242.642 166.638C242.941 166.106 243.366 165.7 243.916 165.42C244.476 165.131 245.13 164.986 245.876 164.986C246.324 164.986 246.768 165.051 247.206 165.182C247.654 165.313 248.018 165.499 248.298 165.742L247.71 167.198C247.486 167.011 247.225 166.867 246.926 166.764C246.628 166.652 246.348 166.596 246.086 166.596C245.536 166.596 245.106 166.769 244.798 167.114C244.5 167.45 244.35 167.931 244.35 168.556C244.35 169.172 244.5 169.657 244.798 170.012C245.106 170.357 245.536 170.53 246.086 170.53C246.348 170.53 246.628 170.479 246.926 170.376C247.225 170.273 247.486 170.124 247.71 169.928L248.298 171.384C248.018 171.617 247.654 171.804 247.206 171.944C246.758 172.084 246.315 172.154 245.876 172.154ZM252.631 172.154C251.651 172.154 250.923 171.916 250.447 171.44C249.971 170.964 249.733 170.264 249.733 169.34V166.708H248.431V165.126H249.733V163.11H251.847V165.126H253.877V166.708H251.847V169.256C251.847 169.648 251.935 169.942 252.113 170.138C252.299 170.334 252.593 170.432 252.995 170.432C253.116 170.432 253.242 170.418 253.373 170.39C253.513 170.362 253.662 170.325 253.821 170.278L254.129 171.818C253.933 171.921 253.699 172 253.429 172.056C253.158 172.121 252.892 172.154 252.631 172.154ZM254.833 163.796V161.822H257.115V163.796H254.833ZM254.917 172V165.126H257.031V172H254.917ZM259.195 172V166.708H257.893V165.126H259.811L259.195 165.686V165.252C259.195 164.235 259.466 163.474 260.007 162.97C260.548 162.457 261.393 162.167 262.541 162.102L263.311 162.046L263.437 163.586L262.779 163.628C262.415 163.647 262.126 163.707 261.911 163.81C261.696 163.903 261.542 164.039 261.449 164.216C261.356 164.384 261.309 164.608 261.309 164.888V165.364L261.043 165.126H263.101V166.708H261.309V172H259.195ZM268.003 169.074V167.408H272.329V169.074H268.003ZM281.818 172V170.236H277.66V168.682L282.182 162.13H283.946V168.528H285.29V170.236H283.946V172H281.818ZM281.818 168.528V164.72H282.224L279.27 169.06V168.528H281.818Z" fill="white"/>
                        <defs>
                            <filter id="filter0_f_402_3261" x="8.50293" y="14.4316" width="315.049" height="173.922" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                                <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                                <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
                                <feGaussianBlur stdDeviation="4" result="effect1_foregroundBlur_402_3261"/>
                            </filter>
                            <filter id="filter1_d_402_3261" x="295.45" y="158.314" width="31.1006" height="31.1006" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                                <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                                <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                                <feOffset dy="4.31447"/>
                                <feGaussianBlur stdDeviation="4"/>
                                <feComposite in2="hardAlpha" operator="out"/>
                                <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
                                <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_402_3261"/>
                                <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_402_3261" result="shape"/>
                            </filter>
                            <filter id="filter2_d_402_3261" x="2.44971" y="7.31447" width="31.1006" height="31.1006" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                                <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                                <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                                <feOffset dy="4.31447"/>
                                <feGaussianBlur stdDeviation="4"/>
                                <feComposite in2="hardAlpha" operator="out"/>
                                <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
                                <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_402_3261"/>
                                <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_402_3261" result="shape"/>
                            </filter>
                            <filter id="filter3_d_402_3261" x="109" y="88.3145" width="31.1006" height="31.1006" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                                <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                                <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                                <feOffset dy="4.31447"/>
                                <feGaussianBlur stdDeviation="4"/>
                                <feComposite in2="hardAlpha" operator="out"/>
                                <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
                                <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_402_3261"/>
                                <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_402_3261" result="shape"/>
                            </filter>
                            <filter id="filter4_d_402_3261" x="79" y="53" width="92" height="47.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                                <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                                <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                                <feOffset dy="4"/>
                                <feGaussianBlur stdDeviation="4"/>
                                <feComposite in2="hardAlpha" operator="out"/>
                                <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.16 0"/>
                                <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_402_3261"/>
                                <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_402_3261" result="shape"/>
                            </filter>
                            <linearGradient id="paint0_linear_402_3261" x1="18.4497" y1="178.999" x2="313.45" y2="178.999" gradientUnits="userSpaceOnUse">
                                <stop stop-color="#FF574C"/>
                                <stop offset="0.450362" stop-color="#F5E088"/>
                                <stop offset="1" stop-color="#1998CD"/>
                            </linearGradient>
                            <linearGradient id="paint1_linear_402_3261" x1="18.4497" y1="171.999" x2="313.45" y2="171.999" gradientUnits="userSpaceOnUse">
                                <stop stop-color="#FF574C"/>
                                <stop offset="0.450362" stop-color="#F5E088"/>
                                <stop offset="1" stop-color="#1998CD"/>
                            </linearGradient>
                        </defs>
                    </svg>
                    <svg id="tail-selector" class="tail-tooltip tail-tooltip--is-reactivity" width="12" height="7" viewBox="0 0 12 7" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M4.95698 6.31047L0.75 0L11.25 0L6.59544 6.34713C6.18473 6.90719 5.34222 6.88833 4.95698 6.31047Z" fill="#16191E"/>
                    </svg>
                    <div class="quiz__graph-svg-date-wrapp">
                        <p class="quiz__graph-svg-date">
                            MAINTENANT
                        </p>
                        <p class="quiz__graph-svg-date" id="monthGraph1">
                            month 1
                        </p>
                        <p class="quiz__graph-svg-date" id="monthGraph2">
                            month 2
                        </p>
                        <p class="quiz__graph-svg-date" id="monthGraph3">
                            month 3
                        </p>
                    </div>
                </div>
                <div class="quiz__graph-svg-text-box quiz__graph-svg-text-box--is-reactivity" id="tooltip-selector">
                    <p class="quiz__graph-svg-text quiz__graph-svg-text--is-restricted">
                        <span class="important-event-placeholder"></span> - 6
                    </p>
                </div>
            </div>
            <p class="quiz__graph-subtext compl">
                Ce tableau montre vos progrès potentiels si vous suivez toutes les étapes répertoriées dans notre plan
            </p>
        </div>
        <div class="quiz__btn-box">
            <button class="quiz__btn js-btn" id="{{ blockId }}">
                <span></span>
                Continuer
            </button>
        </div>
    </div>
</div>
