<div class="quiz__index android-btn-position graphic" id="block{{ blockId }}" data-id="{{ blockId }}" data-header-hide="Graph" data-animation="Graph Animation">
    <div class="container">
        <h2 class="quiz__title">
            L'ultimo piano di cui avrai mai bisogno per risolvere la reattività di <span class="nameInsertSelector">il tuo cane</span>
        </h2>
        <h3 class="quiz__subtitle">
            Prevediamo che ridurrai il livello di reattività di <span class="nameInsertSelector">il tuo cane</span> di
        </h3>
        <div class="quiz__graph">
            <div class="quiz__graph-title-wrapp">
                <p id="target-date-placeholder" class="quiz__graph-date">Data</p>
                <p class="quiz__graph-text" id="tooltip-subtitle-selector">
                    Giusto in tempo per <span class="important-event-placeholder">EVENTO</span>
                </p>
            </div>
            <div class="quiz__graph-svg-wrapp">
                <div class="quiz__graph-svg-inner">
                    <svg class="quiz__graph-svg" width="327" height="216" viewBox="0 0 327 216" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path class="graphBg" id="graph-bg" fill-rule="evenodd" clip-rule="evenodd" d="M222.278 124.934C233.506 126.358 243.346 127.607 251.41 129.445C274.076 134.614 287.381 143.18 295.703 151.396C302.118 157.73 305.578 163.799 308.244 168.476L308.245 168.477C308.755 169.372 309.235 170.215 309.702 171H314.391V197H17.4497V19.8222C89.8124 108.13 170.69 118.39 222.278 124.934ZM314.391 169.63V169.496L314.45 169.588L314.391 169.63Z" fill="#F2F3F4"/>
                        <path d="M5.78165 175V173.74H2.81165V172.63L6.04165 167.95H7.30165V172.52H8.26165V173.74H7.30165V175H5.78165ZM5.78165 172.52V170.22L4.22165 172.52H5.78165Z" fill="#D7D7D7"/>
                        <path d="M5.72167 103.11C4.82167 103.11 4.125 102.803 3.63167 102.19C3.145 101.57 2.90167 100.697 2.90167 99.57C2.90167 98.39 3.17834 97.473 3.73167 96.82C4.285 96.167 5.05167 95.84 6.03167 95.84C6.41167 95.84 6.785 95.907 7.15167 96.04C7.525 96.173 7.85167 96.36 8.13167 96.6L7.63167 97.75C7.39167 97.543 7.13167 97.39 6.85167 97.29C6.57834 97.183 6.29834 97.13 6.01167 97.13C4.91834 97.13 4.37167 97.847 4.37167 99.28V99.47C4.51167 99.137 4.72834 98.88 5.02167 98.7C5.315 98.513 5.65167 98.42 6.03167 98.42C6.43167 98.42 6.785 98.52 7.09167 98.72C7.405 98.913 7.65167 99.183 7.83167 99.53C8.01167 99.87 8.10167 100.26 8.10167 100.7C8.10167 101.167 7.99834 101.583 7.79167 101.95C7.59167 102.31 7.31167 102.593 6.95167 102.8C6.59834 103.007 6.18834 103.11 5.72167 103.11ZM5.64167 101.9C5.94834 101.9 6.195 101.797 6.38167 101.59C6.575 101.377 6.67167 101.1 6.67167 100.76C6.67167 100.427 6.575 100.153 6.38167 99.94C6.195 99.727 5.94834 99.62 5.64167 99.62C5.32834 99.62 5.075 99.727 4.88167 99.94C4.695 100.153 4.60167 100.427 4.60167 100.76C4.60167 101.1 4.695 101.377 4.88167 101.59C5.075 101.797 5.32834 101.9 5.64167 101.9Z" fill="#D7D7D7"/>
                        <path d="M6 18.1C5.18 18.1 4.53667 17.9233 4.07 17.57C3.61 17.2167 3.38 16.7267 3.38 16.1C3.38 15.6733 3.49 15.31 3.71 15.01C3.93667 14.71 4.23 14.5 4.59 14.38C4.27 14.24 4.01667 14.0267 3.83 13.74C3.65 13.4533 3.56 13.1233 3.56 12.75C3.56 12.1567 3.78 11.6933 4.22 11.36C4.66 11.02 5.25333 10.85 6 10.85C6.75333 10.85 7.35 11.02 7.79 11.36C8.23 11.6933 8.45 12.1567 8.45 12.75C8.45 13.1233 8.35667 13.4567 8.17 13.75C7.99 14.0367 7.74333 14.2467 7.43 14.38C7.79 14.5 8.07667 14.7133 8.29 15.02C8.51 15.32 8.62 15.68 8.62 16.1C8.62 16.7267 8.38667 17.2167 7.92 17.57C7.46 17.9233 6.82 18.1 6 18.1ZM6 13.93C6.39333 13.93 6.7 13.8367 6.92 13.65C7.14667 13.4633 7.26 13.2067 7.26 12.88C7.26 12.56 7.14667 12.31 6.92 12.13C6.7 11.9433 6.39333 11.85 6 11.85C5.60667 11.85 5.3 11.9433 5.08 12.13C4.86 12.31 4.75 12.56 4.75 12.88C4.75 13.2067 4.86 13.4633 5.08 13.65C5.3 13.8367 5.60667 13.93 6 13.93ZM6 17.1C6.95333 17.1 7.43 16.7367 7.43 16.01C7.43 15.29 6.95333 14.93 6 14.93C5.53333 14.93 5.17667 15.02 4.93 15.2C4.69 15.38 4.57 15.65 4.57 16.01C4.57 16.37 4.69 16.6433 4.93 16.83C5.17667 17.01 5.53333 17.1 6 17.1Z" fill="white"/>
                        <path d="M6 18.1C5.18 18.1 4.53667 17.9233 4.07 17.57C3.61 17.2167 3.38 16.7267 3.38 16.1C3.38 15.6733 3.49 15.31 3.71 15.01C3.93667 14.71 4.23 14.5 4.59 14.38C4.27 14.24 4.01667 14.0267 3.83 13.74C3.65 13.4533 3.56 13.1233 3.56 12.75C3.56 12.1567 3.78 11.6933 4.22 11.36C4.66 11.02 5.25333 10.85 6 10.85C6.75333 10.85 7.35 11.02 7.79 11.36C8.23 11.6933 8.45 12.1567 8.45 12.75C8.45 13.1233 8.35667 13.4567 8.17 13.75C7.99 14.0367 7.74333 14.2467 7.43 14.38C7.79 14.5 8.07667 14.7133 8.29 15.02C8.51 15.32 8.62 15.68 8.62 16.1C8.62 16.7267 8.38667 17.2167 7.92 17.57C7.46 17.9233 6.82 18.1 6 18.1ZM6 13.93C6.39333 13.93 6.7 13.8367 6.92 13.65C7.14667 13.4633 7.26 13.2067 7.26 12.88C7.26 12.56 7.14667 12.31 6.92 12.13C6.7 11.9433 6.39333 11.85 6 11.85C5.60667 11.85 5.3 11.9433 5.08 12.13C4.86 12.31 4.75 12.56 4.75 12.88C4.75 13.2067 4.86 13.4633 5.08 13.65C5.3 13.8367 5.60667 13.93 6 13.93ZM6 17.1C6.95333 17.1 7.43 16.7367 7.43 16.01C7.43 15.29 6.95333 14.93 6 14.93C5.53333 14.93 5.17667 15.02 4.93 15.2C4.69 15.38 4.57 15.65 4.57 16.01C4.57 16.37 4.69 16.6433 4.93 16.83C5.17667 17.01 5.53333 17.1 6 17.1Z" fill="#D7D7D7"/>
                        <path d="M17.4497 15.5H313.57" stroke="#E8E8E8"/>
                        <path d="M17.4497 41.5H313.57" stroke="#E8E8E8"/>
                        <path d="M17.4497 67.5H313.57" stroke="#E8E8E8"/>
                        <path d="M17.4497 93.5H313.57" stroke="#E8E8E8"/>
                        <path d="M17.4497 119.5H313.57" stroke="#E8E8E8"/>
                        <path d="M17.4497 145.5H313.57" stroke="#E8E8E8"/>
                        <path d="M17.4497 171.5H313.57" stroke="#E8E8E8"/>
                        <path d="M17.4497 197.5H314.51" stroke="#E8E8E8"/>
                        <path d="M17.8896 204V198" stroke="#E8E8E8"/>
                        <path d="M314.01 204V198" stroke="#E8E8E8"/>
                        <path d="M212.482 204V198" stroke="#E8E8E8"/>
                        <path class="goalSelector" d="M124.5 204V100" stroke="#E8E8E8"/>
                        <g id="graph-line" class="quiz__based-graph-line" opacity="0.3" filter="url(#filter0_f_402_3261)">
                            <path d="M18.4497,24C106.292,133 206.818,124.5 252.853,135C298.888,145.5 307.656,170 313.45,179" stroke="url(#paint0_linear_402_3261)" stroke-width="5"/>
                        </g>
                        <path id="graph-line" class="quiz__based-graph-line" d="M18.4497,17C106.292,126 206.818,117.5 252.853,128C298.888,138.5 307.656,163 313.45,172" stroke="url(#paint1_linear_402_3261)" stroke-width="5"/>
                        <g class="goalSelector" id="goal-dot" filter="url(#filter1_d_402_3261)">
                            <circle cx="311" cy="169.55" r="7.55031" fill="white"/>
                            <circle cx="311" cy="169.55" r="6.55031" stroke="#2D2D2D" stroke-opacity="0.12" stroke-width="2"/>
                        </g>
                        <g filter="url(#filter2_d_402_3261)">
                            <circle cx="18" cy="18.5503" r="7.55031" fill="white"/>
                            <circle cx="18" cy="18.5503" r="6.55031" stroke="#2D2D2D" stroke-opacity="0.12" stroke-width="2"/>
                        </g>
                        <g class="tooltip-dot-selector" id="tooltip-dot" filter="url(#filter3_d_402_3261)">
                            <circle cx="124.55" cy="99.5503" r="7.55031" fill="white"/>
                            <circle cx="124.55" cy="99.5503" r="6.55031" stroke="#2D2D2D" stroke-opacity="0.12" stroke-width="2"/>
                        </g>
                        <path class="goalSelector goal-dot-selector" d="M297.76 168.793L291.45 173V162.5L297.797 167.155C298.357 167.565 298.338 168.408 297.76 168.793Z" fill="#1998CD"/>
                        <path class="goalSelector goal-dot-selector" d="M280.752 152H161.248C155.036 152 150 155.582 150 160V174C150 178.418 155.036 182 161.248 182H280.752C286.964 182 292 178.418 292 174V160C292 155.582 286.964 152 280.752 152Z" fill="#1998CD"/>
                        <path class="goalSelector goal-dot-selector" d="M159.426 172V163.434H161.242V172H159.426ZM165.045 172H163.258V162.883H165.045V172ZM171.602 172L169.105 165.449H170.975L172.24 169.182C172.311 169.4 172.365 169.631 172.404 169.873C172.447 170.115 172.477 170.332 172.492 170.523H172.539C172.551 170.316 172.578 170.096 172.621 169.861C172.668 169.627 172.729 169.4 172.803 169.182L174.062 165.449H175.932L173.436 172H171.602ZM182.828 168.713C182.828 169.26 182.754 169.744 182.605 170.166C182.461 170.588 182.248 170.945 181.967 171.238C181.689 171.527 181.354 171.746 180.959 171.895C180.568 172.043 180.127 172.117 179.635 172.117C179.174 172.117 178.75 172.043 178.363 171.895C177.98 171.746 177.646 171.527 177.361 171.238C177.08 170.945 176.861 170.588 176.705 170.166C176.553 169.744 176.477 169.26 176.477 168.713C176.477 167.986 176.605 167.371 176.863 166.867C177.121 166.363 177.488 165.98 177.965 165.719C178.441 165.457 179.01 165.326 179.67 165.326C180.283 165.326 180.826 165.457 181.299 165.719C181.775 165.98 182.148 166.363 182.418 166.867C182.691 167.371 182.828 167.986 182.828 168.713ZM178.299 168.713C178.299 169.143 178.346 169.504 178.439 169.797C178.533 170.09 178.68 170.311 178.879 170.459C179.078 170.607 179.338 170.682 179.658 170.682C179.975 170.682 180.23 170.607 180.426 170.459C180.625 170.311 180.77 170.09 180.859 169.797C180.953 169.504 181 169.143 181 168.713C181 168.279 180.953 167.92 180.859 167.635C180.77 167.346 180.625 167.129 180.426 166.984C180.227 166.84 179.967 166.768 179.646 166.768C179.174 166.768 178.83 166.93 178.615 167.254C178.404 167.578 178.299 168.064 178.299 168.713ZM188.875 170.055C188.875 170.5 188.77 170.877 188.559 171.186C188.352 171.49 188.041 171.723 187.627 171.883C187.213 172.039 186.697 172.117 186.08 172.117C185.623 172.117 185.23 172.088 184.902 172.029C184.578 171.971 184.25 171.873 183.918 171.736V170.26C184.273 170.42 184.654 170.553 185.061 170.658C185.471 170.76 185.83 170.811 186.139 170.811C186.486 170.811 186.734 170.76 186.883 170.658C187.035 170.553 187.111 170.416 187.111 170.248C187.111 170.139 187.08 170.041 187.018 169.955C186.959 169.865 186.83 169.766 186.631 169.656C186.432 169.543 186.119 169.396 185.693 169.217C185.283 169.045 184.945 168.871 184.68 168.695C184.418 168.52 184.223 168.312 184.094 168.074C183.969 167.832 183.906 167.525 183.906 167.154C183.906 166.549 184.141 166.094 184.609 165.789C185.082 165.48 185.713 165.326 186.502 165.326C186.908 165.326 187.295 165.367 187.662 165.449C188.033 165.531 188.414 165.662 188.805 165.842L188.266 167.131C187.941 166.99 187.635 166.875 187.346 166.785C187.061 166.695 186.77 166.65 186.473 166.65C186.211 166.65 186.014 166.686 185.881 166.756C185.748 166.826 185.682 166.934 185.682 167.078C185.682 167.184 185.715 167.277 185.781 167.359C185.852 167.441 185.984 167.533 186.18 167.635C186.379 167.732 186.67 167.859 187.053 168.016C187.424 168.168 187.746 168.328 188.02 168.496C188.293 168.66 188.504 168.865 188.652 169.111C188.801 169.354 188.875 169.668 188.875 170.055ZM193.029 170.693C193.225 170.693 193.414 170.674 193.598 170.635C193.785 170.596 193.971 170.547 194.154 170.488V171.818C193.963 171.904 193.725 171.975 193.439 172.029C193.158 172.088 192.85 172.117 192.514 172.117C192.123 172.117 191.771 172.055 191.459 171.93C191.15 171.801 190.906 171.58 190.727 171.268C190.551 170.951 190.463 170.512 190.463 169.949V166.791H189.607V166.035L190.592 165.438L191.107 164.055H192.25V165.449H194.084V166.791H192.25V169.949C192.25 170.199 192.32 170.387 192.461 170.512C192.605 170.633 192.795 170.693 193.029 170.693ZM199.141 165.326C199.23 165.326 199.334 165.332 199.451 165.344C199.572 165.352 199.67 165.363 199.744 165.379L199.609 167.055C199.551 167.035 199.467 167.021 199.357 167.014C199.252 167.002 199.16 166.996 199.082 166.996C198.852 166.996 198.627 167.025 198.408 167.084C198.193 167.143 198 167.238 197.828 167.371C197.656 167.5 197.52 167.672 197.418 167.887C197.32 168.098 197.271 168.357 197.271 168.666V172H195.484V165.449H196.838L197.102 166.551H197.189C197.318 166.328 197.479 166.125 197.67 165.941C197.865 165.754 198.086 165.605 198.332 165.496C198.582 165.383 198.852 165.326 199.141 165.326ZM206.887 168.713C206.887 169.26 206.812 169.744 206.664 170.166C206.52 170.588 206.307 170.945 206.025 171.238C205.748 171.527 205.412 171.746 205.018 171.895C204.627 172.043 204.186 172.117 203.693 172.117C203.232 172.117 202.809 172.043 202.422 171.895C202.039 171.746 201.705 171.527 201.42 171.238C201.139 170.945 200.92 170.588 200.764 170.166C200.611 169.744 200.535 169.26 200.535 168.713C200.535 167.986 200.664 167.371 200.922 166.867C201.18 166.363 201.547 165.98 202.023 165.719C202.5 165.457 203.068 165.326 203.729 165.326C204.342 165.326 204.885 165.457 205.357 165.719C205.834 165.98 206.207 166.363 206.477 166.867C206.75 167.371 206.887 167.986 206.887 168.713ZM202.357 168.713C202.357 169.143 202.404 169.504 202.498 169.797C202.592 170.09 202.738 170.311 202.938 170.459C203.137 170.607 203.396 170.682 203.717 170.682C204.033 170.682 204.289 170.607 204.484 170.459C204.684 170.311 204.828 170.09 204.918 169.797C205.012 169.504 205.059 169.143 205.059 168.713C205.059 168.279 205.012 167.92 204.918 167.635C204.828 167.346 204.684 167.129 204.484 166.984C204.285 166.84 204.025 166.768 203.705 166.768C203.232 166.768 202.889 166.93 202.674 167.254C202.463 167.578 202.357 168.064 202.357 168.713ZM217.434 168.713C217.434 169.26 217.359 169.744 217.211 170.166C217.066 170.588 216.854 170.945 216.572 171.238C216.295 171.527 215.959 171.746 215.564 171.895C215.174 172.043 214.732 172.117 214.24 172.117C213.779 172.117 213.355 172.043 212.969 171.895C212.586 171.746 212.252 171.527 211.967 171.238C211.686 170.945 211.467 170.588 211.311 170.166C211.158 169.744 211.082 169.26 211.082 168.713C211.082 167.986 211.211 167.371 211.469 166.867C211.727 166.363 212.094 165.98 212.57 165.719C213.047 165.457 213.615 165.326 214.275 165.326C214.889 165.326 215.432 165.457 215.904 165.719C216.381 165.98 216.754 166.363 217.023 166.867C217.297 167.371 217.434 167.986 217.434 168.713ZM212.904 168.713C212.904 169.143 212.951 169.504 213.045 169.797C213.139 170.09 213.285 170.311 213.484 170.459C213.684 170.607 213.943 170.682 214.264 170.682C214.58 170.682 214.836 170.607 215.031 170.459C215.23 170.311 215.375 170.09 215.465 169.797C215.559 169.504 215.605 169.143 215.605 168.713C215.605 168.279 215.559 167.92 215.465 167.635C215.375 167.346 215.23 167.129 215.031 166.984C214.832 166.84 214.572 166.768 214.252 166.768C213.779 166.768 213.436 166.93 213.221 167.254C213.01 167.578 212.904 168.064 212.904 168.713ZM220.697 162.883V165.004C220.697 165.25 220.689 165.494 220.674 165.736C220.662 165.979 220.646 166.166 220.627 166.299H220.697C220.869 166.029 221.104 165.801 221.4 165.613C221.697 165.422 222.082 165.326 222.555 165.326C223.289 165.326 223.885 165.613 224.342 166.188C224.799 166.762 225.027 167.604 225.027 168.713C225.027 169.459 224.922 170.086 224.711 170.594C224.5 171.098 224.205 171.479 223.826 171.736C223.447 171.99 223.008 172.117 222.508 172.117C222.027 172.117 221.648 172.031 221.371 171.859C221.094 171.688 220.869 171.494 220.697 171.279H220.574L220.275 172H218.91V162.883H220.697ZM221.98 166.756C221.668 166.756 221.42 166.82 221.236 166.949C221.053 167.078 220.918 167.271 220.832 167.529C220.75 167.787 220.705 168.113 220.697 168.508V168.701C220.697 169.338 220.791 169.826 220.979 170.166C221.166 170.502 221.508 170.67 222.004 170.67C222.371 170.67 222.662 170.5 222.877 170.16C223.096 169.82 223.205 169.33 223.205 168.689C223.205 168.049 223.096 167.566 222.877 167.242C222.658 166.918 222.359 166.756 221.98 166.756ZM228.291 165.449V172H226.504V165.449H228.291ZM227.4 162.883C227.666 162.883 227.895 162.945 228.086 163.07C228.277 163.191 228.373 163.42 228.373 163.756C228.373 164.088 228.277 164.318 228.086 164.447C227.895 164.572 227.666 164.635 227.4 164.635C227.131 164.635 226.9 164.572 226.709 164.447C226.521 164.318 226.428 164.088 226.428 163.756C226.428 163.42 226.521 163.191 226.709 163.07C226.9 162.945 227.131 162.883 227.4 162.883ZM232.873 165.326C233.479 165.326 234 165.443 234.438 165.678C234.875 165.908 235.213 166.244 235.451 166.686C235.689 167.127 235.809 167.666 235.809 168.303V169.17H231.584C231.604 169.674 231.754 170.07 232.035 170.359C232.32 170.645 232.715 170.787 233.219 170.787C233.637 170.787 234.02 170.744 234.367 170.658C234.715 170.572 235.072 170.443 235.439 170.271V171.654C235.115 171.814 234.775 171.932 234.42 172.006C234.068 172.08 233.641 172.117 233.137 172.117C232.48 172.117 231.898 171.996 231.391 171.754C230.887 171.512 230.49 171.143 230.201 170.646C229.916 170.15 229.773 169.525 229.773 168.771C229.773 168.006 229.902 167.369 230.16 166.861C230.422 166.35 230.785 165.967 231.25 165.713C231.715 165.455 232.256 165.326 232.873 165.326ZM232.885 166.598C232.537 166.598 232.248 166.709 232.018 166.932C231.791 167.154 231.66 167.504 231.625 167.98H234.133C234.129 167.715 234.08 167.479 233.986 167.271C233.896 167.064 233.76 166.9 233.576 166.779C233.396 166.658 233.166 166.598 232.885 166.598ZM240.021 170.693C240.217 170.693 240.406 170.674 240.59 170.635C240.777 170.596 240.963 170.547 241.146 170.488V171.818C240.955 171.904 240.717 171.975 240.432 172.029C240.15 172.088 239.842 172.117 239.506 172.117C239.115 172.117 238.764 172.055 238.451 171.93C238.143 171.801 237.898 171.58 237.719 171.268C237.543 170.951 237.455 170.512 237.455 169.949V166.791H236.6V166.035L237.584 165.438L238.1 164.055H239.242V165.449H241.076V166.791H239.242V169.949C239.242 170.199 239.312 170.387 239.453 170.512C239.598 170.633 239.787 170.693 240.021 170.693ZM245.236 170.693C245.432 170.693 245.621 170.674 245.805 170.635C245.992 170.596 246.178 170.547 246.361 170.488V171.818C246.17 171.904 245.932 171.975 245.646 172.029C245.365 172.088 245.057 172.117 244.721 172.117C244.33 172.117 243.979 172.055 243.666 171.93C243.357 171.801 243.113 171.58 242.934 171.268C242.758 170.951 242.67 170.512 242.67 169.949V166.791H241.814V166.035L242.799 165.438L243.314 164.055H244.457V165.449H246.291V166.791H244.457V169.949C244.457 170.199 244.527 170.387 244.668 170.512C244.812 170.633 245.002 170.693 245.236 170.693ZM249.479 165.449V172H247.691V165.449H249.479ZM248.588 162.883C248.854 162.883 249.082 162.945 249.273 163.07C249.465 163.191 249.561 163.42 249.561 163.756C249.561 164.088 249.465 164.318 249.273 164.447C249.082 164.572 248.854 164.635 248.588 164.635C248.318 164.635 248.088 164.572 247.896 164.447C247.709 164.318 247.615 164.088 247.615 163.756C247.615 163.42 247.709 163.191 247.896 163.07C248.088 162.945 248.318 162.883 248.588 162.883ZM252.918 172L250.422 165.449H252.291L253.557 169.182C253.627 169.4 253.682 169.631 253.721 169.873C253.764 170.115 253.793 170.332 253.809 170.523H253.855C253.867 170.316 253.895 170.096 253.938 169.861C253.984 169.627 254.045 169.4 254.119 169.182L255.379 165.449H257.248L254.752 172H252.918ZM264.145 168.713C264.145 169.26 264.07 169.744 263.922 170.166C263.777 170.588 263.564 170.945 263.283 171.238C263.006 171.527 262.67 171.746 262.275 171.895C261.885 172.043 261.443 172.117 260.951 172.117C260.49 172.117 260.066 172.043 259.68 171.895C259.297 171.746 258.963 171.527 258.678 171.238C258.396 170.945 258.178 170.588 258.021 170.166C257.869 169.744 257.793 169.26 257.793 168.713C257.793 167.986 257.922 167.371 258.18 166.867C258.438 166.363 258.805 165.98 259.281 165.719C259.758 165.457 260.326 165.326 260.986 165.326C261.6 165.326 262.143 165.457 262.615 165.719C263.092 165.98 263.465 166.363 263.734 166.867C264.008 167.371 264.145 167.986 264.145 168.713ZM259.615 168.713C259.615 169.143 259.662 169.504 259.756 169.797C259.85 170.09 259.996 170.311 260.195 170.459C260.395 170.607 260.654 170.682 260.975 170.682C261.291 170.682 261.547 170.607 261.742 170.459C261.941 170.311 262.086 170.09 262.176 169.797C262.27 169.504 262.316 169.143 262.316 168.713C262.316 168.279 262.27 167.92 262.176 167.635C262.086 167.346 261.941 167.129 261.742 166.984C261.543 166.84 261.283 166.768 260.963 166.768C260.49 166.768 260.146 166.93 259.932 167.254C259.721 167.578 259.615 168.064 259.615 168.713ZM268.158 169.516V168.051H271.305V169.516H268.158ZM281.447 170.225H280.416V172H278.646V170.225H274.99V168.965L278.746 163.434H280.416V168.818H281.447V170.225ZM278.646 168.818V167.365C278.646 167.229 278.648 167.066 278.652 166.879C278.66 166.691 278.668 166.504 278.676 166.316C278.684 166.129 278.691 165.963 278.699 165.818C278.711 165.67 278.719 165.566 278.723 165.508H278.676C278.602 165.668 278.523 165.824 278.441 165.977C278.359 166.125 278.264 166.281 278.154 166.445L276.584 168.818H278.646Z" fill="white"/>
                        <defs>
                            <filter id="filter0_f_402_3261" x="8.50293" y="14.4316" width="315.049" height="173.922" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                                <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                                <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
                                <feGaussianBlur stdDeviation="4" result="effect1_foregroundBlur_402_3261"/>
                            </filter>
                            <filter id="filter1_d_402_3261" x="295.45" y="158.314" width="31.1006" height="31.1006" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                                <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                                <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                                <feOffset dy="4.31447"/>
                                <feGaussianBlur stdDeviation="4"/>
                                <feComposite in2="hardAlpha" operator="out"/>
                                <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
                                <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_402_3261"/>
                                <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_402_3261" result="shape"/>
                            </filter>
                            <filter id="filter2_d_402_3261" x="2.44971" y="7.31447" width="31.1006" height="31.1006" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                                <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                                <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                                <feOffset dy="4.31447"/>
                                <feGaussianBlur stdDeviation="4"/>
                                <feComposite in2="hardAlpha" operator="out"/>
                                <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
                                <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_402_3261"/>
                                <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_402_3261" result="shape"/>
                            </filter>
                            <filter id="filter3_d_402_3261" x="109" y="88.3145" width="31.1006" height="31.1006" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                                <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                                <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                                <feOffset dy="4.31447"/>
                                <feGaussianBlur stdDeviation="4"/>
                                <feComposite in2="hardAlpha" operator="out"/>
                                <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
                                <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_402_3261"/>
                                <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_402_3261" result="shape"/>
                            </filter>
                            <filter id="filter4_d_402_3261" x="79" y="53" width="92" height="47.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                                <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                                <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                                <feOffset dy="4"/>
                                <feGaussianBlur stdDeviation="4"/>
                                <feComposite in2="hardAlpha" operator="out"/>
                                <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.16 0"/>
                                <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_402_3261"/>
                                <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_402_3261" result="shape"/>
                            </filter>
                            <linearGradient id="paint0_linear_402_3261" x1="18.4497" y1="178.999" x2="313.45" y2="178.999" gradientUnits="userSpaceOnUse">
                                <stop stop-color="#FF574C"/>
                                <stop offset="0.450362" stop-color="#F5E088"/>
                                <stop offset="1" stop-color="#1998CD"/>
                            </linearGradient>
                            <linearGradient id="paint1_linear_402_3261" x1="18.4497" y1="171.999" x2="313.45" y2="171.999" gradientUnits="userSpaceOnUse">
                                <stop stop-color="#FF574C"/>
                                <stop offset="0.450362" stop-color="#F5E088"/>
                                <stop offset="1" stop-color="#1998CD"/>
                            </linearGradient>
                        </defs>
                    </svg>
                    <svg id="tail-selector" class="tail-tooltip tail-tooltip--is-reactivity" width="12" height="7" viewBox="0 0 12 7" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M4.95698 6.31047L0.75 0L11.25 0L6.59544 6.34713C6.18473 6.90719 5.34222 6.88833 4.95698 6.31047Z" fill="#16191E"/>
                    </svg>
                    <div class="quiz__graph-svg-date-wrapp">
                        <p class="quiz__graph-svg-date">
                            Ora
                        </p>
                        <p class="quiz__graph-svg-date" id="monthGraph1">
                            mese 1
                        </p>
                        <p class="quiz__graph-svg-date" id="monthGraph2">
                            mese 2
                        </p>
                        <p class="quiz__graph-svg-date" id="monthGraph3">
                            mese 3
                        </p>
                    </div>
                </div>
                <div class="quiz__graph-svg-text-box quiz__graph-svg-text-box--is-reactivity" id="tooltip-selector">
                    <p class="quiz__graph-svg-text">
                        <span class="important-event-placeholder"></span> - 6
                    </p>
                </div>
            </div>
            <p class="quiz__graph-subtext compl">
                Questo grafico mostra il tuo potenziale progresso se segui tutti i passaggi elencati nel nostro piano
            </p>
        </div>
        <div class="quiz__btn-box">
            <button class="quiz__btn js-btn" id="{{ blockId }}">
                <span></span>
                Continuare
            </button>
        </div>
    </div>
</div>
