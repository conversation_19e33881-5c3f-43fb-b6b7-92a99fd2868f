<div class="container quiz__index" id="block{{ blockId }}" data-id="{{ blockId }}" data-header-hide="Profile">
    <div class="quiz__box">
        <h2 class="quiz__title">
            O reforço no treino de cães envolve o uso de recompensas ou consequências para encorajar comportamentos
            desejados
        </h2>
        <p class="quiz__subtitle">
            Isto pode incluir comportamentos gratificantes que quer ver com mais frequência, como sentar-se, ficar ou
            vir quando é chamado. O reforço pode ser:
        </p>

        <div class="quiz__content-container--small">
            <ul>
                <li class="quiz__dog-overreact-item">
                    <img loading="lazy" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1719329297/dog-training/img/reactivity/plus.png" alt="plus icon" class="quiz__dog-overreact-image">
                    <div class="quiz__dog-overreact-item-content">
                        <p class="quiz__dog-overreact-item-content-title">Positivo</p>
                        <ul class="quiz__dog-overreact-item-content-list">
                            <li>guloseimas</li>
                            <li>elogiar</li>
                            <li>hora de brincar</li>
                        </ul>
                    </div>
                </li>
                <li class="quiz__dog-overreact-item">
                    <img loading="lazy" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1719329260/dog-training/img/reactivity/minus.png" alt="minus icon" class="quiz__dog-overreact-image">
                    <div class="quiz__dog-overreact-item-content">
                        <p class="quiz__dog-overreact-item-content-title">Negativo</p>
                        <ul class="quiz__dog-overreact-item-content-list">
                            <li>removendo a pressão</li>
                            <li>cessando a correção</li>
                        </ul>
                    </div>
                </li>
                <li class="quiz__dog-overreact-item">
                    <img loading="lazy" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1719329222/dog-training/img/reactivity/divide.png" alt="divide icon" class="quiz__dog-overreact-image">
                    <div class="quiz__dog-overreact-item-content">
                        <p class="quiz__dog-overreact-item-content-title">Neutro</p>
                        <ul class="quiz__dog-overreact-item-content-list">
                            <li>ignorando comportamentos</li>
                            <li>respostas de rotina</li>
                        </ul>
                    </div>
                </li>
            </ul>
        </div>
    </div>
    <button class="quiz__btn quiz__range-btn js-btn" id="{{ blockId }}">
        <span></span>
        Continuar
    </button>
</div>
