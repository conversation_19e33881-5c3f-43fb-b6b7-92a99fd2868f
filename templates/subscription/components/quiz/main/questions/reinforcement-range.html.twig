<div class="container quiz__index" id="block{{ blockId }}" data-id="{{ blockId }}" >
    <div class="quiz__box">
        <h2 class="quiz__title">
            I'm not sure whether I'm reinforcing my <span class="puppyInsertSelector" data-puppy="pup">dog</span>’s behavior correctly
        </h2>
        <p class="quiz__subtitle">
            Do you relate to the statement?
        </p>
        <div class="quiz__range amplitude-analytic" data-question="I'm not sure whether I'm reinforcing dog's behavior" data-type="range-slider" data-selector="range-choice" data-response-type="not-sure-dog-is-reactive">
            <div class="quiz__range-value-box">
                <p class="quiz__range-value ">
                </p>
            </div>
            <div class="quiz__range-wrapper">
                <ul class="quiz__range-numbers">
                    <li class="quiz__range-numbers-item">
                        0
                    </li>
                    <li class="quiz__range-numbers-item">
                        1
                    </li>
                    <li class="quiz__range-numbers-item">
                        2
                    </li>
                    <li class="quiz__range-numbers-item">
                        3
                    </li>
                    <li class="quiz__range-numbers-item">
                        4
                    </li>
                    <li class="quiz__range-numbers-item">
                        5
                    </li>
                    <li class="quiz__range-numbers-item">
                        6
                    </li>
                    <li class="quiz__range-numbers-item">
                        7
                    </li>
                    <li class="quiz__range-numbers-item">
                        8
                    </li>
                    <li class="quiz__range-numbers-item">
                        9
                    </li>
                    <li class="quiz__range-numbers-item">
                        10
                    </li>
                </ul>
                <div class="quiz__range-slider">
                    <input class="rangeSlider quiz__range-disabled-tooltip range-default" type="range" min="0" max="10" step="1" value="5">
                    <div class='quiz__range-slider-progress'></div>
                </div>
                <ul class="quiz__range-box">
                    <li class="quiz__range-box-text">
                        Not at all
                    </li>
                    <li class="quiz__range-box-text">
                        Strongly
                    </li>
                </ul>
            </div>
        </div>
    </div>
    <button class="quiz__btn quiz__range-btn js-btn" id="{{ blockId }}">
        <span></span>
        Next step
    </button>
</div>
