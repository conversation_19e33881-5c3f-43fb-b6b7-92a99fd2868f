<div class="quiz__index profile" id="block{{ blockId }}" data-id="{{ blockId }}" data-header-hide="Profile" data-animation="Profile Animation">
    <div class="container">
        <h2 class="quiz__title">
            Here’s <span class="nameInsertSelector">your dog</span>’s obedience profile
        </h2>
        <div class="quiz__profile heightFix" id="quizProfile">
            <div class="quiz__profile-title-wrapp">
                <h3 class="quiz__profile-title">
                    <span class="nameInsertSelector">Dog</span> Obedience Level
                </h3>
                <p class="quiz__profile-level">
                    NORMAL - 10
                </p>
            </div>
            <div class="quiz__profile-svg-box">
                <svg class="quiz__profile-graph" width="311" height="40" viewBox="0 0 311 40" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M311 15.5C311 13.567 309.433 12 307.5 12H3.5C1.567 12 0 13.567 0 15.5C0 17.433 1.567 19 3.5 19H307.5C309.433 19 311 17.433 311 15.5Z" fill="url(#paint0_linear_2819_19503)"/>
                    <g filter="url(#filter0_d_2819_19503)" class="range-dot" id="rangeDot">
                        <circle cx="100" cy="15" r="7" fill="white"/>
                        <circle cx="100" cy="15" r="8" stroke="#E6E6E6" stroke-width="2"/>
                    </g>
                    <path d="M0.568 35V29.36H4.432V30.328H1.752V31.648H4.256V32.624H1.752V34.032H4.432V35H0.568ZM4.70031 35L6.85231 32.144L4.76431 29.36H6.20431L7.56431 31.24L8.91631 29.36H10.3643L8.26831 32.144L10.4203 35H8.97231L7.55631 33.056L6.15631 35H4.70031ZM12.2089 35V30.384H10.3449V29.36H15.3129V30.384H13.4489V35H12.2089ZM15.9196 35V29.36H18.4956C19.1249 29.36 19.6102 29.512 19.9516 29.816C20.2929 30.12 20.4636 30.544 20.4636 31.088C20.4636 31.5093 20.3542 31.8613 20.1356 32.144C19.9222 32.4213 19.6156 32.6107 19.2156 32.712C19.4982 32.7973 19.7302 33.0027 19.9116 33.328L20.8236 35H19.4556L18.4796 33.2C18.3996 33.056 18.3009 32.9573 18.1836 32.904C18.0662 32.8453 17.9276 32.816 17.7676 32.816H17.1596V35H15.9196ZM17.1596 31.92H18.2716C18.9382 31.92 19.2716 31.6533 19.2716 31.12C19.2716 30.592 18.9382 30.328 18.2716 30.328H17.1596V31.92ZM21.4899 35V29.36H25.3539V30.328H22.6739V31.648H25.1779V32.624H22.6739V34.032H25.3539V35H21.4899ZM26.3496 35V29.36H27.3816L29.2856 32.824L31.1816 29.36H32.1976V35H31.0696V31.56L29.6136 34.152H28.9176L27.4696 31.584V35H26.3496ZM33.3414 35V29.36H37.2054V30.328H34.5254V31.648H37.0294V32.624H34.5254V34.032H37.2054V35H33.3414ZM38.1852 35V29.36H39.4252V33.952H42.0332V35H38.1852ZM43.154 35V32.456L40.978 29.36H42.418L43.794 31.344L45.17 29.36H46.554L44.394 32.464V35H43.154ZM49.0914 35V29.36H50.3314V33.952H52.9394V35H49.0914ZM55.9809 35.088C55.4262 35.088 54.9382 34.968 54.5169 34.728C54.1009 34.4827 53.7755 34.144 53.5409 33.712C53.3115 33.2747 53.1969 32.7627 53.1969 32.176C53.1969 31.5893 53.3115 31.08 53.5409 30.648C53.7755 30.2107 54.1009 29.872 54.5169 29.632C54.9329 29.392 55.4209 29.272 55.9809 29.272C56.5409 29.272 57.0289 29.392 57.4449 29.632C57.8662 29.872 58.1915 30.2107 58.4209 30.648C58.6555 31.08 58.7729 31.5867 58.7729 32.168C58.7729 32.7547 58.6555 33.2667 58.4209 33.704C58.1915 34.1413 57.8662 34.4827 57.4449 34.728C57.0289 34.968 56.5409 35.088 55.9809 35.088ZM55.9809 34.032C56.4502 34.032 56.8155 33.8693 57.0769 33.544C57.3435 33.2133 57.4769 32.7573 57.4769 32.176C57.4769 31.5893 57.3462 31.136 57.0849 30.816C56.8235 30.4907 56.4555 30.328 55.9809 30.328C55.5169 30.328 55.1515 30.4907 54.8849 30.816C54.6235 31.136 54.4929 31.5893 54.4929 32.176C54.4929 32.7573 54.6235 33.2133 54.8849 33.544C55.1515 33.8693 55.5169 34.032 55.9809 34.032ZM61.1596 35L59.1516 29.36H60.4236L61.7196 33.216L63.0876 29.36H63.9836L65.3116 33.28L66.6556 29.36H67.8556L65.8316 35H64.8236L63.5036 31.256L62.1676 35H61.1596Z"
                          fill="#CCCCCC"/>
                    <path d="M124.568 35V29.36H125.808V33.952H128.416V35H124.568ZM131.457 35.088C130.903 35.088 130.415 34.968 129.993 34.728C129.577 34.4827 129.252 34.144 129.017 33.712C128.788 33.2747 128.673 32.7627 128.673 32.176C128.673 31.5893 128.788 31.08 129.017 30.648C129.252 30.2107 129.577 29.872 129.993 29.632C130.409 29.392 130.897 29.272 131.457 29.272C132.017 29.272 132.505 29.392 132.921 29.632C133.343 29.872 133.668 30.2107 133.897 30.648C134.132 31.08 134.249 31.5867 134.249 32.168C134.249 32.7547 134.132 33.2667 133.897 33.704C133.668 34.1413 133.343 34.4827 132.921 34.728C132.505 34.968 132.017 35.088 131.457 35.088ZM131.457 34.032C131.927 34.032 132.292 33.8693 132.553 33.544C132.82 33.2133 132.953 32.7573 132.953 32.176C132.953 31.5893 132.823 31.136 132.561 30.816C132.3 30.4907 131.932 30.328 131.457 30.328C130.993 30.328 130.628 30.4907 130.361 30.816C130.1 31.136 129.969 31.5893 129.969 32.176C129.969 32.7573 130.1 33.2133 130.361 33.544C130.628 33.8693 130.993 34.032 131.457 34.032ZM136.636 35L134.628 29.36H135.9L137.196 33.216L138.564 29.36H139.46L140.788 33.28L142.132 29.36H143.332L141.308 35H140.3L138.98 31.256L137.644 35H136.636Z"
                          fill="#16191E"/>
                    <path d="M200.584 35V29.36H201.616L203.52 32.824L205.416 29.36H206.432V35H205.304V31.56L203.848 34.152H203.152L201.704 31.584V35H200.584ZM207.576 35V29.36H211.44V30.328H208.76V31.648H211.264V32.624H208.76V34.032H211.44V35H207.576ZM212.42 35V29.36H214.628C215.588 29.36 216.332 29.6053 216.86 30.096C217.388 30.5813 217.652 31.2747 217.652 32.176C217.652 33.0773 217.388 33.7733 216.86 34.264C216.332 34.7547 215.588 35 214.628 35H212.42ZM213.66 33.984H214.556C215.756 33.984 216.356 33.3813 216.356 32.176C216.356 30.976 215.756 30.376 214.556 30.376H213.66V33.984ZM218.615 35V29.36H219.855V35H218.615ZM223.398 35.088C222.598 35.088 221.987 34.8827 221.566 34.472C221.15 34.0613 220.942 33.4507 220.942 32.64V29.36H222.182V32.648C222.182 33.1067 222.286 33.4533 222.494 33.688C222.702 33.9173 223.003 34.032 223.398 34.032C224.203 34.032 224.606 33.5707 224.606 32.648V29.36H225.83V32.64C225.83 33.4507 225.625 34.0613 225.214 34.472C224.803 34.8827 224.198 35.088 223.398 35.088ZM226.943 35V29.36H227.975L229.879 32.824L231.775 29.36H232.791V35H231.663V31.56L230.207 34.152H229.511L228.063 31.584V35H226.943Z"
                          fill="#CCCCCC"/>
                    <path d="M290.568 35V29.36H291.808V31.624H294.432V29.36H295.672V35H294.432V32.656H291.808V35H290.568ZM296.81 35V29.36H298.05V35H296.81ZM301.969 35.088C301.335 35.088 300.796 34.968 300.353 34.728C299.916 34.488 299.583 34.152 299.353 33.72C299.124 33.288 299.009 32.7813 299.009 32.2C299.009 31.6027 299.127 31.0853 299.361 30.648C299.601 30.2107 299.945 29.872 300.393 29.632C300.841 29.392 301.38 29.272 302.009 29.272C302.404 29.272 302.777 29.328 303.129 29.44C303.487 29.552 303.777 29.7013 304.001 29.888L303.601 30.856C303.356 30.6747 303.105 30.5467 302.849 30.472C302.593 30.392 302.316 30.352 302.017 30.352C301.447 30.352 301.017 30.512 300.729 30.832C300.447 31.1467 300.305 31.6027 300.305 32.2C300.305 32.8027 300.449 33.2587 300.737 33.568C301.031 33.8773 301.463 34.032 302.033 34.032C302.337 34.032 302.657 33.984 302.993 33.888V32.784H301.777V31.888H304.017V34.656C303.74 34.7947 303.42 34.9013 303.057 34.976C302.695 35.0507 302.332 35.088 301.969 35.088ZM305.115 35V29.36H306.355V31.624H308.979V29.36H310.219V35H308.979V32.656H306.355V35H305.115Z"
                          fill="#CCCCCC"/>
                    <defs>
                        <filter id="filter0_d_2819_19503" x="81" y="0" width="38" height="38" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                            <feOffset dy="4"/>
                            <feGaussianBlur stdDeviation="5"/>
                            <feComposite in2="hardAlpha" operator="out"/>
                            <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
                            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2819_19503"/>
                            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2819_19503" result="shape"/>
                        </filter>
                        <linearGradient id="paint0_linear_2819_19503" x1="311" y1="19" x2="0" y2="19" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#1998CD"/>
                            <stop offset="0.549638" stop-color="#F5E088"/>
                            <stop offset="1" stop-color="#FF574C"/>
                        </linearGradient>
                    </defs>
                </svg>
                <svg id="obedienceTail" class="tail" width="12" height="7" viewBox="0 0 12 7" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M4.95698 6.31047L0.75 0L11.25 0L6.59544 6.34713C6.18473 6.90719 5.34222 6.88833 4.95698 6.31047Z" fill="#16191E"/>
                </svg>
                <div class="quiz__profile-svg-text-box" id="obediencePopup">
                    <p class="quiz__profile-svg-text">
                        <span class="nameInsertSelector">obedience</span> - 4
                    </p>
                </div>
            </div>
            <div class="quiz__profile-alert">
                <div class="quiz__profile-alert-icon-wrapp">
                    <img loading="lazy" class="quiz__profile-alert-icon" src="https://images.paw-champ.com/landings/behavioral_problems.png" alt="">
                </div>
                <div class="quiz__profile-alert-text-wrapp">
                    <h4 class="quiz__profile-alert-title">
                        Low Obedience Level:
                    </h4>
                    <p class="quiz__profile-alert-text">
                        A low obedience level can lead to difficulties in training, poor socialization, increased risk of behavior problems, and a reduced quality of life for the dog and the owner.
                    </p>
                </div>
            </div>
            <div class="quiz__profile-list-item">
                <div class="quiz__profile-list-icon-wrapp">
                    <img loading="lazy" class="quiz__profile-list-icon" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1684941074/dog-training/icons/better/77.png" alt="">
                </div>
                <div class="quiz__profile-list-text-wrapp">
                    <h4 class="quiz__profile-list-title">
                        Behavioral Issues
                    </h4>
                    <div class="problemsInsertSelector">
                    </div>
                </div>
            </div>
            <div class="quiz__profile-list-item">
                <div class="quiz__profile-list-icon-wrapp">
                    <img loading="lazy" class="quiz__profile-list-icon" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1686214728/dog-training/icons/better/111.png" alt="">
                </div>
                <div class="quiz__profile-list-text-wrapp">
                    <h4 class="quiz__profile-list-title">
                        Current behavior correction
                    </h4>
                    <div class="behaviorInsertSelector">
                    </div>
                </div>
            </div>
            <div class="quiz__profile-list-wrapp">
                <ul class="quiz__profile-list">

                    <li class="quiz__profile-list-item">
                        <div class="quiz__profile-list-icon-wrapp">
                            <img loading="lazy" class="quiz__profile-alert-icon" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1684941074/dog-training/icons/better/78.png" alt="">
                        </div>
                        <div class="quiz__profile-list-text-wrapp">
                            <h4 class="quiz__profile-list-title">
                                Responsiveness
                            </h4>
                            <p class="quiz__profile-list-text">
                                Low
                            </p>
                        </div>
                    </li>
                    <li class="quiz__profile-list-item">
                        <div class="quiz__profile-list-icon-wrapp">
                            <img loading="lazy" class="quiz__profile-alert-icon" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1684941074/dog-training/icons/better/80.png" alt="">
                        </div>
                        <div class="quiz__profile-list-text-wrapp">
                            <h4 class="quiz__profile-list-title">
                                Dog-Owner Bond
                            </h4>
                            <p class="quiz__profile-list-text">
                                Weak
                            </p>
                        </div>
                    </li>
                </ul>
                {% if app.request.query.has('ml_img') %}
                    <div class="quiz__profile-man-img-wrapp" id="profileImg">
                        <img loading="lazy" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1689334575/dog-training/img/man-quiz-profile.png" alt="" class="quiz__profile-man-img">
                    </div>
                {% else %}
                    <div class="quiz__profile-girl-img-wrapp" id="profileImg">
                        <picture>
                            <source srcset="https://res.cloudinary.com/dr0cx27xo/image/upload/v1718626944/dog-training/img/quiz/better/girl.webp" type="image/webp">
                            <img loading="lazy" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1685013727/dog-training/img/better/girl.png" alt="girl with puppies" class="quiz__profile-girl-img">
                        </picture>
                    </div>
                {% endif %}
                <div class="quiz__profile-arrow-box" id="profileArrow">
                    <svg width="77" height="185" viewBox="0 0 77 185" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path class="quiz__profile-arrow" d="M74.5 183L30.8244 104.031C27.1952 97.4691 27.1582 89.5107 30.7261 82.9153L74.5 2" stroke="#1998CD" stroke-opacity="0.13" stroke-width="4" stroke-linecap="round"/>
                        <path class="quiz__profile-arrow" d="M62 183L18.3244 104.031C14.6952 97.4691 14.6582 89.5107 18.2261 82.9153L62 2" stroke="#1998CD" stroke-opacity="0.13" stroke-width="4" stroke-linecap="round"/>
                        <path class="quiz__profile-arrow" d="M49.5 183L5.82436 104.031C2.19519 97.4691 2.15817 89.5107 5.72613 82.9153L49.5 2" stroke="#1998CD" stroke-opacity="0.13" stroke-width="4" stroke-linecap="round"/>
                    </svg>
                </div>
            </div>
        </div>
        <div class="quiz__btn-box">
            <button class="quiz__btn js-btn" id="{{ blockId }}">
                <span></span>
                Continue
            </button>
        </div>
    </div>
</div>
