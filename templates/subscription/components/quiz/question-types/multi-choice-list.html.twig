{% set title = title ?? '' %}
{% set subtitle = subtitle ?? 'Choose all that apply:' %}
{% set submitButtonTitle = submitButtonTitle ?? 'Next step' %}
{% set checkboxHidden = checkboxHidden ?? false %}
{% set questionName = questionName ?? '' %}
{% set responseType = responseType ?? '' %}
{% set hasResetItem = hasResetItem ?? false %}
{% set runtimeLocalization = runtimeLocalization ?? false %}

<div class="container quiz__index"
     id="block{{ blockId }}"
     data-id="{{ blockId }}"
     {% if hasResetItem %}data-remove-checkboxes="remove"{% endif %}
>
    <div class="quiz__box">
        <h2 class="quiz__title"
            {% if runtimeLocalization %}data-i18n="{{ title }}"{% endif %}
        >
            {% block title %}
                {{ title }}
            {% endblock %}
        </h2>
        <p class="quiz__subtitle"
           {% if runtimeLocalization %}data-i18n="{{ subtitle }}"{% endif %}
        >
            {{ subtitle }}
        </p>
        <div class="quiz__multiple button-list left-align amplitude-analytic"
             data-type="multiple-checkbox"
             data-selector="multi-choice"
             data-question="{{ questionName }}"
             data-response-type="{{ responseType }}"
        >
            {% for answer in answers %}
                {% if answer.isSeparator|default(false) %}
                    <div class="quiz__multiple-item-hr"></div>
                {% else %}
                    {% include 'subscription/components/quiz/question-types/multi-choise-list-answer.html.twig' with {
                        answer,
                        checkboxHidden,
                        runtimeLocalization,
                    } %}
                {% endif %}
            {% endfor %}
        </div>
        <button class="quiz__btn quiz__btn-active js-btn" id="{{ blockId }}">
            <span></span>
            {% if runtimeLocalization %}
                <span data-i18n="{{ submitButtonTitle }}"></span>
            {% else %}
                {{ submitButtonTitle }}
            {% endif %}
        </button>
    </div>
</div>
