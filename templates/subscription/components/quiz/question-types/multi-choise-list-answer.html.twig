{% set iconSrc = answer.iconSrc ?? null %}
{% set iconAlt = answer.iconAlt ?? null %}
{% set title = answer.title ?? null %}
{% set value = answer.value ?? title %}
{% set isResetItem = answer.isResetItem ?? false %}
{% set runtimeLocalization = runtimeLocalization ?? false %}

<div class="quiz__multiple-item {% if isResetItem %}removeCheckboxesBtn{% endif %}">
    <div class="quiz__multiple-box">
        {% if iconSrc %}
            <img loading="lazy"
                 src="{{ iconSrc }}"
                 {% if iconAlt %}alt="{{ iconAlt }}"{% endif %}
                 class="quiz__multiple-item-icon"
            >
        {% endif %}

        <span class="quiz__multiple-text small fix-width"
              {% if runtimeLocalization %}data-i18n="{{ title }}"{% endif %}
        >
            {{ title|raw }}
        </span>
        <input class="quiz__multiple-checkbox"
               type="checkbox"
               data-question-value="{{ value }}"
        >
    </div>
    {% if not checkboxHidden %}
        <span class="quiz__multiple-dot"></span>
    {% endif %}
</div>
