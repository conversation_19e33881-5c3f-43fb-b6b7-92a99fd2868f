{% set title = title ?? '' %}
{% set subtitle = subtitle ?? '' %}
{% set textCentered = textCentered ?? false %}
{% set questionName = questionName ?? '' %}
{% set responseType = responseType ?? '' %}
{% set runtimeLocalization = runtimeLocalization ?? false %}

<div class="container quiz__index" id="block{{ blockId }}" data-id="{{ blockId }}">
    <div class="quiz__box">
        <h2 class="quiz__title"
            {% if runtimeLocalization %}data-i18n="{{ title }}"{% endif %}
        >
            {% block title %}
                {{ title }}
            {% endblock %}
        </h2>
        {% if subtitle %}
            <p class="quiz__subtitle"
               {% if runtimeLocalization %}data-i18n="{{ subtitle }}"{% endif %}
            >
                {{ subtitle }}
            </p>
        {% endif %}
        <ul class="quiz__answer button-list amplitude-analytic"
            data-type="one-answer"
            data-selector="one-choice"
            data-question="{{ questionName }}"
            data-response-type="{{ responseType }}"
        >
            {% for answer in answers %}
                {% include 'subscription/components/quiz/question-types/single-choise-list-answer.html.twig' with {
                    answer,
                    textCentered,
                    runtimeLocalization,
                } %}
            {% endfor %}
        </ul>
    </div>
</div>
