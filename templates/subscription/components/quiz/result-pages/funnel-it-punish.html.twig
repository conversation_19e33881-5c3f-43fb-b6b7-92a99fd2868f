<div class="quiz__index quiz__result skipQuiz magic" id="block{{ blockId }}" data-id="{{ blockId }}" data-result="reviews">
    <div class="container " id="resultStepOne">
        <div id="circleBar" class="progressbar reviews">
            <div class="progressbar__shadow"></div>
        </div>
        <p class="quiz__reviews-loading-subtitle" id="replaceSubtitle">
            Creando il piano di addestramento personalizzato per <span class="nameInsertSelector">il tuo cane</span>...
        </p>
        <div class="quiz__hr">
        </div>
        <div class="quiz__reviews-loading-title-red-box">
            <h3 class="quiz__reviews-loading-title-red">
                115 mila proprietari di cani
            </h3>
        </div>
        <p class="quiz__reviews-loading-text">
            hanno scelto PawChamp
        </p>
        <div class="quiz__reviews-slider custom-result">
            <ul class="swiper-wrapper">
                <li class="quiz__reviews-slider-item swiper-slide">
                    <div class="quiz__reviews-slider-item-text-wrapp">
                        <svg width="90" height="16" viewBox="0 0 90 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M9.46744 6.37307H14.2218L10.3768 9.16159L10.3767 9.16159L8.00391 10.8849L10.7058 10.1835L10.3789 9.16822L11.8403 13.6821L7.99524 10.8849L4.15019 13.6821L5.62239 9.16159L1.77734 6.36441L6.53169 6.37307L8.0039 1.85254L9.46744 6.37307Z"
                                  fill="white"/>
                            <rect x="18.3711" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M27.8385 6.37307H32.5929L28.7478 9.16159L28.7478 9.16159L26.375 10.8849L29.0769 10.1835L28.75 9.16822L30.2114 13.6821L26.3663 10.8849L22.5213 13.6821L23.9935 9.16159L20.1484 6.36441L24.9028 6.37307L26.375 1.85254L27.8385 6.37307Z"
                                  fill="white"/>
                            <rect x="36.7402" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M46.2077 6.37307H50.962L47.117 9.16159L47.117 9.16159L44.7441 10.8849L47.4461 10.1835L47.1191 9.16822L48.5805 13.6821L44.7355 10.8849L40.8904 13.6821L42.3626 9.16159L38.5176 6.36441L43.2719 6.37307L44.7441 1.85254L46.2077 6.37307Z"
                                  fill="white"/>
                            <rect x="55.1113" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M64.5788 6.37307H69.3331L65.4881 9.16159L65.4883 9.16217L63.1162 10.8849L65.8181 10.1835L65.6738 9.7352L66.9516 13.6821L63.1066 10.8849L59.2615 13.6821L60.7337 9.16159L56.8887 6.36441L61.643 6.37307L63.1152 1.85254L64.5788 6.37307Z"
                                  fill="white"/>
                            <rect x="73.4824" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M82.9499 6.37307H87.7042L83.8592 9.16159L83.8592 9.16159L81.4863 10.8849L84.1883 10.1835L83.8613 9.16822L85.3227 13.6821L81.4777 10.8849L77.6326 13.6821L79.1048 9.16159L75.2598 6.36441L80.0141 6.37307L81.4863 1.85254L82.9499 6.37307Z"
                                  fill="white"/>
                        </svg>
                        <div class="quiz__reviews-slider-item-name-wrapp">
                            <h5 class="quiz__reviews-slider-item-title">
                                Grandi tecniche di rinforzo positivo
                            </h5>
                            <p class="quiz__reviews-slider-item-name">
                                Dawg Daddy
                            </p>
                        </div>
                        <p class="quiz__reviews-slider-item-text">
                            Questo è un ottimo programma di addestramento che parte dalla base, che è fondamentale. Si fa strada con tecniche di rinforzo positivo che funzionano davvero. Ho visto grandi risultati con i miei due cani.
                        </p>
                    </div>
                </li>
                <li class="quiz__reviews-slider-item swiper-slide">
                    <div class="quiz__reviews-slider-item-text-wrapp">
                        <svg width="90" height="16" viewBox="0 0 90 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M9.46744 6.37307H14.2218L10.3768 9.16159L10.3767 9.16159L8.00391 10.8849L10.7058 10.1835L10.3789 9.16822L11.8403 13.6821L7.99524 10.8849L4.15019 13.6821L5.62239 9.16159L1.77734 6.36441L6.53169 6.37307L8.0039 1.85254L9.46744 6.37307Z"
                                  fill="white"/>
                            <rect x="18.3711" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M27.8385 6.37307H32.5929L28.7478 9.16159L28.7478 9.16159L26.375 10.8849L29.0769 10.1835L28.75 9.16822L30.2114 13.6821L26.3663 10.8849L22.5213 13.6821L23.9935 9.16159L20.1484 6.36441L24.9028 6.37307L26.375 1.85254L27.8385 6.37307Z"
                                  fill="white"/>
                            <rect x="36.7402" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M46.2077 6.37307H50.962L47.117 9.16159L47.117 9.16159L44.7441 10.8849L47.4461 10.1835L47.1191 9.16822L48.5805 13.6821L44.7355 10.8849L40.8904 13.6821L42.3626 9.16159L38.5176 6.36441L43.2719 6.37307L44.7441 1.85254L46.2077 6.37307Z"
                                  fill="white"/>
                            <rect x="55.1113" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M64.5788 6.37307H69.3331L65.4881 9.16159L65.4883 9.16217L63.1162 10.8849L65.8181 10.1835L65.6738 9.7352L66.9516 13.6821L63.1066 10.8849L59.2615 13.6821L60.7337 9.16159L56.8887 6.36441L61.643 6.37307L63.1152 1.85254L64.5788 6.37307Z"
                                  fill="white"/>
                            <rect x="73.4824" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M82.9499 6.37307H87.7042L83.8592 9.16159L83.8592 9.16159L81.4863 10.8849L84.1883 10.1835L83.8613 9.16822L85.3227 13.6821L81.4777 10.8849L77.6326 13.6821L79.1048 9.16159L75.2598 6.36441L80.0141 6.37307L81.4863 1.85254L82.9499 6.37307Z"
                                  fill="white"/>
                        </svg>
                        <div class="quiz__reviews-slider-item-name-wrapp">
                            <h5 class="quiz__reviews-slider-item-title">
                                La struttura della formazione è brillante
                            </h5>
                            <p class="quiz__reviews-slider-item-name">
                                Claire_Ugalde
                            </p>
                        </div>
                        <p class="quiz__reviews-slider-item-text">
                            Il modo in cui la formazione è suddivisa è eccellente. Ho avuto una formazione individuale a casa mia e l'ho trovata meno efficace di questo corso, che consiglio vivamente.
                        </p>
                    </div>
                </li>
                <li class="quiz__reviews-slider-item swiper-slide">
                    <div class="quiz__reviews-slider-item-text-wrapp">
                        <svg width="90" height="16" viewBox="0 0 90 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M9.46744 6.37307H14.2218L10.3768 9.16159L10.3767 9.16159L8.00391 10.8849L10.7058 10.1835L10.3789 9.16822L11.8403 13.6821L7.99524 10.8849L4.15019 13.6821L5.62239 9.16159L1.77734 6.36441L6.53169 6.37307L8.0039 1.85254L9.46744 6.37307Z"
                                  fill="white"/>
                            <rect x="18.3711" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M27.8385 6.37307H32.5929L28.7478 9.16159L28.7478 9.16159L26.375 10.8849L29.0769 10.1835L28.75 9.16822L30.2114 13.6821L26.3663 10.8849L22.5213 13.6821L23.9935 9.16159L20.1484 6.36441L24.9028 6.37307L26.375 1.85254L27.8385 6.37307Z"
                                  fill="white"/>
                            <rect x="36.7402" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M46.2077 6.37307H50.962L47.117 9.16159L47.117 9.16159L44.7441 10.8849L47.4461 10.1835L47.1191 9.16822L48.5805 13.6821L44.7355 10.8849L40.8904 13.6821L42.3626 9.16159L38.5176 6.36441L43.2719 6.37307L44.7441 1.85254L46.2077 6.37307Z"
                                  fill="white"/>
                            <rect x="55.1113" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M64.5788 6.37307H69.3331L65.4881 9.16159L65.4883 9.16217L63.1162 10.8849L65.8181 10.1835L65.6738 9.7352L66.9516 13.6821L63.1066 10.8849L59.2615 13.6821L60.7337 9.16159L56.8887 6.36441L61.643 6.37307L63.1152 1.85254L64.5788 6.37307Z"
                                  fill="white"/>
                            <rect x="73.4824" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M82.9499 6.37307H87.7042L83.8592 9.16159L83.8592 9.16159L81.4863 10.8849L84.1883 10.1835L83.8613 9.16822L85.3227 13.6821L81.4777 10.8849L77.6326 13.6821L79.1048 9.16159L75.2598 6.36441L80.0141 6.37307L81.4863 1.85254L82.9499 6.37307Z"
                                  fill="white"/>
                        </svg>
                        <div class="quiz__reviews-slider-item-name-wrapp">
                            <h5 class="quiz__reviews-slider-item-title">
                                Eccellente corso di formazione
                            </h5>
                            <p class="quiz__reviews-slider-item-name">
                                Diego Fernandez Jr
                            </p>
                        </div>
                        <p class="quiz__reviews-slider-item-text">
                            Corso di addestramento eccellente, molto dettagliato e facile da capire. Ciò che mi piace di questo corso è che sottolineano che l'addestramento di un cane richiede pazienza e comprensione del processo. Sono ancora alla prima settimana, ma il loro approccio sembra facile da seguire.
                            Non vedo l'ora di vedere i risultati finali...
                        </p>
                    </div>
                </li>
            </ul>
        </div>
    </div>
    <div class="container email" id="resultStepTwo">
        <h2 class="quiz__reviews-loading-title">
            Inserisci la tua email per ottenere il piano di obbedienza personalizzato per <span class="nameInsertSelector">il tuo cane</span>
        </h2>
        <p class="quiz__result-label" id="emailText"></p>
        <div class="quiz__result-box">
            <ul id="autoComplete" class="email-autocomplete email-autocomplete-scroll"></ul>
            <input class="quiz__result-input" name="text" type="text" placeholder="Inserisci la tua email per ottenere il tuo piano" id="email" autocomplete="off">
        </div>
        <div class="quiz__result-text-box">
            <svg class="quiz__result-text-icon" width="21" height="26" viewBox="0 0 21 26" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path opacity="0.45"
                      d="M20.1944 9.75H18.5278V7.58332C18.5278 3.40184 15.0387 0 10.75 0C6.46125 0 2.97224 3.40184 2.97224 7.58332V9.75H1.30557C0.99849 9.75 0.75 9.99228 0.75 10.2917V23.8334C0.75 25.0283 1.74661 26 2.97224 26H18.5278C19.7534 26 20.75 25.0283 20.75 23.8333V10.2917C20.75 9.99228 20.5015 9.75 20.1944 9.75ZM12.4134 21.0652C12.4308 21.2181 12.3804 21.3715 12.2751 21.4863C12.1698 21.6011 12.019 21.6667 11.8611 21.6667H9.63891C9.48104 21.6667 9.33021 21.6011 9.22495 21.4863C9.11969 21.3715 9.06922 21.2181 9.08661 21.0652L9.43708 17.9925C8.86797 17.5888 8.52781 16.9504 8.52781 16.25C8.52781 15.0551 9.52443 14.0833 10.7501 14.0833C11.9757 14.0833 12.9723 15.055 12.9723 16.25C12.9723 16.9504 12.6321 17.5888 12.063 17.9925L12.4134 21.0652ZM15.1944 9.75H6.30557V7.58332C6.30557 5.19396 8.29938 3.25 10.75 3.25C13.2006 3.25 15.1944 5.19396 15.1944 7.58332V9.75Z"
                      fill="#16191E"/>
            </svg>
            <p class="quiz__result-text">
                Proteggiamo la tua privacy e ci impegniamo a proteggere i tuoi dati personali. Non inviamo mai email di spam, solo informazioni rilevanti.
            </p>
        </div>
        <div class="quiz__btn-box">
            <button class="quiz__btn js-btn email-submit" id="email-submit" disabled>
                <span></span>
                Continuare
            </button>
        </div>
    </div>
    <div class="container " id="resultStepEmailSubscription">
        <h3 class="quiz__email-subscription-title">
            Vuoi ricevere email con <span>offerte speciali</span>, consigli per l'addestramento dei cani, suggerimenti e <span>regali gratuiti</span>?
        </h3>
        <div class="quiz__email-subscription-btn-box">
            <button class="quiz__btn triggerStep" data-event-value="Apply newsletter">
                <span></span>
                Sì, ci sto!
            </button>
            <label class="quiz__email-subscription-label triggerStep" data-event-value="Reject newsletter">
                <input type="checkbox" class="quiz__email-subscription-input" id="disallowMarketingEmails">
                Non sono interessato
            </label>
        </div>
    </div>
    <div class="container graph " id="resultStepThree">
        <h2 class="quiz__reviews-loading-title">
            L'obbedienza del <span class="nameInsertSelector">tuo cane</span>
        </h2>
        <div class="quiz__reviews-loading-svg-inner">
            <svg class="quiz__reviews-loading-svg" width="343" height="227" viewBox="0 0 343 227" fill="none" xmlns="http://www.w3.org/2000/svg">
                <line x1="22.5" y1="0.5" x2="318.62" y2="0.499974" stroke="#E8E8E8"/>
                <line x1="22.5" y1="26.5" x2="318.62" y2="26.5" stroke="#E8E8E8"/>
                <line x1="22.5" y1="52.5" x2="318.62" y2="52.5" stroke="#E8E8E8"/>
                <line x1="22.5" y1="78.5" x2="318.62" y2="78.5" stroke="#E8E8E8"/>
                <line x1="22.5" y1="104.5" x2="318.62" y2="104.5" stroke="#E8E8E8"/>
                <line x1="22.5" y1="130.5" x2="318.62" y2="130.5" stroke="#E8E8E8"/>
                <line x1="22.5" y1="156.5" x2="318.62" y2="156.5" stroke="#E8E8E8"/>
                <g opacity="0.3" id="resultGraphBg" class="result-graph-bg">
                    <path d="M318.5 16.9996C302.052 20.9767 285.975 24.5721 270.321 28.073C224.14 38.4011 181.638 47.9063 144.134 63.9597C93.4774 85.643 51.9875 119.261 23.049 183H318.5V16.9996Z" fill="#D9D9D9"/>
                    <path d="M318.5 16.9996C302.052 20.9767 285.975 24.5721 270.321 28.073C224.14 38.4011 181.638 47.9063 144.134 63.9597C93.4774 85.643 51.9875 119.261 23.049 183H318.5V16.9996Z"
                          fill="url(#paint0_linear_2819_20021)"/>
                </g>
                <line x1="22.5" y1="182.5" x2="319.56" y2="182.5" stroke="#E8E8E8"/>
                <line x1="22.9399" y1="189" x2="22.9399" y2="183" stroke="#E8E8E8"/>
                <line x1="319.06" y1="189" x2="319.06" y2="183" stroke="#E8E8E8"/>
                <line x1="217.532" y1="189" x2="217.532" y2="183" stroke="#E8E8E8"/>
                <line x1="129.167" y1="189" x2="129.167" y2="183" stroke="#E8E8E8"/>
                <path d="M23 183C39.6001 154.958 64.7246 78.6218 196.628 45.9062C328.532 13.1907 318.841 15.0185 318.841 15.0185" stroke="url(#paint1_linear_2819_20021)" stroke-width="5" class="quiz__based-graph-line"
                      stroke-linecap="round" stroke-linejoin="round"/>
                <g filter="url(#filter0_d_2040_174)" class="result-graph-goal">
                    <path d="M326 31H285C280.582 31 277 34.5817 277 39V55C277 59.4183 280.582 63 285 63H326C330.418 63 334 59.4183 334 55V39C334 34.5817 330.418 31 326 31Z" fill="#323334"/>
                    <path d="M298.59 42.0215C298.59 42.8255 298.436 43.4977 298.126 44.0381C297.82 44.5752 297.376 44.9805 296.793 45.2539C296.211 45.5241 295.509 45.6592 294.689 45.6592H292.667V38.5205H294.909C295.657 38.5205 296.307 38.654 296.857 38.9209C297.407 39.1846 297.833 39.5768 298.136 40.0977C298.439 40.6152 298.59 41.2565 298.59 42.0215ZM297.018 42.0605C297.018 41.5332 296.94 41.1003 296.784 40.7617C296.631 40.4199 296.403 40.1676 296.1 40.0049C295.801 39.8421 295.43 39.7607 294.987 39.7607H294.181V44.4092H294.831C295.569 44.4092 296.118 44.2122 296.476 43.8184C296.837 43.4245 297.018 42.8385 297.018 42.0605ZM304.914 42.9199C304.914 43.3757 304.852 43.7793 304.728 44.1309C304.608 44.4824 304.43 44.7803 304.196 45.0244C303.965 45.2653 303.685 45.4476 303.356 45.5713C303.03 45.695 302.663 45.7568 302.252 45.7568C301.868 45.7568 301.515 45.695 301.193 45.5713C300.874 45.4476 300.596 45.2653 300.358 45.0244C300.124 44.7803 299.941 44.4824 299.811 44.1309C299.684 43.7793 299.621 43.3757 299.621 42.9199C299.621 42.3145 299.728 41.8018 299.943 41.3818C300.158 40.9619 300.464 40.6429 300.861 40.4248C301.258 40.2067 301.732 40.0977 302.282 40.0977C302.793 40.0977 303.245 40.2067 303.639 40.4248C304.036 40.6429 304.347 40.9619 304.572 41.3818C304.8 41.8018 304.914 42.3145 304.914 42.9199ZM301.139 42.9199C301.139 43.278 301.178 43.5791 301.256 43.8232C301.334 44.0674 301.457 44.2513 301.623 44.375C301.789 44.4987 302.005 44.5605 302.272 44.5605C302.536 44.5605 302.749 44.4987 302.912 44.375C303.078 44.2513 303.198 44.0674 303.273 43.8232C303.351 43.5791 303.39 43.278 303.39 42.9199C303.39 42.5586 303.351 42.2591 303.273 42.0215C303.198 41.7806 303.078 41.5999 302.912 41.4795C302.746 41.359 302.529 41.2988 302.262 41.2988C301.868 41.2988 301.582 41.4339 301.403 41.7041C301.227 41.9743 301.139 42.3796 301.139 42.9199ZM309.181 40.0977C309.796 40.0977 310.293 40.3369 310.67 40.8154C311.051 41.2939 311.242 41.9954 311.242 42.9199C311.242 43.5384 311.152 44.0592 310.973 44.4824C310.794 44.9023 310.547 45.2197 310.231 45.4346C309.915 45.6494 309.552 45.7568 309.142 45.7568C308.878 45.7568 308.652 45.7243 308.463 45.6592C308.275 45.5908 308.113 45.5046 307.98 45.4004C307.847 45.293 307.731 45.179 307.633 45.0586H307.555C307.581 45.1888 307.601 45.3223 307.614 45.459C307.627 45.5957 307.633 45.7292 307.633 45.8594V48.0615H306.144V40.2002H307.355L307.565 40.9082H307.633C307.731 40.7617 307.85 40.6266 307.99 40.5029C308.13 40.3792 308.297 40.2816 308.493 40.21C308.691 40.1351 308.921 40.0977 309.181 40.0977ZM308.703 41.2891C308.442 41.2891 308.236 41.3428 308.083 41.4502C307.93 41.5576 307.817 41.7188 307.746 41.9336C307.677 42.1484 307.64 42.4202 307.633 42.749V42.9102C307.633 43.2617 307.666 43.5596 307.731 43.8037C307.799 44.0479 307.912 44.2334 308.068 44.3604C308.227 44.4873 308.445 44.5508 308.722 44.5508C308.95 44.5508 309.137 44.4873 309.284 44.3604C309.43 44.2334 309.539 44.0479 309.611 43.8037C309.686 43.5563 309.723 43.2552 309.723 42.9004C309.723 42.3665 309.64 41.9645 309.474 41.6943C309.308 41.4242 309.051 41.2891 308.703 41.2891ZM317.433 42.9199C317.433 43.3757 317.371 43.7793 317.248 44.1309C317.127 44.4824 316.95 44.7803 316.715 45.0244C316.484 45.2653 316.204 45.4476 315.875 45.5713C315.55 45.695 315.182 45.7568 314.772 45.7568C314.388 45.7568 314.035 45.695 313.712 45.5713C313.393 45.4476 313.115 45.2653 312.877 45.0244C312.643 44.7803 312.461 44.4824 312.331 44.1309C312.204 43.7793 312.14 43.3757 312.14 42.9199C312.14 42.3145 312.248 41.8018 312.462 41.3818C312.677 40.9619 312.983 40.6429 313.38 40.4248C313.778 40.2067 314.251 40.0977 314.801 40.0977C315.312 40.0977 315.765 40.2067 316.159 40.4248C316.556 40.6429 316.867 40.9619 317.091 41.3818C317.319 41.8018 317.433 42.3145 317.433 42.9199ZM313.659 42.9199C313.659 43.278 313.698 43.5791 313.776 43.8232C313.854 44.0674 313.976 44.2513 314.142 44.375C314.308 44.4987 314.525 44.5605 314.792 44.5605C315.055 44.5605 315.268 44.4987 315.431 44.375C315.597 44.2513 315.718 44.0674 315.792 43.8232C315.871 43.5791 315.91 43.278 315.91 42.9199C315.91 42.5586 315.871 42.2591 315.792 42.0215C315.718 41.7806 315.597 41.5999 315.431 41.4795C315.265 41.359 315.049 41.2988 314.782 41.2988C314.388 41.2988 314.101 41.4339 313.922 41.7041C313.747 41.9743 313.659 42.3796 313.659 42.9199ZM295.197 50.1172C295.197 50.446 295.128 50.7308 294.992 50.9717C294.855 51.2126 294.669 51.4095 294.435 51.5625C294.204 51.7155 293.944 51.8278 293.654 51.8994V51.9287C294.227 52.0003 294.661 52.1761 294.958 52.4561C295.257 52.736 295.407 53.1104 295.407 53.5791C295.407 53.9958 295.304 54.3685 295.099 54.6973C294.897 55.026 294.585 55.2848 294.162 55.4736C293.738 55.6624 293.193 55.7568 292.526 55.7568C292.132 55.7568 291.764 55.7243 291.422 55.6592C291.084 55.5973 290.765 55.5013 290.465 55.3711V54.0869C290.771 54.2432 291.092 54.362 291.427 54.4434C291.763 54.5215 292.075 54.5605 292.365 54.5605C292.905 54.5605 293.283 54.4678 293.498 54.2822C293.716 54.0934 293.825 53.8298 293.825 53.4912C293.825 53.2926 293.774 53.125 293.673 52.9883C293.572 52.8516 293.397 52.7474 293.146 52.6758C292.899 52.6042 292.552 52.5684 292.106 52.5684H291.564V51.4111H292.116C292.555 51.4111 292.889 51.3704 293.117 51.2891C293.348 51.2044 293.504 51.0905 293.585 50.9473C293.67 50.8008 293.712 50.6348 293.712 50.4492C293.712 50.1953 293.634 49.9967 293.478 49.8535C293.322 49.7103 293.061 49.6387 292.697 49.6387C292.469 49.6387 292.261 49.668 292.072 49.7266C291.886 49.7819 291.719 49.8503 291.569 49.9316C291.419 50.0098 291.287 50.0863 291.173 50.1611L290.475 49.1211C290.755 48.9193 291.082 48.7516 291.457 48.6182C291.834 48.4847 292.283 48.418 292.804 48.418C293.54 48.418 294.123 48.5661 294.552 48.8623C294.982 49.1585 295.197 49.5768 295.197 50.1172ZM305.597 50.0977C306.216 50.0977 306.683 50.2572 306.999 50.5762C307.318 50.8919 307.477 51.3997 307.477 52.0996V55.6592H305.983V52.4707C305.983 52.0801 305.916 51.7855 305.783 51.5869C305.649 51.3883 305.443 51.2891 305.163 51.2891C304.769 51.2891 304.489 51.4307 304.323 51.7139C304.157 51.9938 304.074 52.3958 304.074 52.9199V55.6592H302.584V52.4707C302.584 52.2103 302.555 51.9922 302.497 51.8164C302.438 51.6406 302.348 51.5088 302.228 51.4209C302.108 51.333 301.953 51.2891 301.764 51.2891C301.487 51.2891 301.269 51.359 301.11 51.499C300.954 51.6357 300.841 51.8392 300.773 52.1094C300.708 52.3763 300.675 52.7035 300.675 53.0908V55.6592H299.186V50.2002H300.324L300.524 50.8984H300.607C300.718 50.7096 300.856 50.5566 301.022 50.4395C301.191 50.3223 301.377 50.236 301.579 50.1807C301.78 50.1253 301.986 50.0977 302.194 50.0977C302.594 50.0977 302.933 50.1628 303.209 50.293C303.489 50.4232 303.704 50.625 303.854 50.8984H303.986C304.149 50.6185 304.378 50.415 304.674 50.2881C304.974 50.1611 305.281 50.0977 305.597 50.0977ZM311.261 50.0977C311.766 50.0977 312.2 50.1953 312.565 50.3906C312.93 50.5827 313.211 50.8626 313.41 51.2305C313.608 51.5983 313.708 52.0475 313.708 52.5781V53.3008H310.187C310.203 53.7207 310.329 54.0511 310.563 54.292C310.801 54.5296 311.129 54.6484 311.549 54.6484C311.898 54.6484 312.217 54.6126 312.506 54.541C312.796 54.4694 313.094 54.362 313.4 54.2188V55.3711C313.13 55.5046 312.847 55.6022 312.55 55.6641C312.257 55.7259 311.901 55.7568 311.481 55.7568C310.934 55.7568 310.449 55.6559 310.026 55.4541C309.606 55.2523 309.276 54.9447 309.035 54.5312C308.797 54.1178 308.678 53.597 308.678 52.9688C308.678 52.3307 308.786 51.8001 309 51.377C309.219 50.9505 309.521 50.6315 309.909 50.4199C310.296 50.2051 310.747 50.0977 311.261 50.0977ZM311.271 51.1572C310.981 51.1572 310.74 51.25 310.548 51.4355C310.36 51.6211 310.25 51.9124 310.221 52.3096H312.311C312.308 52.0882 312.267 51.8913 312.189 51.7188C312.114 51.5462 312 51.4095 311.847 51.3086C311.697 51.2077 311.505 51.1572 311.271 51.1572ZM318.727 54.0381C318.727 54.4092 318.639 54.7233 318.463 54.9805C318.291 55.2344 318.032 55.4281 317.687 55.5615C317.342 55.6917 316.912 55.7568 316.398 55.7568C316.017 55.7568 315.69 55.7324 315.417 55.6836C315.146 55.6348 314.873 55.5534 314.596 55.4395V54.209C314.892 54.3424 315.21 54.4531 315.548 54.541C315.89 54.6257 316.19 54.668 316.447 54.668C316.736 54.668 316.943 54.6257 317.067 54.541C317.194 54.4531 317.257 54.3392 317.257 54.1992C317.257 54.1081 317.231 54.0267 317.179 53.9551C317.13 53.8802 317.023 53.7972 316.857 53.7061C316.691 53.6117 316.431 53.4896 316.076 53.3398C315.734 53.1966 315.452 53.0518 315.231 52.9053C315.013 52.7588 314.85 52.5863 314.743 52.3877C314.639 52.1859 314.586 51.9303 314.586 51.6211C314.586 51.1165 314.782 50.7373 315.172 50.4834C315.566 50.2262 316.092 50.0977 316.75 50.0977C317.088 50.0977 317.41 50.1318 317.716 50.2002C318.026 50.2686 318.343 50.3776 318.668 50.5273L318.219 51.6016C317.949 51.4844 317.694 51.3883 317.453 51.3135C317.215 51.2386 316.972 51.2012 316.725 51.2012C316.507 51.2012 316.343 51.2305 316.232 51.2891C316.121 51.3477 316.066 51.4372 316.066 51.5576C316.066 51.6455 316.094 51.7236 316.149 51.792C316.208 51.8604 316.318 51.9368 316.481 52.0215C316.647 52.1029 316.889 52.2087 317.208 52.3389C317.518 52.4658 317.786 52.5993 318.014 52.7393C318.242 52.876 318.418 53.0469 318.542 53.252C318.665 53.4538 318.727 53.7158 318.727 54.0381ZM321.378 50.2002V55.6592H319.889V50.2002H321.378ZM320.636 48.0615C320.858 48.0615 321.048 48.1136 321.208 48.2178C321.367 48.3187 321.447 48.5091 321.447 48.7891C321.447 49.0658 321.367 49.2578 321.208 49.3652C321.048 49.4694 320.858 49.5215 320.636 49.5215C320.412 49.5215 320.22 49.4694 320.06 49.3652C319.904 49.2578 319.826 49.0658 319.826 48.7891C319.826 48.5091 319.904 48.3187 320.06 48.2178C320.22 48.1136 320.412 48.0615 320.636 48.0615Z" fill="white"/>
                    <path d="M321.293 25.1894L325.5 31.4999H315L319.655 25.1528C320.065 24.5927 320.908 24.6116 321.293 25.1894Z" fill="#323334"/>
                </g>
                <g filter="url(#filter1_d_2819_20021)">
                    <circle cx="23.0503" cy="180.55" r="7.55031" fill="white"/>
                    <circle cx="23.0503" cy="180.55" r="7.05031" stroke="#2D2D2D" stroke-opacity="0.12"/>
                </g>
                <g filter="url(#filter2_d_2040_174)">
                    <path d="M32 141H14C9.58172 141 6 144.582 6 149V155C6 159.418 9.58172 163 14 163H32C36.4183 163 40 159.418 40 155V149C40 144.582 36.4183 141 32 141Z" fill="#D3D5DA"/>
                    <path d="M20.6055 152.421C20.6055 152.971 20.5371 153.472 20.4004 153.925C20.2637 154.374 20.0553 154.761 19.7754 155.087C19.4987 155.412 19.1471 155.663 18.7207 155.839C18.2943 156.011 17.7897 156.098 17.207 156.098C16.6243 156.098 16.1198 156.011 15.6934 155.839C15.2669 155.663 14.9137 155.412 14.6338 155.087C14.3571 154.761 14.1504 154.372 14.0137 153.92C13.877 153.467 13.8086 152.965 13.8086 152.411C13.8086 151.672 13.929 151.029 14.1699 150.482C14.4141 149.932 14.7884 149.506 15.293 149.203C15.7975 148.9 16.4388 148.749 17.2168 148.749C17.9915 148.749 18.6279 148.9 19.126 149.203C19.6273 149.506 19.9984 149.932 20.2393 150.482C20.4834 151.033 20.6055 151.679 20.6055 152.421ZM15.3955 152.421C15.3955 152.919 15.4574 153.349 15.5811 153.71C15.708 154.068 15.9049 154.345 16.1719 154.54C16.4388 154.732 16.7839 154.828 17.207 154.828C17.6367 154.828 17.985 154.732 18.252 154.54C18.5189 154.345 18.7126 154.068 18.833 153.71C18.9567 153.349 19.0186 152.919 19.0186 152.421C19.0186 151.672 18.8786 151.083 18.5986 150.653C18.3187 150.224 17.8581 150.009 17.2168 150.009C16.7904 150.009 16.4421 150.106 16.1719 150.302C15.9049 150.494 15.708 150.771 15.5811 151.132C15.4574 151.49 15.3955 151.92 15.3955 152.421ZM25.0146 150.438C25.0895 150.438 25.1758 150.443 25.2734 150.453C25.3743 150.46 25.4557 150.469 25.5176 150.482L25.4053 151.879C25.3564 151.863 25.2865 151.851 25.1953 151.845C25.1074 151.835 25.0309 151.83 24.9658 151.83C24.7738 151.83 24.5866 151.854 24.4043 151.903C24.2253 151.952 24.0641 152.032 23.9209 152.143C23.7777 152.25 23.6637 152.393 23.5791 152.572C23.4977 152.748 23.457 152.965 23.457 153.222V156H21.9678V150.541H23.0957L23.3154 151.459H23.3887C23.4961 151.273 23.6296 151.104 23.7891 150.951C23.9518 150.795 24.1357 150.671 24.3408 150.58C24.5492 150.486 24.7738 150.438 25.0146 150.438ZM28.7451 150.429C29.4775 150.429 30.0391 150.588 30.4297 150.907C30.8203 151.226 31.0156 151.711 31.0156 152.362V156H29.9756L29.6875 155.258H29.6484C29.4922 155.453 29.3327 155.613 29.1699 155.736C29.0072 155.86 28.82 155.951 28.6084 156.01C28.3968 156.068 28.1396 156.098 27.8369 156.098C27.5146 156.098 27.2249 156.036 26.9678 155.912C26.7139 155.788 26.5137 155.6 26.3672 155.346C26.2207 155.089 26.1475 154.763 26.1475 154.369C26.1475 153.79 26.3509 153.363 26.7578 153.09C27.1647 152.813 27.7751 152.66 28.5889 152.631L29.5361 152.602V152.362C29.5361 152.076 29.4613 151.866 29.3115 151.732C29.1618 151.599 28.9535 151.532 28.6865 151.532C28.4229 151.532 28.1641 151.57 27.9102 151.645C27.6562 151.719 27.4023 151.814 27.1484 151.928L26.6553 150.922C26.945 150.769 27.2689 150.648 27.627 150.561C27.9883 150.473 28.361 150.429 28.7451 150.429ZM29.5361 153.471L28.96 153.49C28.4782 153.503 28.1429 153.59 27.9541 153.749C27.7686 153.909 27.6758 154.118 27.6758 154.379C27.6758 154.607 27.7425 154.77 27.876 154.867C28.0094 154.962 28.1836 155.009 28.3984 155.009C28.7174 155.009 28.986 154.914 29.2041 154.726C29.4255 154.537 29.5361 154.268 29.5361 153.92V153.471Z" fill="#828286"/>
                    <path d="M22.207 169.31L18 163H28.5L23.8454 169.347C23.4347 169.907 22.5922 169.888 22.207 169.31Z" fill="#D3D5DA"/>
                </g>
                <g filter="url(#filter3_d_2819_20021)" id="resultGraphDot" class="result-graph-dot">
                    <circle cx="320.55" cy="14.5503" r="7.55031" fill="white"/>
                    <circle cx="320.55" cy="14.5503" r="7.05031" stroke="#2D2D2D" stroke-opacity="0.12"/>
                </g>
                <defs>
                    <filter id="filter0_d_2819_20021" x="273" y="24.7441" width="65" height="46.2559" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                        <feOffset dy="4"/>
                        <feGaussianBlur stdDeviation="2"/>
                        <feComposite in2="hardAlpha" operator="out"/>
                        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.07 0"/>
                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2819_20021"/>
                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2819_20021" result="shape"/>
                    </filter>
                    <filter id="filter1_d_2819_20021" x="7.5" y="169.314" width="31.1006" height="31.1006" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                        <feOffset dy="4.31447"/>
                        <feGaussianBlur stdDeviation="4"/>
                        <feComposite in2="hardAlpha" operator="out"/>
                        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2819_20021"/>
                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2819_20021" result="shape"/>
                    </filter>
                    <filter id="filter2_d_2819_20021" x="2" y="141" width="42" height="36.7559" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                        <feOffset dy="4"/>
                        <feGaussianBlur stdDeviation="2"/>
                        <feComposite in2="hardAlpha" operator="out"/>
                        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.07 0"/>
                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2819_20021"/>
                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2819_20021" result="shape"/>
                    </filter>
                    <filter id="filter3_d_2819_20021" x="305" y="3.31447" width="31.1006" height="31.1006" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                        <feOffset dy="4.31447"/>
                        <feGaussianBlur stdDeviation="4"/>
                        <feComposite in2="hardAlpha" operator="out"/>
                        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2819_20021"/>
                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2819_20021" result="shape"/>
                    </filter>
                    <linearGradient id="paint0_linear_2819_20021" x1="311.509" y1="181.708" x2="24.5863" y2="181.708" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#1998CD"/>
                        <stop offset="0.549638" stop-color="#F5E088"/>
                        <stop offset="1" stop-color="#FF574C"/>
                    </linearGradient>
                    <linearGradient id="paint1_linear_2819_20021" x1="319" y1="182.999" x2="23" y2="182.999" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#1998CD"/>
                        <stop offset="0.549638" stop-color="#F5E088"/>
                        <stop offset="1" stop-color="#FF574C"/>
                    </linearGradient>
                </defs>
            </svg>
            <div class="quiz__reviews-loading-svg-date-wrapp">
                <p class="quiz__reviews-loading-svg-date">
                    Ora
                </p>
                <p class="quiz__reviews-loading-svg-date" id="monthAfter1">
                    mese 1
                </p>
                <p class="quiz__reviews-loading-svg-date" id="monthAfter2">
                    mese 2
                </p>
                <p class="quiz__reviews-loading-svg-date" id="monthAfter3">
                    mese 3
                </p>
            </div>
            <p class="quiz__reviews-loading-svg-date-text">
                Questo grafico è solo a scopo illustrativo
            </p>
        </div>
        <p class="quiz__reviews-loading-subtext">
            Il <span>piano di obbedienza</span> del tuo <span class="textPuppySelector">cane</span> è pronto
        </p>
        <div class="quiz__btn-box">
            <button class="quiz__btn js-btn email-submit" id="redirect-to-landing-btn" data-landing-url="/questionary/landing-24">
                <span></span>
                Continuare
            </button>
        </div>
    </div>
    <div class="quiz__redirect-loader" id="resultLoader">
        <div class="preload">
            <span></span>
        </div>
    </div>
</div>

<div class="quiz__popup-wrapp" id="quizPopUpProblem">
    <div class="quiz__popup-bg"></div>

    <div class="quiz__popup" id="magicProblemPopup">
        <div class="quiz__popup-box">
            <h4 class="quiz__popup-title">
                Un'altra domanda
            </h4>

            {% if quizData.isPuppyFunnel %}
                <div class="quiz__popup-img-box">
                    <img id="resultPopup" class="quiz__popup-img" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1690991083/dog-training/img/Rectangle_17.jpg" alt="">
                </div>
                <div class="quiz__popup-text">
                    <span class="ageInsertSelector">I cuccioli</span> di solito hanno problemi di morso e mordicchiamento.
                    Hai questo problema?
                </div>
            {% else %}
                <div class="quiz__popup-img-box">
                    <img id="resultPopup" class="quiz__popup-img" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1690985584/dog-training/img/Rectangle_14.jpg" alt="">
                </div>
                <div class="quiz__popup-text">
                    <b class="ageInsertSelector capitalize">I cani adulti</b> a volte possono mostrare aggressività territoriale.
                    Hai mai avuto questo problema?
                </div>
            {% endif %}
            <ul class="quiz__popup-btn-list">
                <li class="quiz__popup-btn-item quizPopUpProblemBtn" data-event="No">
                    No
                </li>
                <li class="quiz__popup-btn-item quizPopUpProblemBtn" data-event="Yes">
                    Sì
                </li>
            </ul>
        </div>
    </div>
</div>
<div class="quiz__popup-wrapp" id="quizPopUpObedience">
    <div class="quiz__popup-bg"></div>

    <div class="quiz__popup" id="magicObediencePopup">
        <div class="quiz__popup-box">
            <h4 class="quiz__popup-title">
                Finalizzando il tuo piano
            </h4>
            <div class="quiz__popup-img-box">
                <img id="resultPopup" class="quiz__popup-img" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1690991083/dog-training/img/Rectangle_16.jpg" alt="">
            </div>
            <div class="quiz__popup-text">
                <span class="nameInsertSelector">Il tuo cane</span> ha mai avuto un addestramento all'obbedienza?
            </div>
            <ul class="quiz__popup-btn-list">
                <li class="quiz__popup-btn-item quizPopUpObedienceBtn" data-event="No">
                    No
                </li>
                <li class="quiz__popup-btn-item quizPopUpObedienceBtn" data-event="Yes">
                    Sì
                </li>
            </ul>
        </div>
    </div>
</div>
