<div class="quiz__index quiz__result skipQuiz magic" id="block{{ blockId }}" data-id="{{ blockId }}" data-result="reviews">
    <div class="container " id="resultStepOne">
        <div id="circleBar" class="progressbar reviews">
            <div class="progressbar__shadow"></div>
        </div>
        <p class="quiz__reviews-loading-subtitle" id="replaceSubtitle">
            Création du plan d'entraînement personnalisé de <span class="nameInsertSelector">chien</span>...
        </p>
        <div class="quiz__hr">
        </div>
        <div class="quiz__reviews-loading-title-red-box">
            <h3 class="quiz__reviews-loading-title-red">
                115 mille propriétaires de chiens
            </h3>
        </div>
        <p class="quiz__reviews-loading-text bold">
            ont choisi PawChamp
        </p>
        <div class="quiz__reviews-slider custom-result">
            <ul class="swiper-wrapper">
                <li class="quiz__reviews-slider-item swiper-slide">
                    <div class="quiz__reviews-slider-item-text-wrapp">
                        <svg width="90" height="16" viewBox="0 0 90 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M9.46744 6.37307H14.2218L10.3768 9.16159L10.3767 9.16159L8.00391 10.8849L10.7058 10.1835L10.3789 9.16822L11.8403 13.6821L7.99524 10.8849L4.15019 13.6821L5.62239 9.16159L1.77734 6.36441L6.53169 6.37307L8.0039 1.85254L9.46744 6.37307Z"
                                  fill="white"/>
                            <rect x="18.3711" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M27.8385 6.37307H32.5929L28.7478 9.16159L28.7478 9.16159L26.375 10.8849L29.0769 10.1835L28.75 9.16822L30.2114 13.6821L26.3663 10.8849L22.5213 13.6821L23.9935 9.16159L20.1484 6.36441L24.9028 6.37307L26.375 1.85254L27.8385 6.37307Z"
                                  fill="white"/>
                            <rect x="36.7402" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M46.2077 6.37307H50.962L47.117 9.16159L47.117 9.16159L44.7441 10.8849L47.4461 10.1835L47.1191 9.16822L48.5805 13.6821L44.7355 10.8849L40.8904 13.6821L42.3626 9.16159L38.5176 6.36441L43.2719 6.37307L44.7441 1.85254L46.2077 6.37307Z"
                                  fill="white"/>
                            <rect x="55.1113" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M64.5788 6.37307H69.3331L65.4881 9.16159L65.4883 9.16217L63.1162 10.8849L65.8181 10.1835L65.6738 9.7352L66.9516 13.6821L63.1066 10.8849L59.2615 13.6821L60.7337 9.16159L56.8887 6.36441L61.643 6.37307L63.1152 1.85254L64.5788 6.37307Z"
                                  fill="white"/>
                            <rect x="73.4824" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M82.9499 6.37307H87.7042L83.8592 9.16159L83.8592 9.16159L81.4863 10.8849L84.1883 10.1835L83.8613 9.16822L85.3227 13.6821L81.4777 10.8849L77.6326 13.6821L79.1048 9.16159L75.2598 6.36441L80.0141 6.37307L81.4863 1.85254L82.9499 6.37307Z"
                                  fill="white"/>
                        </svg>
                        <div class="quiz__reviews-slider-item-name-wrapp">
                            <h5>
                                Nouvelle approche du calme
                            </h5>
                            <p class="quiz__reviews-slider-item-name">
                                Ernesto
                            </p>
                        </div>
                        <p class="quiz__reviews-slider-item-text">
                            C'était une nouvelle façon pour moi d'apprendre à aider mon chien à rester calme. Nous n'avons pas encore terminé, mais j'ai déjà constaté de grandes améliorations avec notre berger allemand de 8 ans qui avait reçu une certaine formation, mais qui avait du mal à être réactif.
                        </p>
                    </div>
                </li>
                <li class="quiz__reviews-slider-item swiper-slide">
                    <div class="quiz__reviews-slider-item-text-wrapp">
                        <svg width="90" height="16" viewBox="0 0 90 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M9.46744 6.37307H14.2218L10.3768 9.16159L10.3767 9.16159L8.00391 10.8849L10.7058 10.1835L10.3789 9.16822L11.8403 13.6821L7.99524 10.8849L4.15019 13.6821L5.62239 9.16159L1.77734 6.36441L6.53169 6.37307L8.0039 1.85254L9.46744 6.37307Z"
                                  fill="white"/>
                            <rect x="18.3711" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M27.8385 6.37307H32.5929L28.7478 9.16159L28.7478 9.16159L26.375 10.8849L29.0769 10.1835L28.75 9.16822L30.2114 13.6821L26.3663 10.8849L22.5213 13.6821L23.9935 9.16159L20.1484 6.36441L24.9028 6.37307L26.375 1.85254L27.8385 6.37307Z"
                                  fill="white"/>
                            <rect x="36.7402" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M46.2077 6.37307H50.962L47.117 9.16159L47.117 9.16159L44.7441 10.8849L47.4461 10.1835L47.1191 9.16822L48.5805 13.6821L44.7355 10.8849L40.8904 13.6821L42.3626 9.16159L38.5176 6.36441L43.2719 6.37307L44.7441 1.85254L46.2077 6.37307Z"
                                  fill="white"/>
                            <rect x="55.1113" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M64.5788 6.37307H69.3331L65.4881 9.16159L65.4883 9.16217L63.1162 10.8849L65.8181 10.1835L65.6738 9.7352L66.9516 13.6821L63.1066 10.8849L59.2615 13.6821L60.7337 9.16159L56.8887 6.36441L61.643 6.37307L63.1152 1.85254L64.5788 6.37307Z"
                                  fill="white"/>
                            <rect x="73.4824" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M82.9499 6.37307H87.7042L83.8592 9.16159L83.8592 9.16159L81.4863 10.8849L84.1883 10.1835L83.8613 9.16822L85.3227 13.6821L81.4777 10.8849L77.6326 13.6821L79.1048 9.16159L75.2598 6.36441L80.0141 6.37307L81.4863 1.85254L82.9499 6.37307Z"
                                  fill="white"/>
                        </svg>
                        <div class="quiz__reviews-slider-item-name-wrapp">
                            <h5>
                                La structure de la formation est brillante
                            </h5>
                            <p class="quiz__reviews-slider-item-name">
                                Claire_Ugalde
                            </p>
                        </div>
                        <p class="quiz__reviews-slider-item-text">
                            La façon dont la formation est décomposée est excellente. J'ai eu des formations individuelles chez moi et je les ai trouvées moins efficaces que ce cours et je le recommande vivement.
                        </p>
                    </div>
                </li>
                <li class="quiz__reviews-slider-item swiper-slide">
                    <div class="quiz__reviews-slider-item-text-wrapp">
                        <svg width="90" height="16" viewBox="0 0 90 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M9.46744 6.37307H14.2218L10.3768 9.16159L10.3767 9.16159L8.00391 10.8849L10.7058 10.1835L10.3789 9.16822L11.8403 13.6821L7.99524 10.8849L4.15019 13.6821L5.62239 9.16159L1.77734 6.36441L6.53169 6.37307L8.0039 1.85254L9.46744 6.37307Z"
                                  fill="white"/>
                            <rect x="18.3711" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M27.8385 6.37307H32.5929L28.7478 9.16159L28.7478 9.16159L26.375 10.8849L29.0769 10.1835L28.75 9.16822L30.2114 13.6821L26.3663 10.8849L22.5213 13.6821L23.9935 9.16159L20.1484 6.36441L24.9028 6.37307L26.375 1.85254L27.8385 6.37307Z"
                                  fill="white"/>
                            <rect x="36.7402" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M46.2077 6.37307H50.962L47.117 9.16159L47.117 9.16159L44.7441 10.8849L47.4461 10.1835L47.1191 9.16822L48.5805 13.6821L44.7355 10.8849L40.8904 13.6821L42.3626 9.16159L38.5176 6.36441L43.2719 6.37307L44.7441 1.85254L46.2077 6.37307Z"
                                  fill="white"/>
                            <rect x="55.1113" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M64.5788 6.37307H69.3331L65.4881 9.16159L65.4883 9.16217L63.1162 10.8849L65.8181 10.1835L65.6738 9.7352L66.9516 13.6821L63.1066 10.8849L59.2615 13.6821L60.7337 9.16159L56.8887 6.36441L61.643 6.37307L63.1152 1.85254L64.5788 6.37307Z"
                                  fill="white"/>
                            <rect x="73.4824" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M82.9499 6.37307H87.7042L83.8592 9.16159L83.8592 9.16159L81.4863 10.8849L84.1883 10.1835L83.8613 9.16822L85.3227 13.6821L81.4777 10.8849L77.6326 13.6821L79.1048 9.16159L75.2598 6.36441L80.0141 6.37307L81.4863 1.85254L82.9499 6.37307Z"
                                  fill="white"/>
                        </svg>
                        <div class="quiz__reviews-slider-item-name-wrapp">
                            <h5>
                                Excellent cours de formation
                            </h5>
                            <p class="quiz__reviews-slider-item-name">
                                Diego Fernandez Jr
                            </p>
                        </div>
                        <p class="quiz__reviews-slider-item-text">
                            Excellent cours de formation, très détaillé et facile à comprendre. Ce que j'aime dans ce cours, c'est qu'ils mettent l'accent sur le fait qu'éduquer un chien demande de la patience et de comprendre le processus. Je n'en suis qu'à la première semaine, mais leur approche semble facile à suivre.
                            J'ai hâte de voir les résultats finaux...
                        </p>
                    </div>
                </li>
            </ul>
        </div>
    </div>
    <div class="container email" id="resultStepTwo">
        <h2 class="quiz__reviews-loading-title">
            Entrez votre email pour obtenir le plan personnalisé de <span class="nameInsertSelector">votre chien</span> pour surmonter la réactivité
        </h2>
        <p class="quiz__result-label" id="emailText"></p>
        <div class="quiz__result-box">
            <ul id="autoComplete" class="email-autocomplete email-autocomplete-scroll"></ul>
            <input class="quiz__result-input" name="text" type="text" placeholder="Entrez votre email" id="email" autocomplete="off">
        </div>
        <div class="quiz__result-text-box">
            <svg class="quiz__result-text-icon" width="21" height="26" viewBox="0 0 21 26" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path opacity="0.45"
                      d="M20.1944 9.75H18.5278V7.58332C18.5278 3.40184 15.0387 0 10.75 0C6.46125 0 2.97224 3.40184 2.97224 7.58332V9.75H1.30557C0.99849 9.75 0.75 9.99228 0.75 10.2917V23.8334C0.75 25.0283 1.74661 26 2.97224 26H18.5278C19.7534 26 20.75 25.0283 20.75 23.8333V10.2917C20.75 9.99228 20.5015 9.75 20.1944 9.75ZM12.4134 21.0652C12.4308 21.2181 12.3804 21.3715 12.2751 21.4863C12.1698 21.6011 12.019 21.6667 11.8611 21.6667H9.63891C9.48104 21.6667 9.33021 21.6011 9.22495 21.4863C9.11969 21.3715 9.06922 21.2181 9.08661 21.0652L9.43708 17.9925C8.86797 17.5888 8.52781 16.9504 8.52781 16.25C8.52781 15.0551 9.52443 14.0833 10.7501 14.0833C11.9757 14.0833 12.9723 15.055 12.9723 16.25C12.9723 16.9504 12.6321 17.5888 12.063 17.9925L12.4134 21.0652ZM15.1944 9.75H6.30557V7.58332C6.30557 5.19396 8.29938 3.25 10.75 3.25C13.2006 3.25 15.1944 5.19396 15.1944 7.58332V9.75Z"
                      fill="#343434"/>
            </svg>
            <p class="quiz__result-text">
                Nous protégeons votre vie privée et nous engageons à protéger vos données personnelles. Nous n'envoyons jamais de spam, uniquement des informations pertinentes.
            </p>
        </div>
        <div class="quiz__btn-box">
            <button class="quiz__btn js-btn email-submit" id="email-submit" disabled>
                <span></span>
                Continuer
            </button>
        </div>
    </div>
    <div class="container " id="resultStepEmailSubscription">
        <h3 class="quiz__email-subscription-title">
            Voulez-vous recevoir des e-mails avec des <span>offres spéciales</span>, des conseils pour le dressage de chiens, des astuces et des <span>cadeaux gratuits</span> ?
        </h3>
        <div class="quiz__email-subscription-btn-box">
            <button class="quiz__btn triggerStep" data-event-value="Apply newsletter">
                <span></span>
                Oui, j'en suis !
            </button>
            <label class="quiz__email-subscription-label triggerStep" data-event-value="Reject newsletter">
                <input type="checkbox" class="quiz__email-subscription-input" id="disallowMarketingEmails">
                Je ne suis pas intéressé
            </label>
        </div>
    </div>
    <div class="container graph " id="resultStepThree">
        <h2 class="quiz__reviews-loading-title">
            La réactivité de <span class="nameInsertSelector">votre chien</span>
        </h2>
        <div class="quiz__reviews-loading-svg-inner">
            <svg class="quiz__reviews-loading-svg quiz__reviews-loading-svg--is-reactivity" width="343" height="227" viewBox="0 0 336 203" fill="none" xmlns="http://www.w3.org/2000/svg">
                <line x1="20" y1="0.5" x2="316.12" y2="0.499974" stroke="#E8E8E8"/>
                <line x1="20" y1="26.5" x2="316.12" y2="26.5" stroke="#E8E8E8"/>
                <line x1="20" y1="52.5" x2="316.12" y2="52.5" stroke="#E8E8E8"/>
                <line x1="20" y1="78.5" x2="316.12" y2="78.5" stroke="#E8E8E8"/>
                <line x1="20" y1="104.5" x2="316.12" y2="104.5" stroke="#E8E8E8"/>
                <line x1="20" y1="130.5" x2="316.12" y2="130.5" stroke="#E8E8E8"/>
                <line x1="20" y1="156.5" x2="316.12" y2="156.5" stroke="#E8E8E8"/>
                <g id="resultGraphBg" class="result-graph-bg" opacity="0.3">
                    <path d="M20 16.9996C36.4481 20.9767 52.5247 24.5721 68.1785 28.073C114.36 38.4011 156.862 47.9063 194.366 63.9597C245.023 85.643 286.513 119.261 315.451 183H20V16.9996Z" fill="#D9D9D9"/>
                    <path d="M20 16.9996C36.4481 20.9767 52.5247 24.5721 68.1785 28.073C114.36 38.4011 156.862 47.9063 194.366 63.9597C245.023 85.643 286.513 119.261 315.451 183H20V16.9996Z" fill="url(#paint0_linear_394_10)"/>
                </g>
                <line x1="20" y1="182.5" x2="317.06" y2="182.5" stroke="#E8E8E8"/>
                <line x1="20.4395" y1="189" x2="20.4395" y2="183" stroke="#E8E8E8"/>
                <line x1="316.561" y1="189" x2="316.561" y2="183" stroke="#E8E8E8"/>
                <line x1="215.032" y1="189" x2="215.032" y2="183" stroke="#E8E8E8"/>
                <line x1="126.667" y1="189" x2="126.667" y2="183" stroke="#E8E8E8"/>
                <path class="quiz__based-graph-line" d="M20.6591,15.0185C20.6591,15.0185 10.9682,13.1907 142.872,45.9062C274.775,78.6218 299.9,154.958 316.5,183" stroke="url(#paint1_linear_394_10)" stroke-width="5"/>
                <g class="result-graph-goal" filter="url(#filter0_d_394_10)" transform="translate(0 -1)">
                    <path d="M318.793 169.31L323 163L312.5 163L317.155 169.347C317.565 169.907 318.408 169.888 318.793 169.31Z" fill="#323334"/>
                </g>
                <g filter="url(#filter1_d_394_10)">
                    <circle cx="20.5503" cy="14.5503" r="7.55031" fill="white"/>
                    <circle cx="20.5503" cy="14.5503" r="7.05031" stroke="#2D2D2D" stroke-opacity="0.12"/>
                </g>
                <g filter="url(#filter2_d_394_10)">
                    <rect x="4" y="31" width="69" height="22" rx="8" fill="#D3D5DA"/>
                    <path d="M10.8823 45V37.95H12.3523L14.7323 42.41H14.5323L16.9023 37.95H18.3423V45H16.8823V40.34H17.0023L15.0723 43.9H14.1423L12.2023 40.34H12.3323V45H10.8823ZM21.5145 45.11C21.1479 45.11 20.8179 45.04 20.5245 44.9C20.2379 44.7533 20.0112 44.56 19.8445 44.32C19.6779 44.08 19.5945 43.8067 19.5945 43.5C19.5945 43.14 19.6945 42.8533 19.8945 42.64C20.0945 42.42 20.4079 42.26 20.8345 42.16C21.2679 42.06 21.8379 42.01 22.5445 42.01H23.1345V42.82H22.5845C22.3245 42.82 22.1012 42.8333 21.9145 42.86C21.7279 42.88 21.5745 42.9133 21.4545 42.96C21.3345 43.0067 21.2445 43.07 21.1845 43.15C21.1312 43.2233 21.1045 43.3167 21.1045 43.43C21.1045 43.6233 21.1745 43.7767 21.3145 43.89C21.4545 43.9967 21.6445 44.05 21.8845 44.05C22.0845 44.05 22.2645 44.0067 22.4245 43.92C22.5845 43.8267 22.7112 43.7 22.8045 43.54C22.9045 43.3733 22.9545 43.1867 22.9545 42.98V41.87C22.9545 41.5767 22.8745 41.37 22.7145 41.25C22.5612 41.13 22.3079 41.07 21.9545 41.07C21.6945 41.07 21.4145 41.11 21.1145 41.19C20.8212 41.27 20.5179 41.3933 20.2045 41.56L19.8045 40.53C20.0045 40.41 20.2312 40.3067 20.4845 40.22C20.7445 40.1267 21.0145 40.0567 21.2945 40.01C21.5745 39.9633 21.8479 39.94 22.1145 39.94C22.6212 39.94 23.0479 40.0167 23.3945 40.17C23.7412 40.3167 24.0012 40.5467 24.1745 40.86C24.3545 41.1733 24.4445 41.58 24.4445 42.08V45H23.0645V44.01H23.0845C23.0245 44.23 22.9212 44.4233 22.7745 44.59C22.6345 44.75 22.4579 44.8767 22.2445 44.97C22.0379 45.0633 21.7945 45.11 21.5145 45.11ZM25.6425 39.11V37.66H27.3225V39.11H25.6425ZM25.7125 45V40.04H27.2725V45H25.7125ZM28.5641 45V40.04H30.0341V40.99H29.9841C30.1441 40.65 30.3774 40.39 30.6841 40.21C30.9974 40.03 31.3474 39.94 31.7341 39.94C32.1341 39.94 32.4674 40.0167 32.7341 40.17C33.0007 40.3233 33.2007 40.56 33.3341 40.88C33.4674 41.1933 33.5341 41.5933 33.5341 42.08V45H31.9741V42.14C31.9741 41.9133 31.9441 41.73 31.8841 41.59C31.8307 41.45 31.7474 41.3467 31.6341 41.28C31.5207 41.2067 31.3741 41.17 31.1941 41.17C30.9807 41.17 30.7941 41.2167 30.6341 41.31C30.4741 41.4033 30.3474 41.5367 30.2541 41.71C30.1674 41.8767 30.1241 42.07 30.1241 42.29V45H28.5641ZM37.2743 45.11C36.5476 45.11 36.011 44.9333 35.6643 44.58C35.3176 44.2267 35.1443 43.7133 35.1443 43.04V41.2H34.1943V40.04H35.1843V38.63H36.6943V40.04H38.2543V41.2H36.6943V42.99C36.6943 43.2767 36.7643 43.4933 36.9043 43.64C37.0443 43.78 37.261 43.85 37.5543 43.85C37.6543 43.85 37.7576 43.84 37.8643 43.82C37.9776 43.7933 38.091 43.7633 38.2043 43.73L38.4243 44.86C38.2643 44.94 38.0843 45 37.8843 45.04C37.6843 45.0867 37.481 45.11 37.2743 45.11ZM41.7316 45.11C41.1183 45.11 40.595 45.0033 40.1616 44.79C39.7283 44.5767 39.3983 44.2767 39.1716 43.89C38.945 43.5033 38.8316 43.0467 38.8316 42.52C38.8316 42.02 38.9383 41.5767 39.1516 41.19C39.3716 40.8033 39.675 40.5 40.0616 40.28C40.455 40.0533 40.9116 39.94 41.4316 39.94C41.9183 39.94 42.3416 40.0433 42.7016 40.25C43.0616 40.45 43.3416 40.7433 43.5416 41.13C43.7416 41.51 43.8416 41.9633 43.8416 42.49V42.89H40.0916V42.06H42.6916L42.5516 42.21C42.5516 41.7967 42.4583 41.4867 42.2716 41.28C42.085 41.0733 41.8216 40.97 41.4816 40.97C41.235 40.97 41.0216 41.0267 40.8416 41.14C40.6683 41.2467 40.5316 41.4033 40.4316 41.61C40.3383 41.8167 40.2916 42.07 40.2916 42.37V42.48C40.2916 42.82 40.345 43.1 40.4516 43.32C40.5583 43.5333 40.7216 43.6933 40.9416 43.8C41.1683 43.9 41.4483 43.95 41.7816 43.95C42.0416 43.95 42.3083 43.9133 42.5816 43.84C42.8616 43.7667 43.1083 43.6533 43.3216 43.5L43.7316 44.53C43.4716 44.7167 43.1616 44.86 42.8016 44.96C42.4416 45.06 42.085 45.11 41.7316 45.11ZM44.8531 45V40.04H46.3231V40.99H46.2731C46.4331 40.65 46.6665 40.39 46.9731 40.21C47.2865 40.03 47.6365 39.94 48.0231 39.94C48.4231 39.94 48.7565 40.0167 49.0231 40.17C49.2898 40.3233 49.4898 40.56 49.6231 40.88C49.7565 41.1933 49.8231 41.5933 49.8231 42.08V45H48.2631V42.14C48.2631 41.9133 48.2331 41.73 48.1731 41.59C48.1198 41.45 48.0365 41.3467 47.9231 41.28C47.8098 41.2067 47.6631 41.17 47.4831 41.17C47.2698 41.17 47.0831 41.2167 46.9231 41.31C46.7631 41.4033 46.6365 41.5367 46.5431 41.71C46.4565 41.8767 46.4131 42.07 46.4131 42.29V45H44.8531ZM52.8134 45.11C52.4467 45.11 52.1167 45.04 51.8234 44.9C51.5367 44.7533 51.31 44.56 51.1434 44.32C50.9767 44.08 50.8934 43.8067 50.8934 43.5C50.8934 43.14 50.9934 42.8533 51.1934 42.64C51.3934 42.42 51.7067 42.26 52.1334 42.16C52.5667 42.06 53.1367 42.01 53.8434 42.01H54.4334V42.82H53.8834C53.6234 42.82 53.4 42.8333 53.2134 42.86C53.0267 42.88 52.8734 42.9133 52.7534 42.96C52.6334 43.0067 52.5434 43.07 52.4834 43.15C52.43 43.2233 52.4034 43.3167 52.4034 43.43C52.4034 43.6233 52.4734 43.7767 52.6134 43.89C52.7534 43.9967 52.9434 44.05 53.1834 44.05C53.3834 44.05 53.5634 44.0067 53.7234 43.92C53.8834 43.8267 54.01 43.7 54.1034 43.54C54.2034 43.3733 54.2534 43.1867 54.2534 42.98V41.87C54.2534 41.5767 54.1734 41.37 54.0134 41.25C53.86 41.13 53.6067 41.07 53.2534 41.07C52.9934 41.07 52.7134 41.11 52.4134 41.19C52.12 41.27 51.8167 41.3933 51.5034 41.56L51.1034 40.53C51.3034 40.41 51.53 40.3067 51.7834 40.22C52.0434 40.1267 52.3134 40.0567 52.5934 40.01C52.8734 39.9633 53.1467 39.94 53.4134 39.94C53.92 39.94 54.3467 40.0167 54.6934 40.17C55.04 40.3167 55.3 40.5467 55.4734 40.86C55.6534 41.1733 55.7434 41.58 55.7434 42.08V45H54.3634V44.01H54.3834C54.3234 44.23 54.22 44.4233 54.0734 44.59C53.9334 44.75 53.7567 44.8767 53.5434 44.97C53.3367 45.0633 53.0934 45.11 52.8134 45.11ZM57.0113 45V40.04H58.4813V40.99H58.4313C58.5913 40.65 58.8247 40.39 59.1313 40.21C59.4447 40.03 59.7947 39.94 60.1813 39.94C60.5813 39.94 60.9147 40.0167 61.1813 40.17C61.448 40.3233 61.648 40.56 61.7813 40.88C61.9147 41.1933 61.9813 41.5933 61.9813 42.08V45H60.4213V42.14C60.4213 41.9133 60.3913 41.73 60.3313 41.59C60.278 41.45 60.1947 41.3467 60.0813 41.28C59.968 41.2067 59.8213 41.17 59.6413 41.17C59.428 41.17 59.2413 41.2167 59.0813 41.31C58.9213 41.4033 58.7947 41.5367 58.7013 41.71C58.6147 41.8767 58.5713 42.07 58.5713 42.29V45H57.0113ZM65.7216 45.11C64.9949 45.11 64.4582 44.9333 64.1116 44.58C63.7649 44.2267 63.5916 43.7133 63.5916 43.04V41.2H62.6416V40.04H63.6316V38.63H65.1416V40.04H66.7016V41.2H65.1416V42.99C65.1416 43.2767 65.2116 43.4933 65.3516 43.64C65.4916 43.78 65.7082 43.85 66.0016 43.85C66.1016 43.85 66.2049 43.84 66.3116 43.82C66.4249 43.7933 66.5382 43.7633 66.6516 43.73L66.8716 44.86C66.7116 44.94 66.5316 45 66.3316 45.04C66.1316 45.0867 65.9282 45.11 65.7216 45.11Z" fill="#828286"/>
                    <path d="M20.207 25.1895L16 31.5L26.5 31.5L21.8454 25.1529C21.4347 24.5928 20.5922 24.6117 20.207 25.1895Z" fill="#D3D5DA"/>
                </g>
                <g id="resultGraphDot" class="result-graph-dot" filter="url(#filter3_d_394_10)">
                    <circle cx="317.55" cy="181.55" r="7.55031" fill="white"/>
                    <circle cx="317.55" cy="181.55" r="7.05031" stroke="#2D2D2D" stroke-opacity="0.12"/>
                </g>
                <rect class="result-graph-goal" x="285" y="131" width="45" height="32" rx="8" fill="#323334"/>
                <path class="result-graph-goal" d="M292.797 145L296.027 137.95H297.347L300.577 145H298.967L298.127 143.04L298.767 143.54H294.587L295.227 143.04L294.397 145H292.797ZM296.657 139.6L295.377 142.66L295.117 142.28H298.247L297.977 142.66L296.697 139.6H296.657ZM301.202 146.8V140.04H302.662V141H302.622C302.748 140.673 302.962 140.417 303.262 140.23C303.568 140.037 303.922 139.94 304.322 139.94C304.768 139.94 305.155 140.047 305.482 140.26C305.815 140.473 306.072 140.773 306.252 141.16C306.432 141.54 306.522 141.993 306.522 142.52C306.522 143.033 306.432 143.487 306.252 143.88C306.078 144.267 305.825 144.57 305.492 144.79C305.165 145.003 304.775 145.11 304.322 145.11C303.935 145.11 303.598 145.02 303.312 144.84C303.025 144.653 302.822 144.413 302.702 144.12H302.762V146.8H301.202ZM303.852 143.94C304.198 143.94 304.472 143.82 304.672 143.58C304.872 143.333 304.972 142.977 304.972 142.51C304.972 142.043 304.872 141.693 304.672 141.46C304.472 141.227 304.198 141.11 303.852 141.11C303.512 141.11 303.238 141.227 303.032 141.46C302.832 141.693 302.732 142.043 302.732 142.51C302.732 142.977 302.832 143.333 303.032 143.58C303.238 143.82 303.512 143.94 303.852 143.94ZM307.569 145V140.04H309.009V141.27H308.969C309.062 140.877 309.242 140.567 309.509 140.34C309.782 140.113 310.136 139.987 310.569 139.96L311.099 139.92L311.209 141.28L310.279 141.37C309.899 141.403 309.612 141.52 309.419 141.72C309.232 141.92 309.139 142.19 309.139 142.53V145H307.569ZM314.526 145.11C313.912 145.11 313.389 145.003 312.956 144.79C312.522 144.577 312.192 144.277 311.966 143.89C311.739 143.503 311.626 143.047 311.626 142.52C311.626 142.02 311.732 141.577 311.946 141.19C312.166 140.803 312.469 140.5 312.856 140.28C313.249 140.053 313.706 139.94 314.226 139.94C314.712 139.94 315.136 140.043 315.496 140.25C315.856 140.45 316.136 140.743 316.336 141.13C316.536 141.51 316.636 141.963 316.636 142.49V142.89H312.886V142.06H315.486L315.346 142.21C315.346 141.797 315.252 141.487 315.066 141.28C314.879 141.073 314.616 140.97 314.276 140.97C314.029 140.97 313.816 141.027 313.636 141.14C313.462 141.247 313.326 141.403 313.226 141.61C313.132 141.817 313.086 142.07 313.086 142.37V142.48C313.086 142.82 313.139 143.1 313.246 143.32C313.352 143.533 313.516 143.693 313.736 143.8C313.962 143.9 314.242 143.95 314.576 143.95C314.836 143.95 315.102 143.913 315.376 143.84C315.656 143.767 315.902 143.653 316.116 143.5L316.526 144.53C316.266 144.717 315.956 144.86 315.596 144.96C315.236 145.06 314.879 145.11 314.526 145.11ZM313.716 139.4L312.226 137.36H313.766L314.786 139.4H313.716ZM319.607 145.11C319.307 145.11 319.017 145.087 318.737 145.04C318.464 145 318.21 144.94 317.977 144.86C317.75 144.78 317.55 144.68 317.377 144.56L317.747 143.54C317.92 143.64 318.107 143.727 318.307 143.8C318.514 143.873 318.724 143.93 318.937 143.97C319.157 144.003 319.37 144.02 319.577 144.02C319.89 144.02 320.12 143.98 320.267 143.9C320.414 143.813 320.487 143.697 320.487 143.55C320.487 143.423 320.44 143.327 320.347 143.26C320.26 143.193 320.127 143.147 319.947 143.12L318.847 142.95C318.4 142.877 318.06 142.72 317.827 142.48C317.6 142.233 317.487 141.93 317.487 141.57C317.487 141.243 317.577 140.96 317.757 140.72C317.937 140.473 318.197 140.283 318.537 140.15C318.884 140.01 319.294 139.94 319.767 139.94C320.014 139.94 320.257 139.96 320.497 140C320.737 140.04 320.96 140.1 321.167 140.18C321.38 140.253 321.567 140.353 321.727 140.48L321.327 141.49C321.194 141.39 321.04 141.307 320.867 141.24C320.694 141.167 320.51 141.113 320.317 141.08C320.13 141.04 319.954 141.02 319.787 141.02C319.46 141.02 319.224 141.063 319.077 141.15C318.93 141.237 318.857 141.357 318.857 141.51C318.857 141.623 318.897 141.717 318.977 141.79C319.064 141.857 319.194 141.903 319.367 141.93L320.437 142.1C320.917 142.18 321.274 142.333 321.507 142.56C321.747 142.787 321.867 143.093 321.867 143.48C321.867 143.82 321.774 144.113 321.587 144.36C321.4 144.6 321.137 144.787 320.797 144.92C320.457 145.047 320.06 145.11 319.607 145.11ZM294.158 155.11C293.831 155.11 293.511 155.077 293.198 155.01C292.885 154.95 292.591 154.863 292.318 154.75C292.051 154.63 291.825 154.49 291.638 154.33L292.138 153.15C292.438 153.377 292.755 153.543 293.088 153.65C293.421 153.757 293.761 153.81 294.108 153.81C294.361 153.81 294.578 153.777 294.758 153.71C294.938 153.643 295.075 153.547 295.168 153.42C295.268 153.287 295.318 153.12 295.318 152.92C295.318 152.64 295.221 152.43 295.028 152.29C294.835 152.143 294.551 152.07 294.178 152.07H292.958V150.82H294.018C294.238 150.82 294.428 150.787 294.588 150.72C294.755 150.647 294.881 150.547 294.968 150.42C295.061 150.287 295.108 150.127 295.108 149.94C295.108 149.673 295.015 149.473 294.828 149.34C294.648 149.207 294.391 149.14 294.058 149.14C293.725 149.14 293.408 149.193 293.108 149.3C292.808 149.407 292.525 149.577 292.258 149.81L291.748 148.66C292.041 148.4 292.405 148.2 292.838 148.06C293.278 147.913 293.731 147.84 294.198 147.84C294.698 147.84 295.128 147.917 295.488 148.07C295.855 148.217 296.131 148.433 296.318 148.72C296.511 149 296.608 149.34 296.608 149.74C296.608 150.14 296.498 150.487 296.278 150.78C296.065 151.073 295.761 151.277 295.368 151.39V151.32C295.661 151.38 295.915 151.487 296.128 151.64C296.348 151.793 296.518 151.99 296.638 152.23C296.758 152.463 296.818 152.737 296.818 153.05C296.818 153.477 296.708 153.847 296.488 154.16C296.275 154.467 295.968 154.703 295.568 154.87C295.175 155.03 294.705 155.11 294.158 155.11ZM300.895 155V150.04H302.355V151H302.305C302.398 150.78 302.525 150.593 302.685 150.44C302.845 150.28 303.035 150.157 303.255 150.07C303.475 149.983 303.715 149.94 303.975 149.94C304.355 149.94 304.675 150.033 304.935 150.22C305.201 150.4 305.388 150.667 305.495 151.02H305.425C305.558 150.7 305.775 150.44 306.075 150.24C306.381 150.04 306.738 149.94 307.145 149.94C307.525 149.94 307.841 150.017 308.095 150.17C308.355 150.323 308.548 150.56 308.675 150.88C308.801 151.193 308.865 151.593 308.865 152.08V155H307.315V152.12C307.315 151.793 307.261 151.553 307.155 151.4C307.055 151.247 306.871 151.17 306.605 151.17C306.411 151.17 306.241 151.217 306.095 151.31C305.948 151.403 305.835 151.533 305.755 151.7C305.681 151.867 305.645 152.07 305.645 152.31V155H304.105V152.12C304.105 151.793 304.048 151.553 303.935 151.4C303.828 151.247 303.648 151.17 303.395 151.17C303.201 151.17 303.031 151.22 302.885 151.32C302.738 151.413 302.625 151.543 302.545 151.71C302.471 151.877 302.435 152.073 302.435 152.3V155H300.895ZM312.572 155.11C312.032 155.11 311.562 155.003 311.162 154.79C310.762 154.577 310.452 154.277 310.232 153.89C310.018 153.497 309.912 153.04 309.912 152.52C309.912 151.993 310.018 151.537 310.232 151.15C310.452 150.763 310.762 150.467 311.162 150.26C311.562 150.047 312.032 149.94 312.572 149.94C313.112 149.94 313.578 150.047 313.972 150.26C314.372 150.467 314.682 150.763 314.902 151.15C315.122 151.537 315.232 151.993 315.232 152.52C315.232 153.04 315.122 153.497 314.902 153.89C314.682 154.277 314.372 154.577 313.972 154.79C313.578 155.003 313.112 155.11 312.572 155.11ZM312.572 153.92C312.912 153.92 313.182 153.8 313.382 153.56C313.588 153.32 313.692 152.97 313.692 152.51C313.692 152.05 313.588 151.707 313.382 151.48C313.182 151.247 312.912 151.13 312.572 151.13C312.232 151.13 311.958 151.247 311.752 151.48C311.552 151.707 311.452 152.05 311.452 152.51C311.452 152.97 311.552 153.32 311.752 153.56C311.958 153.8 312.232 153.92 312.572 153.92ZM316.215 149.11V147.66H317.895V149.11H316.215ZM316.285 155V150.04H317.845V155H316.285ZM321.096 155.11C320.796 155.11 320.506 155.087 320.226 155.04C319.953 155 319.7 154.94 319.466 154.86C319.24 154.78 319.04 154.68 318.866 154.56L319.236 153.54C319.41 153.64 319.596 153.727 319.796 153.8C320.003 153.873 320.213 153.93 320.426 153.97C320.646 154.003 320.86 154.02 321.066 154.02C321.38 154.02 321.61 153.98 321.756 153.9C321.903 153.813 321.976 153.697 321.976 153.55C321.976 153.423 321.93 153.327 321.836 153.26C321.75 153.193 321.616 153.147 321.436 153.12L320.336 152.95C319.89 152.877 319.55 152.72 319.316 152.48C319.09 152.233 318.976 151.93 318.976 151.57C318.976 151.243 319.066 150.96 319.246 150.72C319.426 150.473 319.686 150.283 320.026 150.15C320.373 150.01 320.783 149.94 321.256 149.94C321.503 149.94 321.746 149.96 321.986 150C322.226 150.04 322.45 150.1 322.656 150.18C322.87 150.253 323.056 150.353 323.216 150.48L322.816 151.49C322.683 151.39 322.53 151.307 322.356 151.24C322.183 151.167 322 151.113 321.806 151.08C321.62 151.04 321.443 151.02 321.276 151.02C320.95 151.02 320.713 151.063 320.566 151.15C320.42 151.237 320.346 151.357 320.346 151.51C320.346 151.623 320.386 151.717 320.466 151.79C320.553 151.857 320.683 151.903 320.856 151.93L321.926 152.1C322.406 152.18 322.763 152.333 322.996 152.56C323.236 152.787 323.356 153.093 323.356 153.48C323.356 153.82 323.263 154.113 323.076 154.36C322.89 154.6 322.626 154.787 322.286 154.92C321.946 155.047 321.55 155.11 321.096 155.11Z" fill="white"/>
                <defs>
                    <filter id="filter0_d_394_10" x="270.5" y="131" width="56.5" height="46.7559" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                        <feOffset dy="4"/>
                        <feGaussianBlur stdDeviation="2"/>
                        <feComposite in2="hardAlpha" operator="out"/>
                        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.07 0"/>
                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_394_10"/>
                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_394_10" result="shape"/>
                    </filter>
                    <filter id="filter1_d_394_10" x="5" y="3.31447" width="31.1006" height="31.1006" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                        <feOffset dy="4.31447"/>
                        <feGaussianBlur stdDeviation="4"/>
                        <feComposite in2="hardAlpha" operator="out"/>
                        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_394_10"/>
                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_394_10" result="shape"/>
                    </filter>
                    <filter id="filter2_d_394_10" x="0" y="24.7441" width="77" height="36.2559" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                        <feOffset dy="4"/>
                        <feGaussianBlur stdDeviation="2"/>
                        <feComposite in2="hardAlpha" operator="out"/>
                        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.07 0"/>
                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_394_10"/>
                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_394_10" result="shape"/>
                    </filter>
                    <filter id="filter3_d_394_10" x="302" y="170.314" width="31.1006" height="31.1006" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                        <feOffset dy="4.31447"/>
                        <feGaussianBlur stdDeviation="4"/>
                        <feComposite in2="hardAlpha" operator="out"/>
                        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_394_10"/>
                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_394_10" result="shape"/>
                    </filter>
                    <linearGradient id="paint0_linear_394_10" x1="26.9914" y1="181.708" x2="313.914" y2="181.708" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#FF574C"/>
                        <stop offset="0.549638" stop-color="#F5E088"/>
                        <stop offset="1" stop-color="#1998CD"/>
                    </linearGradient>
                    <linearGradient id="paint1_linear_394_10" x1="20.5" y1="182.999" x2="316.5" y2="182.999" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#FF574C"/>
                        <stop offset="0.549638" stop-color="#F5E088"/>
                        <stop offset="1" stop-color="#1998CD"/>
                    </linearGradient>
                </defs>
            </svg>

            <div class="quiz__reviews-loading-svg-date-wrapp">
                <p class="quiz__reviews-loading-svg-date">
                    MAINTENANT
                </p>
                <p class="quiz__reviews-loading-svg-date" id="monthAfter1">
                    month 1
                </p>
                <p class="quiz__reviews-loading-svg-date" id="monthAfter2">
                    month 2
                </p>
                <p class="quiz__reviews-loading-svg-date" id="monthAfter3">
                    month 3
                </p>
            </div>
            <p class="quiz__reviews-loading-svg-date-text">
                Ce tableau est à titre indicatif uniquement
            </p>
        </div>

        <p class="quiz__reviews-loading-subtext">
            <span>Le plan de dressage</span> de votre <span class="puppyInsertSelector" data-puppy="chiot">chien</span> est prêt
        </p>
        <div class="quiz__btn-box">
            <button class="quiz__btn js-btn email-submit" id="redirect-to-landing-btn" data-landing-url="/questionary/landing-19">
                <span></span>
                Continuer
            </button>
        </div>
    </div>
    <div class="quiz__redirect-loader" id="resultLoader">
        <div class="preload">
            <span></span>
        </div>
    </div>
</div>

<div class="quiz__popup-wrapp" id="quizPopUpProblem">
    <div class="quiz__popup-bg"></div>

    <div class="quiz__popup" id="magicProblemPopup">
        <div class="quiz__popup-box">
            <h4 class="quiz__popup-title">
                Encore une question
            </h4>
            {% if quizData.isPuppyFunnel %}
                <div class="quiz__popup-img-box">
                    <picture>
                        <source srcset="https://res.cloudinary.com/dr0cx27xo/image/upload/v1719408096/dog-training/img/reactivity/webp/puppy-with-blue-ball.webp">
                        <img id="resultPopup" class="quiz__popup-img" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1719408087/dog-training/img/reactivity/puppy-with-blue-ball.jpg" alt="puppy with blue ball">
                    </picture>
                </div>
                <div class="quiz__popup-text">
                    Les <span class="ageInsertSelector">chiots</span> gardent souvent leurs jouets, leur lit ou leurs friandises.
                    Avez-vous ce problème&nbsp;?
                </div>
            {% else %}
                <div class="quiz__popup-img-box">
                    <picture>
                        <source srcset="https://res.cloudinary.com/dr0cx27xo/image/upload/v1719408098/dog-training/img/reactivity/webp/dog-with-teddy-bear-in-mouth.webp">
                        <img id="resultPopup" class="quiz__popup-img" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1719408088/dog-training/img/reactivity/dog-with-teddy-bear-in-mouth.jpg" alt="dog with teddy bear in mouth">
                    </picture>
                </div>
                <div class="quiz__popup-text">
                    Les <span class="ageInsertSelector capitalize"></span> gardent souvent leurs jouets, leur lit ou leurs friandises.
                    Avez-vous ce problème&nbsp;?
                </div>
            {% endif %}
            <ul class="quiz__popup-btn-list">
                <li class="quiz__popup-btn-item quizPopUpProblemBtn" data-event="No">
                    Non
                </li>
                <li class="quiz__popup-btn-item quizPopUpProblemBtn" data-event="Yes">
                    Oui
                </li>
            </ul>
        </div>
    </div>
</div>
<div class="quiz__popup-wrapp" id="quizPopUpObedience">
    <div class="quiz__popup-bg"></div>

    <div class="quiz__popup" id="magicObediencePopup">
        <div class="quiz__popup-box">
            <h4 class="quiz__popup-title">
                Finaliser votre plan
            </h4>
            <div class="quiz__popup-img-box">
                <img id="resultPopup" class="quiz__popup-img" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1719408097/dog-training/img/reactivity/webp/dog-sit-command.webp" alt="dog sit command">
            </div>
            <div class="quiz__popup-text">
                <span class="nameInsertSelector capitalize">Chiot</span> a-t-il déjà suivi une formation de désensibilisation ?
            </div>
            <ul class="quiz__popup-btn-list">
                <li class="quiz__popup-btn-item quizPopUpObedienceBtn" data-event="No">
                    Non
                </li>
                <li class="quiz__popup-btn-item quizPopUpObedienceBtn" data-event="Yes">
                    Oui
                </li>
            </ul>
        </div>
    </div>
</div>
