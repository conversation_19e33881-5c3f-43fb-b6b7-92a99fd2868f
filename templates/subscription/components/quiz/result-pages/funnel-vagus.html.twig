{% set clientCountry = getCountry()|upper %}

{% set discountPercents = '60%' %}
{% if clientCountry == 'GB' %}
    {% set discountPercents = '50%' %}
{% endif %}

<div class="quiz__index quiz__result skipQuiz magic  {% if isPcTrustSplitGroup2Active %}trust{% endif %}" id="block{{ blockId }}" data-id="{{ blockId }}" data-result="reviews">
    <div class="container " id="resultStepOne">
        <div id="circleBar" class="progressbar reviews">
            <div class="progressbar__shadow"></div>
        </div>
        <p class="quiz__reviews-loading-subtitle" id="replaceSubtitle" data-i18n="quiz.result.result_step_one.subtitle">
        </p>
        <div class="quiz__hr">
        </div>
        <div class="quiz__reviews-loading-title-red-box">
            <h3 class="quiz__reviews-loading-title-red">
                {{ 'quiz.result.result_step_one.title_red'|trans(domain=funnelTranslationsDomain) }}
            </h3>
        </div>
        <p class="quiz__reviews-loading-text">
            {{ 'quiz.result.result_step_one.text'|trans(domain=funnelTranslationsDomain) }}
        </p>
        <div class="quiz__reviews-slider custom-result">
            <ul class="swiper-wrapper">
                <li class="quiz__reviews-slider-item swiper-slide">
                    <div class="quiz__reviews-slider-item-text-wrapp">
                        <svg width="90" height="16" viewBox="0 0 90 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                    d="M9.46744 6.37307H14.2218L10.3768 9.16159L10.3767 9.16159L8.00391 10.8849L10.7058 10.1835L10.3789 9.16822L11.8403 13.6821L7.99524 10.8849L4.15019 13.6821L5.62239 9.16159L1.77734 6.36441L6.53169 6.37307L8.0039 1.85254L9.46744 6.37307Z"
                                    fill="white"/>
                            <rect x="18.3711" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                    d="M27.8385 6.37307H32.5929L28.7478 9.16159L28.7478 9.16159L26.375 10.8849L29.0769 10.1835L28.75 9.16822L30.2114 13.6821L26.3663 10.8849L22.5213 13.6821L23.9935 9.16159L20.1484 6.36441L24.9028 6.37307L26.375 1.85254L27.8385 6.37307Z"
                                    fill="white"/>
                            <rect x="36.7402" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                    d="M46.2077 6.37307H50.962L47.117 9.16159L47.117 9.16159L44.7441 10.8849L47.4461 10.1835L47.1191 9.16822L48.5805 13.6821L44.7355 10.8849L40.8904 13.6821L42.3626 9.16159L38.5176 6.36441L43.2719 6.37307L44.7441 1.85254L46.2077 6.37307Z"
                                    fill="white"/>
                            <rect x="55.1113" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                    d="M64.5788 6.37307H69.3331L65.4881 9.16159L65.4883 9.16217L63.1162 10.8849L65.8181 10.1835L65.6738 9.7352L66.9516 13.6821L63.1066 10.8849L59.2615 13.6821L60.7337 9.16159L56.8887 6.36441L61.643 6.37307L63.1152 1.85254L64.5788 6.37307Z"
                                    fill="white"/>
                            <rect x="73.4824" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                    d="M82.9499 6.37307H87.7042L83.8592 9.16159L83.8592 9.16159L81.4863 10.8849L84.1883 10.1835L83.8613 9.16822L85.3227 13.6821L81.4777 10.8849L77.6326 13.6821L79.1048 9.16159L75.2598 6.36441L80.0141 6.37307L81.4863 1.85254L82.9499 6.37307Z"
                                    fill="white"/>
                        </svg>
                        <div class="quiz__reviews-slider-item-name-wrapp">
                            <h5 data-i18n="quiz.result.reviews.calming_approach.title">
                            </h5>
                            <p class="quiz__reviews-slider-item-name">
                                Ernesto
                            </p>
                        </div>
                        <div class="quiz__reviews-slider-item-text" data-i18n="quiz.result.reviews.calming_approach.text">
                        </div>
                    </div>
                </li>
                <li class="quiz__reviews-slider-item swiper-slide">
                    <div class="quiz__reviews-slider-item-text-wrapp">
                        <svg width="90" height="16" viewBox="0 0 90 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                    d="M9.46744 6.37307H14.2218L10.3768 9.16159L10.3767 9.16159L8.00391 10.8849L10.7058 10.1835L10.3789 9.16822L11.8403 13.6821L7.99524 10.8849L4.15019 13.6821L5.62239 9.16159L1.77734 6.36441L6.53169 6.37307L8.0039 1.85254L9.46744 6.37307Z"
                                    fill="white"/>
                            <rect x="18.3711" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                    d="M27.8385 6.37307H32.5929L28.7478 9.16159L28.7478 9.16159L26.375 10.8849L29.0769 10.1835L28.75 9.16822L30.2114 13.6821L26.3663 10.8849L22.5213 13.6821L23.9935 9.16159L20.1484 6.36441L24.9028 6.37307L26.375 1.85254L27.8385 6.37307Z"
                                    fill="white"/>
                            <rect x="36.7402" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                    d="M46.2077 6.37307H50.962L47.117 9.16159L47.117 9.16159L44.7441 10.8849L47.4461 10.1835L47.1191 9.16822L48.5805 13.6821L44.7355 10.8849L40.8904 13.6821L42.3626 9.16159L38.5176 6.36441L43.2719 6.37307L44.7441 1.85254L46.2077 6.37307Z"
                                    fill="white"/>
                            <rect x="55.1113" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                    d="M64.5788 6.37307H69.3331L65.4881 9.16159L65.4883 9.16217L63.1162 10.8849L65.8181 10.1835L65.6738 9.7352L66.9516 13.6821L63.1066 10.8849L59.2615 13.6821L60.7337 9.16159L56.8887 6.36441L61.643 6.37307L63.1152 1.85254L64.5788 6.37307Z"
                                    fill="white"/>
                            <rect x="73.4824" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                    d="M82.9499 6.37307H87.7042L83.8592 9.16159L83.8592 9.16159L81.4863 10.8849L84.1883 10.1835L83.8613 9.16822L85.3227 13.6821L81.4777 10.8849L77.6326 13.6821L79.1048 9.16159L75.2598 6.36441L80.0141 6.37307L81.4863 1.85254L82.9499 6.37307Z"
                                    fill="white"/>
                        </svg>
                        <div class="quiz__reviews-slider-item-name-wrapp">
                            <h5 data-i18n="quiz.result.reviews.structure_brilliant.title">
                            </h5>
                            <p class="quiz__reviews-slider-item-name">
                                Claire_Ugalde
                            </p>
                        </div>
                        <div class="quiz__reviews-slider-item-text" data-i18n="quiz.result.reviews.structure_brilliant.text">
                        </div>
                    </div>
                </li>
                <li class="quiz__reviews-slider-item swiper-slide">
                    <div class="quiz__reviews-slider-item-text-wrapp">
                        <svg width="90" height="16" viewBox="0 0 90 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                    d="M9.46744 6.37307H14.2218L10.3768 9.16159L10.3767 9.16159L8.00391 10.8849L10.7058 10.1835L10.3789 9.16822L11.8403 13.6821L7.99524 10.8849L4.15019 13.6821L5.62239 9.16159L1.77734 6.36441L6.53169 6.37307L8.0039 1.85254L9.46744 6.37307Z"
                                    fill="white"/>
                            <rect x="18.3711" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                    d="M27.8385 6.37307H32.5929L28.7478 9.16159L28.7478 9.16159L26.375 10.8849L29.0769 10.1835L28.75 9.16822L30.2114 13.6821L26.3663 10.8849L22.5213 13.6821L23.9935 9.16159L20.1484 6.36441L24.9028 6.37307L26.375 1.85254L27.8385 6.37307Z"
                                    fill="white"/>
                            <rect x="36.7402" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                    d="M46.2077 6.37307H50.962L47.117 9.16159L47.117 9.16159L44.7441 10.8849L47.4461 10.1835L47.1191 9.16822L48.5805 13.6821L44.7355 10.8849L40.8904 13.6821L42.3626 9.16159L38.5176 6.36441L43.2719 6.37307L44.7441 1.85254L46.2077 6.37307Z"
                                    fill="white"/>
                            <rect x="55.1113" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                    d="M64.5788 6.37307H69.3331L65.4881 9.16159L65.4883 9.16217L63.1162 10.8849L65.8181 10.1835L65.6738 9.7352L66.9516 13.6821L63.1066 10.8849L59.2615 13.6821L60.7337 9.16159L56.8887 6.36441L61.643 6.37307L63.1152 1.85254L64.5788 6.37307Z"
                                    fill="white"/>
                            <rect x="73.4824" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                    d="M82.9499 6.37307H87.7042L83.8592 9.16159L83.8592 9.16159L81.4863 10.8849L84.1883 10.1835L83.8613 9.16822L85.3227 13.6821L81.4777 10.8849L77.6326 13.6821L79.1048 9.16159L75.2598 6.36441L80.0141 6.37307L81.4863 1.85254L82.9499 6.37307Z"
                                    fill="white"/>
                        </svg>
                        <div class="quiz__reviews-slider-item-name-wrapp">
                            <h5 data-i18n="quiz.result.reviews.excellent_course.title">
                            </h5>
                            <p class="quiz__reviews-slider-item-name">
                                Diego Fernandez Jr
                            </p>
                        </div>
                        <p class="quiz__reviews-slider-item-text" data-i18n="quiz.result.reviews.excellent_course.text">
                        </p>
                    </div>
                </li>
            </ul>
        </div>
    </div>

    <div class="container email" id="resultStepTwo">
        <h2 class="quiz__reviews-loading-title" data-i18n="quiz.result.result_step_two.title">
        </h2>
        <p class="quiz__result-label" id="emailText"></p>
        <div class="quiz__result-box">
            <ul id="autoComplete" class="email-autocomplete email-autocomplete-scroll"></ul>
            <p class="quiz__result-text black" data-i18n="quiz.result.result_step_two.enter_email_prompt">
            </p>
            <input class="quiz__result-input" name="text" type="text" placeholder="{{ 'quiz.result.result_step_two.email_placeholder'|trans(domain=funnelTranslationsDomain) }}"
                   id="email" autocomplete="off">
        </div>
        <div class="quiz__result-text-box">
            <svg class="quiz__result-text-icon" width="21" height="26" viewBox="0 0 21 26" fill="none"
                 xmlns="http://www.w3.org/2000/svg">
                <path opacity="0.45"
                      d="M20.1944 9.75H18.5278V7.58332C18.5278 3.40184 15.0387 0 10.75 0C6.46125 0 2.97224 3.40184 2.97224 7.58332V9.75H1.30557C0.99849 9.75 0.75 9.99228 0.75 10.2917V23.8334C0.75 25.0283 1.74661 26 2.97224 26H18.5278C19.7534 26 20.75 25.0283 20.75 23.8333V10.2917C20.75 9.99228 20.5015 9.75 20.1944 9.75ZM12.4134 21.0652C12.4308 21.2181 12.3804 21.3715 12.2751 21.4863C12.1698 21.6011 12.019 21.6667 11.8611 21.6667H9.63891C9.48104 21.6667 9.33021 21.6011 9.22495 21.4863C9.11969 21.3715 9.06922 21.2181 9.08661 21.0652L9.43708 17.9925C8.86797 17.5888 8.52781 16.9504 8.52781 16.25C8.52781 15.0551 9.52443 14.0833 10.7501 14.0833C11.9757 14.0833 12.9723 15.055 12.9723 16.25C12.9723 16.9504 12.6321 17.5888 12.063 17.9925L12.4134 21.0652ZM15.1944 9.75H6.30557V7.58332C6.30557 5.19396 8.29938 3.25 10.75 3.25C13.2006 3.25 15.1944 5.19396 15.1944 7.58332V9.75Z"
                      fill="#343434"/>
            </svg>
            <p class="quiz__result-text" data-i18n="quiz.result.result_step_two.privacy_note">
            </p>
        </div>
        <div class="quiz__btn-box">
            <button class="quiz__btn js-btn email-submit" id="email-submit" disabled>
                <span></span>
                <span data-i18n="quiz.result.continue_button"></span>
            </button>
        </div>
    </div>

    <div class="container " id="resultStepEmailSubscription">
        <h3 class="quiz__email-subscription-title" data-i18n="quiz.result.email_subscription.title">
        </h3>
        <div class="quiz__email-subscription-btn-box">
            <button class="quiz__btn triggerStep" data-event-value="Apply newsletter">
                <span></span>
                <span data-i18n="quiz.result.email_subscription.yes_button"></span>
            </button>
            <label class="quiz__email-subscription-label triggerStep" data-event-value="Reject newsletter">
                <input type="checkbox" class="quiz__email-subscription-input" id="disallowMarketingEmails">
                <span data-i18n="quiz.result.email_subscription.no_checkbox"></span>
            </label>
        </div>
    </div>

    <div class="container graph " id="resultStepThree">
        <h2 class="quiz__reviews-loading-title" data-i18n="quiz.result.result_step_three.title"></h2>
        <div class="quiz__reviews-loading-svg-inner">


            <svg class="quiz__reviews-loading-svg" viewBox="0 0 368 200" fill="none" xmlns="http://www.w3.org/2000/svg">
                <line y1="-0.5" x2="341.915" y2="-0.5" transform="matrix(-1 -8.74228e-08 -8.74228e-08 1 354 18)" stroke="#EAEDF0"/>
                <line y1="-0.5" x2="341.915" y2="-0.5" transform="matrix(-1 -8.74228e-08 -8.74228e-08 1 354 44)" stroke="#EAEDF0"/>
                <line y1="-0.5" x2="341.915" y2="-0.5" transform="matrix(-1 -8.74228e-08 -8.74228e-08 1 354 70)" stroke="#EAEDF0"/>
                <line y1="-0.5" x2="341.915" y2="-0.5" transform="matrix(-1 -8.74228e-08 -8.74228e-08 1 354 96)" stroke="#EAEDF0"/>
                <line y1="-0.5" x2="341.915" y2="-0.5" transform="matrix(-1 -8.74228e-08 -8.74228e-08 1 354 122)" stroke="#EAEDF0"/>
                <line y1="-0.5" x2="341.915" y2="-0.5" transform="matrix(-1 -8.74228e-08 -8.74228e-08 1 354 148)" stroke="#EAEDF0"/>
                <line y1="-0.5" x2="341.915" y2="-0.5" transform="matrix(-1 -8.74228e-08 -8.74228e-08 1 354 174)" stroke="#EAEDF0"/>
                <line y1="-0.5" x2="343" y2="-0.5" transform="matrix(-1 -8.74228e-08 -8.74228e-08 1 354 200)" stroke="#EAEDF0"/>
                <path class="iteration-show-el iteration-show-el-p10" d="M44.5547 200L44.5547 50" stroke="#E8E8E8"/>
                <path class="iteration-show-el iteration-show-el-p30" d="M136.926 200L136.926 103" stroke="#E8E8E8"/>
                <path class="iteration-show-el iteration-show-el-p60" d="M229 200L229 124.5" stroke="#E8E8E8"/>
                <path class="iteration-show-el iteration-show-el-p90" d="M321.67 200L321.67 143.5" stroke="#EAEDF0"/>
                <path id="resultGraphBg" class="result-graph-bg" opacity="0.2" fill-rule="evenodd" clip-rule="evenodd"
                      d="M247.552 127.934C260.519 129.358 271.884 130.607 281.196 132.445C307.373 137.614 322.739 146.18 332.35 154.396C339.759 160.73 343.754 166.799 346.833 171.476L346.834 171.477C347.423 172.372 347.978 173.215 348.517 174H353.932V200H11V22.8222C94.5704 111.13 187.975 121.39 247.552 127.934ZM353.932 172.63V172.496L354 172.588L353.932 172.63Z"
                      fill="url(#paint0_linear_519_71952)"/>
                <g class="result-graph-bg" opacity="0.3" filter="url(#filter0_f_519_71952)">
                    <path d="M352.846 182C346.155 173 336.028 148.5 282.863 138C229.698 127.5 113.603 136 12.1555 27" stroke="url(#paint1_linear_519_71952)" stroke-width="5"/>
                </g>
                <path class="quiz__based-graph-line" stroke-linecap="round" d="M12.1555 20C113.603 129, 229.698 120.5, 282.863 131C336.028 141.5, 346.155 166, 352.846 175" stroke="url(#paint2_linear_519_71952)" stroke-width="5"/>
                <g class="iteration-show-el iteration-show-el-p10" filter="url(#filter3_d_519_71952)">
                    <circle cx="7.55031" cy="7.55031" r="7.55031" transform="matrix(-1 0 0 1 24 14)" fill="white"/>
                    <circle cx="7.55031" cy="7.55031" r="6.55031" transform="matrix(-1 0 0 1 24 14)"
                            stroke="url(#paint5_linear_519_71952)" stroke-opacity="0.12" stroke-width="2"/>
                </g>
                <g class="iteration-show-el iteration-show-el-p30" filter="url(#filter2_d_519_71952)">
                    <circle cx="7.55031" cy="7.55031" r="7.55031" transform="matrix(-1 0 0 1 143 95)" fill="white"/>
                    <circle cx="7.55031" cy="7.55031" r="6.55031" transform="matrix(-1 0 0 1 143 95)"
                            stroke="url(#paint4_linear_519_71952)" stroke-opacity="0.12" stroke-width="2"/>
                </g>
                <g class="iteration-show-el iteration-show-el-p60" filter="url(#filter1_d_519_71952)">
                    <circle cx="7.55031" cy="7.55031" r="7.55031" transform="matrix(-1 0 0 1 234 115)" fill="white"/>
                    <circle cx="7.55031" cy="7.55031" r="6.55031" transform="matrix(-1 0 0 1 234 115)"
                            stroke="url(#paint3_linear_519_71952)" stroke-opacity="0.12" stroke-width="2"/>
                </g>
                <g id="resultGraphDot" class="result-graph-dot" filter="url(#filter4_d_519_71952)">
                    <circle cx="7.55031" cy="7.55031" r="7.55031" transform="matrix(-1 0 0 1 327 136)" fill="white"/>
                    <circle cx="7.55031" cy="7.55031" r="6.55031" transform="matrix(-1 0 0 1 327 136)"
                            stroke="url(#paint6_linear_519_71952)" stroke-opacity="0.12" stroke-width="2"/>
                </g>
                <g class="result-graph-goal" filter="url(#filter5_d_519_71952)">
                    <rect width="81" height="36" rx="4" transform="matrix(-1 0 0 1 360 86)" fill="#1998CD"/>
                    <path d="M304.655 102L308.543 93.54H310.043L313.931 102H312.059L311.051 99.648L311.795 100.164H306.791L307.535 99.648L306.539 102H304.655ZM309.275 95.52L307.739 99.18L307.415 98.712H311.171L310.847 99.18L309.299 95.52H309.275ZM314.822 102V97.464H313.706V96.108H315.35L314.822 96.588V96.216C314.822 95.344 315.054 94.692 315.518 94.26C315.982 93.82 316.706 93.572 317.69 93.516L318.35 93.468L318.458 94.788L317.894 94.824C317.582 94.84 317.334 94.892 317.15 94.98C316.966 95.06 316.834 95.176 316.754 95.328C316.674 95.472 316.634 95.664 316.634 95.904V96.312L316.406 96.108H318.17V97.464H316.634V102H314.822ZM321.488 102.132C320.648 102.132 320.024 101.928 319.616 101.52C319.208 101.112 319.004 100.512 319.004 99.72V97.464H317.888V96.108H319.004V94.38H320.816V96.108H322.556V97.464H320.816V99.648C320.816 99.984 320.892 100.236 321.044 100.404C321.204 100.572 321.456 100.656 321.8 100.656C321.904 100.656 322.012 100.644 322.124 100.62C322.244 100.596 322.372 100.564 322.508 100.524L322.772 101.844C322.604 101.932 322.404 102 322.172 102.048C321.94 102.104 321.712 102.132 321.488 102.132ZM326.449 102.132C325.753 102.132 325.153 102.008 324.649 101.76C324.153 101.504 323.769 101.148 323.497 100.692C323.233 100.228 323.101 99.684 323.101 99.06C323.101 98.452 323.229 97.92 323.485 97.464C323.741 97 324.097 96.64 324.553 96.384C325.017 96.12 325.541 95.988 326.125 95.988C326.701 95.988 327.197 96.112 327.613 96.36C328.029 96.6 328.349 96.944 328.573 97.392C328.805 97.84 328.921 98.372 328.921 98.988V99.444H324.589V98.508H327.613L327.421 98.676C327.421 98.188 327.313 97.82 327.097 97.572C326.889 97.316 326.589 97.188 326.197 97.188C325.901 97.188 325.649 97.256 325.441 97.392C325.233 97.528 325.073 97.724 324.961 97.98C324.849 98.236 324.793 98.544 324.793 98.904V99C324.793 99.408 324.853 99.744 324.973 100.008C325.101 100.264 325.289 100.456 325.537 100.584C325.793 100.712 326.109 100.776 326.485 100.776C326.805 100.776 327.129 100.728 327.457 100.632C327.785 100.536 328.081 100.388 328.345 100.188L328.825 101.4C328.529 101.624 328.165 101.804 327.733 101.94C327.309 102.068 326.881 102.132 326.449 102.132ZM329.975 102V96.108H331.751V97.548H331.631C331.727 97.068 331.935 96.7 332.255 96.444C332.583 96.18 333.015 96.028 333.551 95.988L334.079 95.952L334.187 97.476L333.167 97.584C332.711 97.624 332.371 97.756 332.147 97.98C331.931 98.204 331.823 98.528 331.823 98.952V102H329.975ZM295.64 114.132C294.728 114.132 293.948 113.952 293.3 113.592C292.652 113.232 292.152 112.728 291.8 112.08C291.456 111.424 291.284 110.652 291.284 109.764C291.284 108.876 291.456 108.108 291.8 107.46C292.152 106.812 292.652 106.308 293.3 105.948C293.948 105.588 294.728 105.408 295.64 105.408C296.2 105.408 296.736 105.496 297.248 105.672C297.76 105.848 298.18 106.088 298.508 106.392L297.932 107.868C297.572 107.588 297.208 107.384 296.84 107.256C296.48 107.12 296.1 107.052 295.7 107.052C294.9 107.052 294.292 107.288 293.876 107.76C293.46 108.224 293.252 108.892 293.252 109.764C293.252 110.636 293.46 111.308 293.876 111.78C294.292 112.252 294.9 112.488 295.7 112.488C296.1 112.488 296.48 112.424 296.84 112.296C297.208 112.16 297.572 111.952 297.932 111.672L298.508 113.148C298.18 113.452 297.76 113.692 297.248 113.868C296.736 114.044 296.2 114.132 295.64 114.132ZM299.571 114V105.54H301.383V109.212H301.215C301.399 108.82 301.671 108.52 302.031 108.312C302.399 108.096 302.815 107.988 303.279 107.988C303.751 107.988 304.139 108.08 304.443 108.264C304.747 108.44 304.975 108.712 305.127 109.08C305.279 109.44 305.355 109.9 305.355 110.46V114H303.543V110.544C303.543 110.272 303.507 110.052 303.435 109.884C303.371 109.708 303.271 109.584 303.135 109.512C303.007 109.432 302.843 109.392 302.643 109.392C302.387 109.392 302.163 109.448 301.971 109.56C301.787 109.664 301.643 109.816 301.539 110.016C301.435 110.216 301.383 110.448 301.383 110.712V114H299.571ZM308.7 114.132C308.268 114.132 307.88 114.048 307.536 113.88C307.192 113.712 306.924 113.484 306.732 113.196C306.54 112.908 306.444 112.584 306.444 112.224C306.444 111.792 306.556 111.452 306.78 111.204C307.004 110.948 307.368 110.768 307.872 110.664C308.376 110.552 309.044 110.496 309.876 110.496H310.512V111.432H309.888C309.576 111.432 309.312 111.444 309.096 111.468C308.888 111.492 308.716 111.536 308.58 111.6C308.444 111.656 308.344 111.728 308.28 111.816C308.224 111.904 308.196 112.016 308.196 112.152C308.196 112.376 308.272 112.56 308.424 112.704C308.584 112.848 308.812 112.92 309.108 112.92C309.34 112.92 309.544 112.868 309.72 112.764C309.904 112.652 310.048 112.504 310.152 112.32C310.256 112.128 310.308 111.912 310.308 111.672V110.292C310.308 109.94 310.228 109.692 310.068 109.548C309.908 109.396 309.636 109.32 309.252 109.32C308.932 109.32 308.592 109.372 308.232 109.476C307.88 109.572 307.528 109.72 307.176 109.92L306.684 108.708C306.892 108.564 307.144 108.44 307.44 108.336C307.744 108.224 308.06 108.14 308.388 108.084C308.716 108.02 309.028 107.988 309.324 107.988C309.94 107.988 310.444 108.08 310.836 108.264C311.236 108.44 311.536 108.716 311.736 109.092C311.936 109.46 312.036 109.936 312.036 110.52V114H310.344V112.812H310.428C310.38 113.084 310.276 113.32 310.116 113.52C309.964 113.712 309.768 113.864 309.528 113.976C309.288 114.08 309.012 114.132 308.7 114.132ZM315.583 114.132C314.823 114.132 314.267 113.936 313.915 113.544C313.563 113.144 313.387 112.552 313.387 111.768V105.54H315.199V111.696C315.199 111.896 315.231 112.072 315.295 112.224C315.359 112.368 315.451 112.476 315.571 112.548C315.699 112.62 315.859 112.656 316.051 112.656C316.131 112.656 316.215 112.652 316.303 112.644C316.391 112.636 316.475 112.62 316.555 112.596L316.531 114C316.379 114.04 316.223 114.072 316.063 114.096C315.911 114.12 315.751 114.132 315.583 114.132ZM319.58 114.132C318.82 114.132 318.264 113.936 317.912 113.544C317.56 113.144 317.384 112.552 317.384 111.768V105.54H319.196V111.696C319.196 111.896 319.228 112.072 319.292 112.224C319.356 112.368 319.448 112.476 319.568 112.548C319.696 112.62 319.856 112.656 320.048 112.656C320.128 112.656 320.212 112.652 320.3 112.644C320.388 112.636 320.472 112.62 320.552 112.596L320.528 114C320.376 114.04 320.22 114.072 320.06 114.096C319.908 114.12 319.748 114.132 319.58 114.132ZM324.381 114.132C323.685 114.132 323.085 114.008 322.581 113.76C322.085 113.504 321.701 113.148 321.429 112.692C321.165 112.228 321.033 111.684 321.033 111.06C321.033 110.452 321.161 109.92 321.417 109.464C321.673 109 322.029 108.64 322.485 108.384C322.949 108.12 323.473 107.988 324.057 107.988C324.633 107.988 325.129 108.112 325.545 108.36C325.961 108.6 326.281 108.944 326.505 109.392C326.737 109.84 326.853 110.372 326.853 110.988V111.444H322.521V110.508H325.545L325.353 110.676C325.353 110.188 325.245 109.82 325.029 109.572C324.821 109.316 324.521 109.188 324.129 109.188C323.833 109.188 323.581 109.256 323.373 109.392C323.165 109.528 323.005 109.724 322.893 109.98C322.781 110.236 322.725 110.544 322.725 110.904V111C322.725 111.408 322.785 111.744 322.905 112.008C323.033 112.264 323.221 112.456 323.469 112.584C323.725 112.712 324.041 112.776 324.417 112.776C324.737 112.776 325.061 112.728 325.389 112.632C325.717 112.536 326.013 112.388 326.277 112.188L326.757 113.4C326.461 113.624 326.097 113.804 325.665 113.94C325.241 114.068 324.813 114.132 324.381 114.132ZM327.907 114V108.108H329.683V109.212H329.551C329.735 108.82 330.007 108.52 330.367 108.312C330.735 108.096 331.151 107.988 331.615 107.988C332.087 107.988 332.475 108.08 332.779 108.264C333.083 108.44 333.311 108.712 333.463 109.08C333.615 109.44 333.691 109.9 333.691 110.46V114H331.879V110.544C331.879 110.272 331.843 110.052 331.771 109.884C331.707 109.708 331.607 109.584 331.471 109.512C331.343 109.432 331.179 109.392 330.979 109.392C330.723 109.392 330.499 109.448 330.307 109.56C330.123 109.664 329.979 109.816 329.875 110.016C329.771 110.216 329.719 110.448 329.719 110.712V114H327.907ZM337.876 116.292C337.316 116.292 336.788 116.232 336.292 116.112C335.796 115.992 335.364 115.804 334.996 115.548L335.488 114.312C335.704 114.44 335.936 114.552 336.184 114.648C336.44 114.744 336.7 114.816 336.964 114.864C337.228 114.912 337.48 114.936 337.72 114.936C338.232 114.936 338.616 114.82 338.872 114.588C339.128 114.356 339.256 114 339.256 113.52V112.608H339.364C339.244 112.976 338.996 113.276 338.62 113.508C338.244 113.74 337.828 113.856 337.372 113.856C336.844 113.856 336.384 113.736 335.992 113.496C335.6 113.248 335.296 112.904 335.08 112.464C334.864 112.016 334.756 111.5 334.756 110.916C334.756 110.324 334.864 109.812 335.08 109.38C335.296 108.94 335.6 108.6 335.992 108.36C336.384 108.112 336.844 107.988 337.372 107.988C337.844 107.988 338.26 108.104 338.62 108.336C338.988 108.56 339.232 108.856 339.352 109.224H339.256V108.108H341.02V113.316C341.02 113.972 340.9 114.52 340.66 114.96C340.42 115.408 340.064 115.74 339.592 115.956C339.128 116.18 338.556 116.292 337.876 116.292ZM337.912 112.5C338.312 112.5 338.632 112.36 338.872 112.08C339.12 111.8 339.244 111.412 339.244 110.916C339.244 110.42 339.12 110.036 338.872 109.764C338.632 109.484 338.312 109.344 337.912 109.344C337.504 109.344 337.18 109.484 336.94 109.764C336.7 110.036 336.58 110.42 336.58 110.916C336.58 111.412 336.7 111.8 336.94 112.08C337.18 112.36 337.504 112.5 337.912 112.5ZM345.486 114.132C344.79 114.132 344.19 114.008 343.686 113.76C343.19 113.504 342.806 113.148 342.534 112.692C342.27 112.228 342.138 111.684 342.138 111.06C342.138 110.452 342.266 109.92 342.522 109.464C342.778 109 343.134 108.64 343.59 108.384C344.054 108.12 344.578 107.988 345.162 107.988C345.738 107.988 346.234 108.112 346.65 108.36C347.066 108.6 347.386 108.944 347.61 109.392C347.842 109.84 347.958 110.372 347.958 110.988V111.444H343.626V110.508H346.65L346.458 110.676C346.458 110.188 346.35 109.82 346.134 109.572C345.926 109.316 345.626 109.188 345.234 109.188C344.938 109.188 344.686 109.256 344.478 109.392C344.27 109.528 344.11 109.724 343.998 109.98C343.886 110.236 343.83 110.544 343.83 110.904V111C343.83 111.408 343.89 111.744 344.01 112.008C344.138 112.264 344.326 112.456 344.574 112.584C344.83 112.712 345.146 112.776 345.522 112.776C345.842 112.776 346.166 112.728 346.494 112.632C346.822 112.536 347.118 112.388 347.382 112.188L347.862 113.4C347.566 113.624 347.202 113.804 346.77 113.94C346.346 114.068 345.918 114.132 345.486 114.132Z"
                          fill="white"/>
                    <path d="M320.543 128.31L324.75 122H314.25L318.905 128.347C319.315 128.907 320.158 128.888 320.543 128.31Z" fill="#1998CD"/>
                </g>
                <g class="result-graph-goal" filter="url(#filter6_d_519_71952)">
                    <rect width="51" height="24" rx="4" transform="matrix(-1 0 0 1 92.5 4)" fill="#404751"/>
                    <path d="M52.8465 20V13.076H50.0505V11.54H57.5025V13.076H54.7065V20H52.8465ZM60.1393 20.132C59.5073 20.132 58.9553 20.008 58.4833 19.76C58.0193 19.512 57.6593 19.156 57.4033 18.692C57.1473 18.228 57.0193 17.68 57.0193 17.048C57.0193 16.416 57.1473 15.872 57.4033 15.416C57.6593 14.96 58.0193 14.608 58.4833 14.36C58.9553 14.112 59.5073 13.988 60.1393 13.988C60.7713 13.988 61.3193 14.112 61.7833 14.36C62.2553 14.608 62.6193 14.964 62.8753 15.428C63.1393 15.884 63.2713 16.424 63.2713 17.048C63.2713 17.68 63.1393 18.228 62.8753 18.692C62.6193 19.156 62.2553 19.512 61.7833 19.76C61.3193 20.008 60.7713 20.132 60.1393 20.132ZM60.1393 18.776C60.5313 18.776 60.8473 18.636 61.0873 18.356C61.3273 18.068 61.4473 17.632 61.4473 17.048C61.4473 16.464 61.3273 16.036 61.0873 15.764C60.8473 15.484 60.5313 15.344 60.1393 15.344C59.7553 15.344 59.4433 15.484 59.2033 15.764C58.9633 16.036 58.8433 16.464 58.8433 17.048C58.8433 17.632 58.9633 18.068 59.2033 18.356C59.4433 18.636 59.7553 18.776 60.1393 18.776ZM66.6657 20.132C66.1537 20.132 65.7017 20.008 65.3097 19.76C64.9257 19.512 64.6257 19.156 64.4097 18.692C64.1937 18.228 64.0857 17.68 64.0857 17.048C64.0857 16.416 64.1937 15.872 64.4097 15.416C64.6257 14.96 64.9257 14.608 65.3097 14.36C65.7017 14.112 66.1537 13.988 66.6657 13.988C67.1297 13.988 67.5377 14.1 67.8897 14.324C68.2497 14.548 68.4937 14.84 68.6217 15.2H68.4897V11.54H70.3017V20H68.5257V18.836H68.6337C68.5217 19.228 68.2817 19.544 67.9137 19.784C67.5537 20.016 67.1377 20.132 66.6657 20.132ZM67.2057 18.776C67.5977 18.776 67.9137 18.636 68.1537 18.356C68.3937 18.068 68.5137 17.632 68.5137 17.048C68.5137 16.464 68.3937 16.036 68.1537 15.764C67.9137 15.484 67.5977 15.344 67.2057 15.344C66.8217 15.344 66.5097 15.484 66.2697 15.764C66.0297 16.036 65.9097 16.464 65.9097 17.048C65.9097 17.632 66.0297 18.068 66.2697 18.356C66.5097 18.636 66.8217 18.776 67.2057 18.776ZM73.69 20.132C73.258 20.132 72.87 20.048 72.526 19.88C72.182 19.712 71.914 19.484 71.722 19.196C71.53 18.908 71.434 18.584 71.434 18.224C71.434 17.792 71.546 17.452 71.77 17.204C71.994 16.948 72.358 16.768 72.862 16.664C73.366 16.552 74.034 16.496 74.866 16.496H75.502V17.432H74.878C74.566 17.432 74.302 17.444 74.086 17.468C73.878 17.492 73.706 17.536 73.57 17.6C73.434 17.656 73.334 17.728 73.27 17.816C73.214 17.904 73.186 18.016 73.186 18.152C73.186 18.376 73.262 18.56 73.414 18.704C73.574 18.848 73.802 18.92 74.098 18.92C74.33 18.92 74.534 18.868 74.71 18.764C74.894 18.652 75.038 18.504 75.142 18.32C75.246 18.128 75.298 17.912 75.298 17.672V16.292C75.298 15.94 75.218 15.692 75.058 15.548C74.898 15.396 74.626 15.32 74.242 15.32C73.922 15.32 73.582 15.372 73.222 15.476C72.87 15.572 72.518 15.72 72.166 15.92L71.674 14.708C71.882 14.564 72.134 14.44 72.43 14.336C72.734 14.224 73.05 14.14 73.378 14.084C73.706 14.02 74.018 13.988 74.314 13.988C74.93 13.988 75.434 14.08 75.826 14.264C76.226 14.44 76.526 14.716 76.726 15.092C76.926 15.46 77.026 15.936 77.026 16.52V20H75.334V18.812H75.418C75.37 19.084 75.266 19.32 75.106 19.52C74.954 19.712 74.758 19.864 74.518 19.976C74.278 20.08 74.002 20.132 73.69 20.132ZM78.775 22.16L80.071 19.316V20.06L77.479 14.108H79.387L81.055 18.332H80.671L82.387 14.108H84.187L80.623 22.16H78.775Z"
                          fill="white"/>
                    <path d="M35.1895 14.957L41.5 10.75V21.25L35.1529 16.5954C34.5928 16.1847 34.6117 15.3422 35.1895 14.957Z" fill="#404751"/>
                </g>
                <defs>
                    <filter id="filter0_f_519_71952" x="2.3252" y="17.2969" width="360.526" height="174.195" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                        <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
                        <feGaussianBlur stdDeviation="4" result="effect1_foregroundBlur_519_71952"/>
                    </filter>
                    <filter id="filter1_d_519_71952" x="210.899" y="111.314" width="31.1006" height="31.1006" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                        <feOffset dy="4.31447"/>
                        <feGaussianBlur stdDeviation="4"/>
                        <feComposite in2="hardAlpha" operator="out"/>
                        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_519_71952"/>
                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_519_71952" result="shape"/>
                    </filter>
                    <filter id="filter2_d_519_71952" x="119.899" y="91.3145" width="31.1006" height="31.1006" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                        <feOffset dy="4.31447"/>
                        <feGaussianBlur stdDeviation="4"/>
                        <feComposite in2="hardAlpha" operator="out"/>
                        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_519_71952"/>
                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_519_71952" result="shape"/>
                    </filter>
                    <filter id="filter3_d_519_71952" x="0.899414" y="10.3145" width="31.1006" height="31.1006" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                        <feOffset dy="4.31447"/>
                        <feGaussianBlur stdDeviation="4"/>
                        <feComposite in2="hardAlpha" operator="out"/>
                        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_519_71952"/>
                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_519_71952" result="shape"/>
                    </filter>
                    <filter id="filter4_d_519_71952" x="303.899" y="132.314" width="31.1006" height="31.1006" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                        <feOffset dy="4.31447"/>
                        <feGaussianBlur stdDeviation="4"/>
                        <feComposite in2="hardAlpha" operator="out"/>
                        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_519_71952"/>
                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_519_71952" result="shape"/>
                    </filter>
                    <filter id="filter5_d_519_71952" x="271" y="82" width="97" height="59.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                        <feOffset dy="4"/>
                        <feGaussianBlur stdDeviation="4"/>
                        <feComposite in2="hardAlpha" operator="out"/>
                        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.16 0"/>
                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_519_71952"/>
                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_519_71952" result="shape"/>
                    </filter>
                    <filter id="filter6_d_519_71952" x="26" y="0" width="74.5" height="40" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                        <feOffset dy="4"/>
                        <feGaussianBlur stdDeviation="4"/>
                        <feComposite in2="hardAlpha" operator="out"/>
                        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.16 0"/>
                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_519_71952"/>
                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_519_71952" result="shape"/>
                    </filter>
                    <linearGradient id="paint0_linear_519_71952" x1="11" y1="199.999" x2="354" y2="199.999" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#FF574C"/>
                        <stop offset="0.450362" stop-color="#F5E088"/>
                        <stop offset="1" stop-color="#1998CD"/>
                    </linearGradient>
                    <linearGradient id="paint1_linear_519_71952" x1="12.1555" y1="181.999" x2="352.846" y2="181.999" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#1998CD"/>
                        <stop offset="0.549638" stop-color="#F5E088"/>
                        <stop offset="1" stop-color="#FF574C"/>
                    </linearGradient>
                    <linearGradient id="paint2_linear_519_71952" x1="12.1545" y1="175.999" x2="352" y2="175.999" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#FF574C"/>
                        <stop offset="0.450362" stop-color="#F5E088"/>
                        <stop offset="1" stop-color="#1998CD"/>
                    </linearGradient>
                    <linearGradient id="paint3_linear_519_71952" x1="7.55031" y1="0" x2="7.55031" y2="15.1006" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#2D2D2D"/>
                        <stop offset="1" stop-color="#939393"/>
                    </linearGradient>
                    <linearGradient id="paint4_linear_519_71952" x1="7.55031" y1="0" x2="7.55031" y2="15.1006" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#2D2D2D"/>
                        <stop offset="1" stop-color="#939393"/>
                    </linearGradient>
                    <linearGradient id="paint5_linear_519_71952" x1="7.55031" y1="0" x2="7.55031" y2="15.1006" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#2D2D2D"/>
                        <stop offset="1" stop-color="#939393"/>
                    </linearGradient>
                    <linearGradient id="paint6_linear_519_71952" x1="7.55031" y1="0" x2="7.55031" y2="15.1006" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#2D2D2D"/>
                        <stop offset="1" stop-color="#939393"/>
                    </linearGradient>
                </defs>
            </svg>


            <div class="quiz__reviews-loading-svg-date-wrapp mt-8 reactive">
                <p class="quiz__reviews-loading-svg-date reactive" data-i18n="quiz.result.result_step_three.week_1">
                </p>
                <p class="quiz__reviews-loading-svg-date reactive" data-i18n="quiz.result.result_step_three.week_2">
                </p>
                <p class="quiz__reviews-loading-svg-date reactive" data-i18n="quiz.result.result_step_three.week_3">
                </p>
                <p class="quiz__reviews-loading-svg-date reactive" data-i18n="quiz.result.result_step_three.week_4">
                </p>
            </div>
            <p class="quiz__reviews-loading-svg-date-text" data-i18n="quiz.result.result_step_three.chart_explanation">
            </p>
        </div>

        <p class="quiz__reviews-loading-subtext" data-i18n="quiz.result.result_step_three.personalized_ready"></p>

        {% set landingUrl = '/questionary/landing-34' %}
        {% set locale = quizData.locale.value|default('en') %}

        {% if locale == 'de' %}
            {% set landingUrl = '/questionary/landing-47' %}
        {% elseif locale == 'fr' %}
            {% set landingUrl = '/questionary/landing-48' %}
        {% elseif locale == 'es_419' %}
            {% set landingUrl = '/questionary/landing-49' %}
        {% elseif locale == 'pt_BR' %}
            {% set landingUrl = '/questionary/landing-50' %}
        {% elseif locale == 'it' %}
            {% set landingUrl = '/questionary/landing-51' %}
        {% endif %}

        <div class="quiz__btn-box">
            <button class="quiz__btn js-btn email-submit"
                    id="redirect-to-landing-btn"
                    data-landing-url="{{ landingUrl }}"
            >
                <span></span>
                <span data-i18n="quiz.result.continue_button"></span>
            </button>
        </div>
    </div>

    {% set discountTitle = "quiz.result.discount.title"|trans(domain=funnelTranslationsDomain) %}
    {% set discountDescription = "quiz.result.discount.description"|trans(domain=funnelTranslationsDomain) %}
    {% set discountMessage = "quiz.result.discount.message"|trans(domain=funnelTranslationsDomain) %}

    {% include 'subscription/components/quiz/result-pages/scratch-card-discount.html.twig'
        with { discountTitle, discountDescription, discountPercents, discountMessage } %}

    <div class="quiz__redirect-loader" id="resultLoader">
        <div class="preload">
            <span></span>
        </div>
    </div>

    <div class="quiz__popup-wrapp newFun" id="quizPopUpProblem">
        <div class="quiz__popup-bg"></div>

        <div class="quiz__popup" id="magicProblemPopup">
            <div class="quiz__popup-box">
                <h4 class="quiz__popup-title" data-i18n="quiz.result.popup_problem.title">
                </h4>
                <div class="quiz__popup-text" data-i18n="quiz.result.popup_problem.text"></div>
                <ul class="quiz__popup-btn-list">
                    <li class="quiz__popup-btn-item quizPopUpProblemBtn" data-event="No" data-i18n="quiz.result.popup_problem.no_button">
                    </li>
                    <li class="quiz__popup-btn-item quizPopUpProblemBtn" data-event="Yes" data-i18n="quiz.result.popup_problem.yes_button">
                    </li>
                </ul>
            </div>
        </div>
    </div>

    <div class="quiz__popup-wrapp newFun" id="quizPopUpObedience">
        <div class="quiz__popup-bg"></div>

        <div class="quiz__popup" id="magicObediencePopup">
            <div class="quiz__popup-box">
                <h4 class="quiz__popup-title" data-i18n="quiz.result.popup_obedience.title">
                </h4>
                <div class="quiz__popup-text" data-i18n="quiz.result.popup_obedience.text">
                </div>
                <ul class="quiz__popup-btn-list">
                    <li class="quiz__popup-btn-item quizPopUpObedienceBtn" data-event="No" data-i18n="quiz.result.popup_obedience.no_button">
                    </li>
                    <li class="quiz__popup-btn-item quizPopUpObedienceBtn" data-event="Yes" data-i18n="quiz.result.popup_obedience.yes_button">
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
