<div class="quiz__index quiz__result skipQuiz magic" id="block{{ blockId }}" data-id="{{ blockId }}" data-result="reviews">
    <div class="container " id="resultStepOne">
        <div id="circleBar" class="progressbar reviews">
            <div class="progressbar__shadow"></div>
        </div>
        <p class="quiz__reviews-loading-subtitle" id="replaceSubtitle">
            Creando el plan de entrenamiento personalizado de <span class="nameInsertSelector">tu perro</span>...
        </p>
        <div class="quiz__hr">
        </div>
        <div class="quiz__reviews-loading-title-red-box">
            <h3 class="quiz__reviews-loading-title-red">
                115 mil dueños de perros
            </h3>
        </div>
        <p class="quiz__reviews-loading-text">
            han elegido PawChamp
        </p>
        <div class="quiz__reviews-slider custom-result">
            <ul class="swiper-wrapper">
                <li class="quiz__reviews-slider-item swiper-slide">
                    <div class="quiz__reviews-slider-item-text-wrapp">
                        <svg width="90" height="16" viewBox="0 0 90 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M9.46744 6.37307H14.2218L10.3768 9.16159L10.3767 9.16159L8.00391 10.8849L10.7058 10.1835L10.3789 9.16822L11.8403 13.6821L7.99524 10.8849L4.15019 13.6821L5.62239 9.16159L1.77734 6.36441L6.53169 6.37307L8.0039 1.85254L9.46744 6.37307Z"
                                  fill="white"/>
                            <rect x="18.3711" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M27.8385 6.37307H32.5929L28.7478 9.16159L28.7478 9.16159L26.375 10.8849L29.0769 10.1835L28.75 9.16822L30.2114 13.6821L26.3663 10.8849L22.5213 13.6821L23.9935 9.16159L20.1484 6.36441L24.9028 6.37307L26.375 1.85254L27.8385 6.37307Z"
                                  fill="white"/>
                            <rect x="36.7402" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M46.2077 6.37307H50.962L47.117 9.16159L47.117 9.16159L44.7441 10.8849L47.4461 10.1835L47.1191 9.16822L48.5805 13.6821L44.7355 10.8849L40.8904 13.6821L42.3626 9.16159L38.5176 6.36441L43.2719 6.37307L44.7441 1.85254L46.2077 6.37307Z"
                                  fill="white"/>
                            <rect x="55.1113" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M64.5788 6.37307H69.3331L65.4881 9.16159L65.4883 9.16217L63.1162 10.8849L65.8181 10.1835L65.6738 9.7352L66.9516 13.6821L63.1066 10.8849L59.2615 13.6821L60.7337 9.16159L56.8887 6.36441L61.643 6.37307L63.1152 1.85254L64.5788 6.37307Z"
                                  fill="white"/>
                            <rect x="73.4824" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M82.9499 6.37307H87.7042L83.8592 9.16159L83.8592 9.16159L81.4863 10.8849L84.1883 10.1835L83.8613 9.16822L85.3227 13.6821L81.4777 10.8849L77.6326 13.6821L79.1048 9.16159L75.2598 6.36441L80.0141 6.37307L81.4863 1.85254L82.9499 6.37307Z"
                                  fill="white"/>
                        </svg>
                        <div class="quiz__reviews-slider-item-name-wrapp">
                            <h5>
                                Un nuevo enfoque de la calma
                            </h5>
                            <p class="quiz__reviews-slider-item-name">
                                Ernesto
                            </p>
                        </div>
                        <div class="quiz__reviews-slider-item-text">
                            Esta ha sido una nueva forma de aprender a ayudar a mi perro a mantener la calma. Aún no hemos terminado, pero ya he visto grandes mejoras con nuestro pastor alemán de 8 años que tenía algo de entrenamiento pero luchaba con la reactividad.
                        </div>
                    </div>
                </li>
                <li class="quiz__reviews-slider-item swiper-slide">
                    <div class="quiz__reviews-slider-item-text-wrapp">
                        <svg width="90" height="16" viewBox="0 0 90 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M9.46744 6.37307H14.2218L10.3768 9.16159L10.3767 9.16159L8.00391 10.8849L10.7058 10.1835L10.3789 9.16822L11.8403 13.6821L7.99524 10.8849L4.15019 13.6821L5.62239 9.16159L1.77734 6.36441L6.53169 6.37307L8.0039 1.85254L9.46744 6.37307Z"
                                  fill="white"/>
                            <rect x="18.3711" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M27.8385 6.37307H32.5929L28.7478 9.16159L28.7478 9.16159L26.375 10.8849L29.0769 10.1835L28.75 9.16822L30.2114 13.6821L26.3663 10.8849L22.5213 13.6821L23.9935 9.16159L20.1484 6.36441L24.9028 6.37307L26.375 1.85254L27.8385 6.37307Z"
                                  fill="white"/>
                            <rect x="36.7402" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M46.2077 6.37307H50.962L47.117 9.16159L47.117 9.16159L44.7441 10.8849L47.4461 10.1835L47.1191 9.16822L48.5805 13.6821L44.7355 10.8849L40.8904 13.6821L42.3626 9.16159L38.5176 6.36441L43.2719 6.37307L44.7441 1.85254L46.2077 6.37307Z"
                                  fill="white"/>
                            <rect x="55.1113" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M64.5788 6.37307H69.3331L65.4881 9.16159L65.4883 9.16217L63.1162 10.8849L65.8181 10.1835L65.6738 9.7352L66.9516 13.6821L63.1066 10.8849L59.2615 13.6821L60.7337 9.16159L56.8887 6.36441L61.643 6.37307L63.1152 1.85254L64.5788 6.37307Z"
                                  fill="white"/>
                            <rect x="73.4824" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M82.9499 6.37307H87.7042L83.8592 9.16159L83.8592 9.16159L81.4863 10.8849L84.1883 10.1835L83.8613 9.16822L85.3227 13.6821L81.4777 10.8849L77.6326 13.6821L79.1048 9.16159L75.2598 6.36441L80.0141 6.37307L81.4863 1.85254L82.9499 6.37307Z"
                                  fill="white"/>
                        </svg>
                        <div class="quiz__reviews-slider-item-name-wrapp">
                            <h5>
                                La estructura de la formación es brillante
                            </h5>
                            <p class="quiz__reviews-slider-item-name">
                                Claire_Ugalde
                            </p>
                        </div>
                        <div class="quiz__reviews-slider-item-text">
                            La forma en que se divide la formación es excelente. He tenido formación individual en mi casa y me ha parecido menos eficaz que este curso y lo recomendaría encarecidamente.
                        </div>
                    </div>
                </li>
                <li class="quiz__reviews-slider-item swiper-slide">
                    <div class="quiz__reviews-slider-item-text-wrapp">
                        <svg width="90" height="16" viewBox="0 0 90 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M9.46744 6.37307H14.2218L10.3768 9.16159L10.3767 9.16159L8.00391 10.8849L10.7058 10.1835L10.3789 9.16822L11.8403 13.6821L7.99524 10.8849L4.15019 13.6821L5.62239 9.16159L1.77734 6.36441L6.53169 6.37307L8.0039 1.85254L9.46744 6.37307Z"
                                  fill="white"/>
                            <rect x="18.3711" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M27.8385 6.37307H32.5929L28.7478 9.16159L28.7478 9.16159L26.375 10.8849L29.0769 10.1835L28.75 9.16822L30.2114 13.6821L26.3663 10.8849L22.5213 13.6821L23.9935 9.16159L20.1484 6.36441L24.9028 6.37307L26.375 1.85254L27.8385 6.37307Z"
                                  fill="white"/>
                            <rect x="36.7402" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M46.2077 6.37307H50.962L47.117 9.16159L47.117 9.16159L44.7441 10.8849L47.4461 10.1835L47.1191 9.16822L48.5805 13.6821L44.7355 10.8849L40.8904 13.6821L42.3626 9.16159L38.5176 6.36441L43.2719 6.37307L44.7441 1.85254L46.2077 6.37307Z"
                                  fill="white"/>
                            <rect x="55.1113" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M64.5788 6.37307H69.3331L65.4881 9.16159L65.4883 9.16217L63.1162 10.8849L65.8181 10.1835L65.6738 9.7352L66.9516 13.6821L63.1066 10.8849L59.2615 13.6821L60.7337 9.16159L56.8887 6.36441L61.643 6.37307L63.1152 1.85254L64.5788 6.37307Z"
                                  fill="white"/>
                            <rect x="73.4824" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M82.9499 6.37307H87.7042L83.8592 9.16159L83.8592 9.16159L81.4863 10.8849L84.1883 10.1835L83.8613 9.16822L85.3227 13.6821L81.4777 10.8849L77.6326 13.6821L79.1048 9.16159L75.2598 6.36441L80.0141 6.37307L81.4863 1.85254L82.9499 6.37307Z"
                                  fill="white"/>
                        </svg>
                        <div class="quiz__reviews-slider-item-name-wrapp">
                            <h5>
                                Excelente curso de formación
                            </h5>
                            <p class="quiz__reviews-slider-item-name">
                                Diego Fernandez Jr
                            </p>
                        </div>
                        <p class="quiz__reviews-slider-item-text">
                            Excelente curso de adiestramiento, muy detallado y fácil de entender. Lo que me gusta de este curso es que enfatizan que entrenar a un perro requiere paciencia y entender el proceso. Todavía estoy en la primera semana, pero su enfoque parece fácil de seguir.
                            No puedo esperar a ver los resultados finales...
                        </p>
                    </div>
                </li>
            </ul>
        </div>
    </div>
    <div class="container email" id="resultStepTwo">
        <h2 class="quiz__reviews-loading-title">
            Introduce tu correo electrónico para obtener el plan personalizado de <span class="nameInsertSelector">tu perro</span> para superar la reactividad
        </h2>
        <p class="quiz__result-label" id="emailText"></p>
        <div class="quiz__result-box">
            <ul id="autoComplete" class="email-autocomplete email-autocomplete-scroll"></ul>
            <input class="quiz__result-input" name="text" type="text" placeholder="Correo electrónico" id="email" autocomplete="off">
        </div>
        <div class="quiz__result-text-box">
            <svg class="quiz__result-text-icon" width="21" height="26" viewBox="0 0 21 26" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path opacity="0.45"
                      d="M20.1944 9.75H18.5278V7.58332C18.5278 3.40184 15.0387 0 10.75 0C6.46125 0 2.97224 3.40184 2.97224 7.58332V9.75H1.30557C0.99849 9.75 0.75 9.99228 0.75 10.2917V23.8334C0.75 25.0283 1.74661 26 2.97224 26H18.5278C19.7534 26 20.75 25.0283 20.75 23.8333V10.2917C20.75 9.99228 20.5015 9.75 20.1944 9.75ZM12.4134 21.0652C12.4308 21.2181 12.3804 21.3715 12.2751 21.4863C12.1698 21.6011 12.019 21.6667 11.8611 21.6667H9.63891C9.48104 21.6667 9.33021 21.6011 9.22495 21.4863C9.11969 21.3715 9.06922 21.2181 9.08661 21.0652L9.43708 17.9925C8.86797 17.5888 8.52781 16.9504 8.52781 16.25C8.52781 15.0551 9.52443 14.0833 10.7501 14.0833C11.9757 14.0833 12.9723 15.055 12.9723 16.25C12.9723 16.9504 12.6321 17.5888 12.063 17.9925L12.4134 21.0652ZM15.1944 9.75H6.30557V7.58332C6.30557 5.19396 8.29938 3.25 10.75 3.25C13.2006 3.25 15.1944 5.19396 15.1944 7.58332V9.75Z"
                      fill="#343434"/>
            </svg>
            <p class="quiz__result-text">
                Protegemos tu privacidad y nos comprometemos proteger tus datos personales. Nunca enviamos correos electrónicos de spam, solo información relevante.
            </p>
        </div>
        <div class="quiz__btn-box">
            <button class="quiz__btn js-btn email-submit" id="email-submit" disabled>
                <span></span>
                Continuar
            </button>
        </div>
    </div>
    <div class="container " id="resultStepEmailSubscription">
        <h3 class="quiz__email-subscription-title">
            Quieres recibir emails con <span>ofertas especiales</span>, trucos de adiestramiento canino, consejos y <span>regalos gratis</span>?
        </h3>
        <div class="quiz__email-subscription-btn-box">
            <button class="quiz__btn triggerStep" data-event-value="Apply newsletter">
                <span></span>
                ¡Sí, me apunto!
            </button>
            <label class="quiz__email-subscription-label triggerStep" data-event-value="Reject newsletter">
                <input type="checkbox" class="quiz__email-subscription-input" id="disallowMarketingEmails">
                No me interesa
            </label>
        </div>
    </div>
    <div class="container graph " id="resultStepThree">
        <h2 class="quiz__reviews-loading-title">
            Reactividad de <span class="nameInsertSelector">tu perro</span>
        </h2>
        <div class="quiz__reviews-loading-svg-inner">
            <svg class="quiz__reviews-loading-svg quiz__reviews-loading-svg--is-reactivity" width="343" height="227" viewBox="0 0 343 227" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M20.4167 10.3958L322.706 10.3958" stroke="#E8E8E8" stroke-width="1.02083"/>
                <path d="M20.4167 36.9375H322.706" stroke="#E8E8E8" stroke-width="1.02083"/>
                <path d="M20.4167 63.4792H322.706" stroke="#E8E8E8" stroke-width="1.02083"/>
                <path d="M20.4167 90.0208H322.706" stroke="#E8E8E8" stroke-width="1.02083"/>
                <path d="M20.4167 116.562H322.706" stroke="#E8E8E8" stroke-width="1.02083"/>
                <path d="M20.4167 143.104H322.706" stroke="#E8E8E8" stroke-width="1.02083"/>
                <path d="M20.4167 169.646H322.706" stroke="#E8E8E8" stroke-width="1.02083"/>
                <g id="resultGraphBg" class="result-graph-bg" opacity="0.3">
                    <path d="M20.4167 27.2392C37.2074 31.2991 53.619 34.9694 69.5989 38.5433C116.742 49.0865 160.13 58.7898 198.415 75.1776C250.128 97.3126 292.482 131.631 322.023 196.698H20.4167V27.2392Z" fill="#D9D9D9"/>
                    <path d="M20.4167 27.2392C37.2074 31.2991 53.619 34.9694 69.5989 38.5433C116.742 49.0865 160.13 58.7898 198.415 75.1776C250.128 97.3126 292.482 131.631 322.023 196.698H20.4167V27.2392Z" fill="url(#paint0_linear_2_2)"/>
                </g>
                <path d="M20.4167 196.188H323.665" stroke="#E8E8E8" stroke-width="1.02083"/>
                <path d="M20.8653 202.823V196.698" stroke="#E8E8E8" stroke-width="1.02083"/>
                <path d="M323.156 202.823V196.698" stroke="#E8E8E8" stroke-width="1.02083"/>
                <path d="M219.512 202.823V196.698" stroke="#E8E8E8" stroke-width="1.02083"/>
                <path d="M129.306 202.823V196.698" stroke="#E8E8E8" stroke-width="1.02083"/>
                <path class="quiz__based-graph-line" d="M21.0895 25.2168C21.0895 25.2168 11.1967 23.3509 145.849 56.748C280.499 90.1452 306.148 168.072 323.094 196.698" stroke="url(#paint1_linear_2_2)" stroke-width="5.10417"/>
                <g class="result-graph-goal" filter="url(#filter0_d_2_2)">
                    <path d="M328.737 144H281.263C276.147 144 272 147.582 272 152V168C272 172.418 276.147 176 281.263 176H328.737C333.853 176 338 172.418 338 168V152C338 147.582 333.853 144 328.737 144Z" fill="#323334"/>
                    <path d="M279.145 150.95H281.885C282.652 150.95 283.312 151.09 283.865 151.37C284.419 151.65 284.842 152.053 285.135 152.58C285.429 153.107 285.575 153.737 285.575 154.47C285.575 155.203 285.429 155.837 285.135 156.37C284.849 156.897 284.429 157.3 283.875 157.58C283.322 157.86 282.659 158 281.885 158H279.145V150.95ZM281.775 156.74C283.275 156.74 284.025 155.983 284.025 154.47C284.025 152.963 283.275 152.21 281.775 152.21H280.705V156.74H281.775ZM291.072 155.83H287.872C287.919 156.223 288.046 156.51 288.252 156.69C288.466 156.863 288.766 156.95 289.152 156.95C289.406 156.95 289.656 156.91 289.902 156.83C290.156 156.743 290.386 156.623 290.592 156.47L290.992 157.48C290.752 157.667 290.459 157.813 290.112 157.92C289.772 158.027 289.429 158.08 289.082 158.08C288.256 158.08 287.602 157.853 287.122 157.4C286.649 156.94 286.412 156.317 286.412 155.53C286.412 155.03 286.516 154.587 286.722 154.2C286.929 153.813 287.216 153.513 287.582 153.3C287.949 153.08 288.366 152.97 288.832 152.97C289.519 152.97 290.062 153.193 290.462 153.64C290.869 154.087 291.072 154.693 291.072 155.46V155.83ZM288.872 154.01C288.586 154.01 288.356 154.103 288.182 154.29C288.016 154.47 287.912 154.733 287.872 155.08H289.802C289.782 154.727 289.692 154.46 289.532 154.28C289.379 154.1 289.159 154.01 288.872 154.01ZM293.874 158.08C293.421 158.08 293.004 158.03 292.624 157.93C292.251 157.823 291.928 157.673 291.654 157.48L292.054 156.44C292.321 156.62 292.614 156.76 292.934 156.86C293.254 156.96 293.578 157.01 293.904 157.01C294.138 157.01 294.318 156.973 294.444 156.9C294.578 156.82 294.644 156.713 294.644 156.58C294.644 156.46 294.598 156.367 294.504 156.3C294.418 156.233 294.251 156.173 294.004 156.12L293.204 155.94C292.731 155.833 292.381 155.67 292.154 155.45C291.934 155.223 291.824 154.917 291.824 154.53C291.824 154.223 291.911 153.953 292.084 153.72C292.264 153.487 292.514 153.303 292.834 153.17C293.161 153.037 293.531 152.97 293.944 152.97C294.298 152.97 294.641 153.023 294.974 153.13C295.308 153.237 295.608 153.387 295.874 153.58L295.474 154.58C294.954 154.22 294.441 154.04 293.934 154.04C293.701 154.04 293.518 154.08 293.384 154.16C293.251 154.24 293.184 154.353 293.184 154.5C293.184 154.607 293.224 154.69 293.304 154.75C293.384 154.81 293.524 154.867 293.724 154.92L294.554 155.11C295.054 155.223 295.418 155.397 295.644 155.63C295.878 155.857 295.994 156.167 295.994 156.56C295.994 157.033 295.804 157.407 295.424 157.68C295.051 157.947 294.534 158.08 293.874 158.08ZM299.897 152.97C300.317 152.97 300.691 153.08 301.017 153.3C301.351 153.513 301.607 153.817 301.787 154.21C301.974 154.597 302.067 155.043 302.067 155.55C302.067 156.057 301.974 156.503 301.787 156.89C301.607 157.27 301.354 157.563 301.027 157.77C300.701 157.977 300.324 158.08 299.897 158.08C299.564 158.08 299.261 158.013 298.987 157.88C298.721 157.74 298.514 157.547 298.367 157.3V159.8H296.857V153.09H298.347V153.78C298.494 153.527 298.704 153.33 298.977 153.19C299.251 153.043 299.557 152.97 299.897 152.97ZM299.457 156.93C299.811 156.93 300.084 156.813 300.277 156.58C300.471 156.34 300.567 155.997 300.567 155.55C300.567 155.097 300.471 154.747 300.277 154.5C300.084 154.247 299.811 154.12 299.457 154.12C299.104 154.12 298.831 154.243 298.637 154.49C298.444 154.73 298.347 155.077 298.347 155.53C298.347 155.977 298.444 156.323 298.637 156.57C298.831 156.81 299.104 156.93 299.457 156.93ZM307.72 153.09V158H306.25V157.3C306.097 157.553 305.893 157.747 305.64 157.88C305.393 158.013 305.113 158.08 304.8 158.08C304.18 158.08 303.717 157.91 303.41 157.57C303.11 157.223 302.96 156.703 302.96 156.01V153.09H304.47V156.04C304.47 156.333 304.53 156.55 304.65 156.69C304.777 156.83 304.967 156.9 305.22 156.9C305.513 156.9 305.75 156.8 305.93 156.6C306.117 156.4 306.21 156.137 306.21 155.81V153.09H307.72ZM313.299 155.83H310.099C310.145 156.223 310.272 156.51 310.479 156.69C310.692 156.863 310.992 156.95 311.379 156.95C311.632 156.95 311.882 156.91 312.129 156.83C312.382 156.743 312.612 156.623 312.819 156.47L313.219 157.48C312.979 157.667 312.685 157.813 312.339 157.92C311.999 158.027 311.655 158.08 311.309 158.08C310.482 158.08 309.829 157.853 309.349 157.4C308.875 156.94 308.639 156.317 308.639 155.53C308.639 155.03 308.742 154.587 308.949 154.2C309.155 153.813 309.442 153.513 309.809 153.3C310.175 153.08 310.592 152.97 311.059 152.97C311.745 152.97 312.289 153.193 312.689 153.64C313.095 154.087 313.299 154.693 313.299 155.46V155.83ZM311.099 154.01C310.812 154.01 310.582 154.103 310.409 154.29C310.242 154.47 310.139 154.733 310.099 155.08H312.029C312.009 154.727 311.919 154.46 311.759 154.28C311.605 154.1 311.385 154.01 311.099 154.01ZM310.589 152.45L311.569 150.4H313.049L311.529 152.45H310.589ZM316.101 158.08C315.648 158.08 315.231 158.03 314.851 157.93C314.478 157.823 314.154 157.673 313.881 157.48L314.281 156.44C314.548 156.62 314.841 156.76 315.161 156.86C315.481 156.96 315.804 157.01 316.131 157.01C316.364 157.01 316.544 156.973 316.671 156.9C316.804 156.82 316.871 156.713 316.871 156.58C316.871 156.46 316.824 156.367 316.731 156.3C316.644 156.233 316.478 156.173 316.231 156.12L315.431 155.94C314.958 155.833 314.608 155.67 314.381 155.45C314.161 155.223 314.051 154.917 314.051 154.53C314.051 154.223 314.138 153.953 314.311 153.72C314.491 153.487 314.741 153.303 315.061 153.17C315.388 153.037 315.758 152.97 316.171 152.97C316.524 152.97 316.868 153.023 317.201 153.13C317.534 153.237 317.834 153.387 318.101 153.58L317.701 154.58C317.181 154.22 316.668 154.04 316.161 154.04C315.928 154.04 315.744 154.08 315.611 154.16C315.478 154.24 315.411 154.353 315.411 154.5C315.411 154.607 315.451 154.69 315.531 154.75C315.611 154.81 315.751 154.867 315.951 154.92L316.781 155.11C317.281 155.223 317.644 155.397 317.871 155.63C318.104 155.857 318.221 156.167 318.221 156.56C318.221 157.033 318.031 157.407 317.651 157.68C317.278 157.947 316.761 158.08 316.101 158.08ZM326.847 150.95V158H325.347V157.27C325.2 157.523 324.99 157.723 324.717 157.87C324.45 158.01 324.147 158.08 323.807 158.08C323.387 158.08 323.01 157.973 322.677 157.76C322.35 157.54 322.094 157.237 321.907 156.85C321.727 156.457 321.637 156.007 321.637 155.5C321.637 154.993 321.727 154.55 321.907 154.17C322.094 153.783 322.35 153.487 322.677 153.28C323.004 153.073 323.38 152.97 323.807 152.97C324.14 152.97 324.44 153.04 324.707 153.18C324.98 153.32 325.19 153.517 325.337 153.77V150.95H326.847ZM324.247 156.93C324.6 156.93 324.87 156.81 325.057 156.57C325.25 156.323 325.347 155.973 325.347 155.52C325.347 155.067 325.25 154.72 325.057 154.48C324.87 154.24 324.6 154.12 324.247 154.12C323.894 154.12 323.62 154.24 323.427 154.48C323.234 154.713 323.137 155.053 323.137 155.5C323.137 155.953 323.234 156.307 323.427 156.56C323.62 156.807 323.894 156.93 324.247 156.93ZM332.43 155.83H329.23C329.276 156.223 329.403 156.51 329.61 156.69C329.823 156.863 330.123 156.95 330.51 156.95C330.763 156.95 331.013 156.91 331.26 156.83C331.513 156.743 331.743 156.623 331.95 156.47L332.35 157.48C332.11 157.667 331.816 157.813 331.47 157.92C331.13 158.027 330.786 158.08 330.44 158.08C329.613 158.08 328.96 157.853 328.48 157.4C328.006 156.94 327.77 156.317 327.77 155.53C327.77 155.03 327.873 154.587 328.08 154.2C328.286 153.813 328.573 153.513 328.94 153.3C329.306 153.08 329.723 152.97 330.19 152.97C330.876 152.97 331.42 153.193 331.82 153.64C332.226 154.087 332.43 154.693 332.43 155.46V155.83ZM330.23 154.01C329.943 154.01 329.713 154.103 329.54 154.29C329.373 154.47 329.27 154.733 329.23 155.08H331.16C331.14 154.727 331.05 154.46 330.89 154.28C330.736 154.1 330.516 154.01 330.23 154.01ZM292.147 164.37C292.56 164.49 292.877 164.7 293.097 165C293.323 165.293 293.437 165.653 293.437 166.08C293.437 166.7 293.2 167.19 292.727 167.55C292.253 167.903 291.613 168.08 290.807 168.08C290.32 168.08 289.853 168.01 289.407 167.87C288.96 167.73 288.597 167.537 288.317 167.29L288.807 166.18C289.447 166.633 290.09 166.86 290.737 166.86C291.163 166.86 291.473 166.787 291.667 166.64C291.86 166.487 291.957 166.25 291.957 165.93C291.957 165.617 291.86 165.39 291.667 165.25C291.473 165.11 291.163 165.04 290.737 165.04H289.727V163.83H290.567C290.973 163.83 291.273 163.76 291.467 163.62C291.667 163.48 291.767 163.263 291.767 162.97C291.767 162.683 291.68 162.463 291.507 162.31C291.333 162.157 291.083 162.08 290.757 162.08C290.463 162.08 290.157 162.14 289.837 162.26C289.517 162.373 289.207 162.54 288.907 162.76L288.417 161.65C288.697 161.41 289.057 161.22 289.497 161.08C289.943 160.933 290.4 160.86 290.867 160.86C291.347 160.86 291.767 160.94 292.127 161.1C292.487 161.253 292.763 161.473 292.957 161.76C293.157 162.047 293.257 162.38 293.257 162.76C293.257 163.14 293.157 163.473 292.957 163.76C292.763 164.04 292.493 164.243 292.147 164.37ZM303.396 162.97C303.956 162.97 304.372 163.14 304.646 163.48C304.919 163.82 305.056 164.34 305.056 165.04V168H303.546V165.09C303.546 164.757 303.492 164.517 303.386 164.37C303.286 164.223 303.112 164.15 302.866 164.15C302.579 164.15 302.356 164.25 302.196 164.45C302.036 164.65 301.956 164.93 301.956 165.29V168H300.446V165.09C300.446 164.757 300.392 164.517 300.286 164.37C300.186 164.223 300.012 164.15 299.766 164.15C299.479 164.15 299.256 164.25 299.096 164.45C298.936 164.65 298.856 164.93 298.856 165.29V168H297.346V163.09H298.816V163.75C298.969 163.497 299.172 163.303 299.426 163.17C299.686 163.037 299.982 162.97 300.316 162.97C301.042 162.97 301.529 163.273 301.776 163.88C301.936 163.6 302.159 163.38 302.446 163.22C302.732 163.053 303.049 162.97 303.396 162.97ZM310.613 165.83H307.413C307.46 166.223 307.587 166.51 307.793 166.69C308.007 166.863 308.307 166.95 308.693 166.95C308.947 166.95 309.197 166.91 309.443 166.83C309.697 166.743 309.927 166.623 310.133 166.47L310.533 167.48C310.293 167.667 310 167.813 309.653 167.92C309.313 168.027 308.97 168.08 308.623 168.08C307.797 168.08 307.143 167.853 306.663 167.4C306.19 166.94 305.953 166.317 305.953 165.53C305.953 165.03 306.057 164.587 306.263 164.2C306.47 163.813 306.757 163.513 307.123 163.3C307.49 163.08 307.907 162.97 308.373 162.97C309.06 162.97 309.603 163.193 310.003 163.64C310.41 164.087 310.613 164.693 310.613 165.46V165.83ZM308.413 164.01C308.127 164.01 307.897 164.103 307.723 164.29C307.557 164.47 307.453 164.733 307.413 165.08H309.343C309.323 164.727 309.233 164.46 309.073 164.28C308.92 164.1 308.7 164.01 308.413 164.01ZM313.415 168.08C312.962 168.08 312.545 168.03 312.165 167.93C311.792 167.823 311.469 167.673 311.195 167.48L311.595 166.44C311.862 166.62 312.155 166.76 312.475 166.86C312.795 166.96 313.119 167.01 313.445 167.01C313.679 167.01 313.859 166.973 313.985 166.9C314.119 166.82 314.185 166.713 314.185 166.58C314.185 166.46 314.139 166.367 314.045 166.3C313.959 166.233 313.792 166.173 313.545 166.12L312.745 165.94C312.272 165.833 311.922 165.67 311.695 165.45C311.475 165.223 311.365 164.917 311.365 164.53C311.365 164.223 311.452 163.953 311.625 163.72C311.805 163.487 312.055 163.303 312.375 163.17C312.702 163.037 313.072 162.97 313.485 162.97C313.839 162.97 314.182 163.023 314.515 163.13C314.849 163.237 315.149 163.387 315.415 163.58L315.015 164.58C314.495 164.22 313.982 164.04 313.475 164.04C313.242 164.04 313.059 164.08 312.925 164.16C312.792 164.24 312.725 164.353 312.725 164.5C312.725 164.607 312.765 164.69 312.845 164.75C312.925 164.81 313.065 164.867 313.265 164.92L314.095 165.11C314.595 165.223 314.959 165.397 315.185 165.63C315.419 165.857 315.535 166.167 315.535 166.56C315.535 167.033 315.345 167.407 314.965 167.68C314.592 167.947 314.075 168.08 313.415 168.08ZM320.818 165.83H317.618C317.665 166.223 317.792 166.51 317.998 166.69C318.212 166.863 318.512 166.95 318.898 166.95C319.152 166.95 319.402 166.91 319.648 166.83C319.902 166.743 320.132 166.623 320.338 166.47L320.738 167.48C320.498 167.667 320.205 167.813 319.858 167.92C319.518 168.027 319.175 168.08 318.828 168.08C318.002 168.08 317.348 167.853 316.868 167.4C316.395 166.94 316.158 166.317 316.158 165.53C316.158 165.03 316.262 164.587 316.468 164.2C316.675 163.813 316.962 163.513 317.328 163.3C317.695 163.08 318.112 162.97 318.578 162.97C319.265 162.97 319.808 163.193 320.208 163.64C320.615 164.087 320.818 164.693 320.818 165.46V165.83ZM318.618 164.01C318.332 164.01 318.102 164.103 317.928 164.29C317.762 164.47 317.658 164.733 317.618 165.08H319.548C319.528 164.727 319.438 164.46 319.278 164.28C319.125 164.1 318.905 164.01 318.618 164.01ZM323.62 168.08C323.167 168.08 322.75 168.03 322.37 167.93C321.997 167.823 321.674 167.673 321.4 167.48L321.8 166.44C322.067 166.62 322.36 166.76 322.68 166.86C323 166.96 323.324 167.01 323.65 167.01C323.884 167.01 324.064 166.973 324.19 166.9C324.324 166.82 324.39 166.713 324.39 166.58C324.39 166.46 324.344 166.367 324.25 166.3C324.164 166.233 323.997 166.173 323.75 166.12L322.95 165.94C322.477 165.833 322.127 165.67 321.9 165.45C321.68 165.223 321.57 164.917 321.57 164.53C321.57 164.223 321.657 163.953 321.83 163.72C322.01 163.487 322.26 163.303 322.58 163.17C322.907 163.037 323.277 162.97 323.69 162.97C324.044 162.97 324.387 163.023 324.72 163.13C325.054 163.237 325.354 163.387 325.62 163.58L325.22 164.58C324.7 164.22 324.187 164.04 323.68 164.04C323.447 164.04 323.264 164.08 323.13 164.16C322.997 164.24 322.93 164.353 322.93 164.5C322.93 164.607 322.97 164.69 323.05 164.75C323.13 164.81 323.27 164.867 323.47 164.92L324.3 165.11C324.8 165.223 325.164 165.397 325.39 165.63C325.624 165.857 325.74 166.167 325.74 166.56C325.74 167.033 325.55 167.407 325.17 167.68C324.797 167.947 324.28 168.08 323.62 168.08Z" fill="white"/>
                    <path d="M325.435 182.723L329.729 176.281H319.01L323.762 182.76C324.181 183.332 325.042 183.313 325.435 182.723Z" fill="#323334"/>
                </g>
                <g filter="url(#filter1_d_2_2)">
                    <path d="M20.9784 32.4465C25.2352 32.4465 28.686 28.9956 28.686 24.7388C28.686 20.482 25.2352 17.0312 20.9784 17.0312C16.7216 17.0312 13.2708 20.482 13.2708 24.7388C13.2708 28.9956 16.7216 32.4465 20.9784 32.4465Z" fill="white"/>
                    <path d="M20.9784 31.936C24.9533 31.936 28.1756 28.7137 28.1756 24.7388C28.1756 20.7639 24.9533 17.5416 20.9784 17.5416C17.0035 17.5416 13.7812 20.7639 13.7812 24.7388C13.7812 28.7137 17.0035 31.936 20.9784 31.936Z" stroke="#2D2D2D" stroke-opacity="0.12" stroke-width="1.02083"/>
                </g>
                <g filter="url(#filter2_d_2_2)">
                    <path d="M30.625 41.5312H12.25C7.73967 41.5312 4.08333 45.1876 4.08333 49.6979V55.8229C4.08333 60.3332 7.73967 63.9896 12.25 63.9896H30.625C35.1353 63.9896 38.7917 60.3332 38.7917 55.8229V49.6979C38.7917 45.1876 35.1353 41.5312 30.625 41.5312Z" fill="#D3D5DA"/>
                    <path d="M12.5057 53.44H9.2257L8.5557 55H6.9857L10.2257 47.95H11.4757L14.7157 55H13.1757L12.5057 53.44ZM11.9957 52.26L10.8657 49.62L9.7357 52.26H11.9957ZM18.2655 49.97C18.8521 49.97 19.2888 50.1433 19.5755 50.49C19.8621 50.83 20.0055 51.3467 20.0055 52.04V55H18.4955V52.11C18.4955 51.7767 18.4321 51.5333 18.3055 51.38C18.1855 51.2267 17.9955 51.15 17.7355 51.15C17.4221 51.15 17.1688 51.25 16.9755 51.45C16.7888 51.65 16.6955 51.9167 16.6955 52.25V55H15.1855V47.95H16.6955V50.74C16.8688 50.4867 17.0888 50.2967 17.3555 50.17C17.6288 50.0367 17.9321 49.97 18.2655 49.97ZM23.4927 55.08C22.9727 55.08 22.5161 54.9767 22.1227 54.77C21.7294 54.5567 21.4261 54.26 21.2127 53.88C20.9994 53.4933 20.8927 53.04 20.8927 52.52C20.8927 52 20.9994 51.55 21.2127 51.17C21.4261 50.7833 21.7294 50.4867 22.1227 50.28C22.5161 50.0733 22.9727 49.97 23.4927 49.97C24.0127 49.97 24.4694 50.0733 24.8627 50.28C25.2561 50.4867 25.5594 50.7833 25.7727 51.17C25.9861 51.55 26.0927 52 26.0927 52.52C26.0927 53.04 25.9861 53.4933 25.7727 53.88C25.5594 54.26 25.2561 54.5567 24.8627 54.77C24.4694 54.9767 24.0127 55.08 23.4927 55.08ZM23.4927 53.93C24.2261 53.93 24.5927 53.46 24.5927 52.52C24.5927 52.0467 24.4961 51.6933 24.3027 51.46C24.1161 51.2267 23.8461 51.11 23.4927 51.11C22.7594 51.11 22.3927 51.58 22.3927 52.52C22.3927 53.46 22.7594 53.93 23.4927 53.93ZM30.5216 51.21L29.6716 51.3C29.2516 51.34 28.955 51.46 28.7816 51.66C28.6083 51.8533 28.5216 52.1133 28.5216 52.44V55H27.0116V50.09H28.4616V50.92C28.7083 50.3533 29.2183 50.0433 29.9916 49.99L30.4316 49.96L30.5216 51.21ZM33.2053 49.97C33.9386 49.97 34.4786 50.1433 34.8253 50.49C35.1786 50.8367 35.3553 51.3733 35.3553 52.1V55H33.9253V54.27C33.8253 54.5233 33.6586 54.7233 33.4253 54.87C33.192 55.01 32.9186 55.08 32.6053 55.08C32.272 55.08 31.9686 55.0133 31.6953 54.88C31.4286 54.7467 31.2153 54.56 31.0553 54.32C30.902 54.08 30.8253 53.8133 30.8253 53.52C30.8253 53.16 30.9153 52.8767 31.0953 52.67C31.282 52.4633 31.5786 52.3133 31.9853 52.22C32.392 52.1267 32.9486 52.08 33.6553 52.08H33.9153V51.9C33.9153 51.6067 33.852 51.4 33.7253 51.28C33.5986 51.16 33.3786 51.1 33.0653 51.1C32.8253 51.1 32.5586 51.1433 32.2653 51.23C31.972 51.3167 31.692 51.4367 31.4253 51.59L31.0253 50.58C31.3053 50.4067 31.6486 50.2633 32.0553 50.15C32.4686 50.03 32.852 49.97 33.2053 49.97ZM32.9353 54.04C33.2286 54.04 33.4653 53.9433 33.6453 53.75C33.8253 53.55 33.9153 53.2933 33.9153 52.98V52.81H33.7453C33.2053 52.81 32.8253 52.8533 32.6053 52.94C32.392 53.0267 32.2853 53.1833 32.2853 53.41C32.2853 53.59 32.3453 53.74 32.4653 53.86C32.592 53.98 32.7486 54.04 32.9353 54.04Z" fill="#828286"/>
                    <path d="M20.628 35.5997L16.3333 42.0417H27.0521L22.3005 35.5623C21.8813 34.9906 21.0212 35.0099 20.628 35.5997Z" fill="#D3D5DA"/>
                </g>
                <g id="resultGraphDot" class="result-graph-dot" filter="url(#filter3_d_2_2)">
                    <path d="M324.166 203.946C328.422 203.946 331.873 200.495 331.873 196.239C331.873 191.982 328.422 188.531 324.166 188.531C319.909 188.531 316.458 191.982 316.458 196.239C316.458 200.495 319.909 203.946 324.166 203.946Z" fill="white"/>
                    <path d="M324.166 203.436C328.141 203.436 331.363 200.213 331.363 196.239C331.363 192.264 328.141 189.041 324.166 189.041C320.191 189.041 316.968 192.264 316.968 196.239C316.968 200.213 320.191 203.436 324.166 203.436Z" stroke="#2D2D2D" stroke-opacity="0.12" stroke-width="1.02083"/>
                </g>
                <defs>
                    <filter id="filter0_d_2_2" x="267.917" y="144" width="74.1667" height="47.3442" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                        <feOffset dy="4.08333"/>
                        <feGaussianBlur stdDeviation="2.04167"/>
                        <feComposite in2="hardAlpha" operator="out"/>
                        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.07 0"/>
                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2_2"/>
                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2_2" result="shape"/>
                    </filter>
                    <filter id="filter1_d_2_2" x="5.10415" y="13.2689" width="31.7486" height="31.7485" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                        <feOffset dy="4.40435"/>
                        <feGaussianBlur stdDeviation="4.08333"/>
                        <feComposite in2="hardAlpha" operator="out"/>
                        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2_2"/>
                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2_2" result="shape"/>
                    </filter>
                    <filter id="filter2_d_2_2" x="-5.24521e-06" y="35.1452" width="42.875" height="37.0111" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                        <feOffset dy="4.08333"/>
                        <feGaussianBlur stdDeviation="2.04167"/>
                        <feComposite in2="hardAlpha" operator="out"/>
                        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.07 0"/>
                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2_2"/>
                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2_2" result="shape"/>
                    </filter>
                    <filter id="filter3_d_2_2" x="308.291" y="184.769" width="31.7486" height="31.7485" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                        <feOffset dy="4.40435"/>
                        <feGaussianBlur stdDeviation="4.08333"/>
                        <feComposite in2="hardAlpha" operator="out"/>
                        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2_2"/>
                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2_2" result="shape"/>
                    </filter>
                    <linearGradient id="paint0_linear_2_2" x1="27.5537" y1="195.379" x2="320.454" y2="195.379" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#FF574C"/>
                        <stop offset="0.549638" stop-color="#F5E088"/>
                        <stop offset="1" stop-color="#1998CD"/>
                    </linearGradient>
                    <linearGradient id="paint1_linear_2_2" x1="20.9271" y1="196.697" x2="323.094" y2="196.697" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#FF574C"/>
                        <stop offset="0.549638" stop-color="#F5E088"/>
                        <stop offset="1" stop-color="#1998CD"/>
                    </linearGradient>
                </defs>
            </svg>



            <div class="quiz__reviews-loading-svg-date-wrapp">
                <p class="quiz__reviews-loading-svg-date">
                    Ahora
                </p>
                <p class="quiz__reviews-loading-svg-date" id="monthAfter1">
                    month 1
                </p>
                <p class="quiz__reviews-loading-svg-date" id="monthAfter2">
                    month 2
                </p>
                <p class="quiz__reviews-loading-svg-date" id="monthAfter3">
                    month 3
                </p>
            </div>
            <p class="quiz__reviews-loading-svg-date-text">
                Este gráfico es solo para fines ilustrativos
            </p>
        </div>

        <p class="quiz__reviews-loading-subtext ">
            <span>El plan de entrenamiento</span> de tu <span class="puppyInsertSelector" data-puppy="cachorro">perro</span> está listo
        </p>
        <div class="quiz__btn-box">
            <button class="quiz__btn js-btn email-submit" id="redirect-to-landing-btn" data-landing-url="/questionary/landing-20">
                <span></span>
                Continuar
            </button>
        </div>
    </div>
    <div class="quiz__redirect-loader" id="resultLoader">
        <div class="preload">
            <span></span>
        </div>
    </div>
</div>

<div class="quiz__popup-wrapp" id="quizPopUpProblem">
    <div class="quiz__popup-bg"></div>

    <div class="quiz__popup" id="magicProblemPopup">
        <div class="quiz__popup-box">
            <h4 class="quiz__popup-title">
                Una pregunta más
            </h4>
            {% if quizData.isPuppyFunnel %}
                <div class="quiz__popup-img-box">
                    <picture>
                        <source srcset="https://res.cloudinary.com/dr0cx27xo/image/upload/v1719408096/dog-training/img/reactivity/webp/puppy-with-blue-ball.webp">
                        <img id="resultPopup" class="quiz__popup-img" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1719408087/dog-training/img/reactivity/puppy-with-blue-ball.jpg" alt="puppy with blue ball">
                    </picture>
                </div>
                <div class="quiz__popup-text">
                    <span>Los</span> <span class="ageInsertSelector">cachorros</span> a menudo protegen sus juguetes, camas o golosinas. ¿Tienes este problema?
                </div>
            {% else %}
                <div class="quiz__popup-img-box">
                    <picture>
                        <source srcset="https://res.cloudinary.com/dr0cx27xo/image/upload/v1719408098/dog-training/img/reactivity/webp/dog-with-teddy-bear-in-mouth.webp">
                        <img id="resultPopup" class="quiz__popup-img" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1719408088/dog-training/img/reactivity/dog-with-teddy-bear-in-mouth.jpg" alt="dog with teddy bear in mouth">
                    </picture>
                </div>
                <div class="quiz__popup-text">
                    Los perros de <b class="ageInsertSelector capitalize">AGE</b> a menudo protegen sus juguetes, camas o golosinas.
                    <br>
                    ¿Tienes este problema?
                </div>
            {% endif %}
            <ul class="quiz__popup-btn-list">
                <li class="quiz__popup-btn-item quizPopUpProblemBtn" data-event="No">
                    No
                </li>
                <li class="quiz__popup-btn-item quizPopUpProblemBtn" data-event="Yes">
                    Sí
                </li>
            </ul>
        </div>
    </div>
</div>
<div class="quiz__popup-wrapp" id="quizPopUpObedience">
    <div class="quiz__popup-bg"></div>

    <div class="quiz__popup" id="magicObediencePopup">
        <div class="quiz__popup-box">
            <h4 class="quiz__popup-title">
                Finalizando tu plan
            </h4>
            <div class="quiz__popup-img-box">
                <img id="resultPopup" class="quiz__popup-img" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1719408097/dog-training/img/reactivity/webp/dog-sit-command.webp" alt="dog sit command">
            </div>
            <div class="quiz__popup-text">
                ¿Ha tenido <span class="nameInsertSelector">to perro</span> algún entrenamiento de desensibilización?
            </div>
            <ul class="quiz__popup-btn-list">
                <li class="quiz__popup-btn-item quizPopUpObedienceBtn" data-event="No">
                    No
                </li>
                <li class="quiz__popup-btn-item quizPopUpObedienceBtn" data-event="Yes">
                    Sí
                </li>
            </ul>
        </div>
    </div>
</div>
