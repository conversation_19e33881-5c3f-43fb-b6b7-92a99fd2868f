{# "pc_bundle" split file #}
{# In case of split applying, please, remove the comment #}
{# In case of split refusing, please, double-check if it isn't used in any other places and delete this file #}
{# If you using this file in any other places but splits, please, remove the comment #}
{% set pc_bundle_split = getSplitValue('pc_bundle') %}

{% set courseCardItem = {
    "barkingCourse": {
        header: "Why Is Your Dog Barking?",
        userCount: "278k",
        price: 3899,
        percent: 70,
        image: "https://images.paw-champ.com/pc/images/common/interactive-course/barking.png",
        slotImg: "https://images.paw-champ.com/pc/images/common/interactive-course/barking-slot.png",
        imageAlt: "Why Is Your Dog Barking?",
    },
    "separationAnxietyCourse": {
        header: "Separation Anxiety in Dogs",
        userCount: "277k",
        price: 4199,
        percent: 70,
        image: "https://images.paw-champ.com/pc/images/common/interactive-course/separation-anxiety.png",
        slotImg: "https://images.paw-champ.com/pc/images/common/interactive-course/separation-anxiety-slot.png",
        imageAlt: "Separation Anxiety in Dogs"
    },
    "vagusNerveCourse": {
        header: "Dog Vagus Nerve<br>Reset",
        userCount: "259k",
        price: 4399,
        percent: 70,
        image: "https://images.paw-champ.com/pc/images/common/interactive-course/vagus-nerve.png",
        slotImg: "https://images.paw-champ.com/pc/images/common/interactive-course/vagus-nerve-slot.png",
        imageAlt: "Dog Vagus Nerve Reset"
    },
    "aggressionCourse": {
        header: "Dog Aggression and Triggers",
        userCount: "277k",
        price: 4099,
        percent: 70,
        image: "https://images.paw-champ.com/pc/images/common/interactive-course/aggression.png",
        slotImg: "https://images.paw-champ.com/pc/images/common/interactive-course/aggression-slot.png",
        imageAlt: "Dog Aggression and Triggers"
    },
    "heartToPawCourse": {
        header: "Heart to Paw",
        userCount: "259k",
        price: 3699,
        percent: 70,
        image: "https://images.paw-champ.com/pc/images/common/interactive-course/heart-to-paw.png",
        slotImg: "https://images.paw-champ.com/pc/images/common/interactive-course/heart-to-paw-slot.png",
        imageAlt: "Heart to Paw",
    },
    "reactivityCourse": {
        header: "Hyper Dog",
        userCount: "277k",
        price: 3999,
        percent: 70,
        image: "https://images.paw-champ.com/pc/images/common/interactive-course/reactivity.png",
        slotImg: "https://images.paw-champ.com/pc/images/common/interactive-course/reactivity-slot.png",
        imageAlt: "Hyper Dog",
    }
} %}

<div class="course" id="interactiveCourse">
    <div class="course-slot-container container">
        <div>
            <h1 class="course-slot-header">
                Pick <span class="nameInsertSelector">[NAME]</span>’s journey
            </h1>
            <p class="course-slot-title">You can switch and add journeys at any point</p>
        </div>

        <div class="course-slot-list">
            {% for place in ['1st journey', '2nd journey', '3rd journey'] %}
                <div class="course-slot-list-item" data-course-id="">
                    <div class="course-slot-list-item-icon">
                        <svg xmlns="https://www.w3.org/2000/svg" width="33" height="33" viewbox="0 0 33 33" fill="none">
                            <path d="M24.4993 18.2155H17.8327V24.8822C17.8327 25.2358 17.6922 25.5749 17.4422 25.825C17.1921 26.075 16.853 26.2155 16.4993 26.2155C16.1457 26.2155 15.8066 26.075 15.5565 25.825C15.3065 25.5749 15.166 25.2358 15.166 24.8822V18.2155H8.49935C8.14573 18.2155 7.80659 18.075 7.55654 17.825C7.30649 17.5749 7.16602 17.2358 7.16602 16.8822C7.16602 16.5285 7.30649 16.1894 7.55654 15.9394C7.80659 15.6893 8.14573 15.5488 8.49935 15.5488H15.166V8.88216C15.166 8.52854 15.3065 8.1894 15.5565 7.93935C15.8066 7.6893 16.1457 7.54883 16.4993 7.54883C16.853 7.54883 17.1921 7.6893 17.4422 7.93935C17.6922 8.1894 17.8327 8.52854 17.8327 8.88216V15.5488H24.4993C24.853 15.5488 25.1921 15.6893 25.4422 15.9394C25.6922 16.1894 25.8327 16.5285 25.8327 16.8822C25.8327 17.2358 25.6922 17.5749 25.4422 17.825C25.1921 18.075 24.853 18.2155 24.4993 18.2155Z"
                                  fill="#C5C6CB"/>
                        </svg>
                    </div>

                    <p class="course-slot-list-item-text">{{ place }}</p>
                </div>
            {% endfor %}
        </div>
    </div>

    <div class="course-content container">
        {% for cardId, cardData in courseCardItem %}
            <div class="course_card" id="{{ cardId }}" data-percent="{{ cardData.percent }}">
                <div class="course_card_check">
                    <svg xmlns="https://www.w3.org/2000/svg" width="12" height="12" viewbox="0 0 12 12" fill="none">
                        <path d="M4.73372 10.235C4.50977 10.235 4.34831 10.1361 4.19206 9.93294L1.48893 6.49023C1.38997 6.36523 1.33789 6.22982 1.33789 6.11003C1.33789 5.82357 1.54102 5.62044 1.83789 5.62044C2.02539 5.62044 2.1556 5.69336 2.28581 5.87565L4.71289 9.05274L9.38997 1.59961C9.51497 1.40169 9.62956 1.33398 9.83789 1.33398C10.1243 1.33398 10.3118 1.52148 10.3118 1.80794C10.3118 1.92773 10.2754 2.04232 10.1816 2.19336L5.25977 9.94336C5.13477 10.1361 4.95768 10.235 4.73372 10.235Z"
                              fill="white"/>
                    </svg>
                </div>
                <img loading="lazy" class="course_card_img" src="{{ cardData.image }}" alt="{{ cardData.imageAlt }}">
                <div class="course_card_content">
                    <h2 class="course_card_content_header">
                        {{ cardData.header|length > 35
                            ? (cardData.header[:35]|trim ~ '…')
                            : cardData.header
                        |raw }}
                    </h2>
                    <div class="course_card_content_description">
                        <span class="course_card_percent">{{ (cardData.percent ~ '%') }}</span>
                        match

                        {% if pc_bundle_split == 2 %}
                            <p class="course_card_content_description-users">{{ (cardData.userCount ~ ' learners')|trans }}</p>
                        {% else %}
                            <div class="course_card_content_description-price">
                                <p class="course_card_content_description-price-red">
                                    {{ (userCurrencySign() ~ (cardData.price / 100)|number_format(2, '.', '')) }}
                                </p>
                                <p class="course_card_content_description-price-green">&nbsp;{{ userCurrencySign() }}0</p>
                            </div>
                        {% endif %}
                    </div>


                    <button class="course_card_content_button">
                        Select
                    </button>
                </div>
            </div>
        {% endfor %}
    </div>

    <button class="quiz__btn quiz__btn-active js-btn container course_btn-position" disabled>
        <span></span>
        Continue
    </button>
</div>

<script>
    window.interactiveCourseList = {{ courseCardItem|json_encode|raw }};
</script>
