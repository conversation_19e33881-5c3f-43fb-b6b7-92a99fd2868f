<div class="quiz__index quiz__result skipQuiz magic" id="block{{ blockId }}" data-id="{{ blockId }}" data-result="reviews">
    <div class="container " id="resultStepOne">
        <div id="circleBar" class="progressbar reviews">
            <div class="progressbar__shadow"></div>
        </div>
        <p class="quiz__reviews-loading-subtitle" id="replaceSubtitle">
            Criando o plano de treinamento personalizado de <span class="nameInsertSelector">seu cachorro</span>...
        </p>
        <div class="quiz__hr">
        </div>
        <div class="quiz__reviews-loading-title-red-box">
            <h3 class="quiz__reviews-loading-title-red">
                115 mil donos de cães
            </h3>
        </div>
        <p class="quiz__reviews-loading-text">
            escolhi PawChamp
        </p>
        <div class="quiz__reviews-slider custom-result">
            <ul class="swiper-wrapper">
                <li class="quiz__reviews-slider-item swiper-slide">
                    <div class="quiz__reviews-slider-item-text-wrapp">
                        <svg width="90" height="16" viewBox="0 0 90 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M9.46744 6.37307H14.2218L10.3768 9.16159L10.3767 9.16159L8.00391 10.8849L10.7058 10.1835L10.3789 9.16822L11.8403 13.6821L7.99524 10.8849L4.15019 13.6821L5.62239 9.16159L1.77734 6.36441L6.53169 6.37307L8.0039 1.85254L9.46744 6.37307Z"
                                  fill="white"/>
                            <rect x="18.3711" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M27.8385 6.37307H32.5929L28.7478 9.16159L28.7478 9.16159L26.375 10.8849L29.0769 10.1835L28.75 9.16822L30.2114 13.6821L26.3663 10.8849L22.5213 13.6821L23.9935 9.16159L20.1484 6.36441L24.9028 6.37307L26.375 1.85254L27.8385 6.37307Z"
                                  fill="white"/>
                            <rect x="36.7402" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M46.2077 6.37307H50.962L47.117 9.16159L47.117 9.16159L44.7441 10.8849L47.4461 10.1835L47.1191 9.16822L48.5805 13.6821L44.7355 10.8849L40.8904 13.6821L42.3626 9.16159L38.5176 6.36441L43.2719 6.37307L44.7441 1.85254L46.2077 6.37307Z"
                                  fill="white"/>
                            <rect x="55.1113" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M64.5788 6.37307H69.3331L65.4881 9.16159L65.4883 9.16217L63.1162 10.8849L65.8181 10.1835L65.6738 9.7352L66.9516 13.6821L63.1066 10.8849L59.2615 13.6821L60.7337 9.16159L56.8887 6.36441L61.643 6.37307L63.1152 1.85254L64.5788 6.37307Z"
                                  fill="white"/>
                            <rect x="73.4824" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M82.9499 6.37307H87.7042L83.8592 9.16159L83.8592 9.16159L81.4863 10.8849L84.1883 10.1835L83.8613 9.16822L85.3227 13.6821L81.4777 10.8849L77.6326 13.6821L79.1048 9.16159L75.2598 6.36441L80.0141 6.37307L81.4863 1.85254L82.9499 6.37307Z"
                                  fill="white"/>
                        </svg>
                        <div class="quiz__reviews-slider-item-name-wrapp">
                            <h5>
                                Nova abordagem à calma
                            </h5>
                            <p class="quiz__reviews-slider-item-name">
                                Ernesto

                            </p>
                        </div>
                        <div class="quiz__reviews-slider-item-text">
                            Esta foi uma nova forma de aprender a ajudar o meu cão a manter-se calmo. Ainda não terminámos, mas já vi grandes melhorias no nosso pastor alemão de 8 anos, que já tinha algum treino mas tinha dificuldades com a reatividade.
                        </div>
                    </div>
                </li>
                <li class="quiz__reviews-slider-item swiper-slide">
                    <div class="quiz__reviews-slider-item-text-wrapp">
                        <svg width="90" height="16" viewBox="0 0 90 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M9.46744 6.37307H14.2218L10.3768 9.16159L10.3767 9.16159L8.00391 10.8849L10.7058 10.1835L10.3789 9.16822L11.8403 13.6821L7.99524 10.8849L4.15019 13.6821L5.62239 9.16159L1.77734 6.36441L6.53169 6.37307L8.0039 1.85254L9.46744 6.37307Z"
                                  fill="white"/>
                            <rect x="18.3711" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M27.8385 6.37307H32.5929L28.7478 9.16159L28.7478 9.16159L26.375 10.8849L29.0769 10.1835L28.75 9.16822L30.2114 13.6821L26.3663 10.8849L22.5213 13.6821L23.9935 9.16159L20.1484 6.36441L24.9028 6.37307L26.375 1.85254L27.8385 6.37307Z"
                                  fill="white"/>
                            <rect x="36.7402" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M46.2077 6.37307H50.962L47.117 9.16159L47.117 9.16159L44.7441 10.8849L47.4461 10.1835L47.1191 9.16822L48.5805 13.6821L44.7355 10.8849L40.8904 13.6821L42.3626 9.16159L38.5176 6.36441L43.2719 6.37307L44.7441 1.85254L46.2077 6.37307Z"
                                  fill="white"/>
                            <rect x="55.1113" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M64.5788 6.37307H69.3331L65.4881 9.16159L65.4883 9.16217L63.1162 10.8849L65.8181 10.1835L65.6738 9.7352L66.9516 13.6821L63.1066 10.8849L59.2615 13.6821L60.7337 9.16159L56.8887 6.36441L61.643 6.37307L63.1152 1.85254L64.5788 6.37307Z"
                                  fill="white"/>
                            <rect x="73.4824" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M82.9499 6.37307H87.7042L83.8592 9.16159L83.8592 9.16159L81.4863 10.8849L84.1883 10.1835L83.8613 9.16822L85.3227 13.6821L81.4777 10.8849L77.6326 13.6821L79.1048 9.16159L75.2598 6.36441L80.0141 6.37307L81.4863 1.85254L82.9499 6.37307Z"
                                  fill="white"/>
                        </svg>
                        <div class="quiz__reviews-slider-item-name-wrapp">
                            <h5>
                                A estrutura de formação é brilhante
                            </h5>
                            <p class="quiz__reviews-slider-item-name">
                                Claire_Ugalde
                            </p>
                        </div>
                        <div class="quiz__reviews-slider-item-text">
                            A forma como a formação é dividida é excelente. Já tive uma formação individual em minha casa e achei-a menos eficaz do que este curso e recomendo-a vivamente.
                        </div>
                    </div>
                </li>
                <li class="quiz__reviews-slider-item swiper-slide">
                    <div class="quiz__reviews-slider-item-text-wrapp">
                        <svg width="90" height="16" viewBox="0 0 90 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M9.46744 6.37307H14.2218L10.3768 9.16159L10.3767 9.16159L8.00391 10.8849L10.7058 10.1835L10.3789 9.16822L11.8403 13.6821L7.99524 10.8849L4.15019 13.6821L5.62239 9.16159L1.77734 6.36441L6.53169 6.37307L8.0039 1.85254L9.46744 6.37307Z"
                                  fill="white"/>
                            <rect x="18.3711" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M27.8385 6.37307H32.5929L28.7478 9.16159L28.7478 9.16159L26.375 10.8849L29.0769 10.1835L28.75 9.16822L30.2114 13.6821L26.3663 10.8849L22.5213 13.6821L23.9935 9.16159L20.1484 6.36441L24.9028 6.37307L26.375 1.85254L27.8385 6.37307Z"
                                  fill="white"/>
                            <rect x="36.7402" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M46.2077 6.37307H50.962L47.117 9.16159L47.117 9.16159L44.7441 10.8849L47.4461 10.1835L47.1191 9.16822L48.5805 13.6821L44.7355 10.8849L40.8904 13.6821L42.3626 9.16159L38.5176 6.36441L43.2719 6.37307L44.7441 1.85254L46.2077 6.37307Z"
                                  fill="white"/>
                            <rect x="55.1113" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M64.5788 6.37307H69.3331L65.4881 9.16159L65.4883 9.16217L63.1162 10.8849L65.8181 10.1835L65.6738 9.7352L66.9516 13.6821L63.1066 10.8849L59.2615 13.6821L60.7337 9.16159L56.8887 6.36441L61.643 6.37307L63.1152 1.85254L64.5788 6.37307Z"
                                  fill="white"/>
                            <rect x="73.4824" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M82.9499 6.37307H87.7042L83.8592 9.16159L83.8592 9.16159L81.4863 10.8849L84.1883 10.1835L83.8613 9.16822L85.3227 13.6821L81.4777 10.8849L77.6326 13.6821L79.1048 9.16159L75.2598 6.36441L80.0141 6.37307L81.4863 1.85254L82.9499 6.37307Z"
                                  fill="white"/>
                        </svg>
                        <div class="quiz__reviews-slider-item-name-wrapp">
                            <h5>
                                Excelente curso de treinamento
                            </h5>
                            <p class="quiz__reviews-slider-item-name">
                                Diego Fernandez Jr
                            </p>
                        </div>
                        <p class="quiz__reviews-slider-item-text">
                            Excelente curso de treinamento, muito detalhado e fácil de entender. O que eu gosto neste curso é que eles enfatizam que o treinamento de um cão requer paciência e compreensão do processo. Ainda estou na primeira semana, mas a abordagem deles parece fácil de seguir.
                            Mal posso esperar para ver os resultados finais…
                        </p>
                    </div>
                </li>
            </ul>
        </div>
    </div>
    <div class="container email" id="resultStepTwo">
        <h2 class="quiz__reviews-loading-title">
            Digite seu e-mail para obter o plano personalizado de <span class="nameInsertSelector">seu cachorro</span> para superar a reatividade
        </h2>
        <p class="quiz__result-label" id="emailText"></p>
        <div class="quiz__result-box">
            <ul id="autoComplete" class="email-autocomplete email-autocomplete-scroll"></ul>
            <input class="quiz__result-input" name="text" type="text" placeholder="Digite seu e-mail para obter seu plano" id="email" autocomplete="off">
        </div>
        <div class="quiz__result-text-box">
            <svg class="quiz__result-text-icon" width="21" height="26" viewBox="0 0 21 26" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path opacity="0.45"
                      d="M20.1944 9.75H18.5278V7.58332C18.5278 3.40184 15.0387 0 10.75 0C6.46125 0 2.97224 3.40184 2.97224 7.58332V9.75H1.30557C0.99849 9.75 0.75 9.99228 0.75 10.2917V23.8334C0.75 25.0283 1.74661 26 2.97224 26H18.5278C19.7534 26 20.75 25.0283 20.75 23.8333V10.2917C20.75 9.99228 20.5015 9.75 20.1944 9.75ZM12.4134 21.0652C12.4308 21.2181 12.3804 21.3715 12.2751 21.4863C12.1698 21.6011 12.019 21.6667 11.8611 21.6667H9.63891C9.48104 21.6667 9.33021 21.6011 9.22495 21.4863C9.11969 21.3715 9.06922 21.2181 9.08661 21.0652L9.43708 17.9925C8.86797 17.5888 8.52781 16.9504 8.52781 16.25C8.52781 15.0551 9.52443 14.0833 10.7501 14.0833C11.9757 14.0833 12.9723 15.055 12.9723 16.25C12.9723 16.9504 12.6321 17.5888 12.063 17.9925L12.4134 21.0652ZM15.1944 9.75H6.30557V7.58332C6.30557 5.19396 8.29938 3.25 10.75 3.25C13.2006 3.25 15.1944 5.19396 15.1944 7.58332V9.75Z"
                      fill="#343434"/>
            </svg>
            <p class="quiz__result-text">
                Protegemos a sua privacidade e estamos empenhados em proteger os seus dados pessoais. Nunca enviamos e-mails de spam, apenas informações relevantes.
            </p>
        </div>
        <div class="quiz__btn-box">
            <button class="quiz__btn js-btn email-submit" id="email-submit" disabled>
                <span></span>
                Continuar
            </button>
        </div>
    </div>
    <div class="container " id="resultStepEmailSubscription">
        <h3 class="quiz__email-subscription-title">
            Deseja receber mensagens de correio eletrónico com <span>ofertas especiais</span>, dicas de treino de cães, conselhos e <span>ofertas gratuitas</span>?
        </h3>
        <div class="quiz__email-subscription-btn-box">
            <button class="quiz__btn triggerStep" data-event-value="Apply newsletter">
                <span></span>
                Sim, estou dentro!
            </button>
            <label class="quiz__email-subscription-label triggerStep" data-event-value="Reject newsletter">
                <input type="checkbox" class="quiz__email-subscription-input" id="disallowMarketingEmails">
                Não estou interessado
            </label>
        </div>
    </div>
    <div class="container graph " id="resultStepThree">
        <h2 class="quiz__reviews-loading-title">
            Reatividade de <span class="nameInsertSelector">seu cachorro</span>
        </h2>
        <div class="quiz__reviews-loading-svg-inner">
            <svg class="quiz__reviews-loading-svg quiz__reviews-loading-svg--is-reactivity" width="343" height="227" viewBox="0 0 336 203" fill="none" xmlns="http://www.w3.org/2000/svg">
                <line x1="20" y1="0.5" x2="316.12" y2="0.499974" stroke="#E8E8E8"/>
                <line x1="20" y1="26.5" x2="316.12" y2="26.5" stroke="#E8E8E8"/>
                <line x1="20" y1="52.5" x2="316.12" y2="52.5" stroke="#E8E8E8"/>
                <line x1="20" y1="78.5" x2="316.12" y2="78.5" stroke="#E8E8E8"/>
                <line x1="20" y1="104.5" x2="316.12" y2="104.5" stroke="#E8E8E8"/>
                <line x1="20" y1="130.5" x2="316.12" y2="130.5" stroke="#E8E8E8"/>
                <line x1="20" y1="156.5" x2="316.12" y2="156.5" stroke="#E8E8E8"/>
                <g id="resultGraphBg" class="result-graph-bg" opacity="0.3">
                    <path d="M20 16.9996C36.4481 20.9767 52.5247 24.5721 68.1785 28.073C114.36 38.4011 156.862 47.9063 194.366 63.9597C245.023 85.643 286.513 119.261 315.451 183H20V16.9996Z" fill="#D9D9D9"/>
                    <path d="M20 16.9996C36.4481 20.9767 52.5247 24.5721 68.1785 28.073C114.36 38.4011 156.862 47.9063 194.366 63.9597C245.023 85.643 286.513 119.261 315.451 183H20V16.9996Z" fill="url(#paint0_linear_2003_11)"/>
                </g>
                <line x1="20" y1="182.5" x2="317.06" y2="182.5" stroke="#E8E8E8"/>
                <line x1="20.4395" y1="189" x2="20.4395" y2="183" stroke="#E8E8E8"/>
                <line x1="316.561" y1="189" x2="316.561" y2="183" stroke="#E8E8E8"/>
                <line x1="215.032" y1="189" x2="215.032" y2="183" stroke="#E8E8E8"/>
                <line x1="126.667" y1="189" x2="126.667" y2="183" stroke="#E8E8E8"/>
                <path d="M20.6591,15.0185C20.6591,15.0185 10.9682,13.1907 142.872,45.9062C274.775,78.6218 299.9,154.958 316.5,183" class="quiz__based-graph-line" stroke="url(#paint1_linear_2003_11)" stroke-width="5"/>
                <g class="result-graph-goal" filter="url(#filter0_d_2003_11)">
                    <rect x="277" y="131" width="51" height="32" rx="8" fill="#323334"/>
                    <path d="M295.713 143.44H292.433L291.763 145H290.193L293.433 137.95H294.683L297.923 145H296.383L295.713 143.44ZM295.203 142.26L294.073 139.62L292.943 142.26H295.203ZM301.433 139.97C301.853 139.97 302.226 140.08 302.553 140.3C302.886 140.513 303.143 140.817 303.322 141.21C303.509 141.597 303.603 142.043 303.603 142.55C303.603 143.057 303.509 143.503 303.322 143.89C303.143 144.27 302.889 144.563 302.563 144.77C302.236 144.977 301.859 145.08 301.433 145.08C301.099 145.08 300.796 145.013 300.523 144.88C300.256 144.74 300.049 144.547 299.903 144.3V146.8H298.393V140.09H299.883V140.78C300.029 140.527 300.239 140.33 300.513 140.19C300.786 140.043 301.093 139.97 301.433 139.97ZM300.993 143.93C301.346 143.93 301.619 143.813 301.813 143.58C302.006 143.34 302.103 142.997 302.103 142.55C302.103 142.097 302.006 141.747 301.813 141.5C301.619 141.247 301.346 141.12 300.993 141.12C300.639 141.12 300.366 141.243 300.173 141.49C299.979 141.73 299.883 142.077 299.883 142.53C299.883 142.977 299.979 143.323 300.173 143.57C300.366 143.81 300.639 143.93 300.993 143.93ZM306.885 145.08C306.365 145.08 305.909 144.977 305.515 144.77C305.122 144.557 304.819 144.26 304.605 143.88C304.392 143.493 304.285 143.04 304.285 142.52C304.285 142 304.392 141.55 304.605 141.17C304.819 140.783 305.122 140.487 305.515 140.28C305.909 140.073 306.365 139.97 306.885 139.97C307.405 139.97 307.862 140.073 308.255 140.28C308.649 140.487 308.952 140.783 309.165 141.17C309.379 141.55 309.485 142 309.485 142.52C309.485 143.04 309.379 143.493 309.165 143.88C308.952 144.26 308.649 144.557 308.255 144.77C307.862 144.977 307.405 145.08 306.885 145.08ZM306.885 143.93C307.619 143.93 307.985 143.46 307.985 142.52C307.985 142.047 307.889 141.693 307.695 141.46C307.509 141.227 307.239 141.11 306.885 141.11C306.152 141.11 305.785 141.58 305.785 142.52C305.785 143.46 306.152 143.93 306.885 143.93ZM306.415 139.45L307.395 137.4H308.875L307.355 139.45H306.415ZM312.294 145.08C311.841 145.08 311.424 145.03 311.044 144.93C310.671 144.823 310.348 144.673 310.074 144.48L310.474 143.44C310.741 143.62 311.034 143.76 311.354 143.86C311.674 143.96 311.998 144.01 312.324 144.01C312.558 144.01 312.738 143.973 312.864 143.9C312.998 143.82 313.064 143.713 313.064 143.58C313.064 143.46 313.018 143.367 312.924 143.3C312.838 143.233 312.671 143.173 312.424 143.12L311.624 142.94C311.151 142.833 310.801 142.67 310.574 142.45C310.354 142.223 310.244 141.917 310.244 141.53C310.244 141.223 310.331 140.953 310.504 140.72C310.684 140.487 310.934 140.303 311.254 140.17C311.581 140.037 311.951 139.97 312.364 139.97C312.718 139.97 313.061 140.023 313.394 140.13C313.728 140.237 314.028 140.387 314.294 140.58L313.894 141.58C313.374 141.22 312.861 141.04 312.354 141.04C312.121 141.04 311.938 141.08 311.804 141.16C311.671 141.24 311.604 141.353 311.604 141.5C311.604 141.607 311.644 141.69 311.724 141.75C311.804 141.81 311.944 141.867 312.144 141.92L312.974 142.11C313.474 142.223 313.838 142.397 314.064 142.63C314.298 142.857 314.414 143.167 314.414 143.56C314.414 144.033 314.224 144.407 313.844 144.68C313.471 144.947 312.954 145.08 312.294 145.08ZM287.647 151.37C288.06 151.49 288.377 151.7 288.597 152C288.823 152.293 288.937 152.653 288.937 153.08C288.937 153.7 288.7 154.19 288.227 154.55C287.753 154.903 287.113 155.08 286.307 155.08C285.82 155.08 285.353 155.01 284.907 154.87C284.46 154.73 284.097 154.537 283.817 154.29L284.307 153.18C284.947 153.633 285.59 153.86 286.237 153.86C286.663 153.86 286.973 153.787 287.167 153.64C287.36 153.487 287.457 153.25 287.457 152.93C287.457 152.617 287.36 152.39 287.167 152.25C286.973 152.11 286.663 152.04 286.237 152.04H285.227V150.83H286.067C286.473 150.83 286.773 150.76 286.967 150.62C287.167 150.48 287.267 150.263 287.267 149.97C287.267 149.683 287.18 149.463 287.007 149.31C286.833 149.157 286.583 149.08 286.257 149.08C285.963 149.08 285.657 149.14 285.337 149.26C285.017 149.373 284.707 149.54 284.407 149.76L283.917 148.65C284.197 148.41 284.557 148.22 284.997 148.08C285.443 147.933 285.9 147.86 286.367 147.86C286.847 147.86 287.267 147.94 287.627 148.1C287.987 148.253 288.263 148.473 288.457 148.76C288.657 149.047 288.757 149.38 288.757 149.76C288.757 150.14 288.657 150.473 288.457 150.76C288.263 151.04 287.993 151.243 287.647 151.37ZM298.896 149.97C299.456 149.97 299.872 150.14 300.146 150.48C300.419 150.82 300.556 151.34 300.556 152.04V155H299.046V152.09C299.046 151.757 298.992 151.517 298.886 151.37C298.786 151.223 298.612 151.15 298.366 151.15C298.079 151.15 297.856 151.25 297.696 151.45C297.536 151.65 297.456 151.93 297.456 152.29V155H295.946V152.09C295.946 151.757 295.892 151.517 295.786 151.37C295.686 151.223 295.512 151.15 295.266 151.15C294.979 151.15 294.756 151.25 294.596 151.45C294.436 151.65 294.356 151.93 294.356 152.29V155H292.846V150.09H294.316V150.75C294.469 150.497 294.672 150.303 294.926 150.17C295.186 150.037 295.482 149.97 295.816 149.97C296.542 149.97 297.029 150.273 297.276 150.88C297.436 150.6 297.659 150.38 297.946 150.22C298.232 150.053 298.549 149.97 298.896 149.97ZM306.113 152.83H302.913C302.96 153.223 303.087 153.51 303.293 153.69C303.507 153.863 303.807 153.95 304.193 153.95C304.447 153.95 304.697 153.91 304.943 153.83C305.197 153.743 305.427 153.623 305.633 153.47L306.033 154.48C305.793 154.667 305.5 154.813 305.153 154.92C304.813 155.027 304.47 155.08 304.123 155.08C303.297 155.08 302.643 154.853 302.163 154.4C301.69 153.94 301.453 153.317 301.453 152.53C301.453 152.03 301.557 151.587 301.763 151.2C301.97 150.813 302.257 150.513 302.623 150.3C302.99 150.08 303.407 149.97 303.873 149.97C304.56 149.97 305.103 150.193 305.503 150.64C305.91 151.087 306.113 151.693 306.113 152.46V152.83ZM303.913 151.01C303.627 151.01 303.397 151.103 303.223 151.29C303.057 151.47 302.953 151.733 302.913 152.08H304.843C304.823 151.727 304.733 151.46 304.573 151.28C304.42 151.1 304.2 151.01 303.913 151.01ZM308.915 155.08C308.462 155.08 308.045 155.03 307.665 154.93C307.292 154.823 306.969 154.673 306.695 154.48L307.095 153.44C307.362 153.62 307.655 153.76 307.975 153.86C308.295 153.96 308.619 154.01 308.945 154.01C309.179 154.01 309.359 153.973 309.485 153.9C309.619 153.82 309.685 153.713 309.685 153.58C309.685 153.46 309.639 153.367 309.545 153.3C309.459 153.233 309.292 153.173 309.045 153.12L308.245 152.94C307.772 152.833 307.422 152.67 307.195 152.45C306.975 152.223 306.865 151.917 306.865 151.53C306.865 151.223 306.952 150.953 307.125 150.72C307.305 150.487 307.555 150.303 307.875 150.17C308.202 150.037 308.572 149.97 308.985 149.97C309.339 149.97 309.682 150.023 310.015 150.13C310.349 150.237 310.649 150.387 310.915 150.58L310.515 151.58C309.995 151.22 309.482 151.04 308.975 151.04C308.742 151.04 308.559 151.08 308.425 151.16C308.292 151.24 308.225 151.353 308.225 151.5C308.225 151.607 308.265 151.69 308.345 151.75C308.425 151.81 308.565 151.867 308.765 151.92L309.595 152.11C310.095 152.223 310.459 152.397 310.685 152.63C310.919 152.857 311.035 153.167 311.035 153.56C311.035 154.033 310.845 154.407 310.465 154.68C310.092 154.947 309.575 155.08 308.915 155.08ZM316.318 152.83H313.118C313.165 153.223 313.292 153.51 313.498 153.69C313.712 153.863 314.012 153.95 314.398 153.95C314.652 153.95 314.902 153.91 315.148 153.83C315.402 153.743 315.632 153.623 315.838 153.47L316.238 154.48C315.998 154.667 315.705 154.813 315.358 154.92C315.018 155.027 314.675 155.08 314.328 155.08C313.502 155.08 312.848 154.853 312.368 154.4C311.895 153.94 311.658 153.317 311.658 152.53C311.658 152.03 311.762 151.587 311.968 151.2C312.175 150.813 312.462 150.513 312.828 150.3C313.195 150.08 313.612 149.97 314.078 149.97C314.765 149.97 315.308 150.193 315.708 150.64C316.115 151.087 316.318 151.693 316.318 152.46V152.83ZM314.118 151.01C313.832 151.01 313.602 151.103 313.428 151.29C313.262 151.47 313.158 151.733 313.118 152.08H315.048C315.028 151.727 314.938 151.46 314.778 151.28C314.625 151.1 314.405 151.01 314.118 151.01ZM319.12 155.08C318.667 155.08 318.25 155.03 317.87 154.93C317.497 154.823 317.174 154.673 316.9 154.48L317.3 153.44C317.567 153.62 317.86 153.76 318.18 153.86C318.5 153.96 318.824 154.01 319.15 154.01C319.384 154.01 319.564 153.973 319.69 153.9C319.824 153.82 319.89 153.713 319.89 153.58C319.89 153.46 319.844 153.367 319.75 153.3C319.664 153.233 319.497 153.173 319.25 153.12L318.45 152.94C317.977 152.833 317.627 152.67 317.4 152.45C317.18 152.223 317.07 151.917 317.07 151.53C317.07 151.223 317.157 150.953 317.33 150.72C317.51 150.487 317.76 150.303 318.08 150.17C318.407 150.037 318.777 149.97 319.19 149.97C319.544 149.97 319.887 150.023 320.22 150.13C320.554 150.237 320.854 150.387 321.12 150.58L320.72 151.58C320.2 151.22 319.687 151.04 319.18 151.04C318.947 151.04 318.764 151.08 318.63 151.16C318.497 151.24 318.43 151.353 318.43 151.5C318.43 151.607 318.47 151.69 318.55 151.75C318.63 151.81 318.77 151.867 318.97 151.92L319.8 152.11C320.3 152.223 320.664 152.397 320.89 152.63C321.124 152.857 321.24 153.167 321.24 153.56C321.24 154.033 321.05 154.407 320.67 154.68C320.297 154.947 319.78 155.08 319.12 155.08Z" fill="white"/>
                    <path d="M318.793 169.31L323 163L312.5 163L317.155 169.347C317.565 169.907 318.408 169.888 318.793 169.31Z" fill="#323334" transform="translate(0 -1)"/>
                </g>
                <g filter="url(#filter1_d_2003_11)">
                    <circle cx="20.5503" cy="14.5503" r="7.55031" fill="white"/>
                    <circle cx="20.5503" cy="14.5503" r="7.05031" stroke="#2D2D2D" stroke-opacity="0.12"/>
                </g>
                <g filter="url(#filter2_d_2003_11)" transform="translate(2)">
                    <rect x="4" y="31" width="42" height="22" rx="8" fill="#D3D5DA" transform="translate(-4)"/>
                    <path d="M15.8934 43.44H12.6134L11.9434 45H10.3734L13.6134 37.95H14.8634L18.1034 45H16.5634L15.8934 43.44ZM15.3834 42.26L14.2534 39.62L13.1234 42.26H15.3834ZM23.5832 40.09V44.47C23.5832 45.2633 23.3565 45.8633 22.9032 46.27C22.4498 46.6767 21.7798 46.88 20.8932 46.88C20.4532 46.88 20.0298 46.8267 19.6232 46.72C19.2232 46.6133 18.8698 46.4633 18.5632 46.27L18.9632 45.23C19.5965 45.5767 20.2265 45.75 20.8532 45.75C21.6798 45.75 22.0932 45.36 22.0932 44.58V44.04C21.9532 44.2933 21.7432 44.4933 21.4632 44.64C21.1832 44.7867 20.8698 44.86 20.5232 44.86C20.0965 44.86 19.7165 44.76 19.3832 44.56C19.0565 44.36 18.7998 44.0767 18.6132 43.71C18.4265 43.3367 18.3332 42.9067 18.3332 42.42C18.3332 41.9333 18.4265 41.5067 18.6132 41.14C18.7998 40.7667 19.0565 40.48 19.3832 40.28C19.7165 40.0733 20.0965 39.97 20.5232 39.97C20.8632 39.97 21.1698 40.0433 21.4432 40.19C21.7232 40.33 21.9365 40.5267 22.0832 40.78V40.09H23.5832ZM20.9632 43.71C21.3165 43.71 21.5898 43.5967 21.7832 43.37C21.9832 43.1433 22.0832 42.8267 22.0832 42.42C22.0832 42.0133 21.9832 41.6967 21.7832 41.47C21.5898 41.2367 21.3165 41.12 20.9632 41.12C20.6098 41.12 20.3332 41.2367 20.1332 41.47C19.9332 41.6967 19.8332 42.0133 19.8332 42.42C19.8332 42.82 19.9332 43.1367 20.1332 43.37C20.3332 43.5967 20.6098 43.71 20.9632 43.71ZM27.105 45.08C26.585 45.08 26.1284 44.9767 25.735 44.77C25.3417 44.5567 25.0384 44.26 24.825 43.88C24.6117 43.4933 24.505 43.04 24.505 42.52C24.505 42 24.6117 41.55 24.825 41.17C25.0384 40.7833 25.3417 40.4867 25.735 40.28C26.1284 40.0733 26.585 39.97 27.105 39.97C27.625 39.97 28.0817 40.0733 28.475 40.28C28.8684 40.4867 29.1717 40.7833 29.385 41.17C29.5984 41.55 29.705 42 29.705 42.52C29.705 43.04 29.5984 43.4933 29.385 43.88C29.1717 44.26 28.8684 44.5567 28.475 44.77C28.0817 44.9767 27.625 45.08 27.105 45.08ZM27.105 43.93C27.8384 43.93 28.205 43.46 28.205 42.52C28.205 42.0467 28.1084 41.6933 27.915 41.46C27.7284 41.2267 27.4584 41.11 27.105 41.11C26.3717 41.11 26.005 41.58 26.005 42.52C26.005 43.46 26.3717 43.93 27.105 43.93ZM34.1339 41.21L33.2839 41.3C32.8639 41.34 32.5673 41.46 32.3939 41.66C32.2206 41.8533 32.1339 42.1133 32.1339 42.44V45H30.6239V40.09H32.0739V40.92C32.3206 40.3533 32.8306 40.0433 33.6039 39.99L34.0439 39.96L34.1339 41.21ZM36.8176 39.97C37.551 39.97 38.091 40.1433 38.4376 40.49C38.791 40.8367 38.9676 41.3733 38.9676 42.1V45H37.5376V44.27C37.4376 44.5233 37.271 44.7233 37.0376 44.87C36.8043 45.01 36.531 45.08 36.2176 45.08C35.8843 45.08 35.581 45.0133 35.3076 44.88C35.041 44.7467 34.8276 44.56 34.6676 44.32C34.5143 44.08 34.4376 43.8133 34.4376 43.52C34.4376 43.16 34.5276 42.8767 34.7076 42.67C34.8943 42.4633 35.191 42.3133 35.5976 42.22C36.0043 42.1267 36.561 42.08 37.2676 42.08H37.5276V41.9C37.5276 41.6067 37.4643 41.4 37.3376 41.28C37.211 41.16 36.991 41.1 36.6776 41.1C36.4376 41.1 36.171 41.1433 35.8776 41.23C35.5843 41.3167 35.3043 41.4367 35.0376 41.59L34.6376 40.58C34.9176 40.4067 35.261 40.2633 35.6676 40.15C36.081 40.03 36.4643 39.97 36.8176 39.97ZM36.5476 44.04C36.841 44.04 37.0776 43.9433 37.2576 43.75C37.4376 43.55 37.5276 43.2933 37.5276 42.98V42.81H37.3576C36.8176 42.81 36.4376 42.8533 36.2176 42.94C36.0043 43.0267 35.8976 43.1833 35.8976 43.41C35.8976 43.59 35.9576 43.74 36.0776 43.86C36.2043 43.98 36.361 44.04 36.5476 44.04Z" fill="#828286" transform="translate(-4)"/>
                    <path d="M17.207 25.1895L13 31.5L23.5 31.5L18.8454 25.1529C18.4347 24.5928 17.5922 24.6117 17.207 25.1895Z" fill="#D3D5DA"/>
                </g>
                <g id="resultGraphDot" class="result-graph-dot" filter="url(#filter3_d_2003_11)">
                    <circle cx="317.55" cy="182.55" r="7.55031" fill="white"/>
                    <circle cx="317.55" cy="182.55" r="7.05031" stroke="#2D2D2D" stroke-opacity="0.12"/>
                </g>
                <defs>
                    <filter id="filter0_d_2003_11" x="270.5" y="131" width="65" height="46.7559" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                        <feOffset dy="4"/>
                        <feGaussianBlur stdDeviation="2"/>
                        <feComposite in2="hardAlpha" operator="out"/>
                        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.07 0"/>
                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2003_11"/>
                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2003_11" result="shape"/>
                    </filter>
                    <filter id="filter1_d_2003_11" x="5" y="3.31447" width="31.1006" height="31.1006" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                        <feOffset dy="4.31447"/>
                        <feGaussianBlur stdDeviation="4"/>
                        <feComposite in2="hardAlpha" operator="out"/>
                        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2003_11"/>
                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2003_11" result="shape"/>
                    </filter>
                    <filter id="filter2_d_2003_11" x="0" y="24.7441" width="42" height="36.2559" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                        <feOffset dy="4"/>
                        <feGaussianBlur stdDeviation="2"/>
                        <feComposite in2="hardAlpha" operator="out"/>
                        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.07 0"/>
                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2003_11"/>
                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2003_11" result="shape"/>
                    </filter>
                    <filter id="filter3_d_2003_11" x="302" y="171.314" width="31.1006" height="31.1006" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                        <feOffset dy="4.31447"/>
                        <feGaussianBlur stdDeviation="4"/>
                        <feComposite in2="hardAlpha" operator="out"/>
                        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2003_11"/>
                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2003_11" result="shape"/>
                    </filter>
                    <linearGradient id="paint0_linear_2003_11" x1="26.9914" y1="181.708" x2="313.914" y2="181.708" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#FF574C"/>
                        <stop offset="0.549638" stop-color="#F5E088"/>
                        <stop offset="1" stop-color="#1998CD"/>
                    </linearGradient>
                    <linearGradient id="paint1_linear_2003_11" x1="20.5" y1="182.999" x2="316.5" y2="182.999" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#FF574C"/>
                        <stop offset="0.549638" stop-color="#F5E088"/>
                        <stop offset="1" stop-color="#1998CD"/>
                    </linearGradient>
                </defs>
            </svg>

            <div class="quiz__reviews-loading-svg-date-wrapp">
                <p class="quiz__reviews-loading-svg-date">
                    AGORA
                </p>
                <p class="quiz__reviews-loading-svg-date" id="monthAfter1">
                    month 1
                </p>
                <p class="quiz__reviews-loading-svg-date" id="monthAfter2">
                    month 2
                </p>
                <p class="quiz__reviews-loading-svg-date" id="monthAfter3">
                    month 3
                </p>
            </div>
            <p class="quiz__reviews-loading-svg-date-text">
                Este gráfico é apenas para fins ilustrativos
            </p>
        </div>

        <p class="quiz__reviews-loading-subtext">
            <span>O plano de treinamento</span> do seu <span class="puppyInsertSelector" data-puppy="filhote">cão</span> está pronto
        </p>
        <div class="quiz__btn-box">
            <button class="quiz__btn js-btn email-submit" id="redirect-to-landing-btn" data-landing-url="/questionary/landing-21">
                <span></span>
                Continuar
            </button>
        </div>
    </div>
    <div class="quiz__redirect-loader" id="resultLoader">
        <div class="preload">
            <span></span>
        </div>
    </div>
</div>

<div class="quiz__popup-wrapp" id="quizPopUpProblem">
    <div class="quiz__popup-bg"></div>

    <div class="quiz__popup" id="magicProblemPopup">
        <div class="quiz__popup-box">
            <h4 class="quiz__popup-title">
                Mais uma pergunta
            </h4>
            {% if quizData.isPuppyFunnel %}
                <div class="quiz__popup-img-box">
                    <picture>
                        <source srcset="https://res.cloudinary.com/dr0cx27xo/image/upload/v1719408096/dog-training/img/reactivity/webp/puppy-with-blue-ball.webp">
                        <img id="resultPopup" class="quiz__popup-img" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1719408087/dog-training/img/reactivity/puppy-with-blue-ball.jpg" alt="puppy with blue ball">
                    </picture>
                </div>
                <div class="quiz__popup-text">
                    <span class="ageInsertSelector">Filhotes</span> geralmente guardam seus brinquedos, camas ou guloseimas.
                    Você tem este problema?
                </div>
            {% else %}
                <div class="quiz__popup-img-box">
                    <picture>
                        <source srcset="https://res.cloudinary.com/dr0cx27xo/image/upload/v1719408098/dog-training/img/reactivity/webp/dog-with-teddy-bear-in-mouth.webp">
                        <img id="resultPopup" class="quiz__popup-img" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1719408088/dog-training/img/reactivity/dog-with-teddy-bear-in-mouth.jpg" alt="dog with teddy bear in mouth">
                    </picture>
                </div>
                <div class="quiz__popup-text">
                    <b class="ageInsertSelector capitalize">AGE</b> geralmente guardam seus brinquedos, camas ou guloseimas.
                    Você tem este problema?
                </div>
            {% endif %}
            <ul class="quiz__popup-btn-list">
                <li class="quiz__popup-btn-item quizPopUpProblemBtn" data-event="No">
                    Não
                </li>
                <li class="quiz__popup-btn-item quizPopUpProblemBtn" data-event="Yes">
                    Sim
                </li>
            </ul>
        </div>
    </div>
</div>
<div class="quiz__popup-wrapp" id="quizPopUpObedience">
    <div class="quiz__popup-bg"></div>

    <div class="quiz__popup" id="magicObediencePopup">
        <div class="quiz__popup-box">
            <h4 class="quiz__popup-title">
                Finalizando seu plano
            </h4>
            <div class="quiz__popup-img-box">
                <img id="resultPopup" class="quiz__popup-img" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1719408097/dog-training/img/reactivity/webp/dog-sit-command.webp" alt="dog sit command">
            </div>
            <div class="quiz__popup-text">
                <span class="nameInsertSelector">seu cachorro</span> já teve treinamento de dessensibilização?
            </div>
            <ul class="quiz__popup-btn-list">
                <li class="quiz__popup-btn-item quizPopUpObedienceBtn" data-event="No">
                    Não
                </li>
                <li class="quiz__popup-btn-item quizPopUpObedienceBtn" data-event="Yes">
                    Sim
                </li>
            </ul>
        </div>
    </div>
</div>
