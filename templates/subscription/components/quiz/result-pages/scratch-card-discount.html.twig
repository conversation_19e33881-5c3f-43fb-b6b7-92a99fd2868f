<div class="container scratch-card-discount" id="scratchCardDiscount">
    {% if isVagusFunnel %}
        <h2 class="scratch-card-title b-highlight-red" data-i18n="quiz.result.discount.title"></h2>
    {% else %}
        <h2 class="scratch-card-title">
            {{ discountTitle|raw }}
        </h2>
    {% endif %}
    <div class="scratch-card-description">
        <p>{{ discountDescription|raw }}</p>
    </div>
    <div class="scratch-card">
        <div class="scratch-card-hint">
            <svg class="scratch-card-hint-svg" width="257" height="140" viewBox="-10 0 257 145" fill="none" xmlns="http://www.w3.org/2000/svg" style="pointer-events: none;">
                <path id="scratch_path"
                      opacity="0.4"
                      stroke-dasharray="500" stroke-dashoffset="500"
                      d="M103.064 20.4999C137.063 12.9999 163.315 22.7434 155.563 30.5013C148.569 37.501 79.5007 37.9731 79.5002 52.1159C79.4998 65.5 130.389 51.2497 150 62.5C169.611 73.7503 103.193 68.9202 95.8962 80.341C88.5997 91.7618 134.015 77.2285 135.367 89.1516C136.718 101.075 103.064 90.1673 103.064 101.5C103.064 112.833 121.5 101.5 121.5 117.5"
                      stroke="white" stroke-opacity="0.88" stroke-width="7" stroke-linecap="round">
                    <animate
                            id="scratch_path_animation"
                            attributeName="stroke-dashoffset"
                            values="500;0"
                            begin="indefinite"
                            dur="3s"
                            calcMode="linear"
                            repeatCount="1"
                            fill="freeze"
                    />
                </path>
                <g transform="translate(-8, 0)">
                    <path id="scratch_hand"
                          d="M8.05908 0.333252C11.7534 0.333252 14.9399 3.06274 15.4351 6.80786C15.4605 6.9856 15.4731 7.21411 15.4731 7.34106C15.4478 8.03931 14.9272 8.54712 14.1655 8.54712C13.48 8.54712 13.0864 8.11548 12.9722 7.44263C12.9595 7.35376 12.9468 7.29028 12.9468 7.22681C12.6421 4.73852 10.5347 2.87231 8.04639 2.87231C5.2915 2.87231 3.05713 5.09399 3.05713 7.83618C3.05713 8.96606 3.46338 10.0579 4.06006 10.8831C4.74561 11.8352 4.03467 12.9143 3.04443 12.9143C2.65088 12.9143 2.23193 12.7239 1.91455 12.2795C1.05127 11.0862 0.518066 9.38501 0.518066 7.83618C0.518066 3.68481 3.89502 0.333252 8.05908 0.333252ZM21.1099 30.1291C16.7681 31.7034 12.6167 30.7131 8.96045 26.2317L4.4917 20.7473C4.31397 20.5442 4.16162 20.2776 4.04736 19.9729C3.71729 19.0461 4.16162 18.1194 5.16455 17.7639C5.93896 17.4719 6.58643 17.7512 7.13232 18.3479L10.3315 21.801C10.522 22.0041 10.687 22.0422 10.8394 21.9915C11.0044 21.928 11.0933 21.7375 11.0044 21.4709L6.16748 8.19165C5.79932 7.16333 6.20557 6.21118 7.17041 5.86841C8.07178 5.53833 9.01123 5.99536 9.39209 7.02368L12.7563 16.2786C12.9087 16.6975 13.1753 16.8372 13.4292 16.7356C13.6704 16.6594 13.7847 16.3674 13.6323 15.9612L12.5913 13.1047C12.4009 12.5842 12.6294 12.1653 13.2134 11.9622C14.4702 11.5051 15.6382 12.0129 16.1841 13.5237L16.6665 14.844C16.8315 15.2756 17.0854 15.4026 17.3647 15.3137C17.5933 15.2249 17.7075 14.9456 17.5552 14.5266L17.0347 13.092C16.8315 12.5588 17.0347 12.1526 17.644 11.9368C18.8882 11.4797 20.1323 12.1018 20.6401 13.4856L20.894 14.1711C21.0464 14.5901 21.313 14.7297 21.5669 14.6409C21.8081 14.552 21.9224 14.2727 21.77 13.8538L21.3892 12.8127C21.2622 12.4446 21.4019 12.1399 21.77 12.0129C23.1411 11.5051 24.7915 12.635 25.604 14.8821L26.5181 17.3958C28.7017 23.3625 26.7339 28.0725 21.1099 30.1291Z"
                          fill="white">
                        <animateMotion dur="2.35s" calcMode="linear" begin="indefinite" repeatCount="1" fill="freeze" id="scratch_hand_animation">
                            <mpath href="#scratch_path"/>
                        </animateMotion>
                    </path>
                </g>
            </svg>
            <p class="scratch-card-hint-text">Scratch your discount</p>
        </div>
        <div class="scratch-card-wrapper">
            <svg class="scratch-card-background" width="257" height="170" viewBox="0 0 257 170" fill="none" xmlns="http://www.w3.org/2000/svg">
                <mask id="cardMask" fill="white">
                    <path d="M246.201 85.4199C246.472 92.1347 250.879 97.5022 256.336 97.7051V145.531C256.336 158.786 245.591 169.531 232.336 169.531H24.5C11.2453 169.531 0.50019 158.786 0.5 145.531V97.7051C5.95728 97.5025 10.3653 92.1348 10.6357 85.4199L10.6484 84.7656C10.6482 77.7484 6.13339 72.0353 0.5 71.8262V24C0.50019 10.7453 11.2453 0 24.5 0H232.336C245.591 0 256.336 10.7453 256.336 24V71.8262C250.703 72.0356 246.188 77.7486 246.188 84.7656L246.201 85.4199Z"/>
                </mask>
                <path d="M246.201 85.4199C246.472 92.1347 250.879 97.5022 256.336 97.7051V145.531C256.336 158.786 245.591 169.531 232.336 169.531H24.5C11.2453 169.531 0.50019 158.786 0.5 145.531V97.7051C5.95728 97.5025 10.3653 92.1348 10.6357 85.4199L10.6484 84.7656C10.6482 77.7484 6.13339 72.0353 0.5 71.8262V24C0.50019 10.7453 11.2453 0 24.5 0H232.336C245.591 0 256.336 10.7453 256.336 24V71.8262C250.703 72.0356 246.188 77.7486 246.188 84.7656L246.201 85.4199Z"
                      fill="#E3FBEA"/>
                <path d="M246.201 85.4199L245.201 85.4408L245.202 85.4505L245.202 85.4602L246.201 85.4199ZM256.336 97.7051H257.336V96.7416L256.373 96.7058L256.336 97.7051ZM256.336 145.531L257.336 145.531V145.531H256.336ZM0.5 145.531H-0.5V145.531L0.5 145.531ZM0.5 97.7051L0.462898 96.7058L-0.5 96.7415V97.7051H0.5ZM10.6357 85.4199L11.6349 85.4602L11.6354 85.4497L11.6356 85.4393L10.6357 85.4199ZM10.6484 84.7656L11.6482 84.785L11.6484 84.7753V84.7656L10.6484 84.7656ZM0.5 71.8262H-0.5V72.7897L0.462898 72.8255L0.5 71.8262ZM0.5 24L-0.5 24V24H0.5ZM256.336 24H257.336V24L256.336 24ZM256.336 71.8262L256.373 72.8255L257.336 72.7897V71.8262H256.336ZM246.188 84.7656L245.188 84.7656V84.7761L245.188 84.7865L246.188 84.7656ZM246.201 85.4199L245.202 85.4602C245.486 92.51 250.152 98.4759 256.299 98.7044L256.336 97.7051L256.373 96.7058C251.606 96.5285 247.457 91.7593 247.2 85.3797L246.201 85.4199ZM256.336 97.7051H255.336V145.531H256.336H257.336V97.7051H256.336ZM256.336 145.531L255.336 145.531C255.336 158.234 245.038 168.531 232.336 168.531V169.531V170.531C246.143 170.531 257.336 159.338 257.336 145.531L256.336 145.531ZM232.336 169.531V168.531H24.5V169.531V170.531H232.336V169.531ZM24.5 169.531V168.531C11.7976 168.531 1.50018 158.234 1.5 145.531L0.5 145.531L-0.5 145.531C-0.499802 159.338 10.693 170.531 24.5 170.531V169.531ZM0.5 145.531H1.5V97.7051H0.5H-0.5V145.531H0.5ZM0.5 97.7051L0.537102 98.7044C6.68429 98.4762 11.351 92.5103 11.6349 85.4602L10.6357 85.4199L9.63655 85.3797C9.37964 91.7594 5.23028 96.5288 0.462898 96.7058L0.5 97.7051ZM10.6357 85.4199L11.6356 85.4393L11.6482 84.785L10.6484 84.7656L9.64863 84.7462L9.63593 85.4005L10.6357 85.4199ZM10.6484 84.7656L11.6484 84.7656C11.6482 77.4062 6.88412 71.0625 0.537102 70.8269L0.5 71.8262L0.462898 72.8255C5.38265 73.0081 9.64825 78.0905 9.64844 84.7657L10.6484 84.7656ZM0.5 71.8262H1.5V24H0.5H-0.5V71.8262H0.5ZM0.5 24L1.5 24C1.50018 11.2976 11.7976 1 24.5 1V0V-1C10.693 -1 -0.499802 10.1931 -0.5 24L0.5 24ZM24.5 0V1H232.336V0V-1H24.5V0ZM232.336 0V1C245.038 1 255.336 11.2976 255.336 24L256.336 24L257.336 24C257.336 10.1931 246.143 -1 232.336 -1V0ZM256.336 24H255.336V71.8262H256.336H257.336V24H256.336ZM256.336 71.8262L256.299 70.8269C249.952 71.0628 245.188 77.4064 245.188 84.7656L246.188 84.7656L247.188 84.7657C247.188 78.0907 251.453 73.0084 256.373 72.8255L256.336 71.8262ZM246.188 84.7656L245.188 84.7865L245.201 85.4408L246.201 85.4199L247.201 85.399L247.187 84.7447L246.188 84.7656Z"
                      fill="#BFEDCC" mask="url(#cardMask)"/>
                <path d="M11.5 85.5H246" stroke="#BFEDCC" stroke-width="2" stroke-dasharray="8 8"/>
            </svg>
            <div class="scratch-card-content">
                <div class="scratch-card-value">{{ discountPercents }}</div>
                {% if isVagusFunnel %}
                    <div class="scratch-card-message" data-i18n="quiz.result.discount.message"></div>
                {% else %}
                    <div class="scratch-card-message">{{ discountMessage|raw }}</div>
                {% endif %}
            </div>
            <canvas class="scratch-card-foreground" id="scratchCardCanvas" width="257" height="170"></canvas>
        </div>
    </div>
</div>
<div class="scratch-card__overlay" id="scratchCardDiscountOverlay">
    <div class="scratch-card__popup" id="scratchCardDiscountPopup">
        <div class="scratch-card__popup__discount">
            <div class="scratch-card__popup__discount-box">
                <img class="scratch-card__popup__discount-title-icon" src="https://images.paw-champ.com/pc/emoji/face-with-party-horn-and-party-hat.png" alt="partying face"/>
                <h2 class="scratch-card__popup__discount-title">Woo hoo!</h2>
            </div>
            <div class="scratch-card__popup__discount-message-box">
                <p class="scratch-card__popup__discount-message" >You won a discount</p>
                <h2 class="scratch-card__popup__discount-message-percents">{{ discountPercents }} off</h2>
            </div>
        </div>
        <div class="scratch-card__popup__disclaimer">
            *This discount will be applied automatically
        </div>
        <button class="scratch-card__popup__button">
            <span></span>
            Continue
        </button>
    </div>
</div>
