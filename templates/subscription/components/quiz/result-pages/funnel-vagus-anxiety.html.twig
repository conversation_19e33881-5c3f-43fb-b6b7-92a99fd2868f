<div class="quiz__index quiz__result skipQuiz magic" id="block{{ blockId }}" data-id="{{ blockId }}" data-result="reviews">
    <div class="container " id="resultStepOne">
        <div id="circleBar" class="progressbar reviews">
            <div class="progressbar__shadow"></div>
        </div>
        <p class="quiz__reviews-loading-subtitle" id="replaceSubtitle">
            Creating <span class="nameInsertSelector">your dog</span>'s personalized content for vagus reset...
        </p>
        <div class="quiz__hr">
        </div>
        <div class="quiz__reviews-loading-title-red-box">
            <h3 class="quiz__reviews-loading-title-red">
                Over 250,000 dog owners
            </h3>
        </div>
        <p class="quiz__reviews-loading-text">
            have chosen PawChamp
        </p>
        <div class="quiz__reviews-slider custom-result">
            <ul class="swiper-wrapper">
                <li class="quiz__reviews-slider-item swiper-slide">
                    <div class="quiz__reviews-slider-item-text-wrapp">
                        <svg width="90" height="16" viewBox="0 0 90 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M9.46744 6.37307H14.2218L10.3768 9.16159L10.3767 9.16159L8.00391 10.8849L10.7058 10.1835L10.3789 9.16822L11.8403 13.6821L7.99524 10.8849L4.15019 13.6821L5.62239 9.16159L1.77734 6.36441L6.53169 6.37307L8.0039 1.85254L9.46744 6.37307Z"
                                  fill="white"/>
                            <rect x="18.3711" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M27.8385 6.37307H32.5929L28.7478 9.16159L28.7478 9.16159L26.375 10.8849L29.0769 10.1835L28.75 9.16822L30.2114 13.6821L26.3663 10.8849L22.5213 13.6821L23.9935 9.16159L20.1484 6.36441L24.9028 6.37307L26.375 1.85254L27.8385 6.37307Z"
                                  fill="white"/>
                            <rect x="36.7402" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M46.2077 6.37307H50.962L47.117 9.16159L47.117 9.16159L44.7441 10.8849L47.4461 10.1835L47.1191 9.16822L48.5805 13.6821L44.7355 10.8849L40.8904 13.6821L42.3626 9.16159L38.5176 6.36441L43.2719 6.37307L44.7441 1.85254L46.2077 6.37307Z"
                                  fill="white"/>
                            <rect x="55.1113" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M64.5788 6.37307H69.3331L65.4881 9.16159L65.4883 9.16217L63.1162 10.8849L65.8181 10.1835L65.6738 9.7352L66.9516 13.6821L63.1066 10.8849L59.2615 13.6821L60.7337 9.16159L56.8887 6.36441L61.643 6.37307L63.1152 1.85254L64.5788 6.37307Z"
                                  fill="white"/>
                            <rect x="73.4824" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M82.9499 6.37307H87.7042L83.8592 9.16159L83.8592 9.16159L81.4863 10.8849L84.1883 10.1835L83.8613 9.16822L85.3227 13.6821L81.4777 10.8849L77.6326 13.6821L79.1048 9.16159L75.2598 6.36441L80.0141 6.37307L81.4863 1.85254L82.9499 6.37307Z"
                                  fill="white"/>
                        </svg>
                        <div class="quiz__reviews-slider-item-name-wrapp">
                            <h5>
                                New approach to calmness
                            </h5>
                            <p class="quiz__reviews-slider-item-name">
                                Ernesto
                            </p>
                        </div>
                        <div class="quiz__reviews-slider-item-text">
                            This was a new way for me to learn how to help my dog stay calm. We're not finished yet, but I’ve already seen big improvements with our 8-year-old German shepherd who had some training but struggled
                            with reactivity.
                        </div>
                    </div>
                </li>
                <li class="quiz__reviews-slider-item swiper-slide">
                    <div class="quiz__reviews-slider-item-text-wrapp">
                        <svg width="90" height="16" viewBox="0 0 90 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M9.46744 6.37307H14.2218L10.3768 9.16159L10.3767 9.16159L8.00391 10.8849L10.7058 10.1835L10.3789 9.16822L11.8403 13.6821L7.99524 10.8849L4.15019 13.6821L5.62239 9.16159L1.77734 6.36441L6.53169 6.37307L8.0039 1.85254L9.46744 6.37307Z"
                                  fill="white"/>
                            <rect x="18.3711" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M27.8385 6.37307H32.5929L28.7478 9.16159L28.7478 9.16159L26.375 10.8849L29.0769 10.1835L28.75 9.16822L30.2114 13.6821L26.3663 10.8849L22.5213 13.6821L23.9935 9.16159L20.1484 6.36441L24.9028 6.37307L26.375 1.85254L27.8385 6.37307Z"
                                  fill="white"/>
                            <rect x="36.7402" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M46.2077 6.37307H50.962L47.117 9.16159L47.117 9.16159L44.7441 10.8849L47.4461 10.1835L47.1191 9.16822L48.5805 13.6821L44.7355 10.8849L40.8904 13.6821L42.3626 9.16159L38.5176 6.36441L43.2719 6.37307L44.7441 1.85254L46.2077 6.37307Z"
                                  fill="white"/>
                            <rect x="55.1113" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M64.5788 6.37307H69.3331L65.4881 9.16159L65.4883 9.16217L63.1162 10.8849L65.8181 10.1835L65.6738 9.7352L66.9516 13.6821L63.1066 10.8849L59.2615 13.6821L60.7337 9.16159L56.8887 6.36441L61.643 6.37307L63.1152 1.85254L64.5788 6.37307Z"
                                  fill="white"/>
                            <rect x="73.4824" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M82.9499 6.37307H87.7042L83.8592 9.16159L83.8592 9.16159L81.4863 10.8849L84.1883 10.1835L83.8613 9.16822L85.3227 13.6821L81.4777 10.8849L77.6326 13.6821L79.1048 9.16159L75.2598 6.36441L80.0141 6.37307L81.4863 1.85254L82.9499 6.37307Z"
                                  fill="white"/>
                        </svg>
                        <div class="quiz__reviews-slider-item-name-wrapp">
                            <h5>
                                Structure is brilliant
                            </h5>
                            <p class="quiz__reviews-slider-item-name">
                                Claire_Ugalde
                            </p>
                        </div>
                        <div class="quiz__reviews-slider-item-text">
                            The way the everything is broken down is excellent. I’ve had one on one training at my house and have found it less effective than this course and would highly recommend it.
                        </div>
                    </div>
                </li>
                <li class="quiz__reviews-slider-item swiper-slide">
                    <div class="quiz__reviews-slider-item-text-wrapp">
                        <svg width="90" height="16" viewBox="0 0 90 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M9.46744 6.37307H14.2218L10.3768 9.16159L10.3767 9.16159L8.00391 10.8849L10.7058 10.1835L10.3789 9.16822L11.8403 13.6821L7.99524 10.8849L4.15019 13.6821L5.62239 9.16159L1.77734 6.36441L6.53169 6.37307L8.0039 1.85254L9.46744 6.37307Z"
                                  fill="white"/>
                            <rect x="18.3711" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M27.8385 6.37307H32.5929L28.7478 9.16159L28.7478 9.16159L26.375 10.8849L29.0769 10.1835L28.75 9.16822L30.2114 13.6821L26.3663 10.8849L22.5213 13.6821L23.9935 9.16159L20.1484 6.36441L24.9028 6.37307L26.375 1.85254L27.8385 6.37307Z"
                                  fill="white"/>
                            <rect x="36.7402" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M46.2077 6.37307H50.962L47.117 9.16159L47.117 9.16159L44.7441 10.8849L47.4461 10.1835L47.1191 9.16822L48.5805 13.6821L44.7355 10.8849L40.8904 13.6821L42.3626 9.16159L38.5176 6.36441L43.2719 6.37307L44.7441 1.85254L46.2077 6.37307Z"
                                  fill="white"/>
                            <rect x="55.1113" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M64.5788 6.37307H69.3331L65.4881 9.16159L65.4883 9.16217L63.1162 10.8849L65.8181 10.1835L65.6738 9.7352L66.9516 13.6821L63.1066 10.8849L59.2615 13.6821L60.7337 9.16159L56.8887 6.36441L61.643 6.37307L63.1152 1.85254L64.5788 6.37307Z"
                                  fill="white"/>
                            <rect x="73.4824" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M82.9499 6.37307H87.7042L83.8592 9.16159L83.8592 9.16159L81.4863 10.8849L84.1883 10.1835L83.8613 9.16822L85.3227 13.6821L81.4777 10.8849L77.6326 13.6821L79.1048 9.16159L75.2598 6.36441L80.0141 6.37307L81.4863 1.85254L82.9499 6.37307Z"
                                  fill="white"/>
                        </svg>
                        <div class="quiz__reviews-slider-item-name-wrapp">
                            <h5>
                                Excellent course

                            </h5>
                            <p class="quiz__reviews-slider-item-name">
                                Diego Fernandez Jr
                            </p>
                        </div>
                        <p class="quiz__reviews-slider-item-text">
                            Excellent course, very detailed and easy to understand. What I like about this course is they emphasize that a dog requires patience and understanding the process. I’m still on week one, but their
                            approach seems easy to follow.
                            Can’t wait to see the end results…
                        </p>
                    </div>
                </li>
            </ul>
        </div>
    </div>
    <div class="container email" id="resultStepTwo">
        <h2 class="quiz__reviews-loading-title">
            Enter your email to get <span class="nameInsertSelector">your dog</span>’s personalized Dog Vagus Nerve Reset Challenge
        </h2>
        <p class="quiz__result-label" id="emailText"></p>
        <div class="quiz__result-box">
            <ul id="autoComplete" class="email-autocomplete email-autocomplete-scroll"></ul>
            <p class="quiz__result-text black">
                Type your email:
            </p>
            <input class="quiz__result-input" name="text" type="text" placeholder="Enter your email to get your plan" id="email" autocomplete="off">
        </div>
        <div class="quiz__result-text-box">
            <svg class="quiz__result-text-icon" width="21" height="26" viewBox="0 0 21 26" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path opacity="0.45"
                      d="M20.1944 9.75H18.5278V7.58332C18.5278 3.40184 15.0387 0 10.75 0C6.46125 0 2.97224 3.40184 2.97224 7.58332V9.75H1.30557C0.99849 9.75 0.75 9.99228 0.75 10.2917V23.8334C0.75 25.0283 1.74661 26 2.97224 26H18.5278C19.7534 26 20.75 25.0283 20.75 23.8333V10.2917C20.75 9.99228 20.5015 9.75 20.1944 9.75ZM12.4134 21.0652C12.4308 21.2181 12.3804 21.3715 12.2751 21.4863C12.1698 21.6011 12.019 21.6667 11.8611 21.6667H9.63891C9.48104 21.6667 9.33021 21.6011 9.22495 21.4863C9.11969 21.3715 9.06922 21.2181 9.08661 21.0652L9.43708 17.9925C8.86797 17.5888 8.52781 16.9504 8.52781 16.25C8.52781 15.0551 9.52443 14.0833 10.7501 14.0833C11.9757 14.0833 12.9723 15.055 12.9723 16.25C12.9723 16.9504 12.6321 17.5888 12.063 17.9925L12.4134 21.0652ZM15.1944 9.75H6.30557V7.58332C6.30557 5.19396 8.29938 3.25 10.75 3.25C13.2006 3.25 15.1944 5.19396 15.1944 7.58332V9.75Z"
                      fill="#343434"/>
            </svg>
            <p class="quiz__result-text">
                We protect your privacy and are committed to protecting your personal data. We never send spam emails, only relevant information.
            </p>
        </div>
        <div class="quiz__btn-box">
            <button class="quiz__btn js-btn email-submit" id="email-submit" disabled>
                <span></span>
                Continue
            </button>
        </div>
    </div>
    <div class="container " id="resultStepEmailSubscription">
        <h3 class="quiz__email-subscription-title">
            Do you want to receive emails with <span>special offers</span>, dog training tips, advice and <span>free gifts</span>?
        </h3>
        <div class="quiz__email-subscription-btn-box">
            <button class="quiz__btn triggerStep" data-event-value="Apply newsletter">
                <span></span>
                Yes, I'm in!
            </button>
            <label class="quiz__email-subscription-label triggerStep" data-event-value="Reject newsletter">
                <input type="checkbox" class="quiz__email-subscription-input" id="disallowMarketingEmails">
                I'm not interested
            </label>
        </div>
    </div>
    <div class="container graph " id="resultStepThree">
        <h2 class="quiz__reviews-loading-title">
            <span class="nameInsertSelector">Your dog</span>’s vagus nerve index
        </h2>
        <div class="quiz__reviews-loading-svg-inner">
            <svg class="quiz__reviews-loading-svg" width="361" height="270" viewBox="0 0 361 200" fill="none" xmlns="http://www.w3.org/2000/svg">
                <line x1="9" y1="17.5" x2="350.915" y2="17.5" stroke="#E8E8E8"/>
                <line x1="9" y1="43.5" x2="350.915" y2="43.5" stroke="#E8E8E8"/>
                <line x1="9" y1="69.5" x2="350.915" y2="69.5" stroke="#E8E8E8"/>
                <line x1="9" y1="95.5" x2="350.915" y2="95.5" stroke="#E8E8E8"/>
                <line x1="9" y1="121.5" x2="350.915" y2="121.5" stroke="#E8E8E8"/>
                <line x1="9" y1="147.5" x2="350.915" y2="147.5" stroke="#E8E8E8"/>
                <line x1="9" y1="173.5" x2="350.915" y2="173.5" stroke="#E8E8E8"/>
                <line x1="9" y1="199.5" x2="352" y2="199.5" stroke="#E8E8E8"/>
                <path class="iteration-show-el iteration-show-el-p10" d="M41.3301 200L41.3301 143.5" stroke="#E8E8E8"/>
                <path class="iteration-show-el iteration-show-el-p30" d="M134 200L134 124.5" stroke="#E8E8E8"/>
                <path class="iteration-show-el iteration-show-el-p60" d="M226.074 200L226.074 103" stroke="#E8E8E8"/>
                <path class="iteration-show-el iteration-show-el-p90" d="M318.446 200L318.446 50" stroke="#E8E8E8"/>
                <path id="resultGraphBg" class="result-graph-bg" opacity="0.2" fill-rule="evenodd" clip-rule="evenodd"
                      d="M115.448 127.934C102.481 129.358 91.1159 130.607 81.8038 132.445C55.6267 137.614 40.2613 146.18 30.6505 154.396C23.2415 160.73 19.2459 166.799 16.1667 171.476L16.1661 171.477C15.5773 172.372 15.022 173.215 14.4827 174H9.06783V200H352V22.8222C268.43 111.13 175.025 121.39 115.448 127.934ZM9.06783 172.63V172.496L9 172.588L9.06783 172.63Z"
                      fill="url(#paint0_linear_491_2815)"/>
                <g class="result-graph-bg" opacity="0.3" filter="url(#filter0_f_491_2815)">
                    <path d="M10.1553 182C16.8461 173 26.9728 148.5 80.1378 138C133.303 127.5 249.398 136 350.846 27" stroke="url(#paint1_linear_491_2815)" stroke-width="5"/>
                </g>
                <path class="quiz__based-graph-line" stroke-linecap="round" d="M11 176C15.3447 167.5 26.9728 141.5 80.1378 131C133.303 120.5 249.398 129 350.846 20" stroke="url(#paint2_linear_491_2815)" stroke-width="5"/>
                <g class="iteration-show-el iteration-show-el-p10" filter="url(#filter4_d_491_2815)">
                    <circle cx="41.5503" cy="143.55" r="7.55031" fill="white"/>
                    <circle cx="41.5503" cy="143.55" r="6.55031" stroke="url(#paint6_linear_491_2815)" stroke-opacity="0.12" stroke-width="2"/>
                </g>
                <g class="iteration-show-el iteration-show-el-p10" filter="url(#filter5_d_491_2815)">
                    <rect x="16" y="100" width="51" height="24" rx="4" fill="#343434"/>
                    <path d="M27.3465 116V109.076H24.5505V107.54H32.0025V109.076H29.2065V116H27.3465ZM34.6393 116.132C34.0073 116.132 33.4553 116.008 32.9833 115.76C32.5193 115.512 32.1593 115.156 31.9033 114.692C31.6473 114.228 31.5193 113.68 31.5193 113.048C31.5193 112.416 31.6473 111.872 31.9033 111.416C32.1593 110.96 32.5193 110.608 32.9833 110.36C33.4553 110.112 34.0073 109.988 34.6393 109.988C35.2713 109.988 35.8193 110.112 36.2833 110.36C36.7553 110.608 37.1193 110.964 37.3753 111.428C37.6393 111.884 37.7713 112.424 37.7713 113.048C37.7713 113.68 37.6393 114.228 37.3753 114.692C37.1193 115.156 36.7553 115.512 36.2833 115.76C35.8193 116.008 35.2713 116.132 34.6393 116.132ZM34.6393 114.776C35.0313 114.776 35.3473 114.636 35.5873 114.356C35.8273 114.068 35.9473 113.632 35.9473 113.048C35.9473 112.464 35.8273 112.036 35.5873 111.764C35.3473 111.484 35.0313 111.344 34.6393 111.344C34.2553 111.344 33.9433 111.484 33.7033 111.764C33.4633 112.036 33.3433 112.464 33.3433 113.048C33.3433 113.632 33.4633 114.068 33.7033 114.356C33.9433 114.636 34.2553 114.776 34.6393 114.776ZM41.1657 116.132C40.6537 116.132 40.2017 116.008 39.8097 115.76C39.4257 115.512 39.1257 115.156 38.9097 114.692C38.6937 114.228 38.5857 113.68 38.5857 113.048C38.5857 112.416 38.6937 111.872 38.9097 111.416C39.1257 110.96 39.4257 110.608 39.8097 110.36C40.2017 110.112 40.6537 109.988 41.1657 109.988C41.6297 109.988 42.0377 110.1 42.3897 110.324C42.7497 110.548 42.9937 110.84 43.1217 111.2H42.9897V107.54H44.8017V116H43.0257V114.836H43.1337C43.0217 115.228 42.7817 115.544 42.4137 115.784C42.0537 116.016 41.6377 116.132 41.1657 116.132ZM41.7057 114.776C42.0977 114.776 42.4137 114.636 42.6537 114.356C42.8937 114.068 43.0137 113.632 43.0137 113.048C43.0137 112.464 42.8937 112.036 42.6537 111.764C42.4137 111.484 42.0977 111.344 41.7057 111.344C41.3217 111.344 41.0097 111.484 40.7697 111.764C40.5297 112.036 40.4097 112.464 40.4097 113.048C40.4097 113.632 40.5297 114.068 40.7697 114.356C41.0097 114.636 41.3217 114.776 41.7057 114.776ZM48.19 116.132C47.758 116.132 47.37 116.048 47.026 115.88C46.682 115.712 46.414 115.484 46.222 115.196C46.03 114.908 45.934 114.584 45.934 114.224C45.934 113.792 46.046 113.452 46.27 113.204C46.494 112.948 46.858 112.768 47.362 112.664C47.866 112.552 48.534 112.496 49.366 112.496H50.002V113.432H49.378C49.066 113.432 48.802 113.444 48.586 113.468C48.378 113.492 48.206 113.536 48.07 113.6C47.934 113.656 47.834 113.728 47.77 113.816C47.714 113.904 47.686 114.016 47.686 114.152C47.686 114.376 47.762 114.56 47.914 114.704C48.074 114.848 48.302 114.92 48.598 114.92C48.83 114.92 49.034 114.868 49.21 114.764C49.394 114.652 49.538 114.504 49.642 114.32C49.746 114.128 49.798 113.912 49.798 113.672V112.292C49.798 111.94 49.718 111.692 49.558 111.548C49.398 111.396 49.126 111.32 48.742 111.32C48.422 111.32 48.082 111.372 47.722 111.476C47.37 111.572 47.018 111.72 46.666 111.92L46.174 110.708C46.382 110.564 46.634 110.44 46.93 110.336C47.234 110.224 47.55 110.14 47.878 110.084C48.206 110.02 48.518 109.988 48.814 109.988C49.43 109.988 49.934 110.08 50.326 110.264C50.726 110.44 51.026 110.716 51.226 111.092C51.426 111.46 51.526 111.936 51.526 112.52V116H49.834V114.812H49.918C49.87 115.084 49.766 115.32 49.606 115.52C49.454 115.712 49.258 115.864 49.018 115.976C48.778 116.08 48.502 116.132 48.19 116.132ZM53.275 118.16L54.571 115.316V116.06L51.979 110.108H53.887L55.555 114.332H55.171L56.887 110.108H58.687L55.123 118.16H53.275Z"
                          fill="white"/>
                    <path d="M40.457 130.31L36.25 124H46.75L42.0954 130.347C41.6847 130.907 40.8422 130.888 40.457 130.31Z" fill="#343434"/>
                </g>
                <g class="iteration-show-el iteration-show-el-p30" filter="url(#filter1_d_491_2815)">
                    <circle cx="134.55" cy="122.55" r="7.55031" fill="white"/>
                    <circle cx="134.55" cy="122.55" r="6.55031" stroke="url(#paint3_linear_491_2815)" stroke-opacity="0.12" stroke-width="2"/>
                </g>
                <g class="iteration-show-el iteration-show-el-p60" filter="url(#filter2_d_491_2815)">
                    <circle cx="225.55" cy="102.55" r="7.55031" fill="white"/>
                    <circle cx="225.55" cy="102.55" r="6.55031" stroke="url(#paint4_linear_491_2815)" stroke-opacity="0.12" stroke-width="2"/>
                </g>
                <g id="resultGraphDot" class="result-graph-dot" filter="url(#filter3_d_491_2815)">
                    <circle cx="346.55" cy="21.5503" r="7.55031" fill="white"/>
                    <circle cx="346.55" cy="21.5503" r="6.55031" stroke="url(#paint5_linear_491_2815)" stroke-opacity="0.12" stroke-width="2"/>
                </g>
                <g class="result-graph-goal" filter="url(#filter6_d_491_2815)">
                    <rect x="244.25" y="4" width="83" height="36" rx="4" fill="#1998CD"/>
                    <path d="M270.905 20L274.793 11.54H276.293L280.181 20H278.309L277.301 17.648L278.045 18.164H273.041L273.785 17.648L272.789 20H270.905ZM275.525 13.52L273.989 17.18L273.665 16.712H277.421L277.097 17.18L275.549 13.52H275.525ZM281.072 20V15.464H279.956V14.108H281.6L281.072 14.588V14.216C281.072 13.344 281.304 12.692 281.768 12.26C282.232 11.82 282.956 11.572 283.94 11.516L284.6 11.468L284.708 12.788L284.144 12.824C283.832 12.84 283.584 12.892 283.4 12.98C283.216 13.06 283.084 13.176 283.004 13.328C282.924 13.472 282.884 13.664 282.884 13.904V14.312L282.656 14.108H284.42V15.464H282.884V20H281.072ZM287.738 20.132C286.898 20.132 286.274 19.928 285.866 19.52C285.458 19.112 285.254 18.512 285.254 17.72V15.464H284.138V14.108H285.254V12.38H287.066V14.108H288.806V15.464H287.066V17.648C287.066 17.984 287.142 18.236 287.294 18.404C287.454 18.572 287.706 18.656 288.05 18.656C288.154 18.656 288.262 18.644 288.374 18.62C288.494 18.596 288.622 18.564 288.758 18.524L289.022 19.844C288.854 19.932 288.654 20 288.422 20.048C288.19 20.104 287.962 20.132 287.738 20.132ZM292.699 20.132C292.003 20.132 291.403 20.008 290.899 19.76C290.403 19.504 290.019 19.148 289.747 18.692C289.483 18.228 289.351 17.684 289.351 17.06C289.351 16.452 289.479 15.92 289.735 15.464C289.991 15 290.347 14.64 290.803 14.384C291.267 14.12 291.791 13.988 292.375 13.988C292.951 13.988 293.447 14.112 293.863 14.36C294.279 14.6 294.599 14.944 294.823 15.392C295.055 15.84 295.171 16.372 295.171 16.988V17.444H290.839V16.508H293.863L293.671 16.676C293.671 16.188 293.563 15.82 293.347 15.572C293.139 15.316 292.839 15.188 292.447 15.188C292.151 15.188 291.899 15.256 291.691 15.392C291.483 15.528 291.323 15.724 291.211 15.98C291.099 16.236 291.043 16.544 291.043 16.904V17C291.043 17.408 291.103 17.744 291.223 18.008C291.351 18.264 291.539 18.456 291.787 18.584C292.043 18.712 292.359 18.776 292.735 18.776C293.055 18.776 293.379 18.728 293.707 18.632C294.035 18.536 294.331 18.388 294.595 18.188L295.075 19.4C294.779 19.624 294.415 19.804 293.983 19.94C293.559 20.068 293.131 20.132 292.699 20.132ZM296.225 20V14.108H298.001V15.548H297.881C297.977 15.068 298.185 14.7 298.505 14.444C298.833 14.18 299.265 14.028 299.801 13.988L300.329 13.952L300.437 15.476L299.417 15.584C298.961 15.624 298.621 15.756 298.397 15.98C298.181 16.204 298.073 16.528 298.073 16.952V20H296.225ZM261.89 32.132C260.978 32.132 260.198 31.952 259.55 31.592C258.902 31.232 258.402 30.728 258.05 30.08C257.706 29.424 257.534 28.652 257.534 27.764C257.534 26.876 257.706 26.108 258.05 25.46C258.402 24.812 258.902 24.308 259.55 23.948C260.198 23.588 260.978 23.408 261.89 23.408C262.45 23.408 262.986 23.496 263.498 23.672C264.01 23.848 264.43 24.088 264.758 24.392L264.182 25.868C263.822 25.588 263.458 25.384 263.09 25.256C262.73 25.12 262.35 25.052 261.95 25.052C261.15 25.052 260.542 25.288 260.126 25.76C259.71 26.224 259.502 26.892 259.502 27.764C259.502 28.636 259.71 29.308 260.126 29.78C260.542 30.252 261.15 30.488 261.95 30.488C262.35 30.488 262.73 30.424 263.09 30.296C263.458 30.16 263.822 29.952 264.182 29.672L264.758 31.148C264.43 31.452 264.01 31.692 263.498 31.868C262.986 32.044 262.45 32.132 261.89 32.132ZM265.821 32V23.54H267.633V27.212H267.465C267.649 26.82 267.921 26.52 268.281 26.312C268.649 26.096 269.065 25.988 269.529 25.988C270.001 25.988 270.389 26.08 270.693 26.264C270.997 26.44 271.225 26.712 271.377 27.08C271.529 27.44 271.605 27.9 271.605 28.46V32H269.793V28.544C269.793 28.272 269.757 28.052 269.685 27.884C269.621 27.708 269.521 27.584 269.385 27.512C269.257 27.432 269.093 27.392 268.893 27.392C268.637 27.392 268.413 27.448 268.221 27.56C268.037 27.664 267.893 27.816 267.789 28.016C267.685 28.216 267.633 28.448 267.633 28.712V32H265.821ZM274.95 32.132C274.518 32.132 274.13 32.048 273.786 31.88C273.442 31.712 273.174 31.484 272.982 31.196C272.79 30.908 272.694 30.584 272.694 30.224C272.694 29.792 272.806 29.452 273.03 29.204C273.254 28.948 273.618 28.768 274.122 28.664C274.626 28.552 275.294 28.496 276.126 28.496H276.762V29.432H276.138C275.826 29.432 275.562 29.444 275.346 29.468C275.138 29.492 274.966 29.536 274.83 29.6C274.694 29.656 274.594 29.728 274.53 29.816C274.474 29.904 274.446 30.016 274.446 30.152C274.446 30.376 274.522 30.56 274.674 30.704C274.834 30.848 275.062 30.92 275.358 30.92C275.59 30.92 275.794 30.868 275.97 30.764C276.154 30.652 276.298 30.504 276.402 30.32C276.506 30.128 276.558 29.912 276.558 29.672V28.292C276.558 27.94 276.478 27.692 276.318 27.548C276.158 27.396 275.886 27.32 275.502 27.32C275.182 27.32 274.842 27.372 274.482 27.476C274.13 27.572 273.778 27.72 273.426 27.92L272.934 26.708C273.142 26.564 273.394 26.44 273.69 26.336C273.994 26.224 274.31 26.14 274.638 26.084C274.966 26.02 275.278 25.988 275.574 25.988C276.19 25.988 276.694 26.08 277.086 26.264C277.486 26.44 277.786 26.716 277.986 27.092C278.186 27.46 278.286 27.936 278.286 28.52V32H276.594V30.812H276.678C276.63 31.084 276.526 31.32 276.366 31.52C276.214 31.712 276.018 31.864 275.778 31.976C275.538 32.08 275.262 32.132 274.95 32.132ZM281.833 32.132C281.073 32.132 280.517 31.936 280.165 31.544C279.813 31.144 279.637 30.552 279.637 29.768V23.54H281.449V29.696C281.449 29.896 281.481 30.072 281.545 30.224C281.609 30.368 281.701 30.476 281.821 30.548C281.949 30.62 282.109 30.656 282.301 30.656C282.381 30.656 282.465 30.652 282.553 30.644C282.641 30.636 282.725 30.62 282.805 30.596L282.781 32C282.629 32.04 282.473 32.072 282.313 32.096C282.161 32.12 282.001 32.132 281.833 32.132ZM285.83 32.132C285.07 32.132 284.514 31.936 284.162 31.544C283.81 31.144 283.634 30.552 283.634 29.768V23.54H285.446V29.696C285.446 29.896 285.478 30.072 285.542 30.224C285.606 30.368 285.698 30.476 285.818 30.548C285.946 30.62 286.106 30.656 286.298 30.656C286.378 30.656 286.462 30.652 286.55 30.644C286.638 30.636 286.722 30.62 286.802 30.596L286.778 32C286.626 32.04 286.47 32.072 286.31 32.096C286.158 32.12 285.998 32.132 285.83 32.132ZM290.631 32.132C289.935 32.132 289.335 32.008 288.831 31.76C288.335 31.504 287.951 31.148 287.679 30.692C287.415 30.228 287.283 29.684 287.283 29.06C287.283 28.452 287.411 27.92 287.667 27.464C287.923 27 288.279 26.64 288.735 26.384C289.199 26.12 289.723 25.988 290.307 25.988C290.883 25.988 291.379 26.112 291.795 26.36C292.211 26.6 292.531 26.944 292.755 27.392C292.987 27.84 293.103 28.372 293.103 28.988V29.444H288.771V28.508H291.795L291.603 28.676C291.603 28.188 291.495 27.82 291.279 27.572C291.071 27.316 290.771 27.188 290.379 27.188C290.083 27.188 289.831 27.256 289.623 27.392C289.415 27.528 289.255 27.724 289.143 27.98C289.031 28.236 288.975 28.544 288.975 28.904V29C288.975 29.408 289.035 29.744 289.155 30.008C289.283 30.264 289.471 30.456 289.719 30.584C289.975 30.712 290.291 30.776 290.667 30.776C290.987 30.776 291.311 30.728 291.639 30.632C291.967 30.536 292.263 30.388 292.527 30.188L293.007 31.4C292.711 31.624 292.347 31.804 291.915 31.94C291.491 32.068 291.063 32.132 290.631 32.132ZM294.157 32V26.108H295.933V27.212H295.801C295.985 26.82 296.257 26.52 296.617 26.312C296.985 26.096 297.401 25.988 297.865 25.988C298.337 25.988 298.725 26.08 299.029 26.264C299.333 26.44 299.561 26.712 299.713 27.08C299.865 27.44 299.941 27.9 299.941 28.46V32H298.129V28.544C298.129 28.272 298.093 28.052 298.021 27.884C297.957 27.708 297.857 27.584 297.721 27.512C297.593 27.432 297.429 27.392 297.229 27.392C296.973 27.392 296.749 27.448 296.557 27.56C296.373 27.664 296.229 27.816 296.125 28.016C296.021 28.216 295.969 28.448 295.969 28.712V32H294.157ZM304.126 34.292C303.566 34.292 303.038 34.232 302.542 34.112C302.046 33.992 301.614 33.804 301.246 33.548L301.738 32.312C301.954 32.44 302.186 32.552 302.434 32.648C302.69 32.744 302.95 32.816 303.214 32.864C303.478 32.912 303.73 32.936 303.97 32.936C304.482 32.936 304.866 32.82 305.122 32.588C305.378 32.356 305.506 32 305.506 31.52V30.608H305.614C305.494 30.976 305.246 31.276 304.87 31.508C304.494 31.74 304.078 31.856 303.622 31.856C303.094 31.856 302.634 31.736 302.242 31.496C301.85 31.248 301.546 30.904 301.33 30.464C301.114 30.016 301.006 29.5 301.006 28.916C301.006 28.324 301.114 27.812 301.33 27.38C301.546 26.94 301.85 26.6 302.242 26.36C302.634 26.112 303.094 25.988 303.622 25.988C304.094 25.988 304.51 26.104 304.87 26.336C305.238 26.56 305.482 26.856 305.602 27.224H305.506V26.108H307.27V31.316C307.27 31.972 307.15 32.52 306.91 32.96C306.67 33.408 306.314 33.74 305.842 33.956C305.378 34.18 304.806 34.292 304.126 34.292ZM304.162 30.5C304.562 30.5 304.882 30.36 305.122 30.08C305.37 29.8 305.494 29.412 305.494 28.916C305.494 28.42 305.37 28.036 305.122 27.764C304.882 27.484 304.562 27.344 304.162 27.344C303.754 27.344 303.43 27.484 303.19 27.764C302.95 28.036 302.83 28.42 302.83 28.916C302.83 29.412 302.95 29.8 303.19 30.08C303.43 30.36 303.754 30.5 304.162 30.5ZM311.736 32.132C311.04 32.132 310.44 32.008 309.936 31.76C309.44 31.504 309.056 31.148 308.784 30.692C308.52 30.228 308.388 29.684 308.388 29.06C308.388 28.452 308.516 27.92 308.772 27.464C309.028 27 309.384 26.64 309.84 26.384C310.304 26.12 310.828 25.988 311.412 25.988C311.988 25.988 312.484 26.112 312.9 26.36C313.316 26.6 313.636 26.944 313.86 27.392C314.092 27.84 314.208 28.372 314.208 28.988V29.444H309.876V28.508H312.9L312.708 28.676C312.708 28.188 312.6 27.82 312.384 27.572C312.176 27.316 311.876 27.188 311.484 27.188C311.188 27.188 310.936 27.256 310.728 27.392C310.52 27.528 310.36 27.724 310.248 27.98C310.136 28.236 310.08 28.544 310.08 28.904V29C310.08 29.408 310.14 29.744 310.26 30.008C310.388 30.264 310.576 30.456 310.824 30.584C311.08 30.712 311.396 30.776 311.772 30.776C312.092 30.776 312.416 30.728 312.744 30.632C313.072 30.536 313.368 30.388 313.632 30.188L314.112 31.4C313.816 31.624 313.452 31.804 313.02 31.94C312.596 32.068 312.168 32.132 311.736 32.132Z" fill="white"/>
                    <path d="M333.56 20.957L327.25 16.75V27.25L333.597 22.5954C334.157 22.1847 334.138 21.3422 333.56 20.957Z" fill="#1998CD"/>
                </g>
                <defs>
                    <filter id="filter0_f_491_2815" x="0.149414" y="17.2969" width="360.526" height="174.195" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                        <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
                        <feGaussianBlur stdDeviation="4" result="effect1_foregroundBlur_491_2815"/>
                    </filter>
                    <filter id="filter1_d_491_2815" x="119" y="111.314" width="31.1006" height="31.1006" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                        <feOffset dy="4.31447"/>
                        <feGaussianBlur stdDeviation="4"/>
                        <feComposite in2="hardAlpha" operator="out"/>
                        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_491_2815"/>
                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_491_2815" result="shape"/>
                    </filter>
                    <filter id="filter2_d_491_2815" x="210" y="91.3145" width="31.1006" height="31.1006" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                        <feOffset dy="4.31447"/>
                        <feGaussianBlur stdDeviation="4"/>
                        <feComposite in2="hardAlpha" operator="out"/>
                        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_491_2815"/>
                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_491_2815" result="shape"/>
                    </filter>
                    <filter id="filter3_d_491_2815" x="329" y="10.3145" width="31.1006" height="31.1006" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                        <feOffset dy="4.31447"/>
                        <feGaussianBlur stdDeviation="4"/>
                        <feComposite in2="hardAlpha" operator="out"/>
                        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_491_2815"/>
                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_491_2815" result="shape"/>
                    </filter>
                    <filter id="filter4_d_491_2815" x="26" y="132.314" width="31.1006" height="31.1006" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                        <feOffset dy="4.31447"/>
                        <feGaussianBlur stdDeviation="4"/>
                        <feComposite in2="hardAlpha" operator="out"/>
                        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_491_2815"/>
                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_491_2815" result="shape"/>
                    </filter>
                    <filter id="filter5_d_491_2815" x="8" y="96" width="67" height="47.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                        <feOffset dy="4"/>
                        <feGaussianBlur stdDeviation="4"/>
                        <feComposite in2="hardAlpha" operator="out"/>
                        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.16 0"/>
                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_491_2815"/>
                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_491_2815" result="shape"/>
                    </filter>
                    <filter id="filter6_d_491_2815" x="236.25" y="0" width="106.25" height="52" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                        <feOffset dy="4"/>
                        <feGaussianBlur stdDeviation="4"/>
                        <feComposite in2="hardAlpha" operator="out"/>
                        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.16 0"/>
                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_491_2815"/>
                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_491_2815" result="shape"/>
                    </filter>
                    <linearGradient id="paint0_linear_491_2815" x1="352" y1="199.999" x2="9" y2="199.999" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#1998CD"/>
                        <stop offset="0.549638" stop-color="#F5E088"/>
                        <stop offset="1" stop-color="#FF574C"/>
                    </linearGradient>
                    <linearGradient id="paint1_linear_491_2815" x1="350.846" y1="181.999" x2="10.1553" y2="181.999" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#1998CD"/>
                        <stop offset="0.549638" stop-color="#F5E088"/>
                        <stop offset="1" stop-color="#FF574C"/>
                    </linearGradient>
                    <linearGradient id="paint2_linear_491_2815" x1="350.846" y1="174.999" x2="10.1553" y2="174.999" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#1998CD"/>
                        <stop offset="0.549638" stop-color="#F5E088"/>
                        <stop offset="1" stop-color="#FF574C"/>
                    </linearGradient>
                    <linearGradient id="paint3_linear_491_2815" x1="134.55" y1="115" x2="134.55" y2="130.101" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#2D2D2D"/>
                        <stop offset="1" stop-color="#939393"/>
                    </linearGradient>
                    <linearGradient id="paint4_linear_491_2815" x1="225.55" y1="95" x2="225.55" y2="110.101" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#2D2D2D"/>
                        <stop offset="1" stop-color="#939393"/>
                    </linearGradient>
                    <linearGradient id="paint5_linear_491_2815" x1="344.55" y1="14" x2="344.55" y2="29.1006" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#2D2D2D"/>
                        <stop offset="1" stop-color="#939393"/>
                    </linearGradient>
                    <linearGradient id="paint6_linear_491_2815" x1="41.5503" y1="136" x2="41.5503" y2="151.101" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#2D2D2D"/>
                        <stop offset="1" stop-color="#939393"/>
                    </linearGradient>
                </defs>
            </svg>

            <div class="quiz__reviews-loading-svg-date-wrapp reactive">
                <p class="quiz__reviews-loading-svg-date reactive">
                    WEEK 1
                </p>
                <p class="quiz__reviews-loading-svg-date reactive">
                    WEEK 2
                </p>
                <p class="quiz__reviews-loading-svg-date reactive">
                    WEEK 3
                </p>
                <p class="quiz__reviews-loading-svg-date reactive">
                    WEEK 4
                </p>
            </div>
            <p class="quiz__reviews-loading-svg-date-text">
                This chart shows your potential progress if you follow all the steps listed in our plan
            </p>
        </div>

        <p class="quiz__reviews-loading-subtext">
            <span class="nameInsertSelector black" data-puppy="Puppy">dog</span>’s personalized <span>dog vagus nerve reset challenge</span> is ready
        </p>
        <div class="quiz__btn-box">
            <button class="quiz__btn js-btn email-submit" id="redirect-to-landing-btn" data-landing-url="/questionary/landing-40">
                <span></span>
                Continue
            </button>
        </div>
    </div>
    <div class="quiz__redirect-loader" id="resultLoader">
        <div class="preload">
            <span></span>
        </div>
    </div>
</div>

<div class="quiz__popup-wrapp newFun" id="quizPopUpProblem">
    <div class="quiz__popup-bg"></div>

    <div class="quiz__popup" id="magicProblemPopup">
        <div class="quiz__popup-box">
            <h4 class="quiz__popup-title">
                One more question
            </h4>
            <div class="quiz__popup-text">
                Does your dog have trouble sleeping?
            </div>
            <ul class="quiz__popup-btn-list">
                <li class="quiz__popup-btn-item quizPopUpProblemBtn" data-event="No">
                    No
                </li>
                <li class="quiz__popup-btn-item quizPopUpProblemBtn" data-event="Yes">
                    Yes
                </li>
            </ul>
        </div>
    </div>
</div>
<div class="quiz__popup-wrapp newFun" id="quizPopUpObedience">
    <div class="quiz__popup-bg"></div>

    <div class="quiz__popup" id="magicObediencePopup">
        <div class="quiz__popup-box">
            <h4 class="quiz__popup-title">
                Finalizing your plan
            </h4>
            <div class="quiz__popup-text">
                Does <span class="nameInsertSelector">your dog</span> have trouble focusing on you?
            </div>
            <ul class="quiz__popup-btn-list">
                <li class="quiz__popup-btn-item quizPopUpObedienceBtn" data-event="No">
                    No
                </li>
                <li class="quiz__popup-btn-item quizPopUpObedienceBtn" data-event="Yes">
                    Yes
                </li>
            </ul>
        </div>
    </div>
</div>
