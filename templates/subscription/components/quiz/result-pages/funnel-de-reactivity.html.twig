<div class="quiz__index quiz__result skipQuiz magic" id="block{{ blockId }}" data-id="{{ blockId }}" data-result="reviews">
    <div class="container " id="resultStepOne">
        <div id="circleBar" class="progressbar reviews">
            <div class="progressbar__shadow"></div>
        </div>
        <p class="quiz__reviews-loading-subtitle" id="replaceSubtitle">
            Erstellung von <span class="nameInsertSelector"></span> personalisiertem Trainingsplan...
        </p>
        <div class="quiz__hr">
        </div>
        <div class="quiz__reviews-loading-title-red-box">
            <h3 class="quiz__reviews-loading-title-red">
                115 Tausend Hundebesitzer
            </h3>
        </div>
        <p class="quiz__reviews-loading-text">
            haben sich für PawChamp entschieden
        </p>
        <div class="quiz__reviews-slider custom-result">
            <ul class="swiper-wrapper">
                <li class="quiz__reviews-slider-item swiper-slide">
                    <div class="quiz__reviews-slider-item-text-wrapp">
                        <svg width="90" height="16" viewBox="0 0 90 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M9.46744 6.37307H14.2218L10.3768 9.16159L10.3767 9.16159L8.00391 10.8849L10.7058 10.1835L10.3789 9.16822L11.8403 13.6821L7.99524 10.8849L4.15019 13.6821L5.62239 9.16159L1.77734 6.36441L6.53169 6.37307L8.0039 1.85254L9.46744 6.37307Z"
                                  fill="white"/>
                            <rect x="18.3711" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M27.8385 6.37307H32.5929L28.7478 9.16159L28.7478 9.16159L26.375 10.8849L29.0769 10.1835L28.75 9.16822L30.2114 13.6821L26.3663 10.8849L22.5213 13.6821L23.9935 9.16159L20.1484 6.36441L24.9028 6.37307L26.375 1.85254L27.8385 6.37307Z"
                                  fill="white"/>
                            <rect x="36.7402" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M46.2077 6.37307H50.962L47.117 9.16159L47.117 9.16159L44.7441 10.8849L47.4461 10.1835L47.1191 9.16822L48.5805 13.6821L44.7355 10.8849L40.8904 13.6821L42.3626 9.16159L38.5176 6.36441L43.2719 6.37307L44.7441 1.85254L46.2077 6.37307Z"
                                  fill="white"/>
                            <rect x="55.1113" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M64.5788 6.37307H69.3331L65.4881 9.16159L65.4883 9.16217L63.1162 10.8849L65.8181 10.1835L65.6738 9.7352L66.9516 13.6821L63.1066 10.8849L59.2615 13.6821L60.7337 9.16159L56.8887 6.36441L61.643 6.37307L63.1152 1.85254L64.5788 6.37307Z"
                                  fill="white"/>
                            <rect x="73.4824" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M82.9499 6.37307H87.7042L83.8592 9.16159L83.8592 9.16159L81.4863 10.8849L84.1883 10.1835L83.8613 9.16822L85.3227 13.6821L81.4777 10.8849L77.6326 13.6821L79.1048 9.16159L75.2598 6.36441L80.0141 6.37307L81.4863 1.85254L82.9499 6.37307Z"
                                  fill="white"/>
                        </svg>
                        <div class="quiz__reviews-slider-item-name-wrapp">
                            <h5>
                                Ein neuer Ansatz für mehr Gelassenheit
                            </h5>
                            <p class="quiz__reviews-slider-item-name">
                                Ernesto
                            </p>
                        </div>
                        <div class="quiz__reviews-slider-item-text">
                            Das war ein neuer Weg für mich zu lernen, wie ich meinem Hund helfen kann, ruhig zu bleiben. Wir sind noch nicht fertig, aber ich habe bereits große Verbesserungen bei unserem 8-jährigen Deutschen Schäferhund gesehen, der zwar schon etwas trainiert war, aber mit seiner Reaktivität zu kämpfen hatte.
                        </div>
                    </div>
                </li>
                <li class="quiz__reviews-slider-item swiper-slide">
                    <div class="quiz__reviews-slider-item-text-wrapp">
                        <svg width="90" height="16" viewBox="0 0 90 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M9.46744 6.37307H14.2218L10.3768 9.16159L10.3767 9.16159L8.00391 10.8849L10.7058 10.1835L10.3789 9.16822L11.8403 13.6821L7.99524 10.8849L4.15019 13.6821L5.62239 9.16159L1.77734 6.36441L6.53169 6.37307L8.0039 1.85254L9.46744 6.37307Z"
                                  fill="white"/>
                            <rect x="18.3711" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M27.8385 6.37307H32.5929L28.7478 9.16159L28.7478 9.16159L26.375 10.8849L29.0769 10.1835L28.75 9.16822L30.2114 13.6821L26.3663 10.8849L22.5213 13.6821L23.9935 9.16159L20.1484 6.36441L24.9028 6.37307L26.375 1.85254L27.8385 6.37307Z"
                                  fill="white"/>
                            <rect x="36.7402" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M46.2077 6.37307H50.962L47.117 9.16159L47.117 9.16159L44.7441 10.8849L47.4461 10.1835L47.1191 9.16822L48.5805 13.6821L44.7355 10.8849L40.8904 13.6821L42.3626 9.16159L38.5176 6.36441L43.2719 6.37307L44.7441 1.85254L46.2077 6.37307Z"
                                  fill="white"/>
                            <rect x="55.1113" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M64.5788 6.37307H69.3331L65.4881 9.16159L65.4883 9.16217L63.1162 10.8849L65.8181 10.1835L65.6738 9.7352L66.9516 13.6821L63.1066 10.8849L59.2615 13.6821L60.7337 9.16159L56.8887 6.36441L61.643 6.37307L63.1152 1.85254L64.5788 6.37307Z"
                                  fill="white"/>
                            <rect x="73.4824" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M82.9499 6.37307H87.7042L83.8592 9.16159L83.8592 9.16159L81.4863 10.8849L84.1883 10.1835L83.8613 9.16822L85.3227 13.6821L81.4777 10.8849L77.6326 13.6821L79.1048 9.16159L75.2598 6.36441L80.0141 6.37307L81.4863 1.85254L82.9499 6.37307Z"
                                  fill="white"/>
                        </svg>
                        <div class="quiz__reviews-slider-item-name-wrapp">
                            <h5>
                                Die Ausbildungsstruktur ist brillant
                            </h5>
                            <p class="quiz__reviews-slider-item-name">
                                Claire_Ugalde
                            </p>
                        </div>
                        <div class="quiz__reviews-slider-item-text">
                            Die Art und Weise, wie das Training aufgeteilt ist, ist ausgezeichnet. Ich hatte bereits Einzeltraining bei mir zu Hause und fand es weniger effektiv als diesen Kurs und würde es sehr empfehlen.
                        </div>
                    </div>
                </li>
                <li class="quiz__reviews-slider-item swiper-slide">
                    <div class="quiz__reviews-slider-item-text-wrapp">
                        <svg width="90" height="16" viewBox="0 0 90 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M9.46744 6.37307H14.2218L10.3768 9.16159L10.3767 9.16159L8.00391 10.8849L10.7058 10.1835L10.3789 9.16822L11.8403 13.6821L7.99524 10.8849L4.15019 13.6821L5.62239 9.16159L1.77734 6.36441L6.53169 6.37307L8.0039 1.85254L9.46744 6.37307Z"
                                  fill="white"/>
                            <rect x="18.3711" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M27.8385 6.37307H32.5929L28.7478 9.16159L28.7478 9.16159L26.375 10.8849L29.0769 10.1835L28.75 9.16822L30.2114 13.6821L26.3663 10.8849L22.5213 13.6821L23.9935 9.16159L20.1484 6.36441L24.9028 6.37307L26.375 1.85254L27.8385 6.37307Z"
                                  fill="white"/>
                            <rect x="36.7402" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M46.2077 6.37307H50.962L47.117 9.16159L47.117 9.16159L44.7441 10.8849L47.4461 10.1835L47.1191 9.16822L48.5805 13.6821L44.7355 10.8849L40.8904 13.6821L42.3626 9.16159L38.5176 6.36441L43.2719 6.37307L44.7441 1.85254L46.2077 6.37307Z"
                                  fill="white"/>
                            <rect x="55.1113" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M64.5788 6.37307H69.3331L65.4881 9.16159L65.4883 9.16217L63.1162 10.8849L65.8181 10.1835L65.6738 9.7352L66.9516 13.6821L63.1066 10.8849L59.2615 13.6821L60.7337 9.16159L56.8887 6.36441L61.643 6.37307L63.1152 1.85254L64.5788 6.37307Z"
                                  fill="white"/>
                            <rect x="73.4824" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M82.9499 6.37307H87.7042L83.8592 9.16159L83.8592 9.16159L81.4863 10.8849L84.1883 10.1835L83.8613 9.16822L85.3227 13.6821L81.4777 10.8849L77.6326 13.6821L79.1048 9.16159L75.2598 6.36441L80.0141 6.37307L81.4863 1.85254L82.9499 6.37307Z"
                                  fill="white"/>
                        </svg>
                        <div class="quiz__reviews-slider-item-name-wrapp">
                            <h5>
                                Hervorragender Lehrgang
                            </h5>
                            <p class="quiz__reviews-slider-item-name">
                                Diego Fernandez Jr
                            </p>
                        </div>
                        <p class="quiz__reviews-slider-item-text">
                            Ausgezeichneter Trainingskurs, sehr detailliert und leicht zu verstehen. Was mir an diesem Kurs gefällt, ist, dass sie betonen, dass die Ausbildung eines Hundes Geduld und Verständnis für den Prozess erfordert. Ich bin noch in der ersten Woche, aber ihr Ansatz scheint einfach zu folgen.
                            Ich kann es kaum erwarten, die Endergebnisse zu sehen...
                        </p>
                    </div>
                </li>
            </ul>
        </div>
    </div>
    <div class="container email" id="resultStepTwo">
        <h2 class="quiz__reviews-loading-title">
            Geben Sie Ihre E-Mail-Adresse ein, um <span class="nameInsertSelector"></span> personalisierten Plan zur Überwindung der Reaktivität zu erhalten
        </h2>
        <p class="quiz__result-label" id="emailText"></p>
        <div class="quiz__result-box">
            <ul id="autoComplete" class="email-autocomplete email-autocomplete-scroll"></ul>
            <input class="quiz__result-input" name="text" type="text" placeholder="E-Mail eingeben" id="email" autocomplete="off">
        </div>
        <div class="quiz__result-text-box">
            <svg class="quiz__result-text-icon" width="21" height="26" viewBox="0 0 21 26" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path opacity="0.45"
                      d="M20.1944 9.75H18.5278V7.58332C18.5278 3.40184 15.0387 0 10.75 0C6.46125 0 2.97224 3.40184 2.97224 7.58332V9.75H1.30557C0.99849 9.75 0.75 9.99228 0.75 10.2917V23.8334C0.75 25.0283 1.74661 26 2.97224 26H18.5278C19.7534 26 20.75 25.0283 20.75 23.8333V10.2917C20.75 9.99228 20.5015 9.75 20.1944 9.75ZM12.4134 21.0652C12.4308 21.2181 12.3804 21.3715 12.2751 21.4863C12.1698 21.6011 12.019 21.6667 11.8611 21.6667H9.63891C9.48104 21.6667 9.33021 21.6011 9.22495 21.4863C9.11969 21.3715 9.06922 21.2181 9.08661 21.0652L9.43708 17.9925C8.86797 17.5888 8.52781 16.9504 8.52781 16.25C8.52781 15.0551 9.52443 14.0833 10.7501 14.0833C11.9757 14.0833 12.9723 15.055 12.9723 16.25C12.9723 16.9504 12.6321 17.5888 12.063 17.9925L12.4134 21.0652ZM15.1944 9.75H6.30557V7.58332C6.30557 5.19396 8.29938 3.25 10.75 3.25C13.2006 3.25 15.1944 5.19396 15.1944 7.58332V9.75Z"
                      fill="#343434"/>
            </svg>
            <p class="quiz__result-text">
                Der Schutz Ihrer Privatsphäre und der Schutz Ihrer persönlichen Daten sind uns ein Anliegen. Wir versenden keine Spam-E-Mails, sondern nur relevante Informationen.
            </p>
        </div>
        <div class="quiz__btn-box">
            <button class="quiz__btn js-btn email-submit" id="email-submit" disabled>
                <span></span>
                Weitergehen
            </button>
        </div>
    </div>
    <div class="container " id="resultStepEmailSubscription">
        <h3 class="quiz__email-subscription-title">
            Möchten Sie E-Mails mit <span>Sonderangeboten</span>, Tipps zur Hundeerziehung, Ratschlägen und <span>Gratisgeschenken</span> erhalten?
        </h3>
        <div class="quiz__email-subscription-btn-box">
            <button class="quiz__btn triggerStep" data-event-value="Apply newsletter">
                <span></span>
                Ja, ich bin dabei!
            </button>
            <label class="quiz__email-subscription-label triggerStep" data-event-value="Reject newsletter">
                <input type="checkbox" class="quiz__email-subscription-input" id="disallowMarketingEmails">
                Ich bin nicht interessiert
            </label>
        </div>
    </div>
    <div class="container graph " id="resultStepThree">
        <h2 class="quiz__reviews-loading-title">
            <span class="nameInsertSelector"></span> Reaktivität
        </h2>
        <div class="quiz__reviews-loading-svg-inner">
            <svg class="quiz__reviews-loading-svg quiz__reviews-loading-svg--is-reactivity" width="343" height="227" viewBox="0 0 336 203" fill="none" xmlns="http://www.w3.org/2000/svg">
                <line x1="20" y1="0.5" x2="316.12" y2="0.499974" stroke="#E8E8E8"/>
                <line x1="20" y1="26.5" x2="316.12" y2="26.5" stroke="#E8E8E8"/>
                <line x1="20" y1="52.5" x2="316.12" y2="52.5" stroke="#E8E8E8"/>
                <line x1="20" y1="78.5" x2="316.12" y2="78.5" stroke="#E8E8E8"/>
                <line x1="20" y1="104.5" x2="316.12" y2="104.5" stroke="#E8E8E8"/>
                <line x1="20" y1="130.5" x2="316.12" y2="130.5" stroke="#E8E8E8"/>
                <line x1="20" y1="156.5" x2="316.12" y2="156.5" stroke="#E8E8E8"/>
                <g id="resultGraphBg" class="result-graph-bg" opacity="0.3">
                    <path d="M20 16.9996C36.4481 20.9767 52.5247 24.5721 68.1785 28.073C114.36 38.4011 156.862 47.9063 194.366 63.9597C245.023 85.643 286.513 119.261 315.451 183H20V16.9996Z" fill="#D9D9D9"/>
                    <path d="M20 16.9996C36.4481 20.9767 52.5247 24.5721 68.1785 28.073C114.36 38.4011 156.862 47.9063 194.366 63.9597C245.023 85.643 286.513 119.261 315.451 183H20V16.9996Z" fill="url(#paint0_linear_2003_11)"/>
                </g>
                <line x1="20" y1="182.5" x2="317.06" y2="182.5" stroke="#E8E8E8"/>
                <line x1="20.4395" y1="189" x2="20.4395" y2="183" stroke="#E8E8E8"/>
                <line x1="316.561" y1="189" x2="316.561" y2="183" stroke="#E8E8E8"/>
                <line x1="215.032" y1="189" x2="215.032" y2="183" stroke="#E8E8E8"/>
                <line x1="126.667" y1="189" x2="126.667" y2="183" stroke="#E8E8E8"/>
                <path d="M20.6591,15.0185C20.6591,15.0185 10.9682,13.1907 142.872,45.9062C274.775,78.6218 299.9,154.958 316.5,183" class="quiz__based-graph-line" stroke="url(#paint1_linear_2003_11)" stroke-width="5"/>
                <g class="result-graph-goal" filter="url(#filter0_d_2003_11)">
                    <rect x="273" y="131" width="62" height="32" rx="8" fill="#323334"/>
                    <path d="M301.49 137.95V145H300.33L296.82 140.51V145H295.37V137.95H296.52L300.03 142.42V137.95H301.49ZM304.949 139.97C305.683 139.97 306.223 140.143 306.569 140.49C306.923 140.837 307.099 141.373 307.099 142.1V145H305.669V144.27C305.569 144.523 305.403 144.723 305.169 144.87C304.936 145.01 304.663 145.08 304.349 145.08C304.016 145.08 303.713 145.013 303.439 144.88C303.173 144.747 302.959 144.56 302.799 144.32C302.646 144.08 302.569 143.813 302.569 143.52C302.569 143.16 302.659 142.877 302.839 142.67C303.026 142.463 303.323 142.313 303.729 142.22C304.136 142.127 304.693 142.08 305.399 142.08H305.659V141.9C305.659 141.607 305.596 141.4 305.469 141.28C305.343 141.16 305.123 141.1 304.809 141.1C304.569 141.1 304.303 141.143 304.009 141.23C303.716 141.317 303.436 141.437 303.169 141.59L302.769 140.58C303.049 140.407 303.393 140.263 303.799 140.15C304.213 140.03 304.596 139.97 304.949 139.97ZM304.679 144.04C304.973 144.04 305.209 143.943 305.389 143.75C305.569 143.55 305.659 143.293 305.659 142.98V142.81H305.489C304.949 142.81 304.569 142.853 304.349 142.94C304.136 143.027 304.029 143.183 304.029 143.41C304.029 143.59 304.089 143.74 304.209 143.86C304.336 143.98 304.493 144.04 304.679 144.04ZM310.568 145.08C309.768 145.08 309.138 144.853 308.678 144.4C308.218 143.947 307.988 143.33 307.988 142.55C307.988 142.037 308.098 141.587 308.318 141.2C308.538 140.807 308.848 140.503 309.248 140.29C309.648 140.077 310.112 139.97 310.638 139.97C310.998 139.97 311.345 140.027 311.678 140.14C312.012 140.247 312.282 140.393 312.488 140.58L312.088 141.61C311.895 141.457 311.682 141.34 311.448 141.26C311.222 141.173 310.998 141.13 310.778 141.13C310.385 141.13 310.078 141.247 309.858 141.48C309.645 141.713 309.538 142.06 309.538 142.52C309.538 142.98 309.645 143.33 309.858 143.57C310.078 143.803 310.385 143.92 310.778 143.92C310.998 143.92 311.222 143.88 311.448 143.8C311.682 143.713 311.895 143.593 312.088 143.44L312.488 144.48C312.268 144.667 311.988 144.813 311.648 144.92C311.308 145.027 310.948 145.08 310.568 145.08ZM316.25 139.97C316.837 139.97 317.273 140.143 317.56 140.49C317.847 140.83 317.99 141.347 317.99 142.04V145H316.48V142.11C316.48 141.777 316.417 141.533 316.29 141.38C316.17 141.227 315.98 141.15 315.72 141.15C315.407 141.15 315.153 141.25 314.96 141.45C314.773 141.65 314.68 141.917 314.68 142.25V145H313.17V137.95H314.68V140.74C314.853 140.487 315.073 140.297 315.34 140.17C315.613 140.037 315.917 139.97 316.25 139.97ZM287.351 151.37C287.764 151.49 288.081 151.7 288.301 152C288.527 152.293 288.641 152.653 288.641 153.08C288.641 153.7 288.404 154.19 287.931 154.55C287.457 154.903 286.817 155.08 286.011 155.08C285.524 155.08 285.057 155.01 284.611 154.87C284.164 154.73 283.801 154.537 283.521 154.29L284.011 153.18C284.651 153.633 285.294 153.86 285.941 153.86C286.367 153.86 286.677 153.787 286.871 153.64C287.064 153.487 287.161 153.25 287.161 152.93C287.161 152.617 287.064 152.39 286.871 152.25C286.677 152.11 286.367 152.04 285.941 152.04H284.931V150.83H285.771C286.177 150.83 286.477 150.76 286.671 150.62C286.871 150.48 286.971 150.263 286.971 149.97C286.971 149.683 286.884 149.463 286.711 149.31C286.537 149.157 286.287 149.08 285.961 149.08C285.667 149.08 285.361 149.14 285.041 149.26C284.721 149.373 284.411 149.54 284.111 149.76L283.621 148.65C283.901 148.41 284.261 148.22 284.701 148.08C285.147 147.933 285.604 147.86 286.071 147.86C286.551 147.86 286.971 147.94 287.331 148.1C287.691 148.253 287.967 148.473 288.161 148.76C288.361 149.047 288.461 149.38 288.461 149.76C288.461 150.14 288.361 150.473 288.161 150.76C287.967 151.04 287.697 151.243 287.351 151.37ZM299.67 147.95V155H298.3V150.82L296.68 153.84H295.68L294.06 150.86V155H292.69V147.95H293.92L296.19 152.26L298.46 147.95H299.67ZM303.328 155.08C302.808 155.08 302.351 154.977 301.958 154.77C301.564 154.557 301.261 154.26 301.048 153.88C300.834 153.493 300.728 153.04 300.728 152.52C300.728 152 300.834 151.55 301.048 151.17C301.261 150.783 301.564 150.487 301.958 150.28C302.351 150.073 302.808 149.97 303.328 149.97C303.848 149.97 304.304 150.073 304.698 150.28C305.091 150.487 305.394 150.783 305.608 151.17C305.821 151.55 305.928 152 305.928 152.52C305.928 153.04 305.821 153.493 305.608 153.88C305.394 154.26 305.091 154.557 304.698 154.77C304.304 154.977 303.848 155.08 303.328 155.08ZM303.328 153.93C304.061 153.93 304.428 153.46 304.428 152.52C304.428 152.047 304.331 151.693 304.138 151.46C303.951 151.227 303.681 151.11 303.328 151.11C302.594 151.11 302.228 151.58 302.228 152.52C302.228 153.46 302.594 153.93 303.328 153.93ZM309.927 149.97C310.513 149.97 310.95 150.143 311.237 150.49C311.523 150.83 311.667 151.347 311.667 152.04V155H310.157V152.11C310.157 151.777 310.093 151.533 309.967 151.38C309.847 151.227 309.657 151.15 309.397 151.15C309.083 151.15 308.83 151.25 308.637 151.45C308.45 151.65 308.357 151.917 308.357 152.25V155H306.847V150.09H308.317V150.79C308.49 150.523 308.713 150.32 308.987 150.18C309.267 150.04 309.58 149.97 309.927 149.97ZM314.974 149.97C315.707 149.97 316.247 150.143 316.594 150.49C316.947 150.837 317.124 151.373 317.124 152.1V155H315.694V154.27C315.594 154.523 315.427 154.723 315.194 154.87C314.961 155.01 314.687 155.08 314.374 155.08C314.041 155.08 313.737 155.013 313.464 154.88C313.197 154.747 312.984 154.56 312.824 154.32C312.671 154.08 312.594 153.813 312.594 153.52C312.594 153.16 312.684 152.877 312.864 152.67C313.051 152.463 313.347 152.313 313.754 152.22C314.161 152.127 314.717 152.08 315.424 152.08H315.684V151.9C315.684 151.607 315.621 151.4 315.494 151.28C315.367 151.16 315.147 151.1 314.834 151.1C314.594 151.1 314.327 151.143 314.034 151.23C313.741 151.317 313.461 151.437 313.194 151.59L312.794 150.58C313.074 150.407 313.417 150.263 313.824 150.15C314.237 150.03 314.621 149.97 314.974 149.97ZM314.704 154.04C314.997 154.04 315.234 153.943 315.414 153.75C315.594 153.55 315.684 153.293 315.684 152.98V152.81H315.514C314.974 152.81 314.594 152.853 314.374 152.94C314.161 153.027 314.054 153.183 314.054 153.41C314.054 153.59 314.114 153.74 314.234 153.86C314.361 153.98 314.517 154.04 314.704 154.04ZM320.973 153.97C321.126 153.97 321.286 153.96 321.453 153.94L321.373 155.04C321.18 155.067 320.986 155.08 320.793 155.08C320.046 155.08 319.5 154.917 319.153 154.59C318.813 154.263 318.643 153.767 318.643 153.1V151.22H317.713V150.09H318.643V148.65H320.153V150.09H321.383V151.22H320.153V153.09C320.153 153.677 320.426 153.97 320.973 153.97ZM326.54 152.83H323.34C323.387 153.223 323.513 153.51 323.72 153.69C323.933 153.863 324.233 153.95 324.62 153.95C324.873 153.95 325.123 153.91 325.37 153.83C325.623 153.743 325.853 153.623 326.06 153.47L326.46 154.48C326.22 154.667 325.927 154.813 325.58 154.92C325.24 155.027 324.897 155.08 324.55 155.08C323.723 155.08 323.07 154.853 322.59 154.4C322.117 153.94 321.88 153.317 321.88 152.53C321.88 152.03 321.983 151.587 322.19 151.2C322.397 150.813 322.683 150.513 323.05 150.3C323.417 150.08 323.833 149.97 324.3 149.97C324.987 149.97 325.53 150.193 325.93 150.64C326.337 151.087 326.54 151.693 326.54 152.46V152.83ZM324.34 151.01C324.053 151.01 323.823 151.103 323.65 151.29C323.483 151.47 323.38 151.733 323.34 152.08H325.27C325.25 151.727 325.16 151.46 325 151.28C324.847 151.1 324.627 151.01 324.34 151.01ZM330.532 149.97C331.119 149.97 331.555 150.143 331.842 150.49C332.129 150.83 332.272 151.347 332.272 152.04V155H330.762V152.11C330.762 151.777 330.699 151.533 330.572 151.38C330.452 151.227 330.262 151.15 330.002 151.15C329.689 151.15 329.435 151.25 329.242 151.45C329.055 151.65 328.962 151.917 328.962 152.25V155H327.452V150.09H328.922V150.79C329.095 150.523 329.319 150.32 329.592 150.18C329.872 150.04 330.185 149.97 330.532 149.97Z" fill="white" transform="translate(-4 0)"/>
                    <path d="M321.293 169.31L325.5 163L315 163L319.655 169.347C320.065 169.907 320.908 169.888 321.293 169.31Z" fill="#323334" transform="translate(-4 -1)"/>
                </g>
                <g filter="url(#filter1_d_2003_11)">
                    <circle cx="20.5503" cy="14.5503" r="7.55031" fill="white"/>
                    <circle cx="20.5503" cy="14.5503" r="7.05031" stroke="#2D2D2D" stroke-opacity="0.12"/>
                </g>
                <g filter="url(#filter2_d_2003_11)" transform="translate(-2 0)">
                    <rect x="6" y="31" width="34" height="22" rx="8" fill="#D3D5DA"/>
                    <path d="M11.9981 43.85L12.7581 43.8C13.0248 43.7867 13.2214 43.7133 13.3481 43.58C13.4748 43.44 13.5381 43.2333 13.5381 42.96V37.95H15.1081V42.94C15.1081 44.2667 14.4114 44.97 13.0181 45.05L12.0881 45.1L11.9981 43.85ZM20.8076 42.83H17.6076C17.6543 43.2233 17.781 43.51 17.9876 43.69C18.201 43.8633 18.501 43.95 18.8876 43.95C19.141 43.95 19.391 43.91 19.6376 43.83C19.891 43.7433 20.121 43.6233 20.3276 43.47L20.7276 44.48C20.4876 44.6667 20.1943 44.8133 19.8476 44.92C19.5076 45.0267 19.1643 45.08 18.8176 45.08C17.991 45.08 17.3376 44.8533 16.8576 44.4C16.3843 43.94 16.1476 43.3167 16.1476 42.53C16.1476 42.03 16.251 41.5867 16.4576 41.2C16.6643 40.8133 16.951 40.5133 17.3176 40.3C17.6843 40.08 18.101 39.97 18.5676 39.97C19.2543 39.97 19.7976 40.1933 20.1976 40.64C20.6043 41.0867 20.8076 41.6933 20.8076 42.46V42.83ZM18.6076 41.01C18.321 41.01 18.091 41.1033 17.9176 41.29C17.751 41.47 17.6476 41.7333 17.6076 42.08H19.5376C19.5176 41.7267 19.4276 41.46 19.2676 41.28C19.1143 41.1 18.8943 41.01 18.6076 41.01ZM24.342 43.97C24.4953 43.97 24.6553 43.96 24.822 43.94L24.742 45.04C24.5487 45.0667 24.3553 45.08 24.162 45.08C23.4153 45.08 22.8687 44.9167 22.522 44.59C22.182 44.2633 22.012 43.7667 22.012 43.1V41.22H21.082V40.09H22.012V38.65H23.522V40.09H24.752V41.22H23.522V43.09C23.522 43.6767 23.7953 43.97 24.342 43.97ZM29.768 43.87V45H25.418V43.95L27.858 41.22H25.448V40.09H29.668V41.1L27.188 43.87H29.768ZM33.3947 43.97C33.5481 43.97 33.7081 43.96 33.8747 43.94L33.7947 45.04C33.6014 45.0667 33.4081 45.08 33.2147 45.08C32.4681 45.08 31.9214 44.9167 31.5747 44.59C31.2347 44.2633 31.0647 43.7667 31.0647 43.1V41.22H30.1347V40.09H31.0647V38.65H32.5747V40.09H33.8047V41.22H32.5747V43.09C32.5747 43.6767 32.8481 43.97 33.3947 43.97Z" fill="#828286"/>
                    <path d="M22.207 25.1895L18 31.5L28.5 31.5L23.8454 25.1529C23.4347 24.5928 22.5922 24.6117 22.207 25.1895Z" fill="#D3D5DA"/>
                </g>
                <g id="resultGraphDot" class="result-graph-dot" filter="url(#filter3_d_2003_11)">
                    <circle cx="317.55" cy="182.55" r="7.55031" fill="white"/>
                    <circle cx="317.55" cy="182.55" r="7.05031" stroke="#2D2D2D" stroke-opacity="0.12"/>
                </g>
                <defs>
                    <filter id="filter0_d_2003_11" x="270.5" y="131" width="65" height="46.7559" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                        <feOffset dy="4"/>
                        <feGaussianBlur stdDeviation="2"/>
                        <feComposite in2="hardAlpha" operator="out"/>
                        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.07 0"/>
                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2003_11"/>
                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2003_11" result="shape"/>
                    </filter>
                    <filter id="filter1_d_2003_11" x="5" y="3.31447" width="31.1006" height="31.1006" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                        <feOffset dy="4.31447"/>
                        <feGaussianBlur stdDeviation="4"/>
                        <feComposite in2="hardAlpha" operator="out"/>
                        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2003_11"/>
                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2003_11" result="shape"/>
                    </filter>
                    <filter id="filter2_d_2003_11" x="0" y="24.7441" width="42" height="36.2559" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                        <feOffset dy="4"/>
                        <feGaussianBlur stdDeviation="2"/>
                        <feComposite in2="hardAlpha" operator="out"/>
                        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.07 0"/>
                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2003_11"/>
                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2003_11" result="shape"/>
                    </filter>
                    <filter id="filter3_d_2003_11" x="302" y="171.314" width="31.1006" height="31.1006" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                        <feOffset dy="4.31447"/>
                        <feGaussianBlur stdDeviation="4"/>
                        <feComposite in2="hardAlpha" operator="out"/>
                        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2003_11"/>
                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2003_11" result="shape"/>
                    </filter>
                    <linearGradient id="paint0_linear_2003_11" x1="26.9914" y1="181.708" x2="313.914" y2="181.708" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#FF574C"/>
                        <stop offset="0.549638" stop-color="#F5E088"/>
                        <stop offset="1" stop-color="#1998CD"/>
                    </linearGradient>
                    <linearGradient id="paint1_linear_2003_11" x1="20.5" y1="182.999" x2="316.5" y2="182.999" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#FF574C"/>
                        <stop offset="0.549638" stop-color="#F5E088"/>
                        <stop offset="1" stop-color="#1998CD"/>
                    </linearGradient>
                </defs>
            </svg>

            <div class="quiz__reviews-loading-svg-date-wrapp">
                <p class="quiz__reviews-loading-svg-date">
                    Jetzt
                </p>
                <p class="quiz__reviews-loading-svg-date" id="monthAfter1">
                    month 1
                </p>
                <p class="quiz__reviews-loading-svg-date" id="monthAfter2">
                    month 2
                </p>
                <p class="quiz__reviews-loading-svg-date" id="monthAfter3">
                    month 3
                </p>
            </div>
            <p class="quiz__reviews-loading-svg-date-text">
                Dieses Diagramm dient nur zu Illustrationszwecken
            </p>
        </div>

        <p class="quiz__reviews-loading-subtext">
            Der <span>Trainingsplan</span> Ihres <span class="puppyInsertSelector" data-puppy="Welpen">Hundes</span> ist fertig
        </p>
        <div class="quiz__btn-box">
            <button class="quiz__btn js-btn email-submit" id="redirect-to-landing-btn" data-landing-url="/questionary/landing-18">
                <span></span>
                Weitergehen
            </button>
        </div>
    </div>
    <div class="quiz__redirect-loader" id="resultLoader">
        <div class="preload">
            <span></span>
        </div>
    </div>
</div>

<div class="quiz__popup-wrapp" id="quizPopUpProblem">
    <div class="quiz__popup-bg"></div>

    <div class="quiz__popup" id="magicProblemPopup">
        <div class="quiz__popup-box">
            <h4 class="quiz__popup-title">
                Noch eine Frage
            </h4>
            {% if quizData.isPuppyFunnel %}
                <div class="quiz__popup-img-box">
                    <picture>
                        <source srcset="https://res.cloudinary.com/dr0cx27xo/image/upload/v1719408096/dog-training/img/reactivity/webp/puppy-with-blue-ball.webp">
                        <img id="resultPopup" class="quiz__popup-img" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1719408087/dog-training/img/reactivity/puppy-with-blue-ball.jpg" alt="puppy with blue ball">
                    </picture>
                </div>
                <div class="quiz__popup-text">
                    <span class="ageInsertSelector">Welpen</span> bewachen oft ihr Spielzeug, ihre Betten oder Leckereien.
                    Haben Sie dieses Problem?
                </div>
            {% else %}
                <div class="quiz__popup-img-box">
                    <picture>
                        <source srcset="https://res.cloudinary.com/dr0cx27xo/image/upload/v1719408098/dog-training/img/reactivity/webp/dog-with-teddy-bear-in-mouth.webp">
                        <img id="resultPopup" class="quiz__popup-img" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1719408088/dog-training/img/reactivity/dog-with-teddy-bear-in-mouth.jpg" alt="dog with teddy bear in mouth">
                    </picture>
                </div>
                <div class="quiz__popup-text">
                    <b class="capitalize"><span class="ageInsertSelector">AGE</span>e</b> bewachen oft ihr Spielzeug, ihre Betten oder Leckereien.
                    Haben Sie dieses Problem?
                </div>
            {% endif %}
            <ul class="quiz__popup-btn-list">
                <li class="quiz__popup-btn-item quizPopUpProblemBtn" data-event="No">
                    Nein
                </li>
                <li class="quiz__popup-btn-item quizPopUpProblemBtn" data-event="Yes">
                    Ja
                </li>
            </ul>
        </div>
    </div>
</div>
<div class="quiz__popup-wrapp" id="quizPopUpObedience">
    <div class="quiz__popup-bg"></div>

    <div class="quiz__popup" id="magicObediencePopup">
        <div class="quiz__popup-box">
            <h4 class="quiz__popup-title">
                Abschließung Ihres Plans
            </h4>
            <div class="quiz__popup-img-box">
                <img id="resultPopup" class="quiz__popup-img" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1719408097/dog-training/img/reactivity/webp/dog-sit-command.webp" alt="dog sit command">
            </div>
            <div class="quiz__popup-text">
                Hat <span class="nameInsertSelector">your dog</span> jemals ein Desensibilisierungstraining gehabt?
            </div>
            <ul class="quiz__popup-btn-list">
                <li class="quiz__popup-btn-item quizPopUpObedienceBtn" data-event="No">
                    Nein
                </li>
                <li class="quiz__popup-btn-item quizPopUpObedienceBtn" data-event="Yes">
                    Ja
                </li>
            </ul>
        </div>
    </div>
</div>
