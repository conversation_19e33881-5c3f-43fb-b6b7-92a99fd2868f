<div class="quiz__index quiz__result skipQuiz magic" id="block{{ blockId }}" data-id="{{ blockId }}" data-result="reviews">
    <div class="container " id="resultStepOne">
        <div id="circleBar" class="progressbar reviews">
            <div class="progressbar__shadow"></div>
        </div>
        <p class="quiz__reviews-loading-subtitle" id="replaceSubtitle">
            Creating <span class="nameInsertSelector">your dog</span>'s personalized training plan...
        </p>
        <div class="quiz__hr">
        </div>
        <div class="quiz__reviews-loading-title-red-box">
            <h3 class="quiz__reviews-loading-title-red">
                115 thousand dog owners
            </h3>
        </div>
        <p class="quiz__reviews-loading-text">
            have chosen PawChamp
        </p>
        <div class="quiz__reviews-slider custom-result">
            <ul class="swiper-wrapper">
                <li class="quiz__reviews-slider-item swiper-slide">
                    <div class="quiz__reviews-slider-item-text-wrapp">
                        <svg width="90" height="16" viewBox="0 0 90 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M9.46744 6.37307H14.2218L10.3768 9.16159L10.3767 9.16159L8.00391 10.8849L10.7058 10.1835L10.3789 9.16822L11.8403 13.6821L7.99524 10.8849L4.15019 13.6821L5.62239 9.16159L1.77734 6.36441L6.53169 6.37307L8.0039 1.85254L9.46744 6.37307Z"
                                  fill="white"/>
                            <rect x="18.3711" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M27.8385 6.37307H32.5929L28.7478 9.16159L28.7478 9.16159L26.375 10.8849L29.0769 10.1835L28.75 9.16822L30.2114 13.6821L26.3663 10.8849L22.5213 13.6821L23.9935 9.16159L20.1484 6.36441L24.9028 6.37307L26.375 1.85254L27.8385 6.37307Z"
                                  fill="white"/>
                            <rect x="36.7402" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M46.2077 6.37307H50.962L47.117 9.16159L47.117 9.16159L44.7441 10.8849L47.4461 10.1835L47.1191 9.16822L48.5805 13.6821L44.7355 10.8849L40.8904 13.6821L42.3626 9.16159L38.5176 6.36441L43.2719 6.37307L44.7441 1.85254L46.2077 6.37307Z"
                                  fill="white"/>
                            <rect x="55.1113" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M64.5788 6.37307H69.3331L65.4881 9.16159L65.4883 9.16217L63.1162 10.8849L65.8181 10.1835L65.6738 9.7352L66.9516 13.6821L63.1066 10.8849L59.2615 13.6821L60.7337 9.16159L56.8887 6.36441L61.643 6.37307L63.1152 1.85254L64.5788 6.37307Z"
                                  fill="white"/>
                            <rect x="73.4824" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M82.9499 6.37307H87.7042L83.8592 9.16159L83.8592 9.16159L81.4863 10.8849L84.1883 10.1835L83.8613 9.16822L85.3227 13.6821L81.4777 10.8849L77.6326 13.6821L79.1048 9.16159L75.2598 6.36441L80.0141 6.37307L81.4863 1.85254L82.9499 6.37307Z"
                                  fill="white"/>
                        </svg>
                        <div class="quiz__reviews-slider-item-name-wrapp">
                            <h5>
                                Great positive reinforcement techniques
                            </h5>
                            <p class="quiz__reviews-slider-item-name">
                                Dawg Daddy
                            </p>
                        </div>
                        <div class="quiz__reviews-slider-item-text">
                            This is a great training program that starts from the very base which is critical. It makes its way up the ladder with positive reinforcement techniques that truly work. I’ve seen great results with my
                            2 dogs.
                        </div>
                    </div>
                </li>
                <li class="quiz__reviews-slider-item swiper-slide">
                    <div class="quiz__reviews-slider-item-text-wrapp">
                        <svg width="90" height="16" viewBox="0 0 90 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M9.46744 6.37307H14.2218L10.3768 9.16159L10.3767 9.16159L8.00391 10.8849L10.7058 10.1835L10.3789 9.16822L11.8403 13.6821L7.99524 10.8849L4.15019 13.6821L5.62239 9.16159L1.77734 6.36441L6.53169 6.37307L8.0039 1.85254L9.46744 6.37307Z"
                                  fill="white"/>
                            <rect x="18.3711" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M27.8385 6.37307H32.5929L28.7478 9.16159L28.7478 9.16159L26.375 10.8849L29.0769 10.1835L28.75 9.16822L30.2114 13.6821L26.3663 10.8849L22.5213 13.6821L23.9935 9.16159L20.1484 6.36441L24.9028 6.37307L26.375 1.85254L27.8385 6.37307Z"
                                  fill="white"/>
                            <rect x="36.7402" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M46.2077 6.37307H50.962L47.117 9.16159L47.117 9.16159L44.7441 10.8849L47.4461 10.1835L47.1191 9.16822L48.5805 13.6821L44.7355 10.8849L40.8904 13.6821L42.3626 9.16159L38.5176 6.36441L43.2719 6.37307L44.7441 1.85254L46.2077 6.37307Z"
                                  fill="white"/>
                            <rect x="55.1113" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M64.5788 6.37307H69.3331L65.4881 9.16159L65.4883 9.16217L63.1162 10.8849L65.8181 10.1835L65.6738 9.7352L66.9516 13.6821L63.1066 10.8849L59.2615 13.6821L60.7337 9.16159L56.8887 6.36441L61.643 6.37307L63.1152 1.85254L64.5788 6.37307Z"
                                  fill="white"/>
                            <rect x="73.4824" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M82.9499 6.37307H87.7042L83.8592 9.16159L83.8592 9.16159L81.4863 10.8849L84.1883 10.1835L83.8613 9.16822L85.3227 13.6821L81.4777 10.8849L77.6326 13.6821L79.1048 9.16159L75.2598 6.36441L80.0141 6.37307L81.4863 1.85254L82.9499 6.37307Z"
                                  fill="white"/>
                        </svg>
                        <div class="quiz__reviews-slider-item-name-wrapp">
                            <h5>
                                Training structure is brilliant
                            </h5>
                            <p class="quiz__reviews-slider-item-name">
                                Claire_Ugalde
                            </p>
                        </div>
                        <div class="quiz__reviews-slider-item-text">
                            The way the training is broken down is excellent. I’ve had one on one training at my house and have found it less effective than this course and would highly recommend it.
                        </div>
                    </div>
                </li>
                <li class="quiz__reviews-slider-item swiper-slide">
                    <div class="quiz__reviews-slider-item-text-wrapp">
                        <svg width="90" height="16" viewBox="0 0 90 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M9.46744 6.37307H14.2218L10.3768 9.16159L10.3767 9.16159L8.00391 10.8849L10.7058 10.1835L10.3789 9.16822L11.8403 13.6821L7.99524 10.8849L4.15019 13.6821L5.62239 9.16159L1.77734 6.36441L6.53169 6.37307L8.0039 1.85254L9.46744 6.37307Z"
                                  fill="white"/>
                            <rect x="18.3711" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M27.8385 6.37307H32.5929L28.7478 9.16159L28.7478 9.16159L26.375 10.8849L29.0769 10.1835L28.75 9.16822L30.2114 13.6821L26.3663 10.8849L22.5213 13.6821L23.9935 9.16159L20.1484 6.36441L24.9028 6.37307L26.375 1.85254L27.8385 6.37307Z"
                                  fill="white"/>
                            <rect x="36.7402" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M46.2077 6.37307H50.962L47.117 9.16159L47.117 9.16159L44.7441 10.8849L47.4461 10.1835L47.1191 9.16822L48.5805 13.6821L44.7355 10.8849L40.8904 13.6821L42.3626 9.16159L38.5176 6.36441L43.2719 6.37307L44.7441 1.85254L46.2077 6.37307Z"
                                  fill="white"/>
                            <rect x="55.1113" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M64.5788 6.37307H69.3331L65.4881 9.16159L65.4883 9.16217L63.1162 10.8849L65.8181 10.1835L65.6738 9.7352L66.9516 13.6821L63.1066 10.8849L59.2615 13.6821L60.7337 9.16159L56.8887 6.36441L61.643 6.37307L63.1152 1.85254L64.5788 6.37307Z"
                                  fill="white"/>
                            <rect x="73.4824" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M82.9499 6.37307H87.7042L83.8592 9.16159L83.8592 9.16159L81.4863 10.8849L84.1883 10.1835L83.8613 9.16822L85.3227 13.6821L81.4777 10.8849L77.6326 13.6821L79.1048 9.16159L75.2598 6.36441L80.0141 6.37307L81.4863 1.85254L82.9499 6.37307Z"
                                  fill="white"/>
                        </svg>
                        <div class="quiz__reviews-slider-item-name-wrapp">
                            <h5>
                                Excellent training course
                            </h5>
                            <p class="quiz__reviews-slider-item-name">
                                Diego Fernandez Jr
                            </p>
                        </div>
                        <p class="quiz__reviews-slider-item-text">
                            Excellent training course, very detailed and easy to understand. What I like about this course is they emphasize that training a dog requires patience and understanding the process. I’m still on week
                            one, but their approach seems easy to follow.
                            Can’t wait to see the end results…
                        </p>
                    </div>
                </li>
            </ul>
        </div>
    </div>
    <div class="container email" id="resultStepTwo">
        <h2 class="quiz__reviews-loading-title">
            Enter your email to get <span class="nameInsertSelector">your dog</span>’s personalized obedience plan
        </h2>
        <p class="quiz__result-label" id="emailText"></p>
        <div class="quiz__result-box">
            <ul id="autoComplete" class="email-autocomplete email-autocomplete-scroll"></ul>
            <input class="quiz__result-input" name="text" type="text" placeholder="Enter your email to get your plan" id="email" autocomplete="off">
        </div>
        <div class="quiz__result-text-box">
            <svg class="quiz__result-text-icon" width="21" height="26" viewBox="0 0 21 26" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path opacity="0.45"
                      d="M20.1944 9.75H18.5278V7.58332C18.5278 3.40184 15.0387 0 10.75 0C6.46125 0 2.97224 3.40184 2.97224 7.58332V9.75H1.30557C0.99849 9.75 0.75 9.99228 0.75 10.2917V23.8334C0.75 25.0283 1.74661 26 2.97224 26H18.5278C19.7534 26 20.75 25.0283 20.75 23.8333V10.2917C20.75 9.99228 20.5015 9.75 20.1944 9.75ZM12.4134 21.0652C12.4308 21.2181 12.3804 21.3715 12.2751 21.4863C12.1698 21.6011 12.019 21.6667 11.8611 21.6667H9.63891C9.48104 21.6667 9.33021 21.6011 9.22495 21.4863C9.11969 21.3715 9.06922 21.2181 9.08661 21.0652L9.43708 17.9925C8.86797 17.5888 8.52781 16.9504 8.52781 16.25C8.52781 15.0551 9.52443 14.0833 10.7501 14.0833C11.9757 14.0833 12.9723 15.055 12.9723 16.25C12.9723 16.9504 12.6321 17.5888 12.063 17.9925L12.4134 21.0652ZM15.1944 9.75H6.30557V7.58332C6.30557 5.19396 8.29938 3.25 10.75 3.25C13.2006 3.25 15.1944 5.19396 15.1944 7.58332V9.75Z"
                      fill="#343434"/>
            </svg>
            <p class="quiz__result-text">
                We protect your privacy and are committed to protecting your personal data. We never send spam emails, only relevant information.
            </p>
        </div>
        <div class="quiz__btn-box">
            <button class="quiz__btn js-btn email-submit" id="email-submit" disabled>
                <span></span>
                Continue
            </button>
        </div>
    </div>
    <div class="container" id="resultStepEmailSubscription">
        <h3 class="quiz__email-subscription-title">
            Do you want to receive emails with <span>special offers</span>, dog training tips, advice and <span>free gifts</span>?
        </h3>
        <div class="quiz__email-subscription-btn-box">
            <button class="quiz__btn triggerStep" data-event-value="Apply newsletter" data-response-value="0">
                <span></span>
                Yes, I'm in!
            </button>
            <button class="quiz__email-subscription-label triggerStep" data-event-value="Reject newsletter" data-response-value="1">
                I'm not interested
            </button>
        </div>
    </div>
    <div class="container graph " id="resultStepThree">
        <h2 class="quiz__reviews-loading-title">
            <span class="nameInsertSelector">Your dog</span>’s obedience {% if getSplitValue('pc_qualityTime') == 2 %}level{% endif %}
        </h2>
        <div class="quiz__reviews-loading-svg-inner">
            <svg class="quiz__reviews-loading-svg" width="343" height="227" viewBox="0 0 343 227" fill="none" xmlns="http://www.w3.org/2000/svg">
                <line x1="22.5" y1="0.5" x2="318.62" y2="0.499974" stroke="#E8E8E8"/>
                <line x1="22.5" y1="26.5" x2="318.62" y2="26.5" stroke="#E8E8E8"/>
                <line x1="22.5" y1="52.5" x2="318.62" y2="52.5" stroke="#E8E8E8"/>
                <line x1="22.5" y1="78.5" x2="318.62" y2="78.5" stroke="#E8E8E8"/>
                <line x1="22.5" y1="104.5" x2="318.62" y2="104.5" stroke="#E8E8E8"/>
                <line x1="22.5" y1="130.5" x2="318.62" y2="130.5" stroke="#E8E8E8"/>
                <line x1="22.5" y1="156.5" x2="318.62" y2="156.5" stroke="#E8E8E8"/>
                <g opacity="0.3" id="resultGraphBg" class="result-graph-bg">
                    <path d="M318.5 16.9996C302.052 20.9767 285.975 24.5721 270.321 28.073C224.14 38.4011 181.638 47.9063 144.134 63.9597C93.4774 85.643 51.9875 119.261 23.049 183H318.5V16.9996Z" fill="#D9D9D9"/>
                    <path d="M318.5 16.9996C302.052 20.9767 285.975 24.5721 270.321 28.073C224.14 38.4011 181.638 47.9063 144.134 63.9597C93.4774 85.643 51.9875 119.261 23.049 183H318.5V16.9996Z"
                          fill="url(#paint0_linear_2819_20021)"/>
                </g>
                <line x1="22.5" y1="182.5" x2="319.56" y2="182.5" stroke="#E8E8E8"/>
                <line x1="22.9399" y1="189" x2="22.9399" y2="183" stroke="#E8E8E8"/>
                <line x1="319.06" y1="189" x2="319.06" y2="183" stroke="#E8E8E8"/>
                <line x1="217.532" y1="189" x2="217.532" y2="183" stroke="#E8E8E8"/>
                <line x1="129.167" y1="189" x2="129.167" y2="183" stroke="#E8E8E8"/>
                <path d="M23 183C39.6001 154.958 64.7246 78.6218 196.628 45.9062C328.532 13.1907 318.841 15.0185 318.841 15.0185" stroke="url(#paint1_linear_2819_20021)" stroke-width="5" class="quiz__based-graph-line"
                      stroke-linecap="round" stroke-linejoin="round"/>
                <g filter="url(#filter0_d_2819_20021)" class="result-graph-goal">
                    <rect x="277" y="31" width="57" height="32" rx="8" fill="#323334"/>
                    <path d="M293.134 45L296.374 37.95H297.624L300.864 45H299.304L298.644 43.47H295.354L294.704 45H293.134ZM296.984 39.6L295.874 42.26H298.134L297.004 39.6H296.984ZM301.606 45V41.22H300.676V40.09H301.606C301.62 39.3967 301.82 38.8767 302.206 38.53C302.6 38.1767 303.196 37.9767 303.996 37.93L304.546 37.89L304.636 38.99L304.166 39.02C303.78 39.04 303.506 39.1233 303.346 39.27C303.193 39.41 303.116 39.6267 303.116 39.92V40.09H304.396V41.22H303.116V45H301.606ZM307.152 45.11C305.772 45.11 305.082 44.44 305.082 43.1V41.22H304.152V40.09H305.082V38.65H306.592V40.09H308.042V41.22H306.592V43.04C306.592 43.32 306.655 43.53 306.782 43.67C306.915 43.81 307.125 43.88 307.412 43.88C307.499 43.88 307.589 43.87 307.682 43.85C307.782 43.83 307.889 43.8033 308.002 43.77L308.222 44.87C308.082 44.9433 307.915 45 307.722 45.04C307.529 45.0867 307.339 45.11 307.152 45.11ZM311.286 45.11C310.706 45.11 310.206 45.0067 309.786 44.8C309.373 44.5867 309.053 44.29 308.826 43.91C308.606 43.5233 308.496 43.07 308.496 42.55C308.496 42.0433 308.603 41.6 308.816 41.22C309.036 40.8333 309.333 40.5333 309.706 40.32C310.086 40.1 310.523 39.99 311.016 39.99C311.73 39.99 312.296 40.2167 312.716 40.67C313.136 41.1167 313.346 41.7233 313.346 42.49V42.87H309.936C309.99 43.2567 310.13 43.54 310.356 43.72C310.59 43.8933 310.91 43.98 311.316 43.98C311.583 43.98 311.853 43.94 312.126 43.86C312.4 43.78 312.646 43.6567 312.866 43.49L313.266 44.5C313.02 44.6867 312.716 44.8367 312.356 44.95C312.003 45.0567 311.646 45.11 311.286 45.11ZM311.076 40.99C310.756 40.99 310.496 41.0867 310.296 41.28C310.103 41.4733 309.983 41.7433 309.936 42.09H312.096C312.056 41.3567 311.716 40.99 311.076 40.99ZM314.225 45V40.09H315.705V40.94C315.938 40.3667 316.438 40.05 317.205 39.99L317.645 39.96L317.735 41.23L316.885 41.32C316.138 41.3933 315.765 41.7733 315.765 42.46V45H314.225ZM286.123 55.11C285.63 55.11 285.16 55.04 284.713 54.9C284.266 54.76 283.9 54.57 283.613 54.33L284.103 53.16C284.696 53.6 285.35 53.82 286.063 53.82C286.876 53.82 287.283 53.5167 287.283 52.91C287.283 52.35 286.903 52.07 286.143 52.07H284.963V50.82H285.993C286.326 50.82 286.59 50.7433 286.783 50.59C286.976 50.4367 287.073 50.2233 287.073 49.95C287.073 49.4033 286.716 49.13 286.003 49.13C285.336 49.13 284.743 49.3533 284.223 49.8L283.733 48.66C284.026 48.4 284.386 48.2 284.813 48.06C285.24 47.9133 285.683 47.84 286.143 47.84C286.903 47.84 287.496 48.01 287.923 48.35C288.35 48.69 288.563 49.1567 288.563 49.75C288.563 50.1167 288.47 50.44 288.283 50.72C288.096 51 287.836 51.2033 287.503 51.33C287.903 51.4433 288.213 51.6533 288.433 51.96C288.66 52.26 288.773 52.6233 288.773 53.05C288.773 53.69 288.536 54.1933 288.063 54.56C287.59 54.9267 286.943 55.11 286.123 55.11ZM292.672 55V50.09H294.152V50.8C294.299 50.5467 294.499 50.35 294.752 50.21C295.012 50.0633 295.312 49.99 295.652 49.99C295.999 49.99 296.292 50.0667 296.532 50.22C296.779 50.3733 296.966 50.6067 297.092 50.92C297.246 50.6267 297.466 50.4 297.752 50.24C298.046 50.0733 298.369 49.99 298.722 49.99C299.282 49.99 299.699 50.16 299.972 50.5C300.246 50.8333 300.382 51.35 300.382 52.05V55H298.872V52.1C298.872 51.7733 298.819 51.5367 298.712 51.39C298.612 51.2367 298.439 51.16 298.192 51.16C297.906 51.16 297.682 51.26 297.522 51.46C297.362 51.66 297.282 51.9433 297.282 52.31V55H295.772V52.1C295.772 51.7733 295.719 51.5367 295.612 51.39C295.506 51.2367 295.332 51.16 295.092 51.16C294.806 51.16 294.582 51.26 294.422 51.46C294.262 51.66 294.182 51.9433 294.182 52.31V55H292.672ZM303.899 55.11C303.372 55.11 302.912 55.0067 302.519 54.8C302.132 54.5867 301.832 54.29 301.619 53.91C301.406 53.5233 301.299 53.0667 301.299 52.54C301.299 52.02 301.406 51.57 301.619 51.19C301.832 50.8033 302.132 50.5067 302.519 50.3C302.912 50.0933 303.372 49.99 303.899 49.99C304.426 49.99 304.882 50.0933 305.269 50.3C305.662 50.5067 305.966 50.8033 306.179 51.19C306.399 51.57 306.509 52.02 306.509 52.54C306.509 53.0667 306.399 53.5233 306.179 53.91C305.966 54.29 305.662 54.5867 305.269 54.8C304.882 55.0067 304.426 55.11 303.899 55.11ZM303.899 53.98C304.219 53.98 304.479 53.8633 304.679 53.63C304.886 53.3967 304.989 53.0333 304.989 52.54C304.989 52.0533 304.886 51.6967 304.679 51.47C304.479 51.2367 304.219 51.12 303.899 51.12C303.579 51.12 303.319 51.2367 303.119 51.47C302.919 51.6967 302.819 52.0533 302.819 52.54C302.819 53.0333 302.919 53.3967 303.119 53.63C303.319 53.8633 303.579 53.98 303.899 53.98ZM307.428 55V50.09H308.908V50.81C309.074 50.5433 309.298 50.34 309.578 50.2C309.858 50.06 310.171 49.99 310.518 49.99C311.098 49.99 311.531 50.16 311.818 50.5C312.104 50.8333 312.248 51.35 312.248 52.05V55H310.738V52.12C310.738 51.78 310.674 51.5367 310.548 51.39C310.421 51.2367 310.234 51.16 309.988 51.16C309.668 51.16 309.411 51.26 309.218 51.46C309.031 51.66 308.938 51.9267 308.938 52.26V55H307.428ZM315.785 55.11C314.405 55.11 313.715 54.44 313.715 53.1V51.22H312.785V50.09H313.715V48.65H315.225V50.09H316.675V51.22H315.225V53.04C315.225 53.32 315.288 53.53 315.415 53.67C315.548 53.81 315.758 53.88 316.045 53.88C316.132 53.88 316.222 53.87 316.315 53.85C316.415 53.83 316.522 53.8033 316.635 53.77L316.855 54.87C316.715 54.9433 316.548 55 316.355 55.04C316.162 55.0867 315.972 55.11 315.785 55.11ZM317.418 55V47.95H318.928V50.76C319.101 50.5067 319.325 50.3167 319.598 50.19C319.871 50.0567 320.175 49.99 320.508 49.99C321.661 49.99 322.238 50.6767 322.238 52.05V55H320.728V52.12C320.728 51.78 320.665 51.5367 320.538 51.39C320.411 51.2367 320.225 51.16 319.978 51.16C319.658 51.16 319.401 51.26 319.208 51.46C319.021 51.66 318.928 51.9267 318.928 52.26V55H317.418ZM325.245 55.11C324.812 55.11 324.408 55.06 324.035 54.96C323.662 54.86 323.352 54.7233 323.105 54.55L323.465 53.57C323.712 53.7233 323.992 53.8467 324.305 53.94C324.625 54.0267 324.942 54.07 325.255 54.07C325.535 54.07 325.742 54.0267 325.875 53.94C326.008 53.8467 326.075 53.7267 326.075 53.58C326.075 53.3467 325.905 53.2 325.565 53.14L324.515 52.95C324.095 52.8767 323.775 52.7267 323.555 52.5C323.335 52.2733 323.225 51.9767 323.225 51.61C323.225 51.2767 323.318 50.99 323.505 50.75C323.692 50.51 323.948 50.3233 324.275 50.19C324.602 50.0567 324.978 49.99 325.405 49.99C325.758 49.99 326.102 50.0367 326.435 50.13C326.768 50.2167 327.055 50.3567 327.295 50.55L326.915 51.52C326.715 51.3733 326.475 51.2533 326.195 51.16C325.922 51.0667 325.665 51.02 325.425 51.02C325.125 51.02 324.908 51.07 324.775 51.17C324.642 51.2633 324.575 51.3833 324.575 51.53C324.575 51.7633 324.732 51.91 325.045 51.97L326.095 52.16C326.528 52.2333 326.858 52.38 327.085 52.6C327.312 52.8133 327.425 53.1067 327.425 53.48C327.425 53.9933 327.225 54.3933 326.825 54.68C326.425 54.9667 325.898 55.11 325.245 55.11Z"
                          fill="white"/>
                    <path d="M321.293 25.1895L325.5 31.5L315 31.5L319.655 25.1529C320.065 24.5928 320.908 24.6117 321.293 25.1895Z" fill="#323334"/>
                </g>
                <g filter="url(#filter1_d_2819_20021)">
                    <circle cx="23.0503" cy="180.55" r="7.55031" fill="white"/>
                    <circle cx="23.0503" cy="180.55" r="7.05031" stroke="#2D2D2D" stroke-opacity="0.12"/>
                </g>
                <g filter="url(#filter2_d_2819_20021)">
                    <rect x="6" y="141" width="34" height="22" rx="8" fill="#D3D5DA"/>
                    <path d="M12.753 155V147.95H13.903L17.423 152.44V147.95H18.863V155H17.723L14.193 150.5V155H12.753ZM22.5123 155.11C21.9856 155.11 21.5256 155.007 21.1323 154.8C20.7456 154.587 20.4456 154.29 20.2323 153.91C20.0189 153.523 19.9123 153.067 19.9123 152.54C19.9123 152.02 20.0189 151.57 20.2323 151.19C20.4456 150.803 20.7456 150.507 21.1323 150.3C21.5256 150.093 21.9856 149.99 22.5123 149.99C23.0389 149.99 23.4956 150.093 23.8823 150.3C24.2756 150.507 24.5789 150.803 24.7923 151.19C25.0123 151.57 25.1223 152.02 25.1223 152.54C25.1223 153.067 25.0123 153.523 24.7923 153.91C24.5789 154.29 24.2756 154.587 23.8823 154.8C23.4956 155.007 23.0389 155.11 22.5123 155.11ZM22.5123 153.98C22.8323 153.98 23.0923 153.863 23.2923 153.63C23.4989 153.397 23.6023 153.033 23.6023 152.54C23.6023 152.053 23.4989 151.697 23.2923 151.47C23.0923 151.237 22.8323 151.12 22.5123 151.12C22.1923 151.12 21.9323 151.237 21.7323 151.47C21.5323 151.697 21.4323 152.053 21.4323 152.54C21.4323 153.033 21.5323 153.397 21.7323 153.63C21.9323 153.863 22.1923 153.98 22.5123 153.98ZM27.3138 155L25.3137 150.09H26.8438L28.0138 153.24L29.1838 150.09H30.1838L31.3538 153.28L32.5238 150.09H33.9738L31.9938 155H30.7138L29.6338 152.21L28.5838 155H27.3138Z"
                          fill="#828286"/>
                    <path d="M22.207 169.31L18 163L28.5 163L23.8454 169.347C23.4347 169.907 22.5922 169.888 22.207 169.31Z" fill="#D3D5DA"/>
                </g>
                <g filter="url(#filter3_d_2819_20021)" id="resultGraphDot" class="result-graph-dot">
                    <circle cx="320.55" cy="14.5503" r="7.55031" fill="white"/>
                    <circle cx="320.55" cy="14.5503" r="7.05031" stroke="#2D2D2D" stroke-opacity="0.12"/>
                </g>
                <defs>
                    <filter id="filter0_d_2819_20021" x="273" y="24.7441" width="65" height="46.2559" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                        <feOffset dy="4"/>
                        <feGaussianBlur stdDeviation="2"/>
                        <feComposite in2="hardAlpha" operator="out"/>
                        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.07 0"/>
                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2819_20021"/>
                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2819_20021" result="shape"/>
                    </filter>
                    <filter id="filter1_d_2819_20021" x="7.5" y="169.314" width="31.1006" height="31.1006" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                        <feOffset dy="4.31447"/>
                        <feGaussianBlur stdDeviation="4"/>
                        <feComposite in2="hardAlpha" operator="out"/>
                        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2819_20021"/>
                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2819_20021" result="shape"/>
                    </filter>
                    <filter id="filter2_d_2819_20021" x="2" y="141" width="42" height="36.7559" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                        <feOffset dy="4"/>
                        <feGaussianBlur stdDeviation="2"/>
                        <feComposite in2="hardAlpha" operator="out"/>
                        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.07 0"/>
                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2819_20021"/>
                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2819_20021" result="shape"/>
                    </filter>
                    <filter id="filter3_d_2819_20021" x="305" y="3.31447" width="31.1006" height="31.1006" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                        <feOffset dy="4.31447"/>
                        <feGaussianBlur stdDeviation="4"/>
                        <feComposite in2="hardAlpha" operator="out"/>
                        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2819_20021"/>
                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2819_20021" result="shape"/>
                    </filter>
                    <linearGradient id="paint0_linear_2819_20021" x1="311.509" y1="181.708" x2="24.5863" y2="181.708" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#1998CD"/>
                        <stop offset="0.549638" stop-color="#F5E088"/>
                        <stop offset="1" stop-color="#FF574C"/>
                    </linearGradient>
                    <linearGradient id="paint1_linear_2819_20021" x1="319" y1="182.999" x2="23" y2="182.999" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#1998CD"/>
                        <stop offset="0.549638" stop-color="#F5E088"/>
                        <stop offset="1" stop-color="#FF574C"/>
                    </linearGradient>
                </defs>
            </svg>
            <div class="quiz__reviews-loading-svg-date-wrapp">
                <p class="quiz__reviews-loading-svg-date">
                    Now
                </p>
                <p class="quiz__reviews-loading-svg-date" id="monthAfter1">
                    month 1
                </p>
                <p class="quiz__reviews-loading-svg-date" id="monthAfter2">
                    month 2
                </p>
                <p class="quiz__reviews-loading-svg-date" id="monthAfter3">
                    month 3
                </p>
            </div>
            <p class="quiz__reviews-loading-svg-date-text">
                This chart is for illustrative purposes only
            </p>
        </div>
        <p class="quiz__reviews-loading-subtext">
            Your <span class="puppyInsertSelector" data-puppy="puppy">dog</span>’s <span>obedience plan</span> is ready
        </p>
        <div class="quiz__btn-box">
            <button class="quiz__btn js-btn email-submit" id="redirect-to-landing-btn" data-landing-url="/questionary/landing-4">
                <span></span>
                Continue
            </button>
        </div>
    </div>
    <div class="quiz__redirect-loader" id="resultLoader">
        <div class="preload">
            <span></span>
        </div>
    </div>
</div>

<div class="quiz__popup-wrapp" id="quizPopUpProblem">
    <div class="quiz__popup-bg"></div>

    <div class="quiz__popup" id="magicProblemPopup">
        <div class="quiz__popup-box">
            <h4 class="quiz__popup-title">
                One more question
            </h4>
            {% if quizData.isPuppyFunnel %}
                <div class="quiz__popup-img-box">
                    <img id="resultPopup" class="quiz__popup-img" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1690991083/dog-training/img/Rectangle_17.jpg" alt="">
                </div>
                <div class="quiz__popup-text">
                    <span class="ageInsertSelector">Puppies</span>
                    usually have biting & nipping issues.
                    Do you have this problem?
                </div>
            {% else %}
                <div class="quiz__popup-img-box">
                    <img id="resultPopup" class="quiz__popup-img" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1690985584/dog-training/img/Rectangle_14.jpg" alt="">
                </div>
                <div class="quiz__popup-text">
                    <b class="ageInsertSelector capitalize">adult</b> <b>dogs</b> sometimes can show territorial aggression.
                    Have you ever had this problem?
                </div>
            {% endif %}
            <ul class="quiz__popup-btn-list">
                <li class="quiz__popup-btn-item quizPopUpProblemBtn" data-event="No">
                    No
                </li>
                <li class="quiz__popup-btn-item quizPopUpProblemBtn" data-event="Yes">
                    Yes
                </li>
            </ul>
        </div>
    </div>
</div>
<div class="quiz__popup-wrapp" id="quizPopUpObedience">
    <div class="quiz__popup-bg"></div>

    <div class="quiz__popup" id="magicObediencePopup">
        <div class="quiz__popup-box">
            <h4 class="quiz__popup-title">
                Finalizing your plan
            </h4>
            <div class="quiz__popup-img-box">
                <img id="resultPopup" class="quiz__popup-img" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1690991083/dog-training/img/Rectangle_16.jpg" alt="">
            </div>
            <div class="quiz__popup-text">
                Has <span class="nameInsertSelector">your dog</span> ever had obedience training?
            </div>
            <ul class="quiz__popup-btn-list">
                <li class="quiz__popup-btn-item quizPopUpObedienceBtn" data-event="No">
                    No
                </li>
                <li class="quiz__popup-btn-item quizPopUpObedienceBtn" data-event="Yes">
                    Yes
                </li>
            </ul>
        </div>
    </div>
</div>
