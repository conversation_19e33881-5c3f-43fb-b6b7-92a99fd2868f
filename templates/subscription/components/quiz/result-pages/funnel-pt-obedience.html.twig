<div class="quiz__index quiz__result skipQuiz magic" id="block{{ blockId }}" data-id="{{ blockId }}" data-result="reviews">
    <div class="container " id="resultStepOne">
        <div id="circleBar" class="progressbar reviews">
            <div class="progressbar__shadow"></div>
        </div>
        <p class="quiz__reviews-loading-subtitle" id="replaceSubtitle">
            Criando o plano de treinamento personalizado d<span class="genderPrefixInsertSelector" data-prefix="a">o</span> <span class="nameInsertSelector">seu cão</span>...
        </p>
        <div class="quiz__hr">
        </div>
        <div class="quiz__reviews-loading-title-red-box">
            <h3 class="quiz__reviews-loading-title-red">
                170 mil donos de cães
            </h3>
        </div>
        <p class="quiz__reviews-loading-text">
            escolheram o PawChamp
        </p>
        <div class="quiz__reviews-slider custom-result">
            <ul class="swiper-wrapper">
                <li class="quiz__reviews-slider-item swiper-slide">
                    <div class="quiz__reviews-slider-item-text-wrapp">
                        <svg width="90" height="16" viewBox="0 0 90 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M9.46744 6.37307H14.2218L10.3768 9.16159L10.3767 9.16159L8.00391 10.8849L10.7058 10.1835L10.3789 9.16822L11.8403 13.6821L7.99524 10.8849L4.15019 13.6821L5.62239 9.16159L1.77734 6.36441L6.53169 6.37307L8.0039 1.85254L9.46744 6.37307Z"
                                  fill="white"/>
                            <rect x="18.3711" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M27.8385 6.37307H32.5929L28.7478 9.16159L28.7478 9.16159L26.375 10.8849L29.0769 10.1835L28.75 9.16822L30.2114 13.6821L26.3663 10.8849L22.5213 13.6821L23.9935 9.16159L20.1484 6.36441L24.9028 6.37307L26.375 1.85254L27.8385 6.37307Z"
                                  fill="white"/>
                            <rect x="36.7402" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M46.2077 6.37307H50.962L47.117 9.16159L47.117 9.16159L44.7441 10.8849L47.4461 10.1835L47.1191 9.16822L48.5805 13.6821L44.7355 10.8849L40.8904 13.6821L42.3626 9.16159L38.5176 6.36441L43.2719 6.37307L44.7441 1.85254L46.2077 6.37307Z"
                                  fill="white"/>
                            <rect x="55.1113" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M64.5788 6.37307H69.3331L65.4881 9.16159L65.4883 9.16217L63.1162 10.8849L65.8181 10.1835L65.6738 9.7352L66.9516 13.6821L63.1066 10.8849L59.2615 13.6821L60.7337 9.16159L56.8887 6.36441L61.643 6.37307L63.1152 1.85254L64.5788 6.37307Z"
                                  fill="white"/>
                            <rect x="73.4824" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M82.9499 6.37307H87.7042L83.8592 9.16159L83.8592 9.16159L81.4863 10.8849L84.1883 10.1835L83.8613 9.16822L85.3227 13.6821L81.4777 10.8849L77.6326 13.6821L79.1048 9.16159L75.2598 6.36441L80.0141 6.37307L81.4863 1.85254L82.9499 6.37307Z"
                                  fill="white"/>
                        </svg>
                        <div class="quiz__reviews-slider-item-name-wrapp">
                            <h5>
                                Óptimas técnicas de reforço positivo
                            </h5>
                            <p class="quiz__reviews-slider-item-name">
                                Dawg Daddy
                            </p>
                        </div>
                        <div class="quiz__reviews-slider-item-text">
                            Este é um ótimo programa de treino que começa logo na base, o que é fundamental. Ele sobe a escada com técnicas de reforço positivo que realmente funcionam. Tenho visto óptimos resultados com os meus 2 cães.
                        </div>
                    </div>
                </li>
                <li class="quiz__reviews-slider-item swiper-slide">
                    <div class="quiz__reviews-slider-item-text-wrapp">
                        <svg width="90" height="16" viewBox="0 0 90 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M9.46744 6.37307H14.2218L10.3768 9.16159L10.3767 9.16159L8.00391 10.8849L10.7058 10.1835L10.3789 9.16822L11.8403 13.6821L7.99524 10.8849L4.15019 13.6821L5.62239 9.16159L1.77734 6.36441L6.53169 6.37307L8.0039 1.85254L9.46744 6.37307Z"
                                  fill="white"/>
                            <rect x="18.3711" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M27.8385 6.37307H32.5929L28.7478 9.16159L28.7478 9.16159L26.375 10.8849L29.0769 10.1835L28.75 9.16822L30.2114 13.6821L26.3663 10.8849L22.5213 13.6821L23.9935 9.16159L20.1484 6.36441L24.9028 6.37307L26.375 1.85254L27.8385 6.37307Z"
                                  fill="white"/>
                            <rect x="36.7402" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M46.2077 6.37307H50.962L47.117 9.16159L47.117 9.16159L44.7441 10.8849L47.4461 10.1835L47.1191 9.16822L48.5805 13.6821L44.7355 10.8849L40.8904 13.6821L42.3626 9.16159L38.5176 6.36441L43.2719 6.37307L44.7441 1.85254L46.2077 6.37307Z"
                                  fill="white"/>
                            <rect x="55.1113" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M64.5788 6.37307H69.3331L65.4881 9.16159L65.4883 9.16217L63.1162 10.8849L65.8181 10.1835L65.6738 9.7352L66.9516 13.6821L63.1066 10.8849L59.2615 13.6821L60.7337 9.16159L56.8887 6.36441L61.643 6.37307L63.1152 1.85254L64.5788 6.37307Z"
                                  fill="white"/>
                            <rect x="73.4824" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M82.9499 6.37307H87.7042L83.8592 9.16159L83.8592 9.16159L81.4863 10.8849L84.1883 10.1835L83.8613 9.16822L85.3227 13.6821L81.4777 10.8849L77.6326 13.6821L79.1048 9.16159L75.2598 6.36441L80.0141 6.37307L81.4863 1.85254L82.9499 6.37307Z"
                                  fill="white"/>
                        </svg>
                        <div class="quiz__reviews-slider-item-name-wrapp">
                            <h5>
                                A estrutura de formação é brilhante
                            </h5>
                            <p class="quiz__reviews-slider-item-name">
                                Claire_Ugalde
                            </p>
                        </div>
                        <div class="quiz__reviews-slider-item-text">
                            A forma como a formação é dividida é excelente. Já tive uma formação individual em minha casa e achei-a menos eficaz do que este curso e recomendo-a vivamente.
                        </div>
                    </div>
                </li>
                <li class="quiz__reviews-slider-item swiper-slide">
                    <div class="quiz__reviews-slider-item-text-wrapp">
                        <svg width="90" height="16" viewBox="0 0 90 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M9.46744 6.37307H14.2218L10.3768 9.16159L10.3767 9.16159L8.00391 10.8849L10.7058 10.1835L10.3789 9.16822L11.8403 13.6821L7.99524 10.8849L4.15019 13.6821L5.62239 9.16159L1.77734 6.36441L6.53169 6.37307L8.0039 1.85254L9.46744 6.37307Z"
                                  fill="white"/>
                            <rect x="18.3711" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M27.8385 6.37307H32.5929L28.7478 9.16159L28.7478 9.16159L26.375 10.8849L29.0769 10.1835L28.75 9.16822L30.2114 13.6821L26.3663 10.8849L22.5213 13.6821L23.9935 9.16159L20.1484 6.36441L24.9028 6.37307L26.375 1.85254L27.8385 6.37307Z"
                                  fill="white"/>
                            <rect x="36.7402" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M46.2077 6.37307H50.962L47.117 9.16159L47.117 9.16159L44.7441 10.8849L47.4461 10.1835L47.1191 9.16822L48.5805 13.6821L44.7355 10.8849L40.8904 13.6821L42.3626 9.16159L38.5176 6.36441L43.2719 6.37307L44.7441 1.85254L46.2077 6.37307Z"
                                  fill="white"/>
                            <rect x="55.1113" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M64.5788 6.37307H69.3331L65.4881 9.16159L65.4883 9.16217L63.1162 10.8849L65.8181 10.1835L65.6738 9.7352L66.9516 13.6821L63.1066 10.8849L59.2615 13.6821L60.7337 9.16159L56.8887 6.36441L61.643 6.37307L63.1152 1.85254L64.5788 6.37307Z"
                                  fill="white"/>
                            <rect x="73.4824" width="16" height="16" fill="#00B67A"/>
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M82.9499 6.37307H87.7042L83.8592 9.16159L83.8592 9.16159L81.4863 10.8849L84.1883 10.1835L83.8613 9.16822L85.3227 13.6821L81.4777 10.8849L77.6326 13.6821L79.1048 9.16159L75.2598 6.36441L80.0141 6.37307L81.4863 1.85254L82.9499 6.37307Z"
                                  fill="white"/>
                        </svg>
                        <div class="quiz__reviews-slider-item-name-wrapp">
                            <h5>
                                Excelente curso de formação
                            </h5>
                            <p class="quiz__reviews-slider-item-name">
                                Diego Fernandez Jr
                            </p>
                        </div>
                        <p class="quiz__reviews-slider-item-text">
                            Excelente curso de treinamento, muito detalhado e fácil de entender. O que eu gosto neste curso é que eles enfatizam que treinar um cão requer paciência e compreensão do processo. Ainda estou na primeira semana, mas a abordagem deles parece fácil de seguir.
                            Mal posso esperar para ver os resultados finais...
                        </p>
                    </div>
                </li>
            </ul>
        </div>
    </div>
    <div class="container email" id="resultStepTwo">
        <h2 class="quiz__reviews-loading-title">
            Digite seu e-mail para obter o plano de obediência personalizado do <span class="nameInsertSelector">seu cão</span>
        </h2>
        <p class="quiz__result-label" id="emailText"></p>
        <div class="quiz__result-box">
            <ul id="autoComplete" class="email-autocomplete email-autocomplete-scroll"></ul>
            <input class="quiz__result-input" name="text" type="text" placeholder="Digite seu e-mail para obter seu plano" id="email" autocomplete="off">
        </div>
        <div class="quiz__result-text-box">
            <svg class="quiz__result-text-icon" width="21" height="26" viewBox="0 0 21 26" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path opacity="0.45"
                      d="M20.1944 9.75H18.5278V7.58332C18.5278 3.40184 15.0387 0 10.75 0C6.46125 0 2.97224 3.40184 2.97224 7.58332V9.75H1.30557C0.99849 9.75 0.75 9.99228 0.75 10.2917V23.8334C0.75 25.0283 1.74661 26 2.97224 26H18.5278C19.7534 26 20.75 25.0283 20.75 23.8333V10.2917C20.75 9.99228 20.5015 9.75 20.1944 9.75ZM12.4134 21.0652C12.4308 21.2181 12.3804 21.3715 12.2751 21.4863C12.1698 21.6011 12.019 21.6667 11.8611 21.6667H9.63891C9.48104 21.6667 9.33021 21.6011 9.22495 21.4863C9.11969 21.3715 9.06922 21.2181 9.08661 21.0652L9.43708 17.9925C8.86797 17.5888 8.52781 16.9504 8.52781 16.25C8.52781 15.0551 9.52443 14.0833 10.7501 14.0833C11.9757 14.0833 12.9723 15.055 12.9723 16.25C12.9723 16.9504 12.6321 17.5888 12.063 17.9925L12.4134 21.0652ZM15.1944 9.75H6.30557V7.58332C6.30557 5.19396 8.29938 3.25 10.75 3.25C13.2006 3.25 15.1944 5.19396 15.1944 7.58332V9.75Z"
                      fill="#343434"/>
            </svg>
            <p class="quiz__result-text">
                Protegemos sua privacidade e estamos comprometidos em proteger seus dados pessoais. Nunca enviamos e-mails de spam, apenas informações relevantes.
            </p>
        </div>
        <div class="quiz__btn-box">
            <button class="quiz__btn js-btn email-submit" id="email-submit" disabled>
                <span></span>
                Continuar
            </button>
        </div>
    </div>
    <div class="container " id="resultStepEmailSubscription">
        <h3 class="quiz__email-subscription-title">
            Deseja receber mensagens de correio eletrónico com <span>ofertas especiais</span>, dicas de treino de cães, conselhos e <span>ofertas gratuitas</span>?
        </h3>
        <div class="quiz__email-subscription-btn-box">
            <button class="quiz__btn triggerStep" data-event-value="Apply newsletter">
                <span></span>
                Sim, estou dentro!
            </button>
            <label class="quiz__email-subscription-label triggerStep" data-event-value="Reject newsletter">
                <input type="checkbox" class="quiz__email-subscription-input" id="disallowMarketingEmails">
                Não estou interessado
            </label>
        </div>
    </div>
    <div class="container graph " id="resultStepThree">
        <h2 class="quiz__reviews-loading-title">
            Obediência d<span class="genderPrefixInsertSelector" data-prefix="a">o</span> <span class="nameInsertSelector">seu cão</span>
        </h2>
        <div class="quiz__reviews-loading-svg-inner">
            <svg class="quiz__reviews-loading-svg"  width="343" height="227" viewBox="0 0 343 227" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M22.5 0.500026L318.62 0.5" stroke="#E8E8E8"/>
                <path d="M22.5 26.5H318.62" stroke="#E8E8E8"/>
                <path d="M22.5 52.5H318.62" stroke="#E8E8E8"/>
                <path d="M22.5 78.5H318.62" stroke="#E8E8E8"/>
                <path d="M22.5 104.5H318.62" stroke="#E8E8E8"/>
                <path d="M22.5 130.5H318.62" stroke="#E8E8E8"/>
                <path d="M22.5 156.5H318.62" stroke="#E8E8E8"/>
                <g opacity="0.3" id="resultGraphBg" class="result-graph-bg">
                    <path d="M318.5 16.9996C302.052 20.9767 285.975 24.5721 270.321 28.073C224.14 38.4011 181.638 47.9063 144.134 63.9597C93.4772 85.643 51.9873 119.261 23.0488 183H318.5V16.9996Z" fill="#D9D9D9"/>
                    <path d="M318.5 16.9996C302.052 20.9767 285.975 24.5721 270.321 28.073C224.14 38.4011 181.638 47.9063 144.134 63.9597C93.4772 85.643 51.9873 119.261 23.0488 183H318.5V16.9996Z" fill="url(#paint0_linear_2422_851)"/>
                </g>
                <path d="M22.5 182.5H319.56" stroke="#E8E8E8"/>
                <path d="M22.9399 189V183" stroke="#E8E8E8"/>
                <path d="M319.06 189V183" stroke="#E8E8E8"/>
                <path d="M217.532 189V183" stroke="#E8E8E8"/>
                <path d="M129.167 189V183" stroke="#E8E8E8"/>
                <path class="quiz__based-graph-line" d="M23 183C39.6001 154.958 64.7246 78.6218 196.628 45.9062C328.532 13.1907 318.841 15.0185 318.841 15.0185" stroke="url(#paint1_linear_2422_851)" stroke-width="5" stroke-linecap="round" stroke-linejoin="round"/>
                <path class="result-graph-goal" d="M318.5 31H277.5C273.082 31 269.5 34.5817 269.5 39V55C269.5 59.4183 273.082 63 277.5 63H318.5C322.918 63 326.5 59.4183 326.5 55V39C326.5 34.5817 322.918 31 318.5 31Z" fill="#323334"/>
                <path class="result-graph-goal" d="M280.407 45V37.95H283.167C284.367 37.95 285.297 38.2567 285.957 38.87C286.617 39.4767 286.947 40.3433 286.947 41.47C286.947 42.5967 286.617 43.4667 285.957 44.08C285.297 44.6933 284.367 45 283.167 45H280.407ZM281.957 43.73H283.077C284.577 43.73 285.327 42.9767 285.327 41.47C285.327 39.97 284.577 39.22 283.077 39.22H281.957V43.73ZM290.571 45.11C289.991 45.11 289.491 45.0067 289.071 44.8C288.658 44.5867 288.338 44.29 288.111 43.91C287.891 43.5233 287.781 43.07 287.781 42.55C287.781 42.0433 287.888 41.6 288.101 41.22C288.321 40.8333 288.618 40.5333 288.991 40.32C289.371 40.1 289.808 39.99 290.301 39.99C291.015 39.99 291.581 40.2167 292.001 40.67C292.421 41.1167 292.631 41.7233 292.631 42.49V42.87H289.221C289.275 43.2567 289.415 43.54 289.641 43.72C289.875 43.8933 290.195 43.98 290.601 43.98C290.868 43.98 291.138 43.94 291.411 43.86C291.685 43.78 291.931 43.6567 292.151 43.49L292.551 44.5C292.305 44.6867 292.001 44.8367 291.641 44.95C291.288 45.0567 290.931 45.11 290.571 45.11ZM290.361 40.99C290.041 40.99 289.781 41.0867 289.581 41.28C289.388 41.4733 289.268 41.7433 289.221 42.09H291.381C291.341 41.3567 291.001 40.99 290.361 40.99ZM293.51 46.8V40.09H294.99V40.82C295.123 40.5667 295.326 40.3667 295.6 40.22C295.88 40.0667 296.193 39.99 296.54 39.99C296.966 39.99 297.34 40.0933 297.66 40.3C297.986 40.5067 298.24 40.8 298.42 41.18C298.6 41.56 298.69 42.0133 298.69 42.54C298.69 43.0667 298.6 43.5233 298.42 43.91C298.24 44.29 297.986 44.5867 297.66 44.8C297.34 45.0067 296.966 45.11 296.54 45.11C296.213 45.11 295.913 45.04 295.64 44.9C295.373 44.76 295.166 44.5733 295.02 44.34V46.8H293.51ZM296.09 43.98C296.41 43.98 296.67 43.8633 296.87 43.63C297.07 43.3967 297.17 43.0333 297.17 42.54C297.17 42.0533 297.07 41.6967 296.87 41.47C296.67 41.2367 296.41 41.12 296.09 41.12C295.763 41.12 295.5 41.2367 295.3 41.47C295.1 41.6967 295 42.0533 295 42.54C295 43.0333 295.1 43.3967 295.3 43.63C295.5 43.8633 295.763 43.98 296.09 43.98ZM301.973 45.11C301.447 45.11 300.987 45.0067 300.593 44.8C300.207 44.5867 299.907 44.29 299.693 43.91C299.48 43.5233 299.373 43.0667 299.373 42.54C299.373 42.02 299.48 41.57 299.693 41.19C299.907 40.8033 300.207 40.5067 300.593 40.3C300.987 40.0933 301.447 39.99 301.973 39.99C302.5 39.99 302.957 40.0933 303.343 40.3C303.737 40.5067 304.04 40.8033 304.253 41.19C304.473 41.57 304.583 42.02 304.583 42.54C304.583 43.0667 304.473 43.5233 304.253 43.91C304.04 44.29 303.737 44.5867 303.343 44.8C302.957 45.0067 302.5 45.11 301.973 45.11ZM301.973 43.98C302.293 43.98 302.553 43.8633 302.753 43.63C302.96 43.3967 303.063 43.0333 303.063 42.54C303.063 42.0533 302.96 41.6967 302.753 41.47C302.553 41.2367 302.293 41.12 301.973 41.12C301.653 41.12 301.393 41.2367 301.193 41.47C300.993 41.6967 300.893 42.0533 300.893 42.54C300.893 43.0333 300.993 43.3967 301.193 43.63C301.393 43.8633 301.653 43.98 301.973 43.98ZM305.442 39.14V37.73H307.072V39.14H305.442ZM305.502 45V40.09H307.012V45H305.502ZM310.058 45.11C309.624 45.11 309.221 45.06 308.848 44.96C308.474 44.86 308.164 44.7233 307.918 44.55L308.278 43.57C308.524 43.7233 308.804 43.8467 309.118 43.94C309.438 44.0267 309.754 44.07 310.068 44.07C310.348 44.07 310.554 44.0267 310.688 43.94C310.821 43.8467 310.888 43.7267 310.888 43.58C310.888 43.3467 310.718 43.2 310.378 43.14L309.328 42.95C308.908 42.8767 308.588 42.7267 308.368 42.5C308.148 42.2733 308.038 41.9767 308.038 41.61C308.038 41.2767 308.131 40.99 308.318 40.75C308.504 40.51 308.761 40.3233 309.088 40.19C309.414 40.0567 309.791 39.99 310.218 39.99C310.571 39.99 310.914 40.0367 311.248 40.13C311.581 40.2167 311.868 40.3567 312.108 40.55L311.728 41.52C311.528 41.3733 311.288 41.2533 311.008 41.16C310.734 41.0667 310.478 41.02 310.238 41.02C309.938 41.02 309.721 41.07 309.588 41.17C309.454 41.2633 309.388 41.3833 309.388 41.53C309.388 41.7633 309.544 41.91 309.858 41.97L310.908 42.16C311.341 42.2333 311.671 42.38 311.898 42.6C312.124 42.8133 312.238 43.1067 312.238 43.48C312.238 43.9933 312.038 44.3933 311.638 44.68C311.238 44.9667 310.711 45.11 310.058 45.11ZM281.087 55.11C280.594 55.11 280.124 55.04 279.677 54.9C279.23 54.76 278.864 54.57 278.577 54.33L279.067 53.16C279.66 53.6 280.314 53.82 281.027 53.82C281.84 53.82 282.247 53.5167 282.247 52.91C282.247 52.35 281.867 52.07 281.107 52.07H279.927V50.82H280.957C281.29 50.82 281.554 50.7433 281.747 50.59C281.94 50.4367 282.037 50.2233 282.037 49.95C282.037 49.4033 281.68 49.13 280.967 49.13C280.3 49.13 279.707 49.3533 279.187 49.8L278.697 48.66C278.99 48.4 279.35 48.2 279.777 48.06C280.204 47.9133 280.647 47.84 281.107 47.84C281.867 47.84 282.46 48.01 282.887 48.35C283.314 48.69 283.527 49.1567 283.527 49.75C283.527 50.1167 283.434 50.44 283.247 50.72C283.06 51 282.8 51.2033 282.467 51.33C282.867 51.4433 283.177 51.6533 283.397 51.96C283.624 52.26 283.737 52.6233 283.737 53.05C283.737 53.69 283.5 54.1933 283.027 54.56C282.554 54.9267 281.907 55.11 281.087 55.11ZM287.636 55V50.09H289.116V50.8C289.263 50.5467 289.463 50.35 289.716 50.21C289.976 50.0633 290.276 49.99 290.616 49.99C290.963 49.99 291.256 50.0667 291.496 50.22C291.743 50.3733 291.929 50.6067 292.056 50.92C292.209 50.6267 292.429 50.4 292.716 50.24C293.009 50.0733 293.333 49.99 293.686 49.99C294.246 49.99 294.663 50.16 294.936 50.5C295.209 50.8333 295.346 51.35 295.346 52.05V55H293.836V52.1C293.836 51.7733 293.783 51.5367 293.676 51.39C293.576 51.2367 293.403 51.16 293.156 51.16C292.869 51.16 292.646 51.26 292.486 51.46C292.326 51.66 292.246 51.9433 292.246 52.31V55H290.736V52.1C290.736 51.7733 290.683 51.5367 290.576 51.39C290.469 51.2367 290.296 51.16 290.056 51.16C289.769 51.16 289.546 51.26 289.386 51.46C289.226 51.66 289.146 51.9433 289.146 52.31V55H287.636ZM299.053 55.11C298.473 55.11 297.973 55.0067 297.553 54.8C297.14 54.5867 296.82 54.29 296.593 53.91C296.373 53.5233 296.263 53.07 296.263 52.55C296.263 52.0433 296.37 51.6 296.583 51.22C296.803 50.8333 297.1 50.5333 297.473 50.32C297.853 50.1 298.29 49.99 298.783 49.99C299.496 49.99 300.063 50.2167 300.483 50.67C300.903 51.1167 301.113 51.7233 301.113 52.49V52.87H297.703C297.756 53.2567 297.896 53.54 298.123 53.72C298.356 53.8933 298.676 53.98 299.083 53.98C299.35 53.98 299.62 53.94 299.893 53.86C300.166 53.78 300.413 53.6567 300.633 53.49L301.033 54.5C300.786 54.6867 300.483 54.8367 300.123 54.95C299.77 55.0567 299.413 55.11 299.053 55.11ZM298.843 50.99C298.523 50.99 298.263 51.0867 298.063 51.28C297.87 51.4733 297.75 51.7433 297.703 52.09H299.863C299.823 51.3567 299.483 50.99 298.843 50.99ZM303.871 55.11C303.438 55.11 303.034 55.06 302.661 54.96C302.288 54.86 301.978 54.7233 301.731 54.55L302.091 53.57C302.338 53.7233 302.618 53.8467 302.931 53.94C303.251 54.0267 303.568 54.07 303.881 54.07C304.161 54.07 304.368 54.0267 304.501 53.94C304.634 53.8467 304.701 53.7267 304.701 53.58C304.701 53.3467 304.531 53.2 304.191 53.14L303.141 52.95C302.721 52.8767 302.401 52.7267 302.181 52.5C301.961 52.2733 301.851 51.9767 301.851 51.61C301.851 51.2767 301.944 50.99 302.131 50.75C302.318 50.51 302.574 50.3233 302.901 50.19C303.228 50.0567 303.604 49.99 304.031 49.99C304.384 49.99 304.728 50.0367 305.061 50.13C305.394 50.2167 305.681 50.3567 305.921 50.55L305.541 51.52C305.341 51.3733 305.101 51.2533 304.821 51.16C304.548 51.0667 304.291 51.02 304.051 51.02C303.751 51.02 303.534 51.07 303.401 51.17C303.268 51.2633 303.201 51.3833 303.201 51.53C303.201 51.7633 303.358 51.91 303.671 51.97L304.721 52.16C305.154 52.2333 305.484 52.38 305.711 52.6C305.938 52.8133 306.051 53.1067 306.051 53.48C306.051 53.9933 305.851 54.3933 305.451 54.68C305.051 54.9667 304.524 55.11 303.871 55.11ZM309.463 55.11C308.883 55.11 308.383 55.0067 307.963 54.8C307.55 54.5867 307.23 54.29 307.003 53.91C306.783 53.5233 306.673 53.07 306.673 52.55C306.673 52.0433 306.78 51.6 306.993 51.22C307.213 50.8333 307.51 50.5333 307.883 50.32C308.263 50.1 308.7 49.99 309.193 49.99C309.906 49.99 310.473 50.2167 310.893 50.67C311.313 51.1167 311.523 51.7233 311.523 52.49V52.87H308.113C308.166 53.2567 308.306 53.54 308.533 53.72C308.766 53.8933 309.086 53.98 309.493 53.98C309.76 53.98 310.03 53.94 310.303 53.86C310.576 53.78 310.823 53.6567 311.043 53.49L311.443 54.5C311.196 54.6867 310.893 54.8367 310.533 54.95C310.18 55.0567 309.823 55.11 309.463 55.11ZM309.253 50.99C308.933 50.99 308.673 51.0867 308.473 51.28C308.28 51.4733 308.16 51.7433 308.113 52.09H310.273C310.233 51.3567 309.893 50.99 309.253 50.99ZM314.281 55.11C313.848 55.11 313.445 55.06 313.071 54.96C312.698 54.86 312.388 54.7233 312.141 54.55L312.501 53.57C312.748 53.7233 313.028 53.8467 313.341 53.94C313.661 54.0267 313.978 54.07 314.291 54.07C314.571 54.07 314.778 54.0267 314.911 53.94C315.045 53.8467 315.111 53.7267 315.111 53.58C315.111 53.3467 314.941 53.2 314.601 53.14L313.551 52.95C313.131 52.8767 312.811 52.7267 312.591 52.5C312.371 52.2733 312.261 51.9767 312.261 51.61C312.261 51.2767 312.355 50.99 312.541 50.75C312.728 50.51 312.985 50.3233 313.311 50.19C313.638 50.0567 314.015 49.99 314.441 49.99C314.795 49.99 315.138 50.0367 315.471 50.13C315.805 50.2167 316.091 50.3567 316.331 50.55L315.951 51.52C315.751 51.3733 315.511 51.2533 315.231 51.16C314.958 51.0667 314.701 51.02 314.461 51.02C314.161 51.02 313.945 51.07 313.811 51.17C313.678 51.2633 313.611 51.3833 313.611 51.53C313.611 51.7633 313.768 51.91 314.081 51.97L315.131 52.16C315.565 52.2333 315.895 52.38 316.121 52.6C316.348 52.8133 316.461 53.1067 316.461 53.48C316.461 53.9933 316.261 54.3933 315.861 54.68C315.461 54.9667 314.935 55.11 314.281 55.11Z" fill="white"/>
                <path class="result-graph-goal" d="M313.793 25.1895L318 31.5H307.5L312.155 25.1529C312.565 24.5928 313.408 24.6117 313.793 25.1895Z" fill="#323334"/>
                <g filter="url(#filter0_d_2422_851)" class="quiz__based-graph-line">
                    <path d="M23.0503 188.1C27.2202 188.1 30.6006 184.72 30.6006 180.55C30.6006 176.38 27.2202 173 23.0503 173C18.8804 173 15.5 176.38 15.5 180.55C15.5 184.72 18.8804 188.1 23.0503 188.1Z" fill="white"/>
                    <path d="M23.0503 187.6C26.9441 187.6 30.1006 184.444 30.1006 180.55C30.1006 176.656 26.9441 173.5 23.0503 173.5C19.1565 173.5 16 176.656 16 180.55C16 184.444 19.1565 187.6 23.0503 187.6Z" stroke="#2D2D2D" stroke-opacity="0.12"/>
                </g>
                <path d="M55 141H29C24.5817 141 21 144.582 21 149V155C21 159.418 24.5817 163 29 163H55C59.4183 163 63 159.418 63 155V149C63 144.582 59.4183 141 55 141Z" fill="#D3D5DA"/>
                <path d="M27.3198 155L30.5598 147.95H31.8098L35.0498 155H33.4898L32.8298 153.47H29.5398L28.8898 155H27.3198ZM31.1698 149.6L30.0598 152.26H32.3198L31.1898 149.6H31.1698ZM37.8403 156.91C37.367 156.91 36.9236 156.86 36.5103 156.76C36.1036 156.66 35.747 156.503 35.4403 156.29L35.8503 155.26C36.117 155.427 36.417 155.553 36.7503 155.64C37.0903 155.733 37.4103 155.78 37.7103 155.78C38.5636 155.78 38.9903 155.387 38.9903 154.6V154.04C38.857 154.293 38.647 154.497 38.3603 154.65C38.0736 154.803 37.7603 154.88 37.4203 154.88C36.9803 154.88 36.597 154.78 36.2703 154.58C35.9436 154.373 35.6903 154.087 35.5103 153.72C35.3303 153.353 35.2403 152.923 35.2403 152.43C35.2403 151.943 35.3303 151.517 35.5103 151.15C35.6903 150.783 35.9436 150.5 36.2703 150.3C36.597 150.093 36.9803 149.99 37.4203 149.99C37.7736 149.99 38.0903 150.067 38.3703 150.22C38.6503 150.373 38.857 150.577 38.9903 150.83V150.09H40.4603V154.43C40.4603 155.257 40.237 155.877 39.7903 156.29C39.3436 156.703 38.6936 156.91 37.8403 156.91ZM37.8703 153.75C38.2036 153.75 38.4703 153.633 38.6703 153.4C38.877 153.167 38.9803 152.843 38.9803 152.43C38.9803 152.017 38.877 151.697 38.6703 151.47C38.4703 151.237 38.2036 151.12 37.8703 151.12C37.5303 151.12 37.2603 151.237 37.0603 151.47C36.8603 151.697 36.7603 152.017 36.7603 152.43C36.7603 152.843 36.8603 153.167 37.0603 153.4C37.2603 153.633 37.5303 153.75 37.8703 153.75ZM43.9926 155.11C43.466 155.11 43.006 155.007 42.6126 154.8C42.226 154.587 41.926 154.29 41.7126 153.91C41.4993 153.523 41.3926 153.067 41.3926 152.54C41.3926 152.02 41.4993 151.57 41.7126 151.19C41.926 150.803 42.226 150.507 42.6126 150.3C43.006 150.093 43.466 149.99 43.9926 149.99C44.5193 149.99 44.976 150.093 45.3626 150.3C45.756 150.507 46.0593 150.803 46.2726 151.19C46.4926 151.57 46.6026 152.02 46.6026 152.54C46.6026 153.067 46.4926 153.523 46.2726 153.91C46.0593 154.29 45.756 154.587 45.3626 154.8C44.976 155.007 44.5193 155.11 43.9926 155.11ZM43.9926 153.98C44.3126 153.98 44.5726 153.863 44.7726 153.63C44.9793 153.397 45.0826 153.033 45.0826 152.54C45.0826 152.053 44.9793 151.697 44.7726 151.47C44.5726 151.237 44.3126 151.12 43.9926 151.12C43.6726 151.12 43.4126 151.237 43.2126 151.47C43.0126 151.697 42.9126 152.053 42.9126 152.54C42.9126 153.033 43.0126 153.397 43.2126 153.63C43.4126 153.863 43.6726 153.98 43.9926 153.98ZM47.5213 155V150.09H49.0013V150.94C49.2346 150.367 49.7346 150.05 50.5013 149.99L50.9413 149.96L51.0313 151.23L50.1813 151.32C49.4346 151.393 49.0613 151.773 49.0613 152.46V155H47.5213ZM53.2536 155.11C52.8869 155.11 52.5636 155.04 52.2836 154.9C52.0036 154.76 51.7802 154.57 51.6136 154.33C51.4536 154.09 51.3736 153.82 51.3736 153.52C51.3736 153.167 51.4669 152.883 51.6536 152.67C51.8402 152.457 52.1436 152.307 52.5636 152.22C52.9836 152.127 53.5402 152.08 54.2336 152.08H54.5936V151.91C54.5936 151.617 54.5269 151.41 54.3936 151.29C54.2602 151.163 54.0336 151.1 53.7136 151.1C53.4469 151.1 53.1636 151.143 52.8636 151.23C52.5702 151.31 52.2769 151.433 51.9836 151.6L51.5736 150.59C51.7469 150.477 51.9569 150.373 52.2036 150.28C52.4569 150.187 52.7202 150.117 52.9936 150.07C53.2669 150.017 53.5269 149.99 53.7736 149.99C54.5336 149.99 55.1002 150.163 55.4736 150.51C55.8469 150.85 56.0336 151.38 56.0336 152.1V155H54.6236V154.26C54.5236 154.52 54.3536 154.727 54.1136 154.88C53.8802 155.033 53.5936 155.11 53.2536 155.11ZM53.5936 154.1C53.8736 154.1 54.1102 154.003 54.3036 153.81C54.4969 153.617 54.5936 153.367 54.5936 153.06V152.86H54.2436C53.7302 152.86 53.3669 152.907 53.1536 153C52.9402 153.087 52.8336 153.24 52.8336 153.46C52.8336 153.647 52.8969 153.8 53.0236 153.92C53.1569 154.04 53.3469 154.1 53.5936 154.1Z" fill="#828286"/>
                <path d="M37.207 169.31L33 163H43.5L38.8454 169.347C38.4347 169.907 37.5922 169.888 37.207 169.31Z" fill="#D3D5DA"/>
                <g filter="url(#filter1_d_2422_851)" id="resultGraphDot" class="result-graph-dot">
                    <path d="M320.55 22.1006C324.72 22.1006 328.1 18.7202 328.1 14.5503C328.1 10.3804 324.72 7 320.55 7C316.38 7 313 10.3804 313 14.5503C313 18.7202 316.38 22.1006 320.55 22.1006Z" fill="white"/>
                    <path d="M320.55 21.6006C324.444 21.6006 327.6 18.4441 327.6 14.5503C327.6 10.6565 324.444 7.5 320.55 7.5C316.656 7.5 313.5 10.6565 313.5 14.5503C313.5 18.4441 316.656 21.6006 320.55 21.6006Z" stroke="#2D2D2D" stroke-opacity="0.12"/>
                </g>
                <defs>
                    <filter id="filter0_d_2422_851" x="7.5" y="169.314" width="31.1006" height="31.1006" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                        <feOffset dy="4.31447"/>
                        <feGaussianBlur stdDeviation="4"/>
                        <feComposite in2="hardAlpha" operator="out"/>
                        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2422_851"/>
                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2422_851" result="shape"/>
                    </filter>
                    <filter id="filter1_d_2422_851" x="305" y="3.31447" width="31.1006" height="31.1006" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                        <feOffset dy="4.31447"/>
                        <feGaussianBlur stdDeviation="4"/>
                        <feComposite in2="hardAlpha" operator="out"/>
                        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/>
                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2422_851"/>
                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2422_851" result="shape"/>
                    </filter>
                    <linearGradient id="paint0_linear_2422_851" x1="311.509" y1="181.708" x2="24.5861" y2="181.708" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#1998CD"/>
                        <stop offset="0.549638" stop-color="#F5E088"/>
                        <stop offset="1" stop-color="#FF574C"/>
                    </linearGradient>
                    <linearGradient id="paint1_linear_2422_851" x1="319" y1="182.999" x2="23" y2="182.999" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#1998CD"/>
                        <stop offset="0.549638" stop-color="#F5E088"/>
                        <stop offset="1" stop-color="#FF574C"/>
                    </linearGradient>
                </defs>
            </svg>

            <div class="quiz__reviews-loading-svg-date-wrapp">
                <p class="quiz__reviews-loading-svg-date">
                    AGORA
                </p>
                <p class="quiz__reviews-loading-svg-date" id="monthAfter1">
                    month 1
                </p>
                <p class="quiz__reviews-loading-svg-date" id="monthAfter2">
                    month 2
                </p>
                <p class="quiz__reviews-loading-svg-date" id="monthAfter3">
                    month 3
                </p>
            </div>
            <p class="quiz__reviews-loading-svg-date-text">
                * Este gráfico é apenas para fins ilustrativos
            </p>
        </div>
        <p class="quiz__reviews-loading-subtext portugal">
            O plano de <span>obediência d<span class="genderPrefixInsertSelector" data-prefix="a">o</span> seu <span class="textPuppySelector">cão</span></span> está pronto
        </p>
        <div class="quiz__btn-box">
            <button class="quiz__btn js-btn email-submit" id="redirect-to-landing-btn" data-landing-url="/questionary/landing-8">
                <span></span>
                Continuar
            </button>
        </div>
    </div>
    <div class="quiz__redirect-loader" id="resultLoader">
        <div class="preload">
            <span></span>
        </div>
    </div>
</div>

{% if quizData.isPuppyFunnel %}
    <div class="quiz__popup-wrapp" id="quizPopUpProblem">
        <div class="quiz__popup-bg"></div>

        <div class="quiz__popup" id="magicProblemPopup">
            <div class="quiz__popup-box">
                <h4 class="quiz__popup-title">
                    Mais uma pergunta
                </h4>
                <div class="quiz__popup-img-box">
                    <img id="resultPopup" class="quiz__popup-img" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1690991083/dog-training/img/Rectangle_17.jpg" alt="">
                </div>
                <div class="quiz__popup-text">
                    <span class="ageInsertSelector">Filhotes</span> geralmente têm problemas de mordida e beliscões. Você tem este problema?
                </div>
                <ul class="quiz__popup-btn-list">
                    <li class="quiz__popup-btn-item quizPopUpProblemBtn" data-event="No">
                        Não
                    </li>
                    <li class="quiz__popup-btn-item quizPopUpProblemBtn" data-event="Yes">
                        Sim
                    </li>
                </ul>
            </div>
        </div>
    </div>
{% else %}
    <div class="quiz__popup-wrapp" id="quizPopUpProblem">
        <div class="quiz__popup-bg"></div>
        <div class="quiz__popup" id="magicProblemPopup">
            <div class="quiz__popup-box">
                <h4 class="quiz__popup-title">
                    Mais uma pergunta
                </h4>
                <div class="quiz__popup-img-box">
                    <img id="resultPopup" class="quiz__popup-img" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1690985584/dog-training/img/Rectangle_14.jpg" alt="">
                </div>
                <div class="quiz__popup-text">
                    <b class="ageInsertSelector capitalize">Seu cão</b> às vezes podem demonstrar agressão territorial.
                    <br>
                    Você já teve esse problema?
                </div>
                <ul class="quiz__popup-btn-list">
                    <li class="quiz__popup-btn-item quizPopUpProblemBtn" data-event="No">
                        Não
                    </li>
                    <li class="quiz__popup-btn-item quizPopUpProblemBtn" data-event="Yes">
                        Sim
                    </li>
                </ul>
            </div>
        </div>
    </div>
{% endif %}
<div class="quiz__popup-wrapp" id="quizPopUpObedience">
    <div class="quiz__popup-bg"></div>

    <div class="quiz__popup" id="magicObediencePopup">
        <div class="quiz__popup-box">
            <h4 class="quiz__popup-title">
                Finalizando seu plano
            </h4>
            <div class="quiz__popup-img-box">
                <img id="resultPopup" class="quiz__popup-img" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1690991083/dog-training/img/Rectangle_16.jpg" alt="">
            </div>
            <div class="quiz__popup-text">
                <span class="genderPrefixInsertSelector" data-prefix="A">O</span> <span class="nameInsertSelector">seu cão</span> já teve treinamento de obediência?
            </div>
            <ul class="quiz__popup-btn-list">
                <li class="quiz__popup-btn-item quizPopUpObedienceBtn" data-event="No">
                    Não
                </li>
                <li class="quiz__popup-btn-item quizPopUpObedienceBtn" data-event="Yes">
                    Sim
                </li>
            </ul>
        </div>
    </div>
</div>
