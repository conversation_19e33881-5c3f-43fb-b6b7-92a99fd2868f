<script>
    window.solidData = {
        formParams: {
            formTypeClass: 'flat',
            submitButtonText: 'CONTINÚA',
            googleFontLink: '//fonts.googleapis.com/css2?family=Nunito+Sans:wght@500;700&display=swap',
            autoFocus: false,
        },
        appearance: {
            autoFocus: false
        },
        styles: {
            form_body: {
                'font-family': 'Nunito Sans',
                'min-width': '100%',
            },
            submit_button: {
                'background': '#5DCCC2',
                'display': 'flex',
                'align-items': 'center',
                'justify-content': 'center',
                'font-weight': '700',
                'font-size': '16px',
                'height': '50px',
                'margin-top': '16px',
                'box-shadow': 'none',
                'border-radius': '25px',
                ':disabled': {
                    'opacity': '0.4'
                },
                '.title': {
                    'text-transform': 'capitalize',
                    'font-weight': '700',
                    'font-size': '16px',
                    ':before': {
                        'content': 'none',
                    },
                }
            },
            card_number: {
                input: {
                    'background': '#F7F7F9',
                    'border-color': '#A4A4A4',
                    'padding': '24px 16px 4px!important',
                    'text-align': 'center',
                    'color': '#16191E',
                    '-webkit-appearance': 'none',
                    ':focus': {
                        'border-color': '#A4A4A4',
                    },
                    ':focus~.label': {
                        'color': '#A4A4A4'
                    }
                },
                '.error input': {
                    'border-color': '#EE6B63',
                },
                '.label': {
                    color: '#A4A4A4',
                    'input:focus': {
                        'color': 'blue'
                    },
                },
            },
            card_cvv: {
                '.error input': {
                    'border-color': '#A4A4A4',
                },
                '.error .label': {
                    color: '#FC9494'
                },

                '.error-text': {
                    color: '#FC9494',
                    '.triangle': {
                        'border-color': 'transparent transparent #3498db'
                    }
                },
                input: {
                    'border-color': '#A4A4A4',
                    'background': '#F7F7F9',
                    'color': '#16191E',
                    ':focus': {
                        'border-color': '#A4A4A4',
                    },
                    ':focus~.label': {
                        'color': '#A4A4A4'
                    }
                },
                '.label': {
                    color: '#A4A4A4',
                    ':focus': {
                        'color': 'blue'
                    }
                },

            },
            expiry_date: {
                '.error input': {
                    'border-color': '#A4A4A4'
                },
                '.error .label': {
                    color: '#FC9494'
                },
                '.error-text': {
                    color: '#FC9494',
                },
                input: {
                    'border-color': '#A4A4A4',
                    'background': '#F7F7F9',
                    'color': '#16191E',
                    ':focus': {
                        'border-color': '#A4A4A4',
                    },
                    ':focus~.label': {
                        'color': '#A4A4A4'
                    }
                },
                '.label': {
                    color: '#A4A4A4'
                },
            },
        },
        applePayButtonParams: {
            enabled: false,
            containerId: 'applePayContainer'
        },
        googlePayButtonParams: {
            enabled: false,
            containerId: 'googlePayContainer'
        }
    };
</script>

<script src="https://cdn.solidgate.com/js/solid-form.js"></script>
