<aside class="upsale-advanced">
    <div class="upsale-advanced__main" id="upsale-main">
        <div class="upsale-advanced__container">
            <svg class="upsale-advanced__discount-flag" width="99" height="72" viewBox="0 0 99 72" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M0 0H99.5V72H0L17 36L0 0Z" fill="url(#paint0_linear_155_12464)"/>
                <path d="M42.404 12.67V15.686L34.24 31H29.924L38.062 15.92H29.222V12.67H42.404ZM58.6438 27.646H56.1478V31H52.1958V27.646H44.5258V24.656L52.8978 12.67H56.1478V24.552H58.6438V27.646ZM52.1958 24.552V18.832L48.2178 24.552H52.1958ZM64.9777 30.298L75.6637 11.89L78.1857 13.372L67.5257 31.78L64.9777 30.298ZM64.8737 23.59C63.383 23.59 62.213 23.1133 61.3637 22.16C60.5317 21.1893 60.1157 19.7853 60.1157 17.948C60.1157 16.1627 60.5404 14.7933 61.3897 13.84C62.239 12.8693 63.4004 12.384 64.8737 12.384C66.3644 12.384 67.5257 12.8693 68.3577 13.84C69.207 14.7933 69.6317 16.1713 69.6317 17.974C69.6317 19.8113 69.207 21.2067 68.3577 22.16C67.5257 23.1133 66.3644 23.59 64.8737 23.59ZM64.8737 21.146C65.411 21.146 65.801 20.9033 66.0437 20.418C66.2864 19.9327 66.4077 19.118 66.4077 17.974C66.4077 16.882 66.2777 16.0933 66.0177 15.608C65.775 15.1053 65.3937 14.854 64.8737 14.854C64.3537 14.854 63.9724 15.0967 63.7297 15.582C63.487 16.0673 63.3657 16.8647 63.3657 17.974C63.3657 19.1007 63.487 19.9153 63.7297 20.418C63.9724 20.9033 64.3537 21.146 64.8737 21.146ZM78.2897 31.182C76.799 31.182 75.629 30.7053 74.7797 29.752C73.9477 28.7813 73.5317 27.3773 73.5317 25.54C73.5317 23.7373 73.9477 22.3593 74.7797 21.406C75.629 20.4527 76.799 19.976 78.2897 19.976C79.763 19.976 80.9244 20.4613 81.7737 21.432C82.623 22.3853 83.0477 23.7633 83.0477 25.566C83.0477 27.386 82.623 28.7813 81.7737 29.752C80.9417 30.7053 79.7804 31.182 78.2897 31.182ZM78.2897 28.738C78.827 28.738 79.217 28.4953 79.4597 28.01C79.7024 27.5247 79.8237 26.71 79.8237 25.566C79.8237 24.4567 79.6937 23.6593 79.4337 23.174C79.191 22.6713 78.8097 22.42 78.2897 22.42C77.7697 22.42 77.3797 22.6713 77.1197 23.174C76.877 23.6593 76.7557 24.4567 76.7557 25.566C76.7557 26.71 76.877 27.5247 77.1197 28.01C77.3624 28.4953 77.7524 28.738 78.2897 28.738ZM38.244 62.234C36.424 62.234 34.838 61.8527 33.486 61.09C32.1513 60.31 31.12 59.218 30.392 57.814C29.664 56.3927 29.3 54.7287 29.3 52.822C29.3 50.9153 29.664 49.26 30.392 47.856C31.12 46.4347 32.1513 45.3427 33.486 44.58C34.838 43.8173 36.424 43.436 38.244 43.436C40.064 43.436 41.6413 43.8173 42.976 44.58C44.328 45.3427 45.368 46.4347 46.096 47.856C46.824 49.26 47.188 50.9153 47.188 52.822C47.188 54.7287 46.824 56.3927 46.096 57.814C45.368 59.218 44.328 60.31 42.976 61.09C41.6413 61.8527 40.064 62.234 38.244 62.234ZM38.244 59.088C39.7693 59.088 40.9653 58.5507 41.832 57.476C42.716 56.4013 43.158 54.85 43.158 52.822C43.158 50.794 42.7247 49.2513 41.858 48.194C40.9913 47.1193 39.7867 46.582 38.244 46.582C36.7013 46.582 35.4967 47.1193 34.63 48.194C33.7807 49.2513 33.356 50.794 33.356 52.822C33.356 54.85 33.7893 56.4013 34.656 57.476C35.5227 58.5507 36.7187 59.088 38.244 59.088ZM50.2848 43.67H62.6608V46.868H54.2888V51.08H62.1408V54.278H54.2888V62H50.2848V43.67ZM65.1638 43.67H77.5398V46.868H69.1678V51.08H77.0198V54.278H69.1678V62H65.1638V43.67Z" fill="white"/>
                <defs>
                    <linearGradient id="paint0_linear_155_12464" x1="35.7874" y1="-3.96783" x2="57.0856" y2="74.522" gradientUnits="userSpaceOnUse">
                        <stop stop-color="#FF765A"/>
                        <stop offset="1" stop-color="#FF4040"/>
                    </linearGradient>
                </defs>
            </svg>
            <div class="upsale-advanced__header">
                <img class="upsale-advanced__header-logo" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1706198790/paw-champ/logo/logo.svg" alt="">
                <p class="upsale-advanced__header-skip-btn skip-upsale-btn">Skip</p>
            </div>
            <div class="upsale-advanced__top">
                <div class="upsale-advanced__top-poo-box">
                    <picture>
                        <source srcset="https://res.cloudinary.com/dr0cx27xo/image/upload/v1718615191/dog-training/img/landing/poo.webp" type="image/webp">
                        <img class="upsale-advanced__top-poo-img" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1705047542/dog-training/img/poo.png" alt="puppy picture">
                    </picture>
                </div>
                <div class="upsale-advanced__top-advanced-box">
                    <picture>
                        <source srcset="https://res.cloudinary.com/dr0cx27xo/image/upload/v1718615975/dog-training/img/landing/upsale-ADVANCED.webp" type="image/webp">
                        <img class="upsale-advanced__top-advanced-img" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1705334445/dog-training/img/upsale-ADVANCED.png" alt="word advanced">
                    </picture>
                    <p class="upsale-advanced__top-advanced-subtext">
                        Dog Training Series!
                    </p>
                    <div class="upsale-advanced__top-users-box">
                        <picture>
                            <source srcset="https://res.cloudinary.com/dr0cx27xo/image/upload/v1718615191/dog-training/img/landing/users-upsale.webp" type="image/webp">
                            <img class="upsale-advanced__top-users-img" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1705322229/dog-training/img/users-upsale.png" alt="users avatars">
                        </picture>
                        <div class="upsale-advanced__top-users-text-box">
                            <p class="upsale-advanced__top-users-text-big">+40k</p>
                            <p class="upsale-advanced__top-users-text">Advanced owners</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="upsale-advanced__price-inn">
                <div class="upsale-advanced__price-box">
                    <p class="upsale-advanced__price-off" id="discount-placeholder">
                        66% off
                    </p>
                    <div class="upsale-advanced__price-wrapp">
                        <p class="upsale-advanced__price-total">
                            {{ userCurrencySign() }}<span class="total-price-placeholder">9.99</span>
                        </p>
                        <p class="upsale-advanced__price-old">
                            {{ userCurrencySign() }}29.99
                        </p>
                    </div>
                    <div class="upsale-advanced__vat-inn" id="VATUpsellInsertSelector"></div>
                    <p class="upsale-advanced__price-text">
                        Exclusive sign-up offer
                    </p>
                </div>
                <div class="upsale-advanced__price-rate-inn">
                    <div class="upsale-advanced__price-rate-box">
                        <svg class="upsale-advanced__price-rate" width="32" height="31" viewBox="0 0 32 31" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M14.878 0.675716C15.3624 -0.225237 16.6376 -0.225239 17.122 0.675714L21.363 8.56434C21.5468 8.90625 21.8724 9.14622 22.2497 9.2179L30.9561 10.8718C31.9504 11.0607 32.3445 12.2911 31.6495 13.0368L25.5643 19.5661C25.3005 19.8491 25.1761 20.2374 25.2255 20.6236L26.3653 29.5344C26.4955 30.5521 25.4638 31.3125 24.5499 30.8725L16.548 27.0192C16.2012 26.8522 15.7988 26.8522 15.452 27.0192L7.45008 30.8725C6.53619 31.3125 5.50448 30.5521 5.63465 29.5344L6.77446 20.6236C6.82386 20.2374 6.6995 19.8491 6.43575 19.5661L0.350511 13.0368C-0.344479 12.2911 0.0495965 11.0607 1.04394 10.8718L9.75026 9.2179C10.1276 9.14622 10.4532 8.90625 10.637 8.56434L14.878 0.675716Z"
                                  fill="#F59C29"/>
                        </svg>
                        <p class="upsale-advanced__price-rate-value">
                            9.6
                        </p>
                    </div>
                    <div class="upsale-advanced__price-rate-img-box">
                        <svg class="upsale-advanced__price-rate-img" width="62" height="64" viewBox="0 0 62 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect width="61.8688" height="64" rx="14.0274" fill="url(#paint0_linear_142_11733)"/>
                            <path d="M20.8633 33.7228C20.8633 33.3959 20.9893 33.0784 21.2415 32.7703L34.0721 17.0821C34.4177 16.6712 34.7865 16.4705 35.1787 16.4798C35.5803 16.4798 35.8884 16.6386 36.1032 16.9561C36.318 17.2735 36.3273 17.6891 36.1312 18.2027L31.971 29.2404H39.9132C40.2307 29.2404 40.4921 29.3385 40.6976 29.5346C40.903 29.7307 41.0057 29.9735 41.0057 30.263C41.0057 30.5898 40.8797 30.912 40.6275 31.2295L27.7969 46.9036C27.4514 47.3238 27.0825 47.5293 26.6903 47.5199C26.2981 47.5106 25.9946 47.3472 25.7798 47.0297C25.5651 46.7122 25.5557 46.2966 25.7518 45.783L29.898 34.7453H21.9558C21.6384 34.7453 21.3769 34.6472 21.1714 34.4511C20.966 34.255 20.8633 34.0122 20.8633 33.7228Z"
                                  fill="#1998CD"/>
                            <defs>
                                <linearGradient id="paint0_linear_142_11733" x1="30.9344" y1="0" x2="30.9344" y2="64" gradientUnits="userSpaceOnUse">
                                    <stop stop-color="#B6F4A1"/>
                                    <stop offset="1" stop-color="#BDF19F" stop-opacity="0.16"/>
                                </linearGradient>
                            </defs>
                        </svg>
                    </div>
                </div>
            </div>
            <div class="upsale-advanced__list-inn">
                <h5 class="upsale-advanced__list-title">
                    Your Ultimate Guide to Obedience with Advanced Dog Training Series!
                </h5>
                <ul class="upsale-advanced__list">
                    <li class="upsale-advanced__list-item">
                        Master your dog’s obedience
                    </li>
                    <li class="upsale-advanced__list-item">
                        Learn more about dog’s nutrition
                    </li>
                    <li class="upsale-advanced__list-item">
                        Safety tips & travel helper
                    </li>
                    <li class="upsale-advanced__list-item">
                        Handle dog’s anxiety during holidays
                    </li>
                </ul>
            </div>
            <div class="upsale-advanced__feed">
                <h2 class="upsale-advanced__feed-title">
                    User Success Stories
                </h2>
                <ul class="upsale-advanced__feed-list">
                    <li class="upsale-advanced__feed-item">
                        <div class="upsale-advanced__feed-item-box">
                            <h3 class="upsale-advanced__feed-item-title">
                                Maththew
                            </h3>
                            <p class="upsale-advanced__feed-item-text-grey">
                                <svg width="111" height="12" viewBox="0 0 111 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="6" cy="6" r="6" fill="#19CD56"/>
                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                          d="M7.89065 4.02641C7.82973 4.04628 7.74062 4.0948 7.69261 4.1342C7.64462 4.17361 7.10351 4.79514 6.49015 5.51539C5.45242 6.73394 5.36999 6.82492 5.30378 6.82492C5.23895 6.82492 5.19077 6.7763 4.76169 6.27769C4.5027 5.97672 4.25794 5.71011 4.21781 5.68527C4.11531 5.62177 3.80138 5.62086 3.69363 5.68373C3.46567 5.81678 3.33301 6.06124 3.33301 6.34826C3.33301 6.65242 3.34668 6.6733 4.2014 7.67322C5.04875 8.66452 5.05333 8.66863 5.31224 8.6665C5.5817 8.6643 5.51208 8.73421 7.11983 6.85188C8.73084 4.96576 8.6663 5.05413 8.6659 4.73496C8.6653 4.24943 8.2817 3.89883 7.89065 4.02641Z"
                                          fill="white"/>
                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                          d="M7.82424 4.05394C7.7449 4.08707 7.55207 4.30137 6.57346 5.44407C5.93713 6.18712 5.39504 6.80787 5.36879 6.82357C5.34257 6.83923 5.30035 6.84473 5.27498 6.83576C5.24963 6.82682 5.0178 6.57482 4.75983 6.27578C4.23219 5.66414 4.19983 5.63916 3.9364 5.63967C3.70778 5.64011 3.5109 5.78294 3.39771 6.03048C3.3311 6.17612 3.33335 6.52403 3.40188 6.67825C3.46936 6.83009 4.90213 8.50981 5.03083 8.58795C5.1636 8.66857 5.43688 8.6704 5.57284 8.59162C5.6405 8.55241 6.07415 8.05845 7.12868 6.81943C8.51472 5.19093 8.59262 5.09477 8.62702 4.97001C8.6526 4.87724 8.66037 4.79068 8.65341 4.67668C8.62989 4.29274 8.40463 4.03774 8.07519 4.02222C7.96965 4.01723 7.88726 4.02764 7.82424 4.05394Z"
                                          fill="white"/>
                                    <path d="M20.3141 11L16.5461 2.54H18.1901L20.9861 9.032L23.7821 2.54H25.3661L21.5981 11H20.3141ZM27.9031 11.12C27.2391 11.12 26.6671 10.996 26.1871 10.748C25.7071 10.5 25.3351 10.148 25.0711 9.692C24.8151 9.236 24.6871 8.696 24.6871 8.072C24.6871 7.464 24.8111 6.932 25.0591 6.476C25.3151 6.02 25.6631 5.664 26.1031 5.408C26.5511 5.144 27.0591 5.012 27.6271 5.012C28.4591 5.012 29.1151 5.276 29.5951 5.804C30.0831 6.332 30.3271 7.052 30.3271 7.964V8.408H26.1271C26.2391 9.456 26.8391 9.98 27.9271 9.98C28.2551 9.98 28.5831 9.932 28.9111 9.836C29.2391 9.732 29.5391 9.572 29.8111 9.356L30.2311 10.364C29.9511 10.596 29.5991 10.78 29.1751 10.916C28.7511 11.052 28.3271 11.12 27.9031 11.12ZM27.6871 6.032C27.2471 6.032 26.8911 6.168 26.6191 6.44C26.3471 6.712 26.1831 7.08 26.1271 7.544H29.0791C29.0471 7.056 28.9111 6.684 28.6711 6.428C28.4391 6.164 28.1111 6.032 27.6871 6.032ZM31.275 11V5.132H32.739V6.164C33.027 5.468 33.643 5.084 34.587 5.012L35.043 4.976L35.139 6.248L34.275 6.332C33.291 6.428 32.799 6.932 32.799 7.844V11H31.275ZM35.6382 3.872V2.408H37.3182V3.872H35.6382ZM35.7342 11V5.132H37.2342V11H35.7342ZM42.7408 3.872V2.408H44.4088V3.872H42.7408ZM38.9488 11V6.26H37.8208V5.132H38.9488V5.12C38.9488 4.288 39.1648 3.66 39.5968 3.236C40.0368 2.812 40.7168 2.572 41.6368 2.516L42.1648 2.492L42.2368 3.596L41.7688 3.62C41.2888 3.652 40.9488 3.772 40.7488 3.98C40.5488 4.18 40.4488 4.48 40.4488 4.88V5.132H44.3248V11H42.8248V6.26H40.4488V11H38.9488ZM48.5407 11.12C47.8767 11.12 47.3047 10.996 46.8247 10.748C46.3447 10.5 45.9727 10.148 45.7087 9.692C45.4527 9.236 45.3247 8.696 45.3247 8.072C45.3247 7.464 45.4487 6.932 45.6967 6.476C45.9527 6.02 46.3007 5.664 46.7407 5.408C47.1887 5.144 47.6967 5.012 48.2647 5.012C49.0967 5.012 49.7527 5.276 50.2327 5.804C50.7207 6.332 50.9647 7.052 50.9647 7.964V8.408H46.7647C46.8767 9.456 47.4767 9.98 48.5647 9.98C48.8927 9.98 49.2207 9.932 49.5487 9.836C49.8767 9.732 50.1767 9.572 50.4487 9.356L50.8687 10.364C50.5887 10.596 50.2367 10.78 49.8127 10.916C49.3887 11.052 48.9647 11.12 48.5407 11.12ZM48.3247 6.032C47.8847 6.032 47.5287 6.168 47.2567 6.44C46.9847 6.712 46.8207 7.08 46.7647 7.544H49.7167C49.6847 7.056 49.5487 6.684 49.3087 6.428C49.0767 6.164 48.7487 6.032 48.3247 6.032ZM54.1687 11.12C53.6567 11.12 53.2047 10.996 52.8127 10.748C52.4287 10.5 52.1287 10.148 51.9127 9.692C51.6967 9.228 51.5887 8.684 51.5887 8.06C51.5887 7.428 51.6967 6.888 51.9127 6.44C52.1287 5.984 52.4287 5.632 52.8127 5.384C53.2047 5.136 53.6567 5.012 54.1687 5.012C54.5847 5.012 54.9607 5.104 55.2967 5.288C55.6327 5.472 55.8847 5.716 56.0527 6.02V2.54H57.5527V11H56.0887V10.028C55.9287 10.364 55.6767 10.632 55.3327 10.832C54.9887 11.024 54.6007 11.12 54.1687 11.12ZM54.5887 9.98C55.0367 9.98 55.3967 9.82 55.6687 9.5C55.9407 9.172 56.0767 8.692 56.0767 8.06C56.0767 7.42 55.9407 6.944 55.6687 6.632C55.3967 6.312 55.0367 6.152 54.5887 6.152C54.1407 6.152 53.7807 6.312 53.5087 6.632C53.2367 6.944 53.1007 7.42 53.1007 8.06C53.1007 8.692 53.2367 9.172 53.5087 9.5C53.7807 9.82 54.1407 9.98 54.5887 9.98ZM64.5743 11.12C63.9583 11.12 63.4223 10.996 62.9663 10.748C62.5103 10.492 62.1583 10.132 61.9103 9.668C61.6623 9.204 61.5383 8.66 61.5383 8.036C61.5383 7.412 61.6623 6.876 61.9103 6.428C62.1583 5.972 62.5103 5.624 62.9663 5.384C63.4223 5.136 63.9583 5.012 64.5743 5.012C64.9503 5.012 65.3223 5.072 65.6903 5.192C66.0583 5.312 66.3623 5.48 66.6023 5.696L66.1583 6.74C65.9503 6.556 65.7143 6.416 65.4503 6.32C65.1943 6.224 64.9463 6.176 64.7063 6.176C64.1863 6.176 63.7823 6.34 63.4943 6.668C63.2143 6.988 63.0743 7.448 63.0743 8.048C63.0743 8.64 63.2143 9.108 63.4943 9.452C63.7823 9.788 64.1863 9.956 64.7063 9.956C64.9383 9.956 65.1863 9.908 65.4503 9.812C65.7143 9.716 65.9503 9.572 66.1583 9.38L66.6023 10.436C66.3623 10.644 66.0543 10.812 65.6783 10.94C65.3103 11.06 64.9423 11.12 64.5743 11.12ZM69.4065 11.12C67.9665 11.12 67.2465 10.312 67.2465 8.696V5.132H68.7465V8.72C68.7465 9.136 68.8305 9.444 68.9985 9.644C69.1665 9.844 69.4345 9.944 69.8025 9.944C70.2025 9.944 70.5305 9.808 70.7865 9.536C71.0425 9.256 71.1705 8.888 71.1705 8.432V5.132H72.6705V11H71.2065V10.124C70.8145 10.788 70.2145 11.12 69.4065 11.12ZM76.1397 11.12C75.6437 11.12 75.1797 11.06 74.7477 10.94C74.3157 10.82 73.9557 10.648 73.6677 10.424L74.0517 9.44C74.3477 9.64 74.6797 9.796 75.0477 9.908C75.4157 10.012 75.7837 10.064 76.1517 10.064C76.5357 10.064 76.8197 10 77.0037 9.872C77.1957 9.736 77.2917 9.56 77.2917 9.344C77.2917 9.008 77.0437 8.792 76.5477 8.696L75.3477 8.468C74.3317 8.276 73.8237 7.748 73.8237 6.884C73.8237 6.5 73.9277 6.168 74.1357 5.888C74.3517 5.608 74.6477 5.392 75.0237 5.24C75.3997 5.088 75.8317 5.012 76.3197 5.012C76.7357 5.012 77.1357 5.072 77.5197 5.192C77.9037 5.304 78.2317 5.476 78.5037 5.708L78.0957 6.692C77.8637 6.5 77.5837 6.348 77.2557 6.236C76.9357 6.124 76.6277 6.068 76.3317 6.068C75.9397 6.068 75.6477 6.136 75.4557 6.272C75.2717 6.408 75.1797 6.588 75.1797 6.812C75.1797 7.164 75.4077 7.38 75.8637 7.46L77.0637 7.688C77.5837 7.784 77.9757 7.96 78.2397 8.216C78.5117 8.472 78.6477 8.816 78.6477 9.248C78.6477 9.832 78.4197 10.292 77.9637 10.628C77.5077 10.956 76.8997 11.12 76.1397 11.12ZM82.2405 11.12C80.6885 11.12 79.9125 10.352 79.9125 8.816V6.26H78.7845V5.132H79.9125V3.38H81.4125V5.132H83.1885V6.26H81.4125V8.732C81.4125 9.116 81.4965 9.404 81.6645 9.596C81.8325 9.788 82.1045 9.884 82.4805 9.884C82.5925 9.884 82.7085 9.872 82.8285 9.848C82.9485 9.816 83.0725 9.784 83.2005 9.752L83.4285 10.856C83.2845 10.936 83.1005 11 82.8765 11.048C82.6605 11.096 82.4485 11.12 82.2405 11.12ZM86.5354 11.12C85.9274 11.12 85.3994 10.996 84.9514 10.748C84.5034 10.5 84.1554 10.148 83.9074 9.692C83.6594 9.228 83.5354 8.684 83.5354 8.06C83.5354 7.436 83.6594 6.896 83.9074 6.44C84.1554 5.984 84.5034 5.632 84.9514 5.384C85.3994 5.136 85.9274 5.012 86.5354 5.012C87.1434 5.012 87.6714 5.136 88.1194 5.384C88.5674 5.632 88.9154 5.984 89.1634 6.44C89.4114 6.896 89.5354 7.436 89.5354 8.06C89.5354 8.684 89.4114 9.228 89.1634 9.692C88.9154 10.148 88.5674 10.5 88.1194 10.748C87.6714 10.996 87.1434 11.12 86.5354 11.12ZM86.5354 9.98C86.9834 9.98 87.3434 9.82 87.6154 9.5C87.8874 9.172 88.0234 8.692 88.0234 8.06C88.0234 7.42 87.8874 6.944 87.6154 6.632C87.3434 6.312 86.9834 6.152 86.5354 6.152C86.0874 6.152 85.7274 6.312 85.4554 6.632C85.1834 6.944 85.0474 7.42 85.0474 8.06C85.0474 8.692 85.1834 9.172 85.4554 9.5C85.7274 9.82 86.0874 9.98 86.5354 9.98ZM90.5574 11V5.132H92.0214V6.032C92.1974 5.712 92.4414 5.464 92.7534 5.288C93.0654 5.104 93.4254 5.012 93.8334 5.012C94.7134 5.012 95.2894 5.396 95.5614 6.164C95.7454 5.804 96.0094 5.524 96.3534 5.324C96.6974 5.116 97.0894 5.012 97.5294 5.012C98.8494 5.012 99.5094 5.816 99.5094 7.424V11H98.0094V7.484C98.0094 7.036 97.9334 6.708 97.7814 6.5C97.6374 6.292 97.3894 6.188 97.0374 6.188C96.6454 6.188 96.3374 6.328 96.1134 6.608C95.8894 6.88 95.7774 7.26 95.7774 7.748V11H94.2774V7.484C94.2774 7.036 94.2014 6.708 94.0494 6.5C93.9054 6.292 93.6614 6.188 93.3174 6.188C92.9254 6.188 92.6174 6.328 92.3934 6.608C92.1694 6.88 92.0574 7.26 92.0574 7.748V11H90.5574ZM103.709 11.12C103.045 11.12 102.473 10.996 101.993 10.748C101.513 10.5 101.141 10.148 100.877 9.692C100.621 9.236 100.493 8.696 100.493 8.072C100.493 7.464 100.617 6.932 100.865 6.476C101.121 6.02 101.469 5.664 101.909 5.408C102.357 5.144 102.865 5.012 103.433 5.012C104.265 5.012 104.921 5.276 105.401 5.804C105.889 6.332 106.133 7.052 106.133 7.964V8.408H101.933C102.045 9.456 102.645 9.98 103.733 9.98C104.061 9.98 104.389 9.932 104.717 9.836C105.045 9.732 105.345 9.572 105.617 9.356L106.037 10.364C105.757 10.596 105.405 10.78 104.981 10.916C104.557 11.052 104.133 11.12 103.709 11.12ZM103.493 6.032C103.053 6.032 102.697 6.168 102.425 6.44C102.153 6.712 101.989 7.08 101.933 7.544H104.885C104.853 7.056 104.717 6.684 104.477 6.428C104.245 6.164 103.917 6.032 103.493 6.032ZM107.081 11V5.132H108.545V6.164C108.833 5.468 109.449 5.084 110.393 5.012L110.849 4.976L110.945 6.248L110.081 6.332C109.097 6.428 108.605 6.932 108.605 7.844V11H107.081Z"
                                          fill="#19CD56"/>
                                </svg>
                            </p>
                        </div>
                        <div class="upsale-advanced__feed-item-text">
                            Invaluable insights! The dog nutrition tips outlined have been a game-changer in our trainings!
                        </div>
                        <svg width="81" height="13" viewBox="0 0 81 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M6.04419 0.27451C6.24096 -0.0915027 6.75904 -0.0915034 6.95581 0.274509L8.67871 3.47926C8.75339 3.61816 8.88566 3.71565 9.03896 3.74477L12.5759 4.41668C12.9799 4.49342 13.1399 4.99325 12.8576 5.29619L10.3855 7.94875C10.2783 8.06371 10.2278 8.22145 10.2479 8.37835L10.7109 11.9984C10.7638 12.4118 10.3447 12.7207 9.97341 12.5419L6.72264 10.9766C6.58175 10.9087 6.41825 10.9087 6.27736 10.9766L3.02659 12.5419C2.65533 12.7207 2.23619 12.4118 2.28908 11.9984L2.75212 8.37835C2.77219 8.22145 2.72167 8.06371 2.61452 7.94875L0.142395 5.29619C-0.139945 4.99325 0.0201486 4.49342 0.4241 4.41668L3.96104 3.74477C4.11434 3.71565 4.24661 3.61816 4.32129 3.47926L6.04419 0.27451Z"
                                  fill="#F5BC29"/>
                            <path d="M23.0442 0.27451C23.241 -0.0915027 23.759 -0.0915034 23.9558 0.274509L25.6787 3.47926C25.7534 3.61816 25.8857 3.71565 26.039 3.74477L29.5759 4.41668C29.9799 4.49342 30.1399 4.99325 29.8576 5.29619L27.3855 7.94875C27.2783 8.06371 27.2278 8.22145 27.2479 8.37835L27.7109 11.9984C27.7638 12.4118 27.3447 12.7207 26.9734 12.5419L23.7226 10.9766C23.5817 10.9087 23.4183 10.9087 23.2774 10.9766L20.0266 12.5419C19.6553 12.7207 19.2362 12.4118 19.2891 11.9984L19.7521 8.37835C19.7722 8.22145 19.7217 8.06371 19.6145 7.94875L17.1424 5.29619C16.8601 4.99325 17.0201 4.49342 17.4241 4.41668L20.961 3.74477C21.1143 3.71565 21.2466 3.61816 21.3213 3.47926L23.0442 0.27451Z"
                                  fill="#F5BC29"/>
                            <path d="M40.0442 0.27451C40.241 -0.0915027 40.759 -0.0915034 40.9558 0.274509L42.6787 3.47926C42.7534 3.61816 42.8857 3.71565 43.039 3.74477L46.5759 4.41668C46.9799 4.49342 47.1399 4.99325 46.8576 5.29619L44.3855 7.94875C44.2783 8.06371 44.2278 8.22145 44.2479 8.37835L44.7109 11.9984C44.7638 12.4118 44.3447 12.7207 43.9734 12.5419L40.7226 10.9766C40.5817 10.9087 40.4183 10.9087 40.2774 10.9766L37.0266 12.5419C36.6553 12.7207 36.2362 12.4118 36.2891 11.9984L36.7521 8.37835C36.7722 8.22145 36.7217 8.06371 36.6145 7.94875L34.1424 5.29619C33.8601 4.99325 34.0201 4.49342 34.4241 4.41668L37.961 3.74477C38.1143 3.71565 38.2466 3.61816 38.3213 3.47926L40.0442 0.27451Z"
                                  fill="#F5BC29"/>
                            <path d="M57.0442 0.27451C57.241 -0.0915027 57.759 -0.0915034 57.9558 0.274509L59.6787 3.47926C59.7534 3.61816 59.8857 3.71565 60.039 3.74477L63.5759 4.41668C63.9799 4.49342 64.1399 4.99325 63.8576 5.29619L61.3855 7.94875C61.2783 8.06371 61.2278 8.22145 61.2479 8.37835L61.7109 11.9984C61.7638 12.4118 61.3447 12.7207 60.9734 12.5419L57.7226 10.9766C57.5817 10.9087 57.4183 10.9087 57.2774 10.9766L54.0266 12.5419C53.6553 12.7207 53.2362 12.4118 53.2891 11.9984L53.7521 8.37835C53.7722 8.22145 53.7217 8.06371 53.6145 7.94875L51.1424 5.29619C50.8601 4.99325 51.0201 4.49342 51.4241 4.41668L54.961 3.74477C55.1143 3.71565 55.2466 3.61816 55.3213 3.47926L57.0442 0.27451Z"
                                  fill="#F5BC29"/>
                            <path d="M74.0442 0.27451C74.241 -0.0915027 74.759 -0.0915034 74.9558 0.274509L76.6787 3.47926C76.7534 3.61816 76.8857 3.71565 77.039 3.74477L80.5759 4.41668C80.9799 4.49342 81.1399 4.99325 80.8576 5.29619L78.3855 7.94875C78.2783 8.06371 78.2278 8.22145 78.2479 8.37835L78.7109 11.9984C78.7638 12.4118 78.3447 12.7207 77.9734 12.5419L74.7226 10.9766C74.5817 10.9087 74.4183 10.9087 74.2774 10.9766L71.0266 12.5419C70.6553 12.7207 70.2362 12.4118 70.2891 11.9984L70.7521 8.37835C70.7722 8.22145 70.7217 8.06371 70.6145 7.94875L68.1424 5.29619C67.8601 4.99325 68.0201 4.49342 68.4241 4.41668L71.961 3.74477C72.1143 3.71565 72.2466 3.61816 72.3213 3.47926L74.0442 0.27451Z"
                                  fill="#F5BC29"/>
                        </svg>
                    </li>
                    <li class="upsale-advanced__feed-item">
                        <div class="upsale-advanced__feed-item-box">
                            <h3 class="upsale-advanced__feed-item-title">
                                Antonia
                            </h3>
                            <p class="upsale-advanced__feed-item-text-grey">
                                <svg width="111" height="12" viewBox="0 0 111 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="6" cy="6" r="6" fill="#19CD56"/>
                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                          d="M7.89065 4.02641C7.82973 4.04628 7.74062 4.0948 7.69261 4.1342C7.64462 4.17361 7.10351 4.79514 6.49015 5.51539C5.45242 6.73394 5.36999 6.82492 5.30378 6.82492C5.23895 6.82492 5.19077 6.7763 4.76169 6.27769C4.5027 5.97672 4.25794 5.71011 4.21781 5.68527C4.11531 5.62177 3.80138 5.62086 3.69363 5.68373C3.46567 5.81678 3.33301 6.06124 3.33301 6.34826C3.33301 6.65242 3.34668 6.6733 4.2014 7.67322C5.04875 8.66452 5.05333 8.66863 5.31224 8.6665C5.5817 8.6643 5.51208 8.73421 7.11983 6.85188C8.73084 4.96576 8.6663 5.05413 8.6659 4.73496C8.6653 4.24943 8.2817 3.89883 7.89065 4.02641Z"
                                          fill="white"/>
                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                          d="M7.82424 4.05394C7.7449 4.08707 7.55207 4.30137 6.57346 5.44407C5.93713 6.18712 5.39504 6.80787 5.36879 6.82357C5.34257 6.83923 5.30035 6.84473 5.27498 6.83576C5.24963 6.82682 5.0178 6.57482 4.75983 6.27578C4.23219 5.66414 4.19983 5.63916 3.9364 5.63967C3.70778 5.64011 3.5109 5.78294 3.39771 6.03048C3.3311 6.17612 3.33335 6.52403 3.40188 6.67825C3.46936 6.83009 4.90213 8.50981 5.03083 8.58795C5.1636 8.66857 5.43688 8.6704 5.57284 8.59162C5.6405 8.55241 6.07415 8.05845 7.12868 6.81943C8.51472 5.19093 8.59262 5.09477 8.62702 4.97001C8.6526 4.87724 8.66037 4.79068 8.65341 4.67668C8.62989 4.29274 8.40463 4.03774 8.07519 4.02222C7.96965 4.01723 7.88726 4.02764 7.82424 4.05394Z"
                                          fill="white"/>
                                    <path d="M20.3141 11L16.5461 2.54H18.1901L20.9861 9.032L23.7821 2.54H25.3661L21.5981 11H20.3141ZM27.9031 11.12C27.2391 11.12 26.6671 10.996 26.1871 10.748C25.7071 10.5 25.3351 10.148 25.0711 9.692C24.8151 9.236 24.6871 8.696 24.6871 8.072C24.6871 7.464 24.8111 6.932 25.0591 6.476C25.3151 6.02 25.6631 5.664 26.1031 5.408C26.5511 5.144 27.0591 5.012 27.6271 5.012C28.4591 5.012 29.1151 5.276 29.5951 5.804C30.0831 6.332 30.3271 7.052 30.3271 7.964V8.408H26.1271C26.2391 9.456 26.8391 9.98 27.9271 9.98C28.2551 9.98 28.5831 9.932 28.9111 9.836C29.2391 9.732 29.5391 9.572 29.8111 9.356L30.2311 10.364C29.9511 10.596 29.5991 10.78 29.1751 10.916C28.7511 11.052 28.3271 11.12 27.9031 11.12ZM27.6871 6.032C27.2471 6.032 26.8911 6.168 26.6191 6.44C26.3471 6.712 26.1831 7.08 26.1271 7.544H29.0791C29.0471 7.056 28.9111 6.684 28.6711 6.428C28.4391 6.164 28.1111 6.032 27.6871 6.032ZM31.275 11V5.132H32.739V6.164C33.027 5.468 33.643 5.084 34.587 5.012L35.043 4.976L35.139 6.248L34.275 6.332C33.291 6.428 32.799 6.932 32.799 7.844V11H31.275ZM35.6382 3.872V2.408H37.3182V3.872H35.6382ZM35.7342 11V5.132H37.2342V11H35.7342ZM42.7408 3.872V2.408H44.4088V3.872H42.7408ZM38.9488 11V6.26H37.8208V5.132H38.9488V5.12C38.9488 4.288 39.1648 3.66 39.5968 3.236C40.0368 2.812 40.7168 2.572 41.6368 2.516L42.1648 2.492L42.2368 3.596L41.7688 3.62C41.2888 3.652 40.9488 3.772 40.7488 3.98C40.5488 4.18 40.4488 4.48 40.4488 4.88V5.132H44.3248V11H42.8248V6.26H40.4488V11H38.9488ZM48.5407 11.12C47.8767 11.12 47.3047 10.996 46.8247 10.748C46.3447 10.5 45.9727 10.148 45.7087 9.692C45.4527 9.236 45.3247 8.696 45.3247 8.072C45.3247 7.464 45.4487 6.932 45.6967 6.476C45.9527 6.02 46.3007 5.664 46.7407 5.408C47.1887 5.144 47.6967 5.012 48.2647 5.012C49.0967 5.012 49.7527 5.276 50.2327 5.804C50.7207 6.332 50.9647 7.052 50.9647 7.964V8.408H46.7647C46.8767 9.456 47.4767 9.98 48.5647 9.98C48.8927 9.98 49.2207 9.932 49.5487 9.836C49.8767 9.732 50.1767 9.572 50.4487 9.356L50.8687 10.364C50.5887 10.596 50.2367 10.78 49.8127 10.916C49.3887 11.052 48.9647 11.12 48.5407 11.12ZM48.3247 6.032C47.8847 6.032 47.5287 6.168 47.2567 6.44C46.9847 6.712 46.8207 7.08 46.7647 7.544H49.7167C49.6847 7.056 49.5487 6.684 49.3087 6.428C49.0767 6.164 48.7487 6.032 48.3247 6.032ZM54.1687 11.12C53.6567 11.12 53.2047 10.996 52.8127 10.748C52.4287 10.5 52.1287 10.148 51.9127 9.692C51.6967 9.228 51.5887 8.684 51.5887 8.06C51.5887 7.428 51.6967 6.888 51.9127 6.44C52.1287 5.984 52.4287 5.632 52.8127 5.384C53.2047 5.136 53.6567 5.012 54.1687 5.012C54.5847 5.012 54.9607 5.104 55.2967 5.288C55.6327 5.472 55.8847 5.716 56.0527 6.02V2.54H57.5527V11H56.0887V10.028C55.9287 10.364 55.6767 10.632 55.3327 10.832C54.9887 11.024 54.6007 11.12 54.1687 11.12ZM54.5887 9.98C55.0367 9.98 55.3967 9.82 55.6687 9.5C55.9407 9.172 56.0767 8.692 56.0767 8.06C56.0767 7.42 55.9407 6.944 55.6687 6.632C55.3967 6.312 55.0367 6.152 54.5887 6.152C54.1407 6.152 53.7807 6.312 53.5087 6.632C53.2367 6.944 53.1007 7.42 53.1007 8.06C53.1007 8.692 53.2367 9.172 53.5087 9.5C53.7807 9.82 54.1407 9.98 54.5887 9.98ZM64.5743 11.12C63.9583 11.12 63.4223 10.996 62.9663 10.748C62.5103 10.492 62.1583 10.132 61.9103 9.668C61.6623 9.204 61.5383 8.66 61.5383 8.036C61.5383 7.412 61.6623 6.876 61.9103 6.428C62.1583 5.972 62.5103 5.624 62.9663 5.384C63.4223 5.136 63.9583 5.012 64.5743 5.012C64.9503 5.012 65.3223 5.072 65.6903 5.192C66.0583 5.312 66.3623 5.48 66.6023 5.696L66.1583 6.74C65.9503 6.556 65.7143 6.416 65.4503 6.32C65.1943 6.224 64.9463 6.176 64.7063 6.176C64.1863 6.176 63.7823 6.34 63.4943 6.668C63.2143 6.988 63.0743 7.448 63.0743 8.048C63.0743 8.64 63.2143 9.108 63.4943 9.452C63.7823 9.788 64.1863 9.956 64.7063 9.956C64.9383 9.956 65.1863 9.908 65.4503 9.812C65.7143 9.716 65.9503 9.572 66.1583 9.38L66.6023 10.436C66.3623 10.644 66.0543 10.812 65.6783 10.94C65.3103 11.06 64.9423 11.12 64.5743 11.12ZM69.4065 11.12C67.9665 11.12 67.2465 10.312 67.2465 8.696V5.132H68.7465V8.72C68.7465 9.136 68.8305 9.444 68.9985 9.644C69.1665 9.844 69.4345 9.944 69.8025 9.944C70.2025 9.944 70.5305 9.808 70.7865 9.536C71.0425 9.256 71.1705 8.888 71.1705 8.432V5.132H72.6705V11H71.2065V10.124C70.8145 10.788 70.2145 11.12 69.4065 11.12ZM76.1397 11.12C75.6437 11.12 75.1797 11.06 74.7477 10.94C74.3157 10.82 73.9557 10.648 73.6677 10.424L74.0517 9.44C74.3477 9.64 74.6797 9.796 75.0477 9.908C75.4157 10.012 75.7837 10.064 76.1517 10.064C76.5357 10.064 76.8197 10 77.0037 9.872C77.1957 9.736 77.2917 9.56 77.2917 9.344C77.2917 9.008 77.0437 8.792 76.5477 8.696L75.3477 8.468C74.3317 8.276 73.8237 7.748 73.8237 6.884C73.8237 6.5 73.9277 6.168 74.1357 5.888C74.3517 5.608 74.6477 5.392 75.0237 5.24C75.3997 5.088 75.8317 5.012 76.3197 5.012C76.7357 5.012 77.1357 5.072 77.5197 5.192C77.9037 5.304 78.2317 5.476 78.5037 5.708L78.0957 6.692C77.8637 6.5 77.5837 6.348 77.2557 6.236C76.9357 6.124 76.6277 6.068 76.3317 6.068C75.9397 6.068 75.6477 6.136 75.4557 6.272C75.2717 6.408 75.1797 6.588 75.1797 6.812C75.1797 7.164 75.4077 7.38 75.8637 7.46L77.0637 7.688C77.5837 7.784 77.9757 7.96 78.2397 8.216C78.5117 8.472 78.6477 8.816 78.6477 9.248C78.6477 9.832 78.4197 10.292 77.9637 10.628C77.5077 10.956 76.8997 11.12 76.1397 11.12ZM82.2405 11.12C80.6885 11.12 79.9125 10.352 79.9125 8.816V6.26H78.7845V5.132H79.9125V3.38H81.4125V5.132H83.1885V6.26H81.4125V8.732C81.4125 9.116 81.4965 9.404 81.6645 9.596C81.8325 9.788 82.1045 9.884 82.4805 9.884C82.5925 9.884 82.7085 9.872 82.8285 9.848C82.9485 9.816 83.0725 9.784 83.2005 9.752L83.4285 10.856C83.2845 10.936 83.1005 11 82.8765 11.048C82.6605 11.096 82.4485 11.12 82.2405 11.12ZM86.5354 11.12C85.9274 11.12 85.3994 10.996 84.9514 10.748C84.5034 10.5 84.1554 10.148 83.9074 9.692C83.6594 9.228 83.5354 8.684 83.5354 8.06C83.5354 7.436 83.6594 6.896 83.9074 6.44C84.1554 5.984 84.5034 5.632 84.9514 5.384C85.3994 5.136 85.9274 5.012 86.5354 5.012C87.1434 5.012 87.6714 5.136 88.1194 5.384C88.5674 5.632 88.9154 5.984 89.1634 6.44C89.4114 6.896 89.5354 7.436 89.5354 8.06C89.5354 8.684 89.4114 9.228 89.1634 9.692C88.9154 10.148 88.5674 10.5 88.1194 10.748C87.6714 10.996 87.1434 11.12 86.5354 11.12ZM86.5354 9.98C86.9834 9.98 87.3434 9.82 87.6154 9.5C87.8874 9.172 88.0234 8.692 88.0234 8.06C88.0234 7.42 87.8874 6.944 87.6154 6.632C87.3434 6.312 86.9834 6.152 86.5354 6.152C86.0874 6.152 85.7274 6.312 85.4554 6.632C85.1834 6.944 85.0474 7.42 85.0474 8.06C85.0474 8.692 85.1834 9.172 85.4554 9.5C85.7274 9.82 86.0874 9.98 86.5354 9.98ZM90.5574 11V5.132H92.0214V6.032C92.1974 5.712 92.4414 5.464 92.7534 5.288C93.0654 5.104 93.4254 5.012 93.8334 5.012C94.7134 5.012 95.2894 5.396 95.5614 6.164C95.7454 5.804 96.0094 5.524 96.3534 5.324C96.6974 5.116 97.0894 5.012 97.5294 5.012C98.8494 5.012 99.5094 5.816 99.5094 7.424V11H98.0094V7.484C98.0094 7.036 97.9334 6.708 97.7814 6.5C97.6374 6.292 97.3894 6.188 97.0374 6.188C96.6454 6.188 96.3374 6.328 96.1134 6.608C95.8894 6.88 95.7774 7.26 95.7774 7.748V11H94.2774V7.484C94.2774 7.036 94.2014 6.708 94.0494 6.5C93.9054 6.292 93.6614 6.188 93.3174 6.188C92.9254 6.188 92.6174 6.328 92.3934 6.608C92.1694 6.88 92.0574 7.26 92.0574 7.748V11H90.5574ZM103.709 11.12C103.045 11.12 102.473 10.996 101.993 10.748C101.513 10.5 101.141 10.148 100.877 9.692C100.621 9.236 100.493 8.696 100.493 8.072C100.493 7.464 100.617 6.932 100.865 6.476C101.121 6.02 101.469 5.664 101.909 5.408C102.357 5.144 102.865 5.012 103.433 5.012C104.265 5.012 104.921 5.276 105.401 5.804C105.889 6.332 106.133 7.052 106.133 7.964V8.408H101.933C102.045 9.456 102.645 9.98 103.733 9.98C104.061 9.98 104.389 9.932 104.717 9.836C105.045 9.732 105.345 9.572 105.617 9.356L106.037 10.364C105.757 10.596 105.405 10.78 104.981 10.916C104.557 11.052 104.133 11.12 103.709 11.12ZM103.493 6.032C103.053 6.032 102.697 6.168 102.425 6.44C102.153 6.712 101.989 7.08 101.933 7.544H104.885C104.853 7.056 104.717 6.684 104.477 6.428C104.245 6.164 103.917 6.032 103.493 6.032ZM107.081 11V5.132H108.545V6.164C108.833 5.468 109.449 5.084 110.393 5.012L110.849 4.976L110.945 6.248L110.081 6.332C109.097 6.428 108.605 6.932 108.605 7.844V11H107.081Z"
                                          fill="#19CD56"/>
                                </svg>
                            </p>
                        </div>
                        <div class="upsale-advanced__feed-item-text">
                            Safety tips really helped me stop feeling anxious and worrying that 'something bad happened. Now I know how to keep my dog safe!
                        </div>
                        <svg width="81" height="13" viewBox="0 0 81 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M6.04419 0.27451C6.24096 -0.0915027 6.75904 -0.0915034 6.95581 0.274509L8.67871 3.47926C8.75339 3.61816 8.88566 3.71565 9.03896 3.74477L12.5759 4.41668C12.9799 4.49342 13.1399 4.99325 12.8576 5.29619L10.3855 7.94875C10.2783 8.06371 10.2278 8.22145 10.2479 8.37835L10.7109 11.9984C10.7638 12.4118 10.3447 12.7207 9.97341 12.5419L6.72264 10.9766C6.58175 10.9087 6.41825 10.9087 6.27736 10.9766L3.02659 12.5419C2.65533 12.7207 2.23619 12.4118 2.28908 11.9984L2.75212 8.37835C2.77219 8.22145 2.72167 8.06371 2.61452 7.94875L0.142395 5.29619C-0.139945 4.99325 0.0201486 4.49342 0.4241 4.41668L3.96104 3.74477C4.11434 3.71565 4.24661 3.61816 4.32129 3.47926L6.04419 0.27451Z"
                                  fill="#F5BC29"/>
                            <path d="M23.0442 0.27451C23.241 -0.0915027 23.759 -0.0915034 23.9558 0.274509L25.6787 3.47926C25.7534 3.61816 25.8857 3.71565 26.039 3.74477L29.5759 4.41668C29.9799 4.49342 30.1399 4.99325 29.8576 5.29619L27.3855 7.94875C27.2783 8.06371 27.2278 8.22145 27.2479 8.37835L27.7109 11.9984C27.7638 12.4118 27.3447 12.7207 26.9734 12.5419L23.7226 10.9766C23.5817 10.9087 23.4183 10.9087 23.2774 10.9766L20.0266 12.5419C19.6553 12.7207 19.2362 12.4118 19.2891 11.9984L19.7521 8.37835C19.7722 8.22145 19.7217 8.06371 19.6145 7.94875L17.1424 5.29619C16.8601 4.99325 17.0201 4.49342 17.4241 4.41668L20.961 3.74477C21.1143 3.71565 21.2466 3.61816 21.3213 3.47926L23.0442 0.27451Z"
                                  fill="#F5BC29"/>
                            <path d="M40.0442 0.27451C40.241 -0.0915027 40.759 -0.0915034 40.9558 0.274509L42.6787 3.47926C42.7534 3.61816 42.8857 3.71565 43.039 3.74477L46.5759 4.41668C46.9799 4.49342 47.1399 4.99325 46.8576 5.29619L44.3855 7.94875C44.2783 8.06371 44.2278 8.22145 44.2479 8.37835L44.7109 11.9984C44.7638 12.4118 44.3447 12.7207 43.9734 12.5419L40.7226 10.9766C40.5817 10.9087 40.4183 10.9087 40.2774 10.9766L37.0266 12.5419C36.6553 12.7207 36.2362 12.4118 36.2891 11.9984L36.7521 8.37835C36.7722 8.22145 36.7217 8.06371 36.6145 7.94875L34.1424 5.29619C33.8601 4.99325 34.0201 4.49342 34.4241 4.41668L37.961 3.74477C38.1143 3.71565 38.2466 3.61816 38.3213 3.47926L40.0442 0.27451Z"
                                  fill="#F5BC29"/>
                            <path d="M57.0442 0.27451C57.241 -0.0915027 57.759 -0.0915034 57.9558 0.274509L59.6787 3.47926C59.7534 3.61816 59.8857 3.71565 60.039 3.74477L63.5759 4.41668C63.9799 4.49342 64.1399 4.99325 63.8576 5.29619L61.3855 7.94875C61.2783 8.06371 61.2278 8.22145 61.2479 8.37835L61.7109 11.9984C61.7638 12.4118 61.3447 12.7207 60.9734 12.5419L57.7226 10.9766C57.5817 10.9087 57.4183 10.9087 57.2774 10.9766L54.0266 12.5419C53.6553 12.7207 53.2362 12.4118 53.2891 11.9984L53.7521 8.37835C53.7722 8.22145 53.7217 8.06371 53.6145 7.94875L51.1424 5.29619C50.8601 4.99325 51.0201 4.49342 51.4241 4.41668L54.961 3.74477C55.1143 3.71565 55.2466 3.61816 55.3213 3.47926L57.0442 0.27451Z"
                                  fill="#F5BC29"/>
                            <path d="M74.0442 0.27451C74.241 -0.0915027 74.759 -0.0915034 74.9558 0.274509L76.6787 3.47926C76.7534 3.61816 76.8857 3.71565 77.039 3.74477L80.5759 4.41668C80.9799 4.49342 81.1399 4.99325 80.8576 5.29619L78.3855 7.94875C78.2783 8.06371 78.2278 8.22145 78.2479 8.37835L78.7109 11.9984C78.7638 12.4118 78.3447 12.7207 77.9734 12.5419L74.7226 10.9766C74.5817 10.9087 74.4183 10.9087 74.2774 10.9766L71.0266 12.5419C70.6553 12.7207 70.2362 12.4118 70.2891 11.9984L70.7521 8.37835C70.7722 8.22145 70.7217 8.06371 70.6145 7.94875L68.1424 5.29619C67.8601 4.99325 68.0201 4.49342 68.4241 4.41668L71.961 3.74477C72.1143 3.71565 72.2466 3.61816 72.3213 3.47926L74.0442 0.27451Z"
                                  fill="#F5BC29"/>
                        </svg>
                    </li>
                    <li class="upsale-advanced__feed-item">
                        <div class="upsale-advanced__feed-item-box">
                            <h3 class="upsale-advanced__feed-item-title">
                                Taylor
                            </h3>
                            <p class="upsale-advanced__feed-item-text-grey">
                                <svg width="111" height="12" viewBox="0 0 111 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="6" cy="6" r="6" fill="#19CD56"/>
                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                          d="M7.89065 4.02641C7.82973 4.04628 7.74062 4.0948 7.69261 4.1342C7.64462 4.17361 7.10351 4.79514 6.49015 5.51539C5.45242 6.73394 5.36999 6.82492 5.30378 6.82492C5.23895 6.82492 5.19077 6.7763 4.76169 6.27769C4.5027 5.97672 4.25794 5.71011 4.21781 5.68527C4.11531 5.62177 3.80138 5.62086 3.69363 5.68373C3.46567 5.81678 3.33301 6.06124 3.33301 6.34826C3.33301 6.65242 3.34668 6.6733 4.2014 7.67322C5.04875 8.66452 5.05333 8.66863 5.31224 8.6665C5.5817 8.6643 5.51208 8.73421 7.11983 6.85188C8.73084 4.96576 8.6663 5.05413 8.6659 4.73496C8.6653 4.24943 8.2817 3.89883 7.89065 4.02641Z"
                                          fill="white"/>
                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                          d="M7.82424 4.05394C7.7449 4.08707 7.55207 4.30137 6.57346 5.44407C5.93713 6.18712 5.39504 6.80787 5.36879 6.82357C5.34257 6.83923 5.30035 6.84473 5.27498 6.83576C5.24963 6.82682 5.0178 6.57482 4.75983 6.27578C4.23219 5.66414 4.19983 5.63916 3.9364 5.63967C3.70778 5.64011 3.5109 5.78294 3.39771 6.03048C3.3311 6.17612 3.33335 6.52403 3.40188 6.67825C3.46936 6.83009 4.90213 8.50981 5.03083 8.58795C5.1636 8.66857 5.43688 8.6704 5.57284 8.59162C5.6405 8.55241 6.07415 8.05845 7.12868 6.81943C8.51472 5.19093 8.59262 5.09477 8.62702 4.97001C8.6526 4.87724 8.66037 4.79068 8.65341 4.67668C8.62989 4.29274 8.40463 4.03774 8.07519 4.02222C7.96965 4.01723 7.88726 4.02764 7.82424 4.05394Z"
                                          fill="white"/>
                                    <path d="M20.3141 11L16.5461 2.54H18.1901L20.9861 9.032L23.7821 2.54H25.3661L21.5981 11H20.3141ZM27.9031 11.12C27.2391 11.12 26.6671 10.996 26.1871 10.748C25.7071 10.5 25.3351 10.148 25.0711 9.692C24.8151 9.236 24.6871 8.696 24.6871 8.072C24.6871 7.464 24.8111 6.932 25.0591 6.476C25.3151 6.02 25.6631 5.664 26.1031 5.408C26.5511 5.144 27.0591 5.012 27.6271 5.012C28.4591 5.012 29.1151 5.276 29.5951 5.804C30.0831 6.332 30.3271 7.052 30.3271 7.964V8.408H26.1271C26.2391 9.456 26.8391 9.98 27.9271 9.98C28.2551 9.98 28.5831 9.932 28.9111 9.836C29.2391 9.732 29.5391 9.572 29.8111 9.356L30.2311 10.364C29.9511 10.596 29.5991 10.78 29.1751 10.916C28.7511 11.052 28.3271 11.12 27.9031 11.12ZM27.6871 6.032C27.2471 6.032 26.8911 6.168 26.6191 6.44C26.3471 6.712 26.1831 7.08 26.1271 7.544H29.0791C29.0471 7.056 28.9111 6.684 28.6711 6.428C28.4391 6.164 28.1111 6.032 27.6871 6.032ZM31.275 11V5.132H32.739V6.164C33.027 5.468 33.643 5.084 34.587 5.012L35.043 4.976L35.139 6.248L34.275 6.332C33.291 6.428 32.799 6.932 32.799 7.844V11H31.275ZM35.6382 3.872V2.408H37.3182V3.872H35.6382ZM35.7342 11V5.132H37.2342V11H35.7342ZM42.7408 3.872V2.408H44.4088V3.872H42.7408ZM38.9488 11V6.26H37.8208V5.132H38.9488V5.12C38.9488 4.288 39.1648 3.66 39.5968 3.236C40.0368 2.812 40.7168 2.572 41.6368 2.516L42.1648 2.492L42.2368 3.596L41.7688 3.62C41.2888 3.652 40.9488 3.772 40.7488 3.98C40.5488 4.18 40.4488 4.48 40.4488 4.88V5.132H44.3248V11H42.8248V6.26H40.4488V11H38.9488ZM48.5407 11.12C47.8767 11.12 47.3047 10.996 46.8247 10.748C46.3447 10.5 45.9727 10.148 45.7087 9.692C45.4527 9.236 45.3247 8.696 45.3247 8.072C45.3247 7.464 45.4487 6.932 45.6967 6.476C45.9527 6.02 46.3007 5.664 46.7407 5.408C47.1887 5.144 47.6967 5.012 48.2647 5.012C49.0967 5.012 49.7527 5.276 50.2327 5.804C50.7207 6.332 50.9647 7.052 50.9647 7.964V8.408H46.7647C46.8767 9.456 47.4767 9.98 48.5647 9.98C48.8927 9.98 49.2207 9.932 49.5487 9.836C49.8767 9.732 50.1767 9.572 50.4487 9.356L50.8687 10.364C50.5887 10.596 50.2367 10.78 49.8127 10.916C49.3887 11.052 48.9647 11.12 48.5407 11.12ZM48.3247 6.032C47.8847 6.032 47.5287 6.168 47.2567 6.44C46.9847 6.712 46.8207 7.08 46.7647 7.544H49.7167C49.6847 7.056 49.5487 6.684 49.3087 6.428C49.0767 6.164 48.7487 6.032 48.3247 6.032ZM54.1687 11.12C53.6567 11.12 53.2047 10.996 52.8127 10.748C52.4287 10.5 52.1287 10.148 51.9127 9.692C51.6967 9.228 51.5887 8.684 51.5887 8.06C51.5887 7.428 51.6967 6.888 51.9127 6.44C52.1287 5.984 52.4287 5.632 52.8127 5.384C53.2047 5.136 53.6567 5.012 54.1687 5.012C54.5847 5.012 54.9607 5.104 55.2967 5.288C55.6327 5.472 55.8847 5.716 56.0527 6.02V2.54H57.5527V11H56.0887V10.028C55.9287 10.364 55.6767 10.632 55.3327 10.832C54.9887 11.024 54.6007 11.12 54.1687 11.12ZM54.5887 9.98C55.0367 9.98 55.3967 9.82 55.6687 9.5C55.9407 9.172 56.0767 8.692 56.0767 8.06C56.0767 7.42 55.9407 6.944 55.6687 6.632C55.3967 6.312 55.0367 6.152 54.5887 6.152C54.1407 6.152 53.7807 6.312 53.5087 6.632C53.2367 6.944 53.1007 7.42 53.1007 8.06C53.1007 8.692 53.2367 9.172 53.5087 9.5C53.7807 9.82 54.1407 9.98 54.5887 9.98ZM64.5743 11.12C63.9583 11.12 63.4223 10.996 62.9663 10.748C62.5103 10.492 62.1583 10.132 61.9103 9.668C61.6623 9.204 61.5383 8.66 61.5383 8.036C61.5383 7.412 61.6623 6.876 61.9103 6.428C62.1583 5.972 62.5103 5.624 62.9663 5.384C63.4223 5.136 63.9583 5.012 64.5743 5.012C64.9503 5.012 65.3223 5.072 65.6903 5.192C66.0583 5.312 66.3623 5.48 66.6023 5.696L66.1583 6.74C65.9503 6.556 65.7143 6.416 65.4503 6.32C65.1943 6.224 64.9463 6.176 64.7063 6.176C64.1863 6.176 63.7823 6.34 63.4943 6.668C63.2143 6.988 63.0743 7.448 63.0743 8.048C63.0743 8.64 63.2143 9.108 63.4943 9.452C63.7823 9.788 64.1863 9.956 64.7063 9.956C64.9383 9.956 65.1863 9.908 65.4503 9.812C65.7143 9.716 65.9503 9.572 66.1583 9.38L66.6023 10.436C66.3623 10.644 66.0543 10.812 65.6783 10.94C65.3103 11.06 64.9423 11.12 64.5743 11.12ZM69.4065 11.12C67.9665 11.12 67.2465 10.312 67.2465 8.696V5.132H68.7465V8.72C68.7465 9.136 68.8305 9.444 68.9985 9.644C69.1665 9.844 69.4345 9.944 69.8025 9.944C70.2025 9.944 70.5305 9.808 70.7865 9.536C71.0425 9.256 71.1705 8.888 71.1705 8.432V5.132H72.6705V11H71.2065V10.124C70.8145 10.788 70.2145 11.12 69.4065 11.12ZM76.1397 11.12C75.6437 11.12 75.1797 11.06 74.7477 10.94C74.3157 10.82 73.9557 10.648 73.6677 10.424L74.0517 9.44C74.3477 9.64 74.6797 9.796 75.0477 9.908C75.4157 10.012 75.7837 10.064 76.1517 10.064C76.5357 10.064 76.8197 10 77.0037 9.872C77.1957 9.736 77.2917 9.56 77.2917 9.344C77.2917 9.008 77.0437 8.792 76.5477 8.696L75.3477 8.468C74.3317 8.276 73.8237 7.748 73.8237 6.884C73.8237 6.5 73.9277 6.168 74.1357 5.888C74.3517 5.608 74.6477 5.392 75.0237 5.24C75.3997 5.088 75.8317 5.012 76.3197 5.012C76.7357 5.012 77.1357 5.072 77.5197 5.192C77.9037 5.304 78.2317 5.476 78.5037 5.708L78.0957 6.692C77.8637 6.5 77.5837 6.348 77.2557 6.236C76.9357 6.124 76.6277 6.068 76.3317 6.068C75.9397 6.068 75.6477 6.136 75.4557 6.272C75.2717 6.408 75.1797 6.588 75.1797 6.812C75.1797 7.164 75.4077 7.38 75.8637 7.46L77.0637 7.688C77.5837 7.784 77.9757 7.96 78.2397 8.216C78.5117 8.472 78.6477 8.816 78.6477 9.248C78.6477 9.832 78.4197 10.292 77.9637 10.628C77.5077 10.956 76.8997 11.12 76.1397 11.12ZM82.2405 11.12C80.6885 11.12 79.9125 10.352 79.9125 8.816V6.26H78.7845V5.132H79.9125V3.38H81.4125V5.132H83.1885V6.26H81.4125V8.732C81.4125 9.116 81.4965 9.404 81.6645 9.596C81.8325 9.788 82.1045 9.884 82.4805 9.884C82.5925 9.884 82.7085 9.872 82.8285 9.848C82.9485 9.816 83.0725 9.784 83.2005 9.752L83.4285 10.856C83.2845 10.936 83.1005 11 82.8765 11.048C82.6605 11.096 82.4485 11.12 82.2405 11.12ZM86.5354 11.12C85.9274 11.12 85.3994 10.996 84.9514 10.748C84.5034 10.5 84.1554 10.148 83.9074 9.692C83.6594 9.228 83.5354 8.684 83.5354 8.06C83.5354 7.436 83.6594 6.896 83.9074 6.44C84.1554 5.984 84.5034 5.632 84.9514 5.384C85.3994 5.136 85.9274 5.012 86.5354 5.012C87.1434 5.012 87.6714 5.136 88.1194 5.384C88.5674 5.632 88.9154 5.984 89.1634 6.44C89.4114 6.896 89.5354 7.436 89.5354 8.06C89.5354 8.684 89.4114 9.228 89.1634 9.692C88.9154 10.148 88.5674 10.5 88.1194 10.748C87.6714 10.996 87.1434 11.12 86.5354 11.12ZM86.5354 9.98C86.9834 9.98 87.3434 9.82 87.6154 9.5C87.8874 9.172 88.0234 8.692 88.0234 8.06C88.0234 7.42 87.8874 6.944 87.6154 6.632C87.3434 6.312 86.9834 6.152 86.5354 6.152C86.0874 6.152 85.7274 6.312 85.4554 6.632C85.1834 6.944 85.0474 7.42 85.0474 8.06C85.0474 8.692 85.1834 9.172 85.4554 9.5C85.7274 9.82 86.0874 9.98 86.5354 9.98ZM90.5574 11V5.132H92.0214V6.032C92.1974 5.712 92.4414 5.464 92.7534 5.288C93.0654 5.104 93.4254 5.012 93.8334 5.012C94.7134 5.012 95.2894 5.396 95.5614 6.164C95.7454 5.804 96.0094 5.524 96.3534 5.324C96.6974 5.116 97.0894 5.012 97.5294 5.012C98.8494 5.012 99.5094 5.816 99.5094 7.424V11H98.0094V7.484C98.0094 7.036 97.9334 6.708 97.7814 6.5C97.6374 6.292 97.3894 6.188 97.0374 6.188C96.6454 6.188 96.3374 6.328 96.1134 6.608C95.8894 6.88 95.7774 7.26 95.7774 7.748V11H94.2774V7.484C94.2774 7.036 94.2014 6.708 94.0494 6.5C93.9054 6.292 93.6614 6.188 93.3174 6.188C92.9254 6.188 92.6174 6.328 92.3934 6.608C92.1694 6.88 92.0574 7.26 92.0574 7.748V11H90.5574ZM103.709 11.12C103.045 11.12 102.473 10.996 101.993 10.748C101.513 10.5 101.141 10.148 100.877 9.692C100.621 9.236 100.493 8.696 100.493 8.072C100.493 7.464 100.617 6.932 100.865 6.476C101.121 6.02 101.469 5.664 101.909 5.408C102.357 5.144 102.865 5.012 103.433 5.012C104.265 5.012 104.921 5.276 105.401 5.804C105.889 6.332 106.133 7.052 106.133 7.964V8.408H101.933C102.045 9.456 102.645 9.98 103.733 9.98C104.061 9.98 104.389 9.932 104.717 9.836C105.045 9.732 105.345 9.572 105.617 9.356L106.037 10.364C105.757 10.596 105.405 10.78 104.981 10.916C104.557 11.052 104.133 11.12 103.709 11.12ZM103.493 6.032C103.053 6.032 102.697 6.168 102.425 6.44C102.153 6.712 101.989 7.08 101.933 7.544H104.885C104.853 7.056 104.717 6.684 104.477 6.428C104.245 6.164 103.917 6.032 103.493 6.032ZM107.081 11V5.132H108.545V6.164C108.833 5.468 109.449 5.084 110.393 5.012L110.849 4.976L110.945 6.248L110.081 6.332C109.097 6.428 108.605 6.932 108.605 7.844V11H107.081Z"
                                          fill="#19CD56"/>
                                </svg>
                            </p>
                        </div>
                        <div class="upsale-advanced__feed-item-text">
                            Nice to delve deep into the dog training topic. Even after a year, we are still using the techniques described in our dog mental games.
                        </div>
                        <svg width="81" height="13" viewBox="0 0 81 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M6.04419 0.27451C6.24096 -0.0915027 6.75904 -0.0915034 6.95581 0.274509L8.67871 3.47926C8.75339 3.61816 8.88566 3.71565 9.03896 3.74477L12.5759 4.41668C12.9799 4.49342 13.1399 4.99325 12.8576 5.29619L10.3855 7.94875C10.2783 8.06371 10.2278 8.22145 10.2479 8.37835L10.7109 11.9984C10.7638 12.4118 10.3447 12.7207 9.97341 12.5419L6.72264 10.9766C6.58175 10.9087 6.41825 10.9087 6.27736 10.9766L3.02659 12.5419C2.65533 12.7207 2.23619 12.4118 2.28908 11.9984L2.75212 8.37835C2.77219 8.22145 2.72167 8.06371 2.61452 7.94875L0.142395 5.29619C-0.139945 4.99325 0.0201486 4.49342 0.4241 4.41668L3.96104 3.74477C4.11434 3.71565 4.24661 3.61816 4.32129 3.47926L6.04419 0.27451Z"
                                  fill="#F5BC29"/>
                            <path d="M23.0442 0.27451C23.241 -0.0915027 23.759 -0.0915034 23.9558 0.274509L25.6787 3.47926C25.7534 3.61816 25.8857 3.71565 26.039 3.74477L29.5759 4.41668C29.9799 4.49342 30.1399 4.99325 29.8576 5.29619L27.3855 7.94875C27.2783 8.06371 27.2278 8.22145 27.2479 8.37835L27.7109 11.9984C27.7638 12.4118 27.3447 12.7207 26.9734 12.5419L23.7226 10.9766C23.5817 10.9087 23.4183 10.9087 23.2774 10.9766L20.0266 12.5419C19.6553 12.7207 19.2362 12.4118 19.2891 11.9984L19.7521 8.37835C19.7722 8.22145 19.7217 8.06371 19.6145 7.94875L17.1424 5.29619C16.8601 4.99325 17.0201 4.49342 17.4241 4.41668L20.961 3.74477C21.1143 3.71565 21.2466 3.61816 21.3213 3.47926L23.0442 0.27451Z"
                                  fill="#F5BC29"/>
                            <path d="M40.0442 0.27451C40.241 -0.0915027 40.759 -0.0915034 40.9558 0.274509L42.6787 3.47926C42.7534 3.61816 42.8857 3.71565 43.039 3.74477L46.5759 4.41668C46.9799 4.49342 47.1399 4.99325 46.8576 5.29619L44.3855 7.94875C44.2783 8.06371 44.2278 8.22145 44.2479 8.37835L44.7109 11.9984C44.7638 12.4118 44.3447 12.7207 43.9734 12.5419L40.7226 10.9766C40.5817 10.9087 40.4183 10.9087 40.2774 10.9766L37.0266 12.5419C36.6553 12.7207 36.2362 12.4118 36.2891 11.9984L36.7521 8.37835C36.7722 8.22145 36.7217 8.06371 36.6145 7.94875L34.1424 5.29619C33.8601 4.99325 34.0201 4.49342 34.4241 4.41668L37.961 3.74477C38.1143 3.71565 38.2466 3.61816 38.3213 3.47926L40.0442 0.27451Z"
                                  fill="#F5BC29"/>
                            <path d="M57.0442 0.27451C57.241 -0.0915027 57.759 -0.0915034 57.9558 0.274509L59.6787 3.47926C59.7534 3.61816 59.8857 3.71565 60.039 3.74477L63.5759 4.41668C63.9799 4.49342 64.1399 4.99325 63.8576 5.29619L61.3855 7.94875C61.2783 8.06371 61.2278 8.22145 61.2479 8.37835L61.7109 11.9984C61.7638 12.4118 61.3447 12.7207 60.9734 12.5419L57.7226 10.9766C57.5817 10.9087 57.4183 10.9087 57.2774 10.9766L54.0266 12.5419C53.6553 12.7207 53.2362 12.4118 53.2891 11.9984L53.7521 8.37835C53.7722 8.22145 53.7217 8.06371 53.6145 7.94875L51.1424 5.29619C50.8601 4.99325 51.0201 4.49342 51.4241 4.41668L54.961 3.74477C55.1143 3.71565 55.2466 3.61816 55.3213 3.47926L57.0442 0.27451Z"
                                  fill="#F5BC29"/>
                            <path d="M74.0442 0.27451C74.241 -0.0915027 74.759 -0.0915034 74.9558 0.274509L76.6787 3.47926C76.7534 3.61816 76.8857 3.71565 77.039 3.74477L80.5759 4.41668C80.9799 4.49342 81.1399 4.99325 80.8576 5.29619L78.3855 7.94875C78.2783 8.06371 78.2278 8.22145 78.2479 8.37835L78.7109 11.9984C78.7638 12.4118 78.3447 12.7207 77.9734 12.5419L74.7226 10.9766C74.5817 10.9087 74.4183 10.9087 74.2774 10.9766L71.0266 12.5419C70.6553 12.7207 70.2362 12.4118 70.2891 11.9984L70.7521 8.37835C70.7722 8.22145 70.7217 8.06371 70.6145 7.94875L68.1424 5.29619C67.8601 4.99325 68.0201 4.49342 68.4241 4.41668L71.961 3.74477C72.1143 3.71565 72.2466 3.61816 72.3213 3.47926L74.0442 0.27451Z"
                                  fill="#F5BC29"/>
                        </svg>
                    </li>
                </ul>
            </div>
            <div class="upsale-advanced__disc">
                By clicking <b>“Add to my plan”</b> you agree to be charged <b>{{ userCurrencySign() }}<span class="total-price-placeholder">9.99</span></b> for the selected add-ons. The materials are offered on a one-time purchase basis. Payment will be processed <b>automatically</b>
                based on the billing information you provided earlier.
            </div>
            <div class="upsale-advanced__sticky-box">
                <p class="upsale-list-custom__btn-error error-text">
                    Something went wrong, please try again later
                </p>
                <div class="upsale-advanced__btn-loader loader">
                    <div class="preload loader"></div>
                </div>
                <button class="upsale-advanced__btn blue submit-upsale-btn upsale-advanced__sticky hide-btns">Add to my plan</button>
            </div>
            <div class="upsale-advanced__btn-box">
                <p class="upsale-list-custom__btn-error error-text">
                    Something went wrong, please try again later
                </p>
                <div class="upsale-advanced__btn-loader loader">
                    <div class="preload loader"></div>
                </div>
                <div id="loader-buttons" class="hide-btns">
                    <button class="upsale-advanced__btn blue submit-upsale-btn" id="hide-sticky-btn-anchor">Add to my plan</button>
                    <button class="upsale-advanced__btn grey skip-upsale-btn">Skip this and continue enrollment</button>
                </div>
            </div>
        </div>
    </div>
    <div class="upsale-advanced__discount-popup" id="upsale-discount">
        <div class="upsale-advanced__container">
            <h5 class="upsale-advanced__discount-title">
                Don't miss the chance to elevate your dog training!
            </h5>
            <p class="upsale-advanced__discount-text">
                Many beginner dog owners miss out on
                <span>potential success</span> due to not knowing the
                advanced secrets of dog training
            </p>
            <svg style="width: 100%; height: auto;" viewBox="0 0 343 188" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect x="0.5" y="0.5" width="342" height="187" rx="7.5" fill="white" stroke="#F3F3F3"/>
                <line x1="4.37114e-08" y1="140.5" x2="343" y2="140.5" stroke="#F3F3F3"/>
                <line x1="4.37114e-08" y1="93.5" x2="343" y2="93.5" stroke="#F3F3F3"/>
                <line x1="4.37114e-08" y1="46.5" x2="343" y2="46.5" stroke="#F3F3F3"/>
                <path d="M182 55C182 50.5817 185.582 47 190 47H265C269.418 47 273 50.5817 273 55V188H182V55Z" fill="#1998CD"/>
                <path d="M63 137C63 132.582 66.5817 129 71 129H146C150.418 129 154 132.582 154 137V188H63V137Z" fill="#C5C6CB"/>
                <g filter="url(#filter0_d_668_444)">
                    <rect x="85" y="87" width="48" height="24" rx="8" fill="#F9F9F9"/>
                    <path d="M101.375 94.54V103H99.9834L95.7714 97.612V103H94.0314V94.54H95.4114L99.6234 99.904V94.54H101.375ZM106.943 103.108C106.103 103.108 105.371 102.932 104.747 102.58C104.131 102.22 103.655 101.716 103.319 101.068C102.983 100.412 102.815 99.644 102.815 98.764C102.815 97.884 102.983 97.12 103.319 96.472C103.655 95.816 104.131 95.312 104.747 94.96C105.371 94.608 106.103 94.432 106.943 94.432C107.783 94.432 108.511 94.608 109.127 94.96C109.751 95.312 110.231 95.816 110.567 96.472C110.903 97.12 111.071 97.884 111.071 98.764C111.071 99.644 110.903 100.412 110.567 101.068C110.231 101.716 109.751 102.22 109.127 102.58C108.511 102.932 107.783 103.108 106.943 103.108ZM106.943 101.656C107.647 101.656 108.199 101.408 108.599 100.912C109.007 100.416 109.211 99.7 109.211 98.764C109.211 97.828 109.011 97.116 108.611 96.628C108.211 96.132 107.655 95.884 106.943 95.884C106.231 95.884 105.675 96.132 105.275 96.628C104.883 97.116 104.687 97.828 104.687 98.764C104.687 99.7 104.887 100.416 105.287 100.912C105.687 101.408 106.239 101.656 106.943 101.656ZM124.59 94.54L121.578 103H120.078L118.122 97.396L116.142 103H114.642L111.642 94.54H113.538L115.47 100.384L117.522 94.54H118.83L120.81 100.444L122.814 94.54H124.59Z" fill="#343434"/>
                    <path d="M107.957 117.31L103.75 111H114.25L109.595 117.347C109.185 117.907 108.342 117.888 107.957 117.31Z" fill="#F9F9F9"/>
                </g>
                <g filter="url(#filter1_d_668_444)">
                    <rect x="170" y="7" width="110" height="24" rx="8" fill="#343434"/>
                    <path d="M180.6 23L178.2 17.108H180.036L181.44 20.888L182.844 17.108H184.044L185.448 20.936L186.852 17.108H188.592L186.216 23H184.68L183.384 19.652L182.124 23H180.6ZM189.196 15.968V14.276H191.152V15.968H189.196ZM189.268 23V17.108H191.08V23H189.268ZM195.371 23.132C193.715 23.132 192.887 22.328 192.887 20.72V18.464H191.771V17.108H192.887V15.38H194.699V17.108H196.439V18.464H194.699V20.648C194.699 20.984 194.775 21.236 194.927 21.404C195.087 21.572 195.339 21.656 195.683 21.656C195.787 21.656 195.895 21.644 196.007 21.62C196.127 21.596 196.255 21.564 196.391 21.524L196.655 22.844C196.487 22.932 196.287 23 196.055 23.048C195.823 23.104 195.595 23.132 195.371 23.132ZM197.331 23V14.54H199.143V17.912C199.351 17.608 199.619 17.38 199.947 17.228C200.275 17.068 200.639 16.988 201.039 16.988C202.423 16.988 203.115 17.812 203.115 19.46V23H201.303V19.544C201.303 19.136 201.227 18.844 201.075 18.668C200.923 18.484 200.699 18.392 200.403 18.392C200.019 18.392 199.711 18.512 199.479 18.752C199.255 18.992 199.143 19.312 199.143 19.712V23H197.331ZM207.963 23V14.54H211.827C212.771 14.54 213.499 14.78 214.011 15.26C214.523 15.732 214.779 16.392 214.779 17.24C214.779 18.088 214.523 18.752 214.011 19.232C213.499 19.712 212.771 19.952 211.827 19.952H209.823V23H207.963ZM209.823 18.512H211.515C212.499 18.512 212.991 18.088 212.991 17.24C212.991 16.408 212.499 15.992 211.515 15.992H209.823V18.512ZM217.499 23.132C217.059 23.132 216.671 23.048 216.335 22.88C215.999 22.712 215.731 22.484 215.531 22.196C215.339 21.908 215.243 21.584 215.243 21.224C215.243 20.8 215.355 20.46 215.579 20.204C215.803 19.948 216.167 19.768 216.671 19.664C217.175 19.552 217.843 19.496 218.675 19.496H219.107V19.292C219.107 18.94 219.027 18.692 218.867 18.548C218.707 18.396 218.435 18.32 218.051 18.32C217.731 18.32 217.391 18.372 217.031 18.476C216.679 18.572 216.327 18.72 215.975 18.92L215.483 17.708C215.691 17.572 215.943 17.448 216.239 17.336C216.543 17.224 216.859 17.14 217.187 17.084C217.515 17.02 217.827 16.988 218.123 16.988C219.035 16.988 219.715 17.196 220.163 17.612C220.611 18.02 220.835 18.656 220.835 19.52V23H219.143V22.112C219.023 22.424 218.819 22.672 218.531 22.856C218.251 23.04 217.907 23.132 217.499 23.132ZM217.907 21.92C218.243 21.92 218.527 21.804 218.759 21.572C218.991 21.34 219.107 21.04 219.107 20.672V20.432H218.687C218.071 20.432 217.635 20.488 217.379 20.6C217.123 20.704 216.995 20.888 216.995 21.152C216.995 21.376 217.071 21.56 217.223 21.704C217.383 21.848 217.611 21.92 217.907 21.92ZM223.796 23L221.396 17.108H223.232L224.636 20.888L226.04 17.108H227.24L228.644 20.936L230.048 17.108H231.788L229.412 23H227.876L226.58 19.652L225.32 23H223.796ZM236.724 23.132C235.812 23.132 235.032 22.952 234.384 22.592C233.736 22.232 233.236 21.728 232.884 21.08C232.54 20.424 232.368 19.652 232.368 18.764C232.368 17.884 232.54 17.12 232.884 16.472C233.236 15.816 233.736 15.308 234.384 14.948C235.032 14.588 235.812 14.408 236.724 14.408C237.284 14.408 237.82 14.496 238.332 14.672C238.844 14.848 239.264 15.088 239.592 15.392L239.016 16.868C238.656 16.588 238.292 16.384 237.924 16.256C237.564 16.12 237.184 16.052 236.784 16.052C235.976 16.052 235.364 16.288 234.948 16.76C234.54 17.224 234.336 17.892 234.336 18.764C234.336 19.636 234.54 20.308 234.948 20.78C235.364 21.252 235.976 21.488 236.784 21.488C237.184 21.488 237.564 21.424 237.924 21.296C238.292 21.16 238.656 20.952 239.016 20.672L239.592 22.148C239.264 22.444 238.844 22.684 238.332 22.868C237.82 23.044 237.284 23.132 236.724 23.132ZM240.655 23V14.54H242.467V17.912C242.675 17.608 242.943 17.38 243.271 17.228C243.599 17.068 243.963 16.988 244.363 16.988C245.747 16.988 246.439 17.812 246.439 19.46V23H244.627V19.544C244.627 19.136 244.551 18.844 244.399 18.668C244.247 18.484 244.023 18.392 243.727 18.392C243.343 18.392 243.035 18.512 242.803 18.752C242.579 18.992 242.467 19.312 242.467 19.712V23H240.655ZM249.784 23.132C249.344 23.132 248.956 23.048 248.62 22.88C248.284 22.712 248.016 22.484 247.816 22.196C247.624 21.908 247.528 21.584 247.528 21.224C247.528 20.8 247.64 20.46 247.864 20.204C248.088 19.948 248.452 19.768 248.956 19.664C249.46 19.552 250.128 19.496 250.96 19.496H251.392V19.292C251.392 18.94 251.312 18.692 251.152 18.548C250.992 18.396 250.72 18.32 250.336 18.32C250.016 18.32 249.676 18.372 249.316 18.476C248.964 18.572 248.612 18.72 248.26 18.92L247.768 17.708C247.976 17.572 248.228 17.448 248.524 17.336C248.828 17.224 249.144 17.14 249.472 17.084C249.8 17.02 250.112 16.988 250.408 16.988C251.32 16.988 252 17.196 252.448 17.612C252.896 18.02 253.12 18.656 253.12 19.52V23H251.428V22.112C251.308 22.424 251.104 22.672 250.816 22.856C250.536 23.04 250.192 23.132 249.784 23.132ZM250.192 21.92C250.528 21.92 250.812 21.804 251.044 21.572C251.276 21.34 251.392 21.04 251.392 20.672V20.432H250.972C250.356 20.432 249.92 20.488 249.664 20.6C249.408 20.704 249.28 20.888 249.28 21.152C249.28 21.376 249.356 21.56 249.508 21.704C249.668 21.848 249.896 21.92 250.192 21.92ZM254.495 23V17.108H256.271V17.96C256.447 17.656 256.687 17.42 256.991 17.252C257.303 17.076 257.663 16.988 258.071 16.988C258.487 16.988 258.839 17.08 259.127 17.264C259.423 17.448 259.647 17.728 259.799 18.104C259.983 17.752 260.247 17.48 260.591 17.288C260.943 17.088 261.331 16.988 261.755 16.988C262.427 16.988 262.927 17.192 263.255 17.6C263.583 18 263.747 18.62 263.747 19.46V23H261.935V19.52C261.935 19.128 261.871 18.844 261.743 18.668C261.623 18.484 261.415 18.392 261.119 18.392C260.775 18.392 260.507 18.512 260.315 18.752C260.123 18.992 260.027 19.332 260.027 19.772V23H258.215V19.52C258.215 19.128 258.151 18.844 258.023 18.668C257.895 18.484 257.687 18.392 257.399 18.392C257.055 18.392 256.787 18.512 256.595 18.752C256.403 18.992 256.307 19.332 256.307 19.772V23H254.495ZM265.135 25.16V17.108H266.911V17.984C267.071 17.68 267.315 17.44 267.643 17.264C267.979 17.08 268.355 16.988 268.771 16.988C269.283 16.988 269.731 17.112 270.115 17.36C270.507 17.608 270.811 17.96 271.027 18.416C271.243 18.872 271.351 19.416 271.351 20.048C271.351 20.68 271.243 21.228 271.027 21.692C270.811 22.148 270.507 22.504 270.115 22.76C269.731 23.008 269.283 23.132 268.771 23.132C268.379 23.132 268.019 23.048 267.691 22.88C267.371 22.712 267.123 22.488 266.947 22.208V25.16H265.135ZM268.231 21.776C268.615 21.776 268.927 21.636 269.167 21.356C269.407 21.076 269.527 20.64 269.527 20.048C269.527 19.464 269.407 19.036 269.167 18.764C268.927 18.484 268.615 18.344 268.231 18.344C267.839 18.344 267.523 18.484 267.283 18.764C267.043 19.036 266.923 19.464 266.923 20.048C266.923 20.64 267.043 21.076 267.283 21.356C267.523 21.636 267.839 21.776 268.231 21.776Z" fill="white"/>
                    <path d="M223.957 37.3105L219.75 31H230.25L225.595 37.3471C225.185 37.9072 224.342 37.8883 223.957 37.3105Z" fill="#343434"/>
                </g>
                <defs>
                    <filter id="filter0_d_668_444" x="77" y="83" width="64" height="47.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                        <feOffset dy="4"/>
                        <feGaussianBlur stdDeviation="4"/>
                        <feComposite in2="hardAlpha" operator="out"/>
                        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.16 0"/>
                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_668_444"/>
                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_668_444" result="shape"/>
                    </filter>
                    <filter id="filter1_d_668_444" x="162" y="3" width="126" height="47.5" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                        <feOffset dy="4"/>
                        <feGaussianBlur stdDeviation="4"/>
                        <feComposite in2="hardAlpha" operator="out"/>
                        <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.16 0"/>
                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_668_444"/>
                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_668_444" result="shape"/>
                    </filter>
                </defs>
            </svg>
            <p class="upsale-advanced__discount-attention">
                We want you to succeed, which is why we’re offering an <span>additional discount</span> for <span>Advanced Dog Training Series</span> that worth attention.
            </p>
            <button class="upsale-advanced__btn blue" id="discount-popup-close">Got it</button>
        </div>
    </div>
</aside>
