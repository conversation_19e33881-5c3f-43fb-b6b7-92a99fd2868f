{% include 'subscription/components/google-tag-manager/google-tag-manager-head.html.twig' %}
<meta charset="UTF-8">
<title>{{ title|default('PawChamp') }}</title>
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="description" content="{{ description|default('Professional analysis of your dog\'s problems with easy and fun solutions') }}">
<meta name="chrome" content="nointentdetection"/>
<link rel="shortcut icon" href="/favicon.svg" type="image/x-icon">
<link rel="icon" href="/favicon.svg" type="image/x-icon">

{% block viewportMetaTags %}
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no, maximum-scale=1">
{% endblock %}

{% set basePath = app.request.pathinfo %}

{% if canonical_href is defined and canonical_href %}
    <link rel="canonical" href="{{ canonical_href }}"/>
{% elseif basePath starts with '/blog' %}
    {% include 'common/components/seo-meta-tags.html.twig' %}
{% endif %}

{% include 'subscription/components/config.html.twig' %}

<script src="{{ asset("/assets/common/common.js") }}"></script>
<meta name="facebook-domain-verification" content="acivw1bz7jc2dy0u172ukris692z9o"/>

{% block additionalStyles %}{% endblock %}

<link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Nunito+Sans:wght@400;500;600;700;800;900&display=swap" rel="stylesheet">

{% if getSplitValue('pc_newdes') == 2 %}
    <link href="https://fonts.googleapis.com/css2?family=Outfit:wght@100..900&display=swap" rel="stylesheet">
    {# Preload background images for 1-7 steps to prevent flash delay, remove it on pc_newdes split close #}
    {% for imageId in 1..7 %}
        <link rel="preload" as="image" href="https://images.paw-champ.com/pc/images/backgrounds/newdes-background-{{ imageId }}.png">
    {% endfor %}
{% endif %}

{% block additionalScripts %}
    {#    Function that shows all user's split data for testing purposes #}
    <script type="text/javascript">
        window.termsLink = '{{ path('static_page_terms') }}';
        window.allSplitValues = {{ getAllSplitsValues()|json_encode(constant('JSON_PRETTY_PRINT'))|raw }} ||
        {
        }
        window.clientCountry = '{{ getCountry()|upper }}';

        const getSplitsData = () => console.table(window.allSplitValues);
    </script>
{% endblock %}

{% include 'common/hotjar.html.twig' %}

<script>
    {% set funnelName = funnelName.value ?? quizData.funnelName.value ?? '' %}
    {% set locale = quizData.locale.value ?? locale.value ?? 'en' %}

    const funnelNamesObject = {{ getFunnelNames()|json_encode(constant('JSON_PRETTY_PRINT'))|raw }} || {}

    window.routeName = '{{ app.request.attributes.get('_route') ?? '' }}';
    window.isDiscountLanding = {{ (isDiscountLanding ?? false)|json_encode }};
    window.funnelNamesMap = new Map(Object.entries(funnelNamesObject));
    window.funnelName = '{{ funnelName }}';

    {% set funnelTranslationsDomainSuffix = app.request.get('_route') == 'quizsubscription' ? '-quiz' : '-landing' %}
    {% set funnelTranslationsDomain = funnelName ~ funnelTranslationsDomainSuffix %}
    {% set funnelTranslations = getDomainTranslations(funnelTranslationsDomain, locale) | default({}) | md_parse_map %}

    window.funnelTranslations = {{ funnelTranslations | json_encode | raw }};
    window.sharedTranslations = {{ getDomainTranslations('shared', locale) | default({}) | json_encode | raw }};
    window.translations = Object.assign({}, window.sharedTranslations, window.funnelTranslations);

    /**
     * Straightforward fix for annoying error.
     * @link https://sentry.io/organizations/maze-company/issues/3288471131/?project=5888847
     * @returns {*[]}
     * @private
     */
    window._AutofillCallbackHandler = window._AutofillCallbackHandler || function () {
        return [];
    }

    /**
     * @link https://sentry.io/organizations/maze-company/issues/3391859075/?project=5888847
     * @type {*|(function())|(function())}
     * @private
     */
    window._pcmBridgeCallbackHandler = window._pcmBridgeCallbackHandler || function () {
    }

    /**
     * for VAT
     * @type object
     */
    if (window.location.href.indexOf('landing-discount-80') === -1 && window.location.href.indexOf('landing-5') === -1) {
        if (window.location.href.indexOf('-discount') === -1) {
            // main
            window.VATPrices = {
                oneMonth: {
                    vat: {{ calculateTax(19.99) }},
                    fullPrice: 1.39,
                    firstNumberPrice: 0,
                    secondNumberPrice: 56,
                    uk: {
                        fullPrice: 1.11,
                        firstNumberPrice: 0,
                        secondNumberPrice: 56,
                    }
                },
                threeMonth: {
                    vat: {{ calculateTax(31.99) }},
                    fullPrice: 0.74,
                    firstNumberPrice: 0,
                    secondNumberPrice: 30,
                    uk: {
                        fullPrice: 0.62,
                        firstNumberPrice: 0,
                        secondNumberPrice: 30,
                    }
                },
                sixMonth: {
                    vat: {{ calculateTax(47.99) }},
                    fullPrice: 0.56,
                    firstNumberPrice: 0,
                    secondNumberPrice: 22,
                    uk: {
                        fullPrice: 0.44,
                        firstNumberPrice: 0,
                        secondNumberPrice: 22,
                    }
                },
            }
        } else {
            // discount
            window.VATPrices = {
                oneMonth: {
                    vat: {{ calculateTax(17.99) }},
                    fullPrice: 1.39,
                    firstNumberPrice: 0,
                    secondNumberPrice: 50,
                    uk: {
                        fullPrice: 1.11,
                        firstNumberPrice: 0,
                        secondNumberPrice: 50,
                    }
                },
                threeMonth: {
                    vat: {{ calculateTax(29.99) }},
                    fullPrice: 0.74,
                    firstNumberPrice: 0,
                    secondNumberPrice: 27,
                    uk: {
                        fullPrice: 0.62,
                        firstNumberPrice: 0,
                        secondNumberPrice: 28,
                    }
                },
                sixMonth: {
                    vat: {{ calculateTax(43.99) }},
                    fullPrice: 0.56,
                    firstNumberPrice: 0,
                    secondNumberPrice: 20,
                    uk: {
                        fullPrice: 0.44,
                        firstNumberPrice: 0,
                        secondNumberPrice: 20,
                    }
                },
            }
        }
    }

    window.upsaleData = {
        price: {
            main: 9.99,
            discount: 7.99,
            VATMain: {{ calculateTax(9.99) }},
            VATDiscount: {{ calculateTax(7.99) }},
        },
    }

    // add country to global window object for disclamers
    window.clientCountry = '{{ getCountry()|upper }}';
</script>
