<aside class="thank-you-popup">
    <div class="thank-you-popup__bg" id="thank-youBg"></div>
    <div class="thank-you-popup__inner">
        <div class="thank-you-popup__content challenge">
            <h3 class="thank-you-popup__title">
                {{ 'landing.thank_you_popup.title'|resp_i11n_trans(domain=translations_domain) }}
            </h3>
            <p class="thank-you-popup__subtitle challenge">
                {{ 'landing.thank_you_popup.subtitle'|resp_i11n_trans(domain=translations_domain) }}
            </p>
            <p class="thank-you-popup__text">
                {{ 'landing.thank_you_popup.text'|resp_i11n_trans(domain=translations_domain) }}
            </p>

            <div class="thank-you-popup__container">
                <div class="thank-you-popup__frame challenge">
                    <img class="thank-you-popup__frame-img" src="{{ (funnelName ~ '.landing.checkout_img_png')|trans(domain="assets") }}" alt="" id="thankYouPopupImg">
                </div>
            </div>

            <div class="thank-you-popup__list-item active challenge">
                <p class="thank-you-popup__list-item-text bold challenge {% if locale == 'de' %}text-centered{% endif %}">
                    {{ 'landing.thank_you_popup.list_item_text'|resp_i11n_trans(domain=translations_domain) }}
                </p>
                <img class="thank-you-popup__list-item-img" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1672220859/dog-training/img/thank-you-popup/checked.png" alt="">
            </div>

            <p class="thank-you-popup__text-small">
                {{ 'landing.thank_you_popup.contact_info'|resp_i11n_trans({
                    '{supportEmail}': support_email,
                }, domain=translations_domain) }}
            </p>
            <div class="thank-you-popup__btn-wrapp">
                <a href="{{ path('login') }}" class="thank-you-popup__btn" id="thankYouPopupBtn">
                    {{ 'landing.thank_you_popup.start_button'|resp_i11n_trans(domain=translations_domain) }}
                </a>
            </div>
        </div>
    </div>
</aside>
