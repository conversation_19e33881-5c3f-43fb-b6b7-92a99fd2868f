{% set pc_disc_dokrut_love_split = getSplitValue('pc_disc_dokrut_love') %}
{% set pc_disc_dokrut_split = getSplitValue('pc_disc_dokrut') %}
{% set is_pc_discount =  pc_disc_dokrut_love_split > 1 or pc_disc_dokrut_split > 1 %}

<div class="landing__promocode-parent-container {% if is_pc_discount %}pc-discount-dokrut{% endif %}" id="promocode">
    <div class="landing__promocode discount">
        <div class="landing__promocode-wrapp top-round">
            <div class="landing__promocode-discount-wrapp">
                <p class="landing__promocode-discount-previous product-discount-old-value">
                    {{ previousDiscountPercents }}
                </p>
                <p class="landing__promocode-discount-current">
                    <span class="product-discount-value">{{ discountPercents }}</span>
                    {{ 'landing.promocode.discount_off'|resp_i11n_trans(domain=translations_domain) }}
                </p>
            </div>

            <p class="landing__promocode-title">
                {{ 'landing.promocode.discount_promocode_applied'|resp_i11n_trans(domain=translations_domain) }}
            </p>
        </div>
        <div class="landing__promocode-wrapp bottom-round">
            <div class="landing__promocode-name-box">
                <h5 class="landing__promocode-name">
                    <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M1.71436 5.51106L6.12679 10.5538C6.22217 10.6628 6.3982 10.6375 6.45889 10.5059L10.2858 2.21436" stroke="#61C27D" stroke-width="1.5" stroke-linecap="round"/>
                    </svg>
                    <span class="promocode-name-selector">promocode</span>
                </h5>
            </div>
            <div>
                <p class="landing__promocode-timer" id="countdown6">{{ getFormattedTimeToCountdownFinish() }}</p>
                <div class="landing__promocode-timer-box">
                    <span class="landing__promocode-timer-text">{{ 'landing.promocode.minutes'|resp_i11n_trans(domain=translations_domain) }}</span>
                    <span class="landing__promocode-timer-text">{{ 'landing.promocode.seconds'|resp_i11n_trans(domain=translations_domain) }}</span>
                </div>
            </div>
        </div>
    </div>
</div>
