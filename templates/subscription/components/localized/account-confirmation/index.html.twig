{% set funnelTitle = funnelTitle ?? '' %}
{% set funnelImgPath = funnelImgPath ?? '' %}

<div class="confirm-page z-1002">
    <div class="confirm-page-logo">
        <img class="confirm-page-logo-img " src="https://images.paw-champ.com/pc/logo/logo-with-shadow.svg" alt="">
    </div>

    <div class="confirm-page_block">
        <h3 class="confirm-page_header red">{{ 'landing.account_confirmation.welcome_message'|resp_i11n_trans(domain=translations_domain) }}</h3>
        <h2 class="confirm-page_title">
            {{ 'landing.account_confirmation.successfully_created'|resp_i11n_trans(domain=translations_domain) }}
        </h2>
        <p class="confirm-page_subtitle mb-24">
            {{ 'landing.account_confirmation.download_app_description'|resp_i11n_trans(domain=translations_domain) }}
        </p>

        <div class="thank-you-popup__frame">
            <img class="thank-you-popup__frame-img" src="{{ funnelImgPath }}">
        </div>

        <div class="confirm-page_done">
            <p class="confirm-page_done-text">
                <span class="">{{ funnelTitle }}</span>
            </p>
            <img class="confirm-page_done-img"
                src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1672220859/dog-training/img/thank-you-popup/checked.png"
                 alt="check mark button emoji"
            >
        </div>

        {% if getSplitValue('pc_flowtoapp') == 2 %}
            {% include 'subscription/components/localized/account-confirmation/install-instruction/index.html.twig' %}
        {% else %}
            {% include 'subscription/components/localized/account-confirmation/note.html.twig' %}
        {% endif %}

        {% include 'subscription/components/localized/account-confirmation/store-link.html.twig' %}
    </div>
</div>
