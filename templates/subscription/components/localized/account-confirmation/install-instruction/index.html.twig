{% set platformStoreEl %}
    <span class='platform-type' data-platform-type='App Store'>Google Play</span>
{% endset %}

{% set instructionSteps = [
    {
        text: "landing.flow_to_app.step_1"|resp_i11n_trans({
            '{platformStore}': platformStoreEl|trim,
        }, domain=translations_domain)|raw,
        imgGlobalPath: ""
    },
    {
        text: "landing.flow_to_app.step_2"|resp_i11n_trans(domain=translations_domain),
        imgGlobalPath: "https://images.paw-champ.com/pc/images/common/login-screen-dogs-image"
    },
    {
        text: "landing.flow_to_app.step_3"|resp_i11n_trans(domain=translations_domain),
        imgGlobalPath: "https://images.paw-champ.com/pc/images/common/login-form-email-password"
    },
    {
        text: "landing.flow_to_app.step_4"|resp_i11n_trans(domain=translations_domain),
        imgGlobalPath: ""
    },
] %}

<div class="confirm-page_instruction">
    <h3 class="confirm-page_header mb-24">
        {{ 'landing.flow_to_app.title'|resp_i11n_trans(domain=translations_domain) }}
    </h3>

    <div class="confirm-page_instruction-box">
        {% for step in instructionSteps %}
            {% include 'subscription/components/localized/account-confirmation/install-instruction/instruction-step.html.twig' with {
                stepNumber: loop.index,
                text: step.text,
                imgGlobalPath: step.imgGlobalPath
            } %}
        {% endfor %}
    </div>

    {% include 'subscription/components/localized/account-confirmation/install-instruction/instruction-trouble.html.twig' %}
</div>
