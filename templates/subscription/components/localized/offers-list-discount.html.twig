{% set OFFER_ITEM_VALUES_MAIN =  {
    "one_month": {
        "type": "1m_lower_intro_full_monthly_discount",
        "full_price": "49.99",
        "total_price": "17.99",
        "discount": "253.99",
        "day_price": "1.67",
        "discount_day_price": "60",
    },
    "three_month": {
        "type": "3m_lower_intro_full_quarterly_discount",
        "full_price": "79.99",
        "total_price": "29.99",
        "discount": "271.99",
        "day_price": "0.89",
        "discount_day_price": "33",
    },
    "six_month": {
        "type": "6m_lower_intro_full_halfyear_discount",
        "full_price": "119.99",
        "total_price": "43.99",
        "discount": "296.99",
        "day_price": "0.67",
        "discount_day_price": "24",
    }
} %}

{% set OFFER_ITEM_VALUES_MAIN_VAT_UK =  {
    "one_month": {
        "type": "1month_LP_UK_discount",
        "full_price": "39.99",
        "total_price": "17.99",
        "discount": "243.58",
        "day_price": "1.11",
        "discount_day_price": "50",
    },
    "three_month": {
        "type": "3month_LP_UK_discount",
        "full_price": "66.65",
        "total_price": "29.99",
        "discount": "257.98",
        "day_price": "0.62",
        "discount_day_price": "28",
    },
    "six_month": {
        "type": "6month_LP_UK_discount",
        "full_price": "95.99",
        "total_price": "43.99",
        "discount": "273.82",
        "day_price": "0.44",
        "discount_day_price": "20",
    }
} %}

{% set isGB = getCountry()|upper == 'GB' %}
{% set offer_values = isGB ? OFFER_ITEM_VALUES_MAIN_VAT_UK : OFFER_ITEM_VALUES_MAIN %}
{% set isChallengeFunnel = funnelName == 'challenge' %}
{% set isVagusFunnel = funnelName == 'vagus' %}
{% set isEnglishLocale = locale == 'en' %}
{% set isEnglishChallengeFunnel = isChallengeFunnel and isEnglishLocale %}
{% set pc_disc_dokrut_love_split = getSplitValue('pc_disc_dokrut_love') %}
{% set isPcDiscountPercent = pc_disc_dokrut_love_split > 1 or isEnglishChallengeFunnel or isVagusFunnel %}

<ul>
    <li class="landing__offer-item active most-popular {{ localeLanguageClassModifier }}" data-offer-value="4-week plan">
        <div class="landing__offer-item-inner">
            <div class="landing__offer-item-box">
                <input class="paymentInfo"
                       type="radio"
                       checked
                       id="dog-breed-radio-1"
                       name="dog-breed-radio"
                       data-payment-info='{"type": "{{ offer_values.one_month.type }}", "full_price": "{{ offer_values.one_month.full_price }}", "total_price": "{{ offer_values.one_month.total_price }}", "discount": "{{ offer_values.one_month.discount }}"}'
                >
                <label for="dog-breed-radio-1"></label>
                <h4 class="landing__offer-item-title">
                    {{ 'landing.offer.plan_4_week'|resp_i11n_trans(domain=translations_domain) }}
                </h4>
            </div>
            <div class="landing__offer-item-price-wrapper">
                <p class="landing__offer-item-price-full">
                    <span>{{ userCurrencySign() }}{{ offer_values.one_month.day_price }}</span>
                </p>
                <div class="landing__offer-item-price-box">
                    <div class="landing__offer-item-price">
                        <div class="landing__offer-item-price-sign">
                            <span>{{ userCurrencySign() }}</span>
                            <p>0</p>
                        </div>
                        <div class="landing__offer-item-price-value">
                            <span>{{ offer_values.one_month.discount_day_price }}</span>
                            <p class="landing__offer-item-price-text">{{ 'landing.offer.per_day'|resp_i11n_trans(domain=translations_domain) }}</p>
                        </div>
                    </div>
                </div>
            </div>

            {% if isPcDiscountPercent %}
                <div class="pcdiscount_plan-percent">-{{ discountPercents }}</div>
            {% endif %}
        </div>
    </li>
    <li class="landing__offer-item" data-offer-value="12-week plan">
        <div class="landing__offer-item-inner">
            <div class="landing__offer-item-box">
                <input class="paymentInfo"
                       type="radio"
                       id="dog-breed-radio-2"
                       name="dog-breed-radio"
                       data-payment-info='{"type": "{{ offer_values.three_month.type }}", "full_price": "{{ offer_values.three_month.full_price }}", "total_price": "{{ offer_values.three_month.total_price }}", "discount": "{{ offer_values.three_month.discount }}"}'
                >
                <label for="dog-breed-radio-2"></label>
                <h4 class="landing__offer-item-title">
                    {{ 'landing.offer.plan_12_week'|resp_i11n_trans(domain=translations_domain) }}
                </h4>
            </div>
            <div class="landing__offer-item-price-wrapper">
                <p class="landing__offer-item-price-full">
                    <span>{{ userCurrencySign() }}{{ offer_values.three_month.day_price }}</span>
                </p>
                <div class="landing__offer-item-price-box">
                    <div class="landing__offer-item-price">
                        <div class="landing__offer-item-price-sign">
                            <span>{{ userCurrencySign() }}</span>
                            <p>0</p>
                        </div>
                        <div class="landing__offer-item-price-value">
                            <span>{{ offer_values.three_month.discount_day_price }}</span>
                            <p class="landing__offer-item-price-text">{{ 'landing.offer.per_day'|resp_i11n_trans(domain=translations_domain) }}</p>
                        </div>
                    </div>
                </div>
            </div>

            {% if isPcDiscountPercent %}
                <div class="pcdiscount_plan-percent">-{{ discountPercents }}</div>
            {% endif %}
        </div>
    </li>
    <li class="landing__offer-item" data-offer-value="24-week plan">
        <div class="landing__offer-item-inner">
            <div class="landing__offer-item-box">
                <input class="paymentInfo"
                       type="radio"
                       id="dog-breed-radio-3"
                       name="dog-breed-radio"
                       data-payment-info='{"type": "{{ offer_values.six_month.type }}", "full_price": "{{ offer_values.six_month.full_price }}", "total_price": "{{ offer_values.six_month.total_price }}", "discount": "{{ offer_values.six_month.discount }}"}'
                >
                <label for="dog-breed-radio-3"></label>
                <h4 class="landing__offer-item-title">
                    {{ 'landing.offer.plan_24_week'|resp_i11n_trans(domain=translations_domain) }}
                </h4>
            </div>
            <div class="landing__offer-item-price-wrapper">
                <p class="landing__offer-item-price-full">
                    <span>{{ userCurrencySign() }}{{ offer_values.six_month.day_price }}</span>
                </p>
                <div class="landing__offer-item-price-box">
                    <div class="landing__offer-item-price">
                        <div class="landing__offer-item-price-sign">
                            <span>{{ userCurrencySign() }}</span>
                            <p>0</p>
                        </div>
                        <div class="landing__offer-item-price-value">
                            <span>{{ offer_values.six_month.discount_day_price }}</span>
                            <p class="landing__offer-item-price-text">{{ 'landing.offer.per_day'|resp_i11n_trans(domain=translations_domain) }}</p>
                        </div>
                    </div>
                </div>
            </div>

            {% if isPcDiscountPercent %}
                <div class="pcdiscount_plan-percent">-{{ discountPercents }}</div>
            {% endif %}
        </div>
    </li>
</ul>
