{% set pc_bundle_split = getSplitValue('pc_bundle') %}

<div class="landing__offer-people usersBought">
    <div class="landing__goal-top red">
        <b><span class="discount-label">{{ discountPercents }}</span> {{ 'landing.discount'|resp_i11n_trans(domain=translations_domain) }}</b>
        <span id="timerText">{{ 'landing.reserved_timer'|resp_i11n_trans(domain=translations_domain) }} <span id="countdown3">{{ getFormattedTimeToCountdownFinish() }}</span></span>
    </div>
    <div class="landing__goal-wrapp">
        {% if pc_bundle_split > 1 %}
            <div class="course_landing">
                <h2 class="course_landing-header">These journeys are tailored to support your goals!</h2>

                <div class="course-slot-list-landing">
                    {% for place in ['1st journey', '2nd journey', '3rd journey'] %}
                        <div class="course-slot-list-item" data-course-id="">
                            <div class="course-slot-list-item-icon"></div>

                            <p class="course-slot-list-item-text">{{ place }}</p>
                        </div>
                    {% endfor %}
                </div>
            </div>

        {% else %}
            <picture>
                <source srcset="https://res.cloudinary.com/dr0cx27xo/image/upload/v1718206018/dog-training/img/landing/usersBought.webp" type="image/webp">
                <img class="landing__goal-img" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1700826017/dog-training/img/usersBought.jpg" alt="dogs group">
            </picture>

            <span class="landing__goal-text">
                {{ 'landing.goal_text'|resp_i11n_trans(domain=translations_domain) }}
            </span>
        {% endif %}
    </div>
    <div class="landing__goal-people grey">
        {% if isPcTrustSplitGroup2Active %}
            <svg class="landing__goal-trust" width="319" height="37" viewBox="0 0 319 37" fill="none" xmlns="http://www.w3.org/2000/svg">
                <rect y="0.248047" width="319" height="36" rx="8" fill="white"/>
                <path d="M16.8473 25.468C15.2606 25.468 13.9139 25.168 12.8073 24.568C11.7139 23.968 10.8806 23.128 10.3073 22.048C9.73393 20.968 9.44727 19.7014 9.44727 18.248C9.44727 16.7547 9.7406 15.4614 10.3273 14.368C10.9273 13.2747 11.7873 12.428 12.9073 11.828C14.0406 11.228 15.3873 10.928 16.9473 10.928C17.6139 10.928 18.2539 10.9947 18.8673 11.128C19.4806 11.248 20.0473 11.4214 20.5673 11.648C21.1006 11.8747 21.5539 12.148 21.9273 12.468L20.9273 14.888C20.3139 14.4347 19.6873 14.1147 19.0473 13.928C18.4073 13.728 17.7139 13.628 16.9673 13.628C15.5406 13.628 14.4673 14.0214 13.7473 14.808C13.0406 15.5947 12.6873 16.7414 12.6873 18.248C12.6873 19.7547 13.0473 20.8947 13.7673 21.668C14.5006 22.4414 15.5806 22.828 17.0073 22.828C17.4873 22.828 17.9806 22.7814 18.4873 22.688C18.9939 22.5947 19.4939 22.4614 19.9873 22.288L19.4073 23.548V19.708H16.3673V17.468H21.9673V24.388C21.5139 24.6147 21.0006 24.808 20.4273 24.968C19.8673 25.128 19.2806 25.248 18.6673 25.328C18.0539 25.4214 17.4473 25.468 16.8473 25.468ZM24.451 25.248V15.428H27.411V17.828H27.211C27.371 17.028 27.7177 16.4147 28.251 15.988C28.7977 15.548 29.5177 15.2947 30.411 15.228L31.291 15.168L31.471 17.708L29.771 17.888C29.011 17.9547 28.4443 18.1747 28.071 18.548C27.711 18.9214 27.531 19.4614 27.531 20.168V25.248H24.451ZM37.6174 25.468C36.4574 25.468 35.4574 25.2614 34.6174 24.848C33.7908 24.4214 33.1508 23.828 32.6974 23.068C32.2574 22.2947 32.0374 21.388 32.0374 20.348C32.0374 19.3347 32.2508 18.448 32.6774 17.688C33.1041 16.9147 33.6974 16.3147 34.4574 15.888C35.2308 15.448 36.1041 15.228 37.0774 15.228C38.0374 15.228 38.8641 15.4347 39.5574 15.848C40.2508 16.248 40.7841 16.8214 41.1574 17.568C41.5441 18.3147 41.7374 19.2014 41.7374 20.228V20.988H34.5174V19.428H39.5574L39.2374 19.708C39.2374 18.8947 39.0574 18.2814 38.6974 17.868C38.3508 17.4414 37.8508 17.228 37.1974 17.228C36.7041 17.228 36.2841 17.3414 35.9374 17.568C35.5908 17.7947 35.3241 18.1214 35.1374 18.548C34.9508 18.9747 34.8574 19.488 34.8574 20.088V20.248C34.8574 20.928 34.9574 21.488 35.1574 21.928C35.3708 22.3547 35.6841 22.6747 36.0974 22.888C36.5241 23.1014 37.0508 23.208 37.6774 23.208C38.2108 23.208 38.7508 23.128 39.2974 22.968C39.8441 22.808 40.3374 22.5614 40.7774 22.228L41.5774 24.248C41.0841 24.6214 40.4774 24.9214 39.7574 25.148C39.0508 25.3614 38.3374 25.468 37.6174 25.468ZM46.814 25.468C46.094 25.468 45.4473 25.328 44.874 25.048C44.3007 24.768 43.854 24.388 43.534 23.908C43.214 23.428 43.054 22.888 43.054 22.288C43.054 21.568 43.2407 21.0014 43.614 20.588C43.9873 20.1614 44.594 19.8614 45.434 19.688C46.274 19.5014 47.3873 19.408 48.774 19.408H49.834V20.968H48.794C48.274 20.968 47.834 20.988 47.474 21.028C47.1273 21.068 46.8407 21.1414 46.614 21.248C46.3873 21.3414 46.2207 21.4614 46.114 21.608C46.0207 21.7547 45.974 21.9414 45.974 22.168C45.974 22.5414 46.1007 22.848 46.354 23.088C46.6207 23.328 47.0007 23.448 47.494 23.448C47.8807 23.448 48.2207 23.3614 48.514 23.188C48.8207 23.0014 49.0607 22.7547 49.234 22.448C49.4073 22.128 49.494 21.768 49.494 21.368V19.068C49.494 18.4814 49.3607 18.068 49.094 17.828C48.8273 17.5747 48.374 17.448 47.734 17.448C47.2007 17.448 46.634 17.5347 46.034 17.708C45.4473 17.868 44.8607 18.1147 44.274 18.448L43.454 16.428C43.8007 16.188 44.2207 15.9814 44.714 15.808C45.2207 15.6214 45.7473 15.4814 46.294 15.388C46.8407 15.2814 47.3607 15.228 47.854 15.228C48.8807 15.228 49.7207 15.3814 50.374 15.688C51.0407 15.9814 51.5407 16.4414 51.874 17.068C52.2073 17.6814 52.374 18.4747 52.374 19.448V25.248H49.554V23.268H49.694C49.614 23.7214 49.4407 24.1147 49.174 24.448C48.9207 24.768 48.594 25.0214 48.194 25.208C47.794 25.3814 47.334 25.468 46.814 25.468ZM59.4468 25.468C58.0468 25.468 57.0068 25.128 56.3268 24.448C55.6468 23.768 55.3068 22.768 55.3068 21.448V17.688H53.4468V15.428H55.3068V12.548H58.3268V15.428H61.2268V17.688H58.3268V21.328C58.3268 21.888 58.4535 22.308 58.7068 22.588C58.9735 22.868 59.3935 23.008 59.9668 23.008C60.1401 23.008 60.3201 22.988 60.5068 22.948C60.7068 22.908 60.9201 22.8547 61.1468 22.788L61.5868 24.988C61.3068 25.1347 60.9735 25.248 60.5868 25.328C60.2001 25.4214 59.8201 25.468 59.4468 25.468Z"
                      fill="#16191E"/>
                <rect width="20" height="20" transform="translate(87.0117 8.24805)" fill="#00B640"/>
                <path d="M97.0117 21.9241L100.053 21.1097L101.324 25.248L97.0117 21.9241ZM104.012 16.5751H98.6575L97.0117 11.248L95.3659 16.5751H90.0117L94.3451 19.877L92.6992 25.204L97.0326 21.9021L99.6992 19.877L104.012 16.5751Z"
                      fill="white"/>
                <rect width="20" height="20" transform="translate(111.012 8.24805)" fill="#00B640"/>
                <path d="M121.012 21.9241L124.053 21.1097L125.324 25.248L121.012 21.9241ZM128.012 16.5751H122.658L121.012 11.248L119.366 16.5751H114.012L118.345 19.877L116.699 25.204L121.033 21.9021L123.699 19.877L128.012 16.5751Z"
                      fill="white"/>
                <rect width="20" height="20" transform="translate(135.012 8.24805)" fill="#00B640"/>
                <path d="M145.012 21.9241L148.053 21.1097L149.324 25.248L145.012 21.9241ZM152.012 16.5751H146.658L145.012 11.248L143.366 16.5751H138.012L142.345 19.877L140.699 25.204L145.033 21.9021L147.699 19.877L152.012 16.5751Z"
                      fill="white"/>
                <rect width="20" height="20" transform="translate(159.012 8.24805)" fill="#00B640"/>
                <path d="M169.012 21.9241L172.053 21.1097L173.324 25.248L169.012 21.9241ZM176.012 16.5751H170.658L169.012 11.248L167.366 16.5751H162.012L166.345 19.877L164.699 25.204L169.033 21.9021L171.699 19.877L176.012 16.5751Z"
                      fill="white"/>
                <rect width="20" height="20" transform="translate(183.012 8.24805)" fill="#F2F2F2"/>
                <path d="M193.012 21.9241L196.053 21.1097L197.324 25.248L193.012 21.9241ZM200.012 16.5751H194.658L193.012 11.248L191.366 16.5751H186.012L190.345 19.877L188.699 25.204L193.033 21.9021L195.699 19.877L200.012 16.5751Z"
                      fill="white"/>
                <path d="M238.68 23.4442L243.311 22.2929L245.245 28.1426L238.68 23.4442ZM249.336 15.883H241.186L238.68 8.35303L236.175 15.883H228.024L234.621 20.5504L232.116 28.0804L238.712 23.4131L242.771 20.5504L249.336 15.883Z"
                      fill="#00B67A"/>
                <path d="M255.383 22.748V14.5163H252.053V13.3575H260.085V14.5163H256.755V22.748H255.383ZM260.061 22.748V16.2479H261.366V17.8063H261.233C261.375 17.2736 261.628 16.8695 261.992 16.5942C262.365 16.319 262.858 16.1591 263.471 16.1147L263.91 16.0881L264.004 17.2336L263.191 17.3135C262.614 17.3668 262.174 17.5488 261.872 17.8596C261.571 18.1704 261.42 18.5967 261.42 19.1383V22.748H260.061ZM267.348 22.8679C266.824 22.8679 266.384 22.7702 266.029 22.5749C265.683 22.3795 265.421 22.0909 265.243 21.7091C265.074 21.3184 264.99 20.83 264.99 20.2439V16.2479H266.335V20.2572C266.335 20.6035 266.38 20.8921 266.468 21.123C266.566 21.3539 266.713 21.5226 266.908 21.6292C267.112 21.7357 267.365 21.789 267.667 21.789C268.005 21.789 268.302 21.7135 268.56 21.5626C268.817 21.4116 269.017 21.1941 269.159 20.9099C269.301 20.6257 269.372 20.2972 269.372 19.9242V16.2479H270.718V22.748H269.412V21.3228H269.585C269.408 21.8201 269.124 22.2019 268.733 22.4683C268.342 22.7347 267.88 22.8679 267.348 22.8679ZM274.891 22.8679C274.545 22.8679 274.207 22.8368 273.879 22.7747C273.55 22.7125 273.248 22.6237 272.973 22.5083C272.698 22.3928 272.454 22.2508 272.24 22.082L272.627 21.1763C272.849 21.3273 273.084 21.4605 273.333 21.5759C273.581 21.6825 273.839 21.7624 274.105 21.8157C274.381 21.8689 274.647 21.8956 274.904 21.8956C275.375 21.8956 275.73 21.8112 275.97 21.6425C276.21 21.4738 276.33 21.2473 276.33 20.9632C276.33 20.7323 276.25 20.5547 276.09 20.4304C275.93 20.2972 275.69 20.1951 275.371 20.124L274.105 19.8709C273.555 19.7555 273.137 19.5513 272.853 19.2582C272.569 18.9652 272.427 18.5922 272.427 18.1393C272.427 17.7309 272.538 17.3757 272.76 17.0738C272.982 16.7718 273.293 16.541 273.692 16.3811C274.092 16.2124 274.554 16.128 275.078 16.128C275.38 16.128 275.673 16.1591 275.957 16.2213C276.241 16.2834 276.507 16.3722 276.756 16.4877C277.013 16.6031 277.231 16.7496 277.409 16.9272L277.022 17.833C276.854 17.682 276.658 17.5533 276.436 17.4467C276.223 17.3402 276.001 17.2602 275.77 17.207C275.539 17.1448 275.309 17.1137 275.078 17.1137C274.607 17.1137 274.252 17.2025 274.012 17.3801C273.781 17.5488 273.666 17.7797 273.666 18.0727C273.666 18.2947 273.737 18.4768 273.879 18.6189C274.021 18.7609 274.243 18.8631 274.545 18.9252L275.81 19.1783C276.387 19.2937 276.823 19.4891 277.116 19.7644C277.417 20.0397 277.568 20.4126 277.568 20.8833C277.568 21.2917 277.457 21.6469 277.235 21.9489C277.013 22.2508 276.698 22.4816 276.29 22.6415C275.89 22.7924 275.424 22.8679 274.891 22.8679ZM281.771 22.8679C280.963 22.8679 280.354 22.6592 279.946 22.2419C279.546 21.8245 279.347 21.2207 279.347 20.4304V17.2869H278.081V16.2479H279.347V14.2899H280.692V16.2479H282.703V17.2869H280.692V20.3238C280.692 20.7945 280.79 21.1497 280.985 21.3894C281.189 21.6292 281.518 21.7491 281.971 21.7491C282.104 21.7491 282.237 21.7313 282.37 21.6958C282.512 21.6603 282.65 21.6247 282.783 21.5892L282.996 22.6015C282.863 22.6814 282.677 22.7436 282.437 22.788C282.206 22.8413 281.984 22.8679 281.771 22.8679ZM283.865 25.1456V16.2479H285.184V17.7131H285.037C285.179 17.2336 285.459 16.8518 285.876 16.5676C286.294 16.2746 286.787 16.128 287.355 16.128C287.932 16.128 288.434 16.2657 288.86 16.541C289.295 16.8162 289.628 17.2025 289.859 17.6998C290.099 18.1971 290.219 18.7965 290.219 19.498C290.219 20.1817 290.099 20.7811 289.859 21.2962C289.628 21.8023 289.3 22.193 288.873 22.4683C288.447 22.7347 287.941 22.8679 287.355 22.8679C286.796 22.8679 286.307 22.7258 285.89 22.4417C285.472 22.1575 285.193 21.7801 285.051 21.3095H285.21V25.1456H283.865ZM287.022 21.829C287.581 21.829 288.025 21.6292 288.354 21.2296C288.691 20.83 288.86 20.2528 288.86 19.498C288.86 18.7343 288.691 18.1571 288.354 17.7664C288.025 17.3757 287.581 17.1803 287.022 17.1803C286.471 17.1803 286.027 17.3757 285.69 17.7664C285.353 18.1571 285.184 18.7343 285.184 19.498C285.184 20.2528 285.353 20.83 285.69 21.2296C286.027 21.6292 286.471 21.829 287.022 21.829ZM291.615 14.7428V13.3442H293.187V14.7428H291.615ZM291.735 22.748V16.2479H293.08V22.748H291.735ZM297.092 22.8679C296.381 22.8679 295.848 22.6681 295.493 22.2685C295.147 21.8601 294.974 21.274 294.974 20.5103V13.3575H296.319V20.4304C296.319 20.7145 296.359 20.9587 296.439 21.163C296.519 21.3583 296.639 21.5049 296.799 21.6025C296.958 21.7002 297.158 21.7491 297.398 21.7491C297.505 21.7491 297.607 21.7446 297.704 21.7357C297.802 21.7269 297.895 21.7091 297.984 21.6825L297.957 22.7614C297.806 22.7969 297.66 22.8235 297.518 22.8413C297.376 22.859 297.234 22.8679 297.092 22.8679ZM301.804 22.8679C301.147 22.8679 300.579 22.7303 300.099 22.455C299.628 22.1797 299.26 21.789 298.993 21.2829C298.727 20.7767 298.594 20.1817 298.594 19.498C298.594 18.8053 298.727 18.2104 298.993 17.7131C299.26 17.207 299.628 16.8162 300.099 16.541C300.579 16.2657 301.147 16.128 301.804 16.128C302.452 16.128 303.012 16.2657 303.482 16.541C303.962 16.8162 304.335 17.207 304.601 17.7131C304.868 18.2193 305.001 18.8142 305.001 19.498C305.001 20.1817 304.868 20.7767 304.601 21.2829C304.335 21.789 303.962 22.1797 303.482 22.455C303.012 22.7303 302.452 22.8679 301.804 22.8679ZM301.791 21.829C302.35 21.829 302.799 21.6292 303.136 21.2296C303.473 20.83 303.642 20.2528 303.642 19.498C303.642 18.7343 303.473 18.1571 303.136 17.7664C302.799 17.3757 302.35 17.1803 301.791 17.1803C301.24 17.1803 300.796 17.3757 300.459 17.7664C300.121 18.1571 299.953 18.7343 299.953 19.498C299.953 20.2528 300.121 20.83 300.459 21.2296C300.796 21.6292 301.24 21.829 301.791 21.829ZM309.217 22.8679C308.409 22.8679 307.801 22.6592 307.392 22.2419C306.993 21.8245 306.793 21.2207 306.793 20.4304V17.2869H305.528V16.2479H306.793V14.2899H308.138V16.2479H310.15V17.2869H308.138V20.3238C308.138 20.7945 308.236 21.1497 308.431 21.3894C308.636 21.6292 308.964 21.7491 309.417 21.7491C309.55 21.7491 309.683 21.7313 309.817 21.6958C309.959 21.6603 310.096 21.6247 310.229 21.5892L310.443 22.6015C310.309 22.6814 310.123 22.7436 309.883 22.788C309.652 22.8413 309.43 22.8679 309.217 22.8679Z"
                      fill="#16191E"/>
            </svg>
        {% endif %}
        <div class="landing__goal-people-text title">
            {{ 'landing.obedience_insights.today'|resp_i11n_trans(domain=translations_domain) }}
        </div>
        <div class="landing__goal-people-wrapp">
            <div class="landing__goal-people-box disabled" id="disabledFirstFeed">
                <p class="landing__goal-people-text shadow">
                    dimin***
                </p>
                <p class="landing__goal-people-text">
                    {{ 'landing.insights_number'|resp_i11n_trans({'{number}': 8 }, domain=translations_domain) }}
                </p>
                <p class="landing__goal-people-text">
                    {{ 'landing.minutes'|resp_i11n_trans({'{number}': 5 }, domain=translations_domain) }}
                </p>
            </div>
            <div class="landing__goal-people-box">
                <p class="landing__goal-people-text shadow">
                    jesssic***
                </p>
                <p class="landing__goal-people-text">
                    {{ 'landing.insights_number'|resp_i11n_trans({'{number}': 6 }, domain=translations_domain) }}
                </p>
                <p class="landing__goal-people-text">
                    {{ 'landing.minutes'|resp_i11n_trans({'{number}': 12 }, domain=translations_domain) }}
                </p>
            </div>
            <div class="landing__goal-people-box">
                <p class="landing__goal-people-text shadow">
                    ali.k***
                </p>
                <p class="landing__goal-people-text">
                    {{ 'landing.insights_number'|resp_i11n_trans({'{number}': 12 }, domain=translations_domain) }}
                </p>
                <p class="landing__goal-people-text">
                    {{ 'landing.minutes'|resp_i11n_trans({'{number}': 16 }, domain=translations_domain) }}
                </p>
            </div>
            <div class="landing__goal-people-box">
                <p class="landing__goal-people-text shadow">
                    a.kara***
                </p>
                <p class="landing__goal-people-text">
                    {{ 'landing.insights_number'|resp_i11n_trans({'{number}': 24 }, domain=translations_domain) }}
                </p>
                <p class="landing__goal-people-text">
                    {{ 'landing.minutes'|resp_i11n_trans({'{number}': 15 }, domain=translations_domain) }}
                </p>
            </div>
            <div class="landing__goal-people-box">
                <p class="landing__goal-people-text shadow">
                    sizon***
                </p>
                <p class="landing__goal-people-text">
                    {{ 'landing.insights_number'|resp_i11n_trans({'{number}': 17 }, domain=translations_domain) }}
                </p>
                <p class="landing__goal-people-text">
                    {{ 'landing.minutes'|resp_i11n_trans({'{number}': 15 }, domain=translations_domain) }}
                </p>
            </div>
            <div class="landing__goal-people-box">
                <p class="landing__goal-people-text shadow">
                    jeong***
                </p>
                <p class="landing__goal-people-text">
                    {{ 'landing.insights_number'|resp_i11n_trans({'{number}': 14 }, domain=translations_domain) }}
                </p>
                <p class="landing__goal-people-text">
                    {{ 'landing.minutes'|resp_i11n_trans({'{number}': 16 }, domain=translations_domain) }}
                </p>
            </div>
            <div class="landing__goal-people-box">
                <p class="landing__goal-people-text shadow">
                    darsa***
                </p>
                <p class="landing__goal-people-text">
                    {{ 'landing.insights_number'|resp_i11n_trans({'{number}': 24 }, domain=translations_domain) }}
                </p>
                <p class="landing__goal-people-text">
                    {{ 'landing.minutes'|resp_i11n_trans({'{number}': 11 }, domain=translations_domain) }}
                </p>
            </div>
            <div class="landing__goal-people-box">
                <p class="landing__goal-people-text shadow">
                    omar***
                </p>
                <p class="landing__goal-people-text">
                    {{ 'landing.insights_number'|resp_i11n_trans({'{number}': 16 }, domain=translations_domain) }}
                </p>
                <p class="landing__goal-people-text">
                    {{ 'landing.minutes'|resp_i11n_trans({'{number}': 9 }, domain=translations_domain) }}
                </p>
            </div>
            <div class="landing__goal-people-box-more">
                <p class="landing__goal-people-text-more black">
                    {{ 'landing.and_more'|resp_i11n_trans(domain=translations_domain) }}
                </p>
            </div>
        </div>
    </div>
</div>
