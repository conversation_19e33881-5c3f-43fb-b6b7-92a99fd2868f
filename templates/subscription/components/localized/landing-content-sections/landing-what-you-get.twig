<section class="landing__what" id="whatYouGetBlock">
    <div class="container">
        <h2 class="landing__what-title">
            {{ 'landing.what_you_get.title'|resp_i11n_trans(domain=translations_domain) }}
        </h2>
        <ul class="landing__what-list">
            <li class="landing__what-item">
                {% if isLoveFunnel %}
                    <div class="landing__what-item-box">
                        {{ 'landing.what_you_get.make_pets_life_better'|resp_i11n_trans(domain=translations_domain) }}
                    </div>
                {% else %}
                    <div class="landing__what-item-box">
                        {{ 'landing.what_you_get.reach_goal_fast'|resp_i11n_trans({
                            '{goal}': '<span class="goalsInsertSelector"></span>',
                        }, domain=translations_domain)|raw }}
                    </div>
                {% endif %}
            </li>
            <li class="landing__what-item">
                <div class="landing__what-item-box">
                    {{ 'landing.what_you_get.forget_second_problem'|resp_i11n_trans({
                        '{secondProblem}': '<span id="secondProblemInsertSelector">'~'landing.what_you_get.default_second_problem'|resp_i11n_trans(domain=translations_domain)~'</span>',
                    }, domain=translations_domain)|raw }}
                </div>
            </li>
            <li class="landing__what-item">
                <div class="landing__what-item-box">
                    {{ 'landing.what_you_get.learn_new_techniques'|resp_i11n_trans(domain=translations_domain) }}
                </div>
            </li>
            <li class="landing__what-item">
                <div class="landing__what-item-box">
                    {{ 'landing.what_you_get.deal_with_first_problem'|resp_i11n_trans({
                        '{firstProblem}': '<span id="firstProblemInsertSelector">'~'landing.what_you_get.default_first_problem'|resp_i11n_trans(domain=translations_domain)~'</span>',
                    }, domain=translations_domain)|raw }}
                </div>
            </li>
            <li class="landing__what-item">
                <div class="landing__what-item-box">
                    {{ 'landing.what_you_get.maintain_high_obedience'|resp_i11n_trans(domain=translations_domain) }}
                </div>
            </li>
            <li class="landing__what-item">
                <div class="landing__what-item-box">
                    {{ 'landing.what_you_get.bonus_games_mental_health'|resp_i11n_trans(domain=translations_domain) }}
                </div>
            </li>
            <li class="landing__what-item">
                <div class="landing__what-item-box">
                    {{ 'landing.what_you_get.happy_obedient_dog'|resp_i11n_trans(domain=translations_domain) }}
                </div>
            </li>
        </ul>
    </div>
</section>
