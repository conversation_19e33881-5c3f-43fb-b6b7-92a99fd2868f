{% set goalsTitle = goalsTitle ?? 'landing.offer.goal'|resp_i11n_trans(domain=translations_domain) %}
{% set problemsTitle = problemsTitle ?? 'landing.offer.behavioral_problems'|resp_i11n_trans(domain=translations_domain) %}

<div class="landing__offer-problems">
    <div class="landing__offer-problems-list-item">
        <div class="landing__offer-problems-list-icon-wrapp">
            <img class="landing__offer-problems-list-icon" src="https://images.paw-champ.com/landings/goal.png" alt="">
        </div>

        {% if isLoveFunnel %}
            <div class="landing__offer-problems-list-text-wrapp">
                <h4 class="landing__offer-problems-list-title">
                    {{ goalsTitle }}
                </h4>
                <p class="landing__offer-problems-list-text">
                    {{ 'landing.offer.make_pets_life_better'|resp_i11n_trans(domain=translations_domain) }}
                </p>
            </div>
        {% else %}
            <div class="landing__offer-problems-list-text-wrapp goalsInsertSelector">
                <h4 class="landing__offer-problems-list-title">
                    {{ goalsTitle }}
                </h4>
            </div>
        {% endif %}
    </div>
    <div class="landing__offer-problems-list-item">
        <div class="landing__offer-problems-list-icon-wrapp">
            <img class="landing__offer-problems-list-icon" src="https://images.paw-champ.com/landings/behavioral_problems.png" alt="">
        </div>
        <div class="landing__offer-problems-list-text-wrapp problemsInsertSelector">
            <h4 class="landing__offer-problems-list-title">
                {{ problemsTitle }}
            </h4>
        </div>
    </div>
</div>
