<section class="landing__goal" id="goalView">
    <h1 class="landing__title red">
        {{ 'landing.funnel_title'|resp_i11n_trans(domain=translations_domain) }}
    </h1>
    <div class="landing__goal-wrapp">
        <div class="landing__goal-state">
            <div class="landing__goal-state-box">
                <p class="landing__goal-state-item white">
                    {{ 'landing.goal.now'|resp_i11n_trans(domain=translations_domain) }}
                </p>
            </div>
            <svg width="76" height="16" viewBox="0 0 76 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M75.2071 8.70711C75.5976 8.31658 75.5976 7.68342 75.2071 7.29289L68.8431 0.928932C68.4526 0.538408 67.8195 0.538408 67.4289 0.928932C67.0384 1.31946 67.0384 1.95262 67.4289 2.34315L73.0858 8L67.4289 13.6569C67.0384 14.0474 67.0384 14.6805 67.4289 15.0711C67.8195 15.4616 68.4526 15.4616 68.8431 15.0711L75.2071 8.70711ZM0.5 9H74.5V7H0.5V9Z"
                      fill="#1998CD"/>
            </svg>
            <div class="landing__goal-state-box">
                <p class="landing__goal-state-item">
                    {{ 'landing.goal.your_goal'|resp_i11n_trans(domain=translations_domain) }}
                </p>
            </div>
        </div>
        <div class="landing__goal-img-inner">
            <picture>
                <source srcset="https://storage.googleapis.com/cdn-paw-champ-images/pc/images/common/girl-with-dog-black-and-white.webp" type="image/webp">
                <img class="landing__goal-img" src="https://storage.googleapis.com/cdn-paw-champ-images/pc/images/common/girl-with-dog-black-and-white.png" alt="girl problem">
            </picture>
            <picture>
                <source srcset="https://storage.googleapis.com/cdn-paw-champ-images/pc/images/common/girl-with-golden-retreiver.webp" type="image/webp">
                <img class="landing__goal-img" src="https://storage.googleapis.com/cdn-paw-champ-images/pc/images/common/girl-with-golden-retreiver.png" alt="man problem">
            </picture>
        </div>
        <div class="container landing__goal-values">
            <div class="landing__goal-values-box">
                <div class="landing__goal-values-item">
                    <h3 class="landing__goal-values-title">
                        {{ 'landing.goal.love_level'|resp_i11n_trans(domain=translations_domain) }}
                    </h3>
                    <p class="landing__goal-values-text">
                        {{ 'landing.goal.values.high'|resp_i11n_trans(domain=translations_domain) }}
                    </p>
                </div>
                <div class="landing__goal-values-item">
                    <h3 class="landing__goal-values-title">
                        {{ 'landing.goal.obedience_level'|resp_i11n_trans(domain=translations_domain) }}
                    </h3>
                    <p class="landing__goal-values-text">
                        {{ 'landing.goal.values.low'|resp_i11n_trans(domain=translations_domain) }}
                    </p>
                    <svg width="156" height="7" viewBox="0 0 156 7" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <rect width="49.3333" height="7" rx="2" fill="#FF574C"/>
                        <path opacity="0.3"
                              d="M53.3333 2C53.3333 0.89543 54.2287 0 55.3333 0H100.667C101.771 0 102.667 0.895431 102.667 2V5C102.667 6.10457 101.771 7 100.667 7H55.3333C54.2287 7 53.3333 6.10457 53.3333 5V2Z"
                              fill="#FF574C"/>
                        <rect opacity="0.3" x="106.667" width="49.3333" height="7" rx="2" fill="#FF574C"/>
                    </svg>

                </div>
            </div>
            <div class="landing__goal-values-box">
                <div class="landing__goal-values-item">
                    <h3 class="landing__goal-values-title">
                        {{ 'landing.goal.love_level'|resp_i11n_trans(domain=translations_domain) }}
                    </h3>
                    <p class="landing__goal-values-text">
                        {{ 'landing.goal.values.superior'|resp_i11n_trans(domain=translations_domain) }}
                    </p>
                </div>
                <div class="landing__goal-values-item">
                    <h3 class="landing__goal-values-title">
                        {{ 'landing.goal.obedience_level'|resp_i11n_trans(domain=translations_domain) }}
                    </h3>
                    <p class="landing__goal-values-text">
                        {{ 'landing.goal.values.high'|resp_i11n_trans(domain=translations_domain) }}
                    </p>
                    <svg width="156" height="7" viewBox="0 0 156 7" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <rect width="49.3333" height="7" rx="2" fill="#1998CD"/>
                        <rect x="53.3333" width="49.3333" height="7" rx="2" fill="#1998CD"/>
                        <rect x="106.667" width="49.3333" height="7" rx="2" fill="#1998CD"/>
                    </svg>
                </div>
            </div>
        </div>
    </div>
</section>
