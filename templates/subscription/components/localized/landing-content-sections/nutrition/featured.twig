{% set featuredStories = [
    {
        title: "landing.featured.luna.title"|resp_i11n_trans(domain=translations_domain),
        imgSrc: "https://images.paw-champ.com/pc/images/common/nutrition-funnel-luna.png",
        name: "landing.featured.luna.name"|resp_i11n_trans(domain=translations_domain),
        problem: "landing.featured.luna.problem"|resp_i11n_trans(domain=translations_domain),
        text: "landing.featured.luna.text"|resp_i11n_trans(domain=translations_domain)
    },
    {
        title: "landing.featured.max.title"|resp_i11n_trans(domain=translations_domain),
        imgSrc: "https://images.paw-champ.com/pc/images/common/nutrition-funnel-max.png",
        name: "landing.featured.max.name"|resp_i11n_trans(domain=translations_domain),
        problem: "landing.featured.max.problem"|resp_i11n_trans(domain=translations_domain),
        text: "landing.featured.max.text"|resp_i11n_trans(domain=translations_domain)
    },
    {
        title: "landing.featured.rocky.title"|resp_i11n_trans(domain=translations_domain),
        imgSrc: "https://images.paw-champ.com/pc/images/common/nutrition-funnel-rocky.png",
        name: "landing.featured.rocky.name"|resp_i11n_trans(domain=translations_domain),
        problem: "landing.featured.rocky.problem"|resp_i11n_trans(domain=translations_domain),
        text: "landing.featured.rocky.text"|resp_i11n_trans(domain=translations_domain)
    },
] %}
<section class="landing__featured" id="trustedBlock">
    <div class="container">
        <div class="landing__featured-inner">
            <ul class="landing__featured-list swiper-wrapper">
                {% for story in featuredStories %}
                    <li class="landing__featured-item swiper-slide">
                        <h3 class="landing__featured-item-title">
                            {{ story.title|raw }}
                        </h3>
                        <div class="landing__featured-item-img-wrapp">
                            <picture>
                                <source srcset="{{ story.imgSrc }}" type="image/png">
                                <img class="landing__featured-item-img" src="{{ story.imgSrc }}" alt="Luna">
                            </picture>
                        </div>
                        <div class="landing__featured-item-inner">
                            <div class="landing__featured-item-text-wrapp">
                                <p class="landing__featured-item-name">
                                    {{ story.name }}
                                </p>
                                <p class="landing__featured-item-problem">
                                    {{ story.problem|raw }}
                                </p>
                            </div>
                            <p class="landing__featured-item-text">
                                {{ story.text }}
                            </p>
                        </div>
                    </li>
                {% endfor %}
            </ul>
            <div class="landing__featured-btns">
                <div class="swiper-button-prev"></div>
                <div class="swiper-button-next"></div>
            </div>
        </div>
        <div class="landing__featured-disclamer">
            {{ 'landing.featured.disclaimer'|resp_i11n_trans(domain=translations_domain) }}
        </div>
    </div>
</section>
