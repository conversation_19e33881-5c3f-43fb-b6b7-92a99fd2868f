{% if isEmptyHealthTendencies %}
        {% set forgetIssuesValue = "landing.what_you_get.forget_food_issues"|resp_i11n_trans(domain=translations_domain)|raw %}
    {% else %}
        {% set forgetIssuesValue = "landing.what_you_get.forget_health_issues"|resp_i11n_trans({'{healthTend}': healthTendency|join(', ')|lower,}, domain=translations_domain)|raw %}
{% endif %}
{% set wygItems = [
    { value: "landing.what_you_get.health_support"|resp_i11n_trans(domain=translations_domain)|raw },
    { value: forgetIssuesValue },
    { value: "landing.what_you_get.stop_guessing"|resp_i11n_trans(domain=translations_domain)|raw },
    { value: "landing.what_you_get.deal_with_problem"|resp_i11n_trans({'{firstProblem}': behaviorProblems|first|lower ,}, domain=translations_domain)|raw },
    { value: "landing.what_you_get.maintain_feeding_score"|resp_i11n_trans(domain=translations_domain)|raw },
    { value: "landing.what_you_get.learn_how_affects"|resp_i11n_trans(domain=translations_domain)|raw },
    { value: "landing.what_you_get.enjoy_wellbeing"|resp_i11n_trans(domain=translations_domain)|raw }
] %}

<section class="landing__what" id="whatYouGetBlock">
    <div class="container">
        <h2 class="landing__what-title">
            {{ 'landing.what_you_get.title'|resp_i11n_trans(domain=translations_domain) }}
        </h2>
        <ul class="landing__what-list">
            {% for wygItem in wygItems %}
                <li class="landing__what-item">
                    <div class="landing__what-item-box">
                        {{ wygItem.value }}
                    </div>
                </li>
            {% endfor %}
        </ul>
    </div>
</section>
