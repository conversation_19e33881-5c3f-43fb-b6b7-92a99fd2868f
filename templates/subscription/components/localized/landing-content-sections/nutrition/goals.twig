<section class="landing__goal" id="goalView">
    <div class="container">
        <h1 class="landing__title red">
            {{ 'landing.funnel_title'|resp_i11n_trans(domain=translations_domain) }}
        </h1>
    </div>
    <div class="landing__goal-wrapp">
            <div class="landing__goal-state pt-0">
                <div class="landing__goal-state-box">
                    <p class="landing__goal-state-item white">
                        {{ 'landing.goal.now'|resp_i11n_trans(domain=translations_domain) }}
                    </p>
                </div>
                <div class="landing__arrow">
                    <svg width="76" height="16" viewBox="0 0 76 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M75.2071 8.95515C75.5976 8.56463 75.5976 7.93146 75.2071 7.54094L68.8431 1.17698C68.4526 0.786455 67.8195 0.786455 67.4289 1.17698C67.0384 1.5675 67.0384 2.20067 67.4289 2.59119L73.0858 8.24805L67.4289 13.9049C67.0384 14.2954 67.0384 14.9286 67.4289 15.3191C67.8195 15.7096 68.4526 15.7096 68.8431 15.3191L75.2071 8.95515ZM0.5 8.24805V9.24805H74.5V8.24805V7.24805H0.5V8.24805Z" fill="#1998CD"/>
                    </svg>
                </div>
                <div class="landing__goal-state-box">
                    <p class="landing__goal-state-item">
                        {{ 'landing.goal.your_goal'|resp_i11n_trans(domain=translations_domain) }}
                    </p>
                </div>
            </div>
            <div class="landing__goal-img-inner">
                <div class="landing__goal-img-box">
                        <picture>
                            <source srcset="https://images.paw-champ.com/pc/images/common/foodtrash.avif, https://images.paw-champ.com/pc/images/common/foodtrash.png">
                            <img class="landing__goal-img" src="https://images.paw-champ.com/pc/images/common/foodtrash.png" alt="food trash problem">
                        </picture>
                </div>
                <div class="landing__goal-img-arrow-box">
                </div>
                <div class="landing__goal-img-box">
                        <picture>
                            <source srcset="https://images.paw-champ.com/pc/images/common/french-happy.avif, https://images.paw-champ.com/pc/images/common/french-happy.png">
                            <img class="landing__goal-img" src="https://images.paw-champ.com/pc/images/common/french-happy.png" alt="food problem resolved">
                        </picture>
                </div>
            </div>
            <div class="container landing__goal-values">
                <div class="landing__goal-values-box">
                    <div class="landing__goal-values-item">
                        <h3 class="landing__goal-values-title">
                            {{ 'landing.goal.mood_stability'|resp_i11n_trans(domain=translations_domain) }}
                        </h3>
                        <p class="landing__goal-values-text">
                            {{ 'landing.goal.values.moderate'|resp_i11n_trans(domain=translations_domain) }}
                        </p>
                    </div>
                    <div class="landing__goal-values-item">
                        <h3 class="landing__goal-values-title">
                            {{ 'landing.goal.feeding_score'|resp_i11n_trans(domain=translations_domain) }}
                        </h3>
                        <p class="landing__goal-values-text">
                            {{ 'landing.goal.values.low'|resp_i11n_trans(domain=translations_domain) }}
                        </p>
                        <svg width="156" height="7" viewBox="0 0 156 7" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect width="49.3333" height="7" rx="2" fill="#FF574C"/>
                            <path opacity="0.3"
                                  d="M53.3333 2C53.3333 0.89543 54.2287 0 55.3333 0H100.667C101.771 0 102.667 0.895431 102.667 2V5C102.667 6.10457 101.771 7 100.667 7H55.3333C54.2287 7 53.3333 6.10457 53.3333 5V2Z"
                                  fill="#FF574C"/>
                            <rect opacity="0.3" x="106.667" width="49.3333" height="7" rx="2" fill="#FF574C"/>
                        </svg>

                    </div>
                </div>
                <div class="landing__goal-values-box">
                    <div class="landing__goal-values-item">
                        <h3 class="landing__goal-values-title">
                            {{ 'landing.goal.mood_stability'|resp_i11n_trans(domain=translations_domain) }}
                        </h3>
                        <p class="landing__goal-values-text">
                            {{ 'landing.goal.values.excellent'|resp_i11n_trans(domain=translations_domain) }}
                        </p>
                    </div>
                    <div class="landing__goal-values-item">
                        <h3 class="landing__goal-values-title">
                            {{ 'landing.goal.feeding_score'|resp_i11n_trans(domain=translations_domain) }}
                        </h3>
                        <p class="landing__goal-values-text">
                            {{ 'landing.goal.values.high'|resp_i11n_trans(domain=translations_domain) }}
                        </p>
                        <svg width="156" height="7" viewBox="0 0 156 7" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect width="49.3333" height="7" rx="2" fill="#1998CD"/>
                            <rect x="53.3333" width="49.3333" height="7" rx="2" fill="#1998CD"/>
                            <rect x="106.667" width="49.3333" height="7" rx="2" fill="#1998CD"/>
                        </svg>
                    </div>
                </div>
            </div>
    </div>
</section>
