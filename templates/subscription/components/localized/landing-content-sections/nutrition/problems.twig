{# depends on app.user.lastQuiz.responses: nutritionGoal, healthTend #}
<section class="landing__problems">
    <div class="container">
        <h2 class="landing__offer-problems-card-title">{{ 'landing.problems.title'|resp_i11n_trans(domain=translations_domain) }}</h2>
        <div class="landing__offer-problems-card">
            <div class="landing__offer-problems-card-row">
                <div class="info-item landing__offer-problems-card-item full-width">
                    <div class="icon"><!-- Target -->
                        <svg width="23" height="20" viewBox="0 0 23 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M18.0895 6.2816L17.1893 6.93697C17.7592 7.98184 18.0819 9.17291 18.0819 10.437C18.0819 14.5798 14.616 17.9381 10.3409 17.9381C6.06575 17.9381 2.59991 14.5798 2.59991 10.437C2.59991 6.29417 6.06563 2.93572 10.3409 2.93572C12.4094 2.93572 14.2885 3.72193 15.6771 5.00287L16.5852 4.34168L16.7452 3.25898C15.0283 1.81926 12.7895 0.948308 10.3409 0.948308C4.93293 0.948308 0.548828 5.19646 0.548828 10.437C0.548828 15.6775 4.93293 19.9256 10.3409 19.9256C15.7489 19.9256 20.133 15.6774 20.133 10.437C20.133 9.0075 19.8067 7.65183 19.2225 6.43607L18.0895 6.2816Z" fill="#404751"/>
                            <path d="M4.56426 10.437C4.56426 13.5285 7.15051 16.0346 10.3409 16.0346C13.5313 16.0346 16.1175 13.5285 16.1175 10.437C16.1175 9.6028 15.9292 8.81135 15.5916 8.10022L13.9975 9.26092C14.1241 9.63074 14.1928 10.0261 14.1928 10.437C14.1928 12.4984 12.4683 14.1696 10.3409 14.1696C8.21353 14.1696 6.48896 12.4984 6.48896 10.437C6.48896 8.37553 8.21353 6.70439 10.3409 6.70439C11.1318 6.70439 11.8669 6.93557 12.4786 7.33171L14.0773 6.16776C13.07 5.33917 11.7657 4.83932 10.3409 4.83932C7.15051 4.83932 4.56426 7.34544 4.56426 10.437Z" fill="#404751"/>
                            <path d="M20.5719 2.95656L20.8008 0.570801L17.312 3.11091L17.0875 4.62967L11.3195 8.82928C11.0327 8.66503 10.6983 8.57062 10.341 8.57062C9.2774 8.57062 8.41511 9.40619 8.41511 10.4369C8.41511 11.4675 9.2774 12.3031 10.341 12.3031C11.4046 12.3031 12.2669 11.4675 12.2669 10.4369C12.2669 10.2581 12.241 10.0852 12.1924 9.92141L17.9439 5.73391L19.5117 5.94763L23.0004 3.40752L20.5719 2.95656Z" fill="#404751"/>
                        </svg>
                    </div>
                    <div class="text">
                        <div class="label">{{ 'landing.problems.goal'|resp_i11n_trans(domain=translations_domain) }}</div>
                        <div class="value">{{ nutritionGoal }}</div>
                    </div>
                </div>
            </div>

            <div class="landing__offer-problems-card-row">
                <div class="landing__offer-problems-card-item full-width">
                    <div class="icon"><!-- Paw -->
                        <svg width="20" height="19" viewBox="0 0 20 19" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M14.7436 0.387396C15.5967 0.440508 16.3917 0.714611 16.8721 0.941708V0.941911C19.9958 2.41845 20.5723 6.35561 19.2418 9.2924C18.7751 10.3223 18.1422 11.1721 17.3914 11.9958C15.7328 13.815 13.8508 15.4767 11.9211 16.9954C11.7026 17.1674 11.0874 17.6032 10.6191 17.9319C10.2685 18.1781 9.8072 18.1795 9.45504 17.9355C8.98476 17.6097 8.3668 17.1778 8.14716 17.0072C6.20816 15.5005 4.31543 13.8508 2.64551 12.042C1.88929 11.2229 1.25103 10.3772 0.777999 9.35019C-0.5712 6.42195 -0.0195254 2.48112 3.09491 0.985052C3.55166 0.765687 4.29378 0.494433 5.10104 0.426467C5.90809 0.3585 6.78029 0.494026 7.4956 0.767722C8.52141 1.16037 9.35803 1.9868 9.74935 2.42305C9.87503 2.56316 10.1018 2.56242 10.2266 2.42151C10.6152 1.98278 11.4466 1.15111 12.47 0.752053C13.1488 0.487311 13.9701 0.339372 14.7436 0.387396ZM9.56069 5.7462C9.65481 6.68587 9.17824 7.50576 8.49623 7.57749C7.81422 7.64921 7.18505 6.94561 7.09094 6.00595C6.99682 5.06628 7.4734 4.24639 8.1554 4.17466C8.83741 4.10294 9.46658 4.80654 9.56069 5.7462ZM12.8008 6.18634C13.0092 5.26621 12.6405 4.39237 11.9772 4.23457C11.3138 4.07677 10.6071 4.69476 10.3986 5.61489C10.1902 6.53502 10.5589 7.40885 11.2222 7.56665C11.8856 7.72445 12.5923 7.10647 12.8008 6.18634ZM12.3807 9.96017C12.1705 9.73669 12.0096 9.47461 11.8488 9.21271C11.668 8.91825 11.4873 8.62402 11.2369 8.38514C11.0031 8.16211 10.7114 7.98324 10.399 7.90754C10.3788 7.90266 9.99988 7.84222 9.99969 7.88109C9.99969 7.84222 9.62038 7.8992 9.60013 7.90388C9.28696 7.97673 8.99384 8.15316 8.75812 8.37395C8.50565 8.61057 8.32245 8.90317 8.1391 9.196C7.97604 9.45642 7.81285 9.71704 7.60076 9.9386C7.52246 10.0203 7.44221 10.1012 7.36188 10.1822C6.78938 10.7593 6.21286 11.3405 6.31213 12.2547C6.43446 13.3796 7.40336 13.8961 8.41179 13.6092C9.76496 13.2242 9.97625 13.2903 9.97625 13.2903C9.97625 13.2903 10.1881 13.226 11.5379 13.6232C12.5438 13.9191 13.5173 13.4116 13.6491 12.2877C13.7564 11.3743 13.185 10.7881 12.6175 10.2059C12.5378 10.1242 12.4582 10.0425 12.3805 9.95996L12.3807 9.96017ZM6.70854 9.55479C7.27323 9.27681 7.44367 8.43859 7.08923 7.68257C6.73478 6.92656 5.98968 6.53903 5.42499 6.81701C4.8603 7.095 4.68986 7.93322 5.04431 8.68923C5.39875 9.44525 6.14385 9.83277 6.70854 9.55479ZM14.5251 6.85848C15.0874 7.1416 15.2505 7.98134 14.8895 8.73409C14.5285 9.48685 13.7801 9.86756 13.2179 9.58444C12.6556 9.30132 12.4925 8.46158 12.8534 7.70882C13.2144 6.95607 13.9629 6.57536 14.5251 6.85848Z" fill="#404751"/>
                        </svg>
                    </div>
                    <div class="text">
                        <div class="label">{{ 'landing.problems.health_tendencies'|resp_i11n_trans(domain=translations_domain) }}</div>
                        <div class="value">
                            {% if not isEmptyHealthTendencies %}
                                {{ healthTendency|join(', ') }}
                            {% else %}
                                {{ healthTendsMap['none_of_the_above'] }}
                            {%  endif %}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
