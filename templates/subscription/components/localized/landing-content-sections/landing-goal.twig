<section class="landing__goal" id="goalView">
    <div class="landing__goal-wrapp">
        {% if pc_3dogs_split == 2 %}
            <div class="container">
                <h2 class="landing__goal-title">
                    {{ 'landing.goal.obedience_level.title'|resp_i11n_trans(domain=translations_domain) }}
                </h2>
                <div class="landing__goal-obedience-level-wrapp">
                    <ul class="landing__goal-obedience-level-list">
                        <li class="landing__goal-obedience-level-item">
                            <h4 class="landing__goal-obedience-level-item-title">
                                {{ 'landing.goal.future_without_training'|resp_i11n_trans(domain=translations_domain) }}
                            </h4>
                            <div class="landing__goal-obedience-level-item-img-box orange">
                                <picture>
                                    <source srcset="https://images.paw-champ.com/pc/images/common/dirty-dog.webp" type="image/webp">
                                    <img src="https://images.paw-champ.com/pc/images/common/dirty-dog.png" alt="dirty dog" class="landing__goal-obedience-level-item-img">
                                </picture>
                                <p class="landing__goal-obedience-level-item-img-box-number orange">
                                    1
                                </p>
                            </div>
                        </li>
                        <li class="landing__goal-obedience-level-item">
                            <h4 class="landing__goal-obedience-level-item-title yellow">
                                {{ 'landing.goal.now'|resp_i11n_trans(domain=translations_domain) }}
                            </h4>
                            <div class="landing__goal-obedience-level-item-img-box yellow">
                                <p class="landing__goal-obedience-level-item-name">
                                    <span class="nameInsertSelector">
                                        {{ 'shared.pet_name'|resp_i11n_trans(domain='shared') }}'s
                                    </span>
                                </p>
                                <picture>
                                    <source srcset="https://images.paw-champ.com/pc/images/common/dog-with-broken-pot.webp" type="image/webp">
                                    <img src="https://images.paw-champ.com/pc/images/common/dog-with-broken-pot.png" alt="dirty dog" class="landing__goal-obedience-level-item-img">
                                </picture>
                                <p class="landing__goal-obedience-level-item-img-box-number yellow">
                                    4
                                </p>
                            </div>
                        </li>
                        <li class="landing__goal-obedience-level-item">
                            <h4 class="landing__goal-obedience-level-item-title">
                                {{ 'landing.goal.future_with_training'|resp_i11n_trans(domain=translations_domain) }}
                            </h4>
                            <div class="landing__goal-obedience-level-item-img-box green">
                                <picture>
                                    <source srcset="https://images.paw-champ.com/pc/images/common/happy-labrador-dog.webp" type="image/webp">
                                    <img src="https://images.paw-champ.com/pc/images/common/happy-labrador-dog.png" alt="dirty dog" class="landing__goal-obedience-level-item-img">
                                </picture>
                                <p class="landing__goal-obedience-level-item-img-box-number green">
                                    9
                                </p>
                            </div>
                        </li>
                    </ul>
                    <svg class="landing__goal-obedience-level-range" width="343" height="41" viewBox="0 0 343 41" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M343 16.0039C343 14.0709 341.272 12.5039 339.14 12.5039H3.86013C1.72824 12.5039 0 14.0709 0 16.0039C0 17.9369 1.72824 19.5039 3.86013 19.5039H339.14C341.272 19.5039 343 17.9369 343 16.0039Z" fill="url(#paint0_linear_8_15874)"/>
                        <g filter="url(#filter0_d_8_15874)" class="range-dot" id="rangeDot">
                            <circle cx="7" cy="7" r="7" transform="matrix(-1 0 0 1 179 8.24805)" fill="white"/>
                            <circle cx="7" cy="7" r="8" transform="matrix(-1 0 0 1 179 8.24805)" stroke="#EAEDF0" stroke-width="2"/>
                        </g>
                        <path d="M0.696 38.248V32.608H4.688V33.656H1.96V34.872H4.512V35.912H1.96V37.2H4.688V38.248H0.696ZM5.29056 38.248L7.34656 35.104V35.664L5.37056 32.608H6.85856L8.13056 34.696H8.03456L9.32256 32.608H10.8106L8.82656 35.656V35.112L10.8826 38.248H9.38656L8.03456 36.08H8.13856L6.77856 38.248H5.29056ZM12.9853 38.248V33.696H11.1293V32.608H16.1533V33.696H14.2893V38.248H12.9853ZM17.0241 38.248V32.608H19.6641C20.3201 32.608 20.8241 32.7574 21.1761 33.056C21.5281 33.3547 21.7041 33.7787 21.7041 34.328C21.7041 34.632 21.6401 34.8934 21.5121 35.112C21.3895 35.3307 21.2188 35.5067 21.0001 35.64C20.7815 35.7734 20.5335 35.8587 20.2561 35.896L20.2801 35.888L20.4161 35.904C20.5548 35.9307 20.6775 35.9894 20.7841 36.08C20.8961 36.1707 21.0028 36.312 21.1041 36.504L22.0401 38.248H20.6161L19.7121 36.544C19.6588 36.4427 19.5975 36.3627 19.5281 36.304C19.4588 36.2454 19.3788 36.2054 19.2881 36.184C19.2028 36.1627 19.1015 36.152 18.9841 36.152H18.3201V38.248H17.0241ZM18.3201 35.168H19.5041C19.8188 35.168 20.0508 35.104 20.2001 34.976C20.3548 34.848 20.4321 34.6534 20.4321 34.392C20.4321 34.136 20.3548 33.9467 20.2001 33.824C20.0455 33.696 19.8081 33.632 19.4881 33.632H18.3201V35.168ZM22.9616 38.248V32.608H26.9536V33.656H24.2256V34.872H26.7776V35.912H24.2256V37.2H26.9536V38.248H22.9616ZM28.2276 38.248V32.608H29.5396L31.3076 36.04H31.2596L33.0276 32.608H34.3156V38.248H33.1076V34.536H33.1316L31.6676 37.328H30.8756L29.4036 34.536H29.4356V38.248H28.2276ZM35.7273 38.248V32.608H39.7193V33.656H36.9913V34.872H39.5433V35.912H36.9913V37.2H39.7193V38.248H35.7273ZM40.9773 38.248V32.608H42.2733V37.152H44.8893V38.248H40.9773ZM46.2264 38.248V35.328L46.4584 36.184L44.0824 32.608H45.5304L46.9144 34.744H46.8584L48.2424 32.608H49.6664L47.2904 36.184L47.5224 35.328V38.248H46.2264ZM52.6413 38.248V32.608H53.9373V37.152H56.5533V38.248H52.6413ZM59.8831 38.336C59.4564 38.336 59.0697 38.2694 58.7231 38.136C58.3764 37.9974 58.0777 37.8 57.8271 37.544C57.5764 37.288 57.3817 36.9814 57.2431 36.624C57.1097 36.2667 57.0431 35.8667 57.0431 35.424C57.0431 34.9814 57.1097 34.5814 57.2431 34.224C57.3817 33.8667 57.5764 33.5627 57.8271 33.312C58.0777 33.056 58.3764 32.8614 58.7231 32.728C59.0697 32.5894 59.4564 32.52 59.8831 32.52C60.3044 32.52 60.6884 32.5894 61.0351 32.728C61.3871 32.8614 61.6857 33.056 61.9311 33.312C62.1817 33.5627 62.3737 33.8667 62.5071 34.224C62.6457 34.576 62.7151 34.9734 62.7151 35.416C62.7151 35.8587 62.6457 36.2614 62.5071 36.624C62.3737 36.9814 62.1817 37.288 61.9311 37.544C61.6857 37.8 61.3871 37.9974 61.0351 38.136C60.6884 38.2694 60.3044 38.336 59.8831 38.336ZM59.8831 37.224C60.1977 37.224 60.4644 37.1547 60.6831 37.016C60.9071 36.872 61.0777 36.6667 61.1951 36.4C61.3177 36.128 61.3791 35.8027 61.3791 35.424C61.3791 35.04 61.3204 34.7147 61.2031 34.448C61.0857 34.1814 60.9151 33.9787 60.6911 33.84C60.4671 33.7014 60.1977 33.632 59.8831 33.632C59.5684 33.632 59.2964 33.7014 59.0671 33.84C58.8431 33.9787 58.6724 34.1814 58.5551 34.448C58.4377 34.7147 58.3791 35.04 58.3791 35.424C58.3791 35.8027 58.4377 36.128 58.5551 36.4C58.6724 36.6667 58.8431 36.872 59.0671 37.016C59.2964 37.1547 59.5684 37.224 59.8831 37.224ZM65.1823 38.248L63.3423 32.608H64.7103L65.9023 36.656H65.8143L67.1903 32.608H68.1503L69.4783 36.664H69.4143L70.6383 32.608H71.9343L70.0863 38.248H68.9263L67.6223 34.416H67.6543L66.3023 38.248H65.1823Z" fill="#C5C6CB"/>
                        <path d="M137.029 38.248V32.608H138.325V37.152H140.941V38.248H137.029ZM144.271 38.336C143.845 38.336 143.458 38.2694 143.111 38.136C142.765 37.9974 142.466 37.8 142.215 37.544C141.965 37.288 141.77 36.9814 141.631 36.624C141.498 36.2667 141.431 35.8667 141.431 35.424C141.431 34.9814 141.498 34.5814 141.631 34.224C141.77 33.8667 141.965 33.5627 142.215 33.312C142.466 33.056 142.765 32.8614 143.111 32.728C143.458 32.5894 143.845 32.52 144.271 32.52C144.693 32.52 145.077 32.5894 145.423 32.728C145.775 32.8614 146.074 33.056 146.319 33.312C146.57 33.5627 146.762 33.8667 146.895 34.224C147.034 34.576 147.103 34.9734 147.103 35.416C147.103 35.8587 147.034 36.2614 146.895 36.624C146.762 36.9814 146.57 37.288 146.319 37.544C146.074 37.8 145.775 37.9974 145.423 38.136C145.077 38.2694 144.693 38.336 144.271 38.336ZM144.271 37.224C144.586 37.224 144.853 37.1547 145.071 37.016C145.295 36.872 145.466 36.6667 145.583 36.4C145.706 36.128 145.767 35.8027 145.767 35.424C145.767 35.04 145.709 34.7147 145.591 34.448C145.474 34.1814 145.303 33.9787 145.079 33.84C144.855 33.7014 144.586 33.632 144.271 33.632C143.957 33.632 143.685 33.7014 143.455 33.84C143.231 33.9787 143.061 34.1814 142.943 34.448C142.826 34.7147 142.767 35.04 142.767 35.424C142.767 35.8027 142.826 36.128 142.943 36.4C143.061 36.6667 143.231 36.872 143.455 37.016C143.685 37.1547 143.957 37.224 144.271 37.224ZM149.57 38.248L147.73 32.608H149.098L150.29 36.656H150.202L151.578 32.608H152.538L153.866 36.664H153.802L155.026 32.608H156.322L154.474 38.248H153.314L152.01 34.416H152.042L150.69 38.248H149.57Z" fill="#343434"/>
                        <path d="M221.379 38.248V32.608H222.691L224.459 36.04H224.411L226.179 32.608H227.467V38.248H226.259V34.536H226.283L224.819 37.328H224.027L222.555 34.536H222.587V38.248H221.379ZM228.878 38.248V32.608H232.87V33.656H230.142V34.872H232.694V35.912H230.142V37.2H232.87V38.248H228.878ZM234.128 38.248V32.608H236.44C237.069 32.608 237.603 32.72 238.04 32.944C238.483 33.1627 238.819 33.4827 239.048 33.904C239.283 34.32 239.4 34.8267 239.4 35.424C239.4 36.0214 239.283 36.5307 239.048 36.952C238.819 37.3734 238.483 37.696 238.04 37.92C237.597 38.1387 237.064 38.248 236.44 38.248H234.128ZM235.424 37.168H236.352C236.912 37.168 237.336 37.0214 237.624 36.728C237.917 36.4347 238.064 36 238.064 35.424C238.064 34.8427 237.917 34.408 237.624 34.12C237.336 33.832 236.912 33.688 236.352 33.688H235.424V37.168ZM240.597 38.248V32.608H241.893V38.248H240.597ZM245.693 38.336C244.893 38.336 244.279 38.136 243.853 37.736C243.426 37.336 243.213 36.744 243.213 35.96V32.608H244.509V35.912C244.509 36.344 244.607 36.664 244.805 36.872C245.007 37.08 245.303 37.184 245.693 37.184C246.082 37.184 246.373 37.08 246.565 36.872C246.762 36.6587 246.861 36.3387 246.861 35.912V32.608H248.157V35.96C248.157 36.7387 247.943 37.3307 247.517 37.736C247.09 38.136 246.482 38.336 245.693 38.336ZM249.504 38.248V32.608H250.816L252.584 36.04H252.536L254.304 32.608H255.592V38.248H254.384V34.536H254.408L252.944 37.328H252.152L250.68 34.536H250.712V38.248H249.504Z" fill="#C5C6CB"/>
                        <path d="M320.696 38.248V32.608H321.992V34.848H324.544V32.608H325.84V38.248H324.544V35.944H321.992V38.248H320.696ZM327.235 38.248V32.608H328.531V38.248H327.235ZM332.667 38.336C332.059 38.336 331.533 38.2187 331.091 37.984C330.653 37.744 330.315 37.408 330.075 36.976C329.84 36.544 329.723 36.0294 329.723 35.432C329.723 34.824 329.84 34.304 330.075 33.872C330.315 33.44 330.656 33.1067 331.099 32.872C331.541 32.6374 332.067 32.52 332.675 32.52C332.952 32.52 333.219 32.544 333.475 32.592C333.736 32.64 333.976 32.7094 334.195 32.8C334.413 32.8854 334.597 32.9947 334.747 33.128L334.347 34.12C334.091 33.944 333.827 33.8214 333.555 33.752C333.288 33.6774 333.005 33.64 332.707 33.64C332.189 33.64 331.784 33.7894 331.491 34.088C331.197 34.3867 331.051 34.8347 331.051 35.432C331.051 36.0347 331.195 36.488 331.483 36.792C331.771 37.096 332.197 37.248 332.763 37.248C333.013 37.248 333.24 37.232 333.443 37.2C333.651 37.168 333.845 37.1174 334.027 37.048L333.755 37.568V36.024H332.571V35.056H334.851V37.904C334.669 37.9947 334.459 38.072 334.219 38.136C333.984 38.2 333.733 38.248 333.467 38.28C333.2 38.3174 332.933 38.336 332.667 38.336ZM336.18 38.248V32.608H337.476V34.848H340.028V32.608H341.324V38.248H340.028V35.944H337.476V38.248H336.18Z" fill="#C5C6CB"/>
                        <defs>
                            <filter id="filter0_d_8_15874" x="153" y="0.248047" width="38" height="38" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                                <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                                <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                                <feOffset dy="4"/>
                                <feGaussianBlur stdDeviation="5"/>
                                <feComposite in2="hardAlpha" operator="out"/>
                                <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
                                <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_8_15874"/>
                                <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_8_15874" result="shape"/>
                            </filter>
                            <linearGradient id="paint0_linear_8_15874" x1="343" y1="19.5039" x2="0" y2="19.5039" gradientUnits="userSpaceOnUse">
                                <stop stop-color="#30DF4C"/>
                                <stop offset="0.526042" stop-color="#FFD326"/>
                                <stop offset="1" stop-color="#FF574C"/>
                            </linearGradient>
                        </defs>
                    </svg>
                    <p class="landing__goal-obedience-level-subtitle">
                        <b>{{ 'landing.goal.transform_dog_obedience.title'|resp_i11n_trans(domain=translations_domain) }}</b>
                    </p>
                </div>
            </div>
        {% else %}
            <div class="landing__goal-state">
                <div class="landing__goal-state-box">
                    <p class="landing__goal-state-item white">
                        {{ 'landing.goal.now'|resp_i11n_trans(domain=translations_domain) }}
                    </p>
                </div>
                <div class="landing__hr"></div>
                <div class="landing__goal-state-box">
                    <p class="landing__goal-state-item">
                        {{ 'landing.goal.after_challenge'|resp_i11n_trans(domain=translations_domain) }}
                    </p>
                </div>
            </div>
            <div class="landing__goal-img-inner {% if app.request.query.has('ml_img') %}man{% endif %}">
                <div class="landing__goal-img-box">
                    {% if app.request.query.has('ml_img') %}
                        <picture>
                            <source srcset="https://res.cloudinary.com/dr0cx27xo/image/upload/v1718205174/dog-training/img/landing/man-problem.webp" type="image/webp">
                            <img class="landing__goal-img" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1689582634/dog-training/img/man-problem.png" alt="man problem">
                        </picture>
                    {% elseif funnelName == 'vagus' %}
                        <img class="landing__goal-img" src="https://images.paw-champ.com/pc/images/common/vagus-angry-dog-barking.png" alt="Dog with high vagal tension showing reactive behavior">
                    {% else %}
                        <picture>
                            <source srcset="https://res.cloudinary.com/dr0cx27xo/image/upload/v1718202862/dog-training/img/landing/girl-problem.webp" type="image/webp">
                            <img class="landing__goal-img" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1689601188/dog-training/img/girl-problem.png" alt="girl problem">
                        </picture>
                    {% endif %}
                </div>
                <div class="landing__goal-img-arrow-box">
                    <svg width="77" height="185" viewBox="0 0 77 185" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path class="landing__goal-img-arrow" d="M2 2L45.6756 80.9691C49.3048 87.5309 49.3418 95.4894 45.7739 102.085L2 183" stroke="#1998CD" stroke-opacity="0.13" stroke-width="4" stroke-linecap="round"/>
                        <path class="landing__goal-img-arrow" d="M14.5 2L58.1756 80.9691C61.8048 87.5309 61.8418 95.4894 58.2739 102.085L14.5 183" stroke="#1998CD" stroke-opacity="0.13" stroke-width="4"
                              stroke-linecap="round"/>
                        <path class="landing__goal-img-arrow" d="M27 2L70.6756 80.9691C74.3048 87.5309 74.3418 95.4894 70.7739 102.085L27 183" stroke="#1998CD" stroke-opacity="0.13" stroke-width="4"
                              stroke-linecap="round"/>
                    </svg>
                </div>
                <div class="landing__goal-img-box">
                    {% if app.request.query.has('ml_img') %}
                        <picture>
                            <source srcset="https://res.cloudinary.com/dr0cx27xo/image/upload/v1718205174/dog-training/img/landing/man-resolved.webp" type="image/webp">
                            <img class="landing__goal-img" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1689582633/dog-training/img/man-resolved.png" alt="man resolved">
                        </picture>
                    {% elseif funnelName == 'vagus' %}
                        <img class="landing__goal-img" src="https://images.paw-champ.com/pc/images/common/dog-without-vagus-nerve.png" alt="Calm dog with balanced vagus nerve">
                    {% else %}
                        <picture>
                            <source srcset="https://res.cloudinary.com/dr0cx27xo/image/upload/v1718202862/dog-training/img/landing/girl-resolved.webp" type="image/webp">
                            <img class="landing__goal-img" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1689582634/dog-training/img/girl-resolved.png" alt="girl resolved">
                        </picture>
                    {% endif %}
                </div>
            </div>
            <div class="container landing__goal-values">
                <div class="landing__goal-values-box">
                    <div class="landing__goal-values-item">
                        <h3 class="landing__goal-values-title">
                            {{ 'landing.goal.obedience_level'|resp_i11n_trans(domain=translations_domain) }}
                        </h3>
                        <p class="landing__goal-values-text">
                            {{ 'landing.goal.values.low'|resp_i11n_trans(domain=translations_domain) }}
                        </p>
                    </div>
                    <div class="landing__goal-values-item">
                        <h3 class="landing__goal-values-title">
                            {{ 'landing.goal.training_level'|resp_i11n_trans(domain=translations_domain) }}
                        </h3>
                        <p class="landing__goal-values-text">
                            {{ 'landing.goal.values.intermediate'|resp_i11n_trans(domain=translations_domain) }}
                        </p>
                        <div class="landing__goal-values-lines">
                            {% set redLineItemCount = redLineItemCount|default(1) %}

                            {% for i in 1..3 %}
                                <div class="landing__goal-values-lines-item red
                                    {% if i > redLineItemCount %} opacity-30{% endif %}">
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
                <div class="landing__goal-values-box">
                    <div class="landing__goal-values-item">
                        <h3 class="landing__goal-values-title">
                            {{ 'landing.goal.obedience_level'|resp_i11n_trans(domain=translations_domain) }}
                        </h3>
                        <p class="landing__goal-values-text">
                            {{ 'landing.goal.values.high'|resp_i11n_trans(domain=translations_domain) }}
                        </p>
                    </div>
                    <div class="landing__goal-values-item">
                        <h3 class="landing__goal-values-title">
                            {{ 'landing.goal.training_level'|resp_i11n_trans(domain=translations_domain) }}
                        </h3>
                        <p class="landing__goal-values-text">
                            {{ 'landing.goal.values.advanced'|resp_i11n_trans(domain=translations_domain) }}
                        </p>
                        <div class="landing__goal-values-lines">
                            {% set blueLineItemCount = blueLineItemCount|default(1) %}

                            {% for i in 1..3 %}
                                <div class="landing__goal-values-lines-item blue
                                    {% if i > blueLineItemCount %} opacity-30{% endif %}">
                                </div>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            </div>
        {% endif %}
    </div>
</section>
