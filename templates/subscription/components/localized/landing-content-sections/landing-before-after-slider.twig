{% set slideOrder = slideOrder ?? ['jupiter', 'cooper', 'luna' ] %}

{% set slides = {
    'jupiter': {
        'img_webp': funnelName ~ '.landing.before_img_01_webp',
        'img_png': funnelName ~ '.landing.before_img_01_png',
        'name': 'landing.featured_jupiter_name',
        'problem': 'landing.featured_jupiter_problem',
        'story': 'landing.featured_jupiter_story'
    },
    'cooper': {
        'img_webp': funnelName ~ '.landing.before_img_02_webp',
        'img_png': funnelName ~ '.landing.before_img_02_png',
        'name': 'landing.featured_cooper_name',
        'problem': 'landing.featured_cooper_problem',
        'story': 'landing.featured_cooper_story'
    },
    'luna': {
        'img_webp': funnelName ~ '.landing.before_img_03_webp',
        'img_png': funnelName ~ '.landing.before_img_03_png',
        'name': 'landing.featured_luna_name',
        'problem': 'landing.featured_luna_problem',
        'story': 'landing.featured_luna_story'
    },
} %}

{% set isPcTrustSplitGroup2Active = isPcTrustSplitGroup2Active ?? getSplitValue('pc_trust') == 2 %}

<section class="landing__featured" id="trustedBlock">
    <div class="container">
        <div class="landing__featured-inner">
            <ul class="landing__featured-list swiper-wrapper">
                {% for slideKey in slideOrder %}
                    {% if slides[slideKey] is defined %}

                        {% set slide = slides[slideKey] %}

                        <li class="landing__featured-item swiper-slide">
                            <h3 class="landing__featured-item-title">
                                {% if isPcTrustSplitGroup2Active %}
                                    <b>Over 257,039</b> dog owners just like you achieved great results
                                {% else %}
                                    {{ 'landing.featured_result_title'|resp_i11n_trans(domain=translations_domain) }}
                                {% endif %}
                            </h3>
                            <div class="landing__featured-item-img-wrapp">
                                <picture>
                                    <source srcset="{{ slide.img_webp|trans(domain='assets') }}" type="image/webp">
                                    <img class="landing__featured-item-img" src="{{ slide.img_png|trans(domain='assets') }}" alt="{{ slide.name|resp_i11n_trans(domain=translations_domain) }}">
                                </picture>
                            </div>
                            <div class="landing__featured-item-inner">
                                <div class="landing__featured-item-text-wrapp">
                                    <p class="landing__featured-item-name">
                                        {{ slide.name|resp_i11n_trans(domain=translations_domain) }}
                                    </p>
                                    <p class="landing__featured-item-problem">
                                        {{ slide.problem|resp_i11n_trans(domain=translations_domain) }}
                                    </p>
                                </div>
                                <p class="landing__featured-item-text">
                                    {{ slide.story|resp_i11n_trans(domain=translations_domain) }}
                                </p>
                            </div>
                        </li>
                    {% endif %}
                {% endfor %}
            </ul>
            <div class="landing__featured-btns">
                <div class="swiper-button-prev"></div>
                <div class="swiper-button-next"></div>
            </div>
        </div>
        <div class="landing__featured-disclamer">
           {{ 'landing.featured_disclaimer'|resp_i11n_trans(domain=translations_domain) }}
        </div>
    </div>
</section>
