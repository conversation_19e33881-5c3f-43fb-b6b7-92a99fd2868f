{% include 'subscription/components/countdown-options.html.twig' %}

<aside class="sticky-offer__wrapper better" id="getBtnAnchor">
    <div class="container">
        <div class="sticky-offer">
            <div class="sticky-offer__logo">
                <img src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1706198790/paw-champ/logo/logo.svg" alt="">
            </div>
            <div class="sticky-offer__timer-wrapper">
                <div>
                    <p class="sticky-offer__timer" id="countdown2">{{ getFormattedTimeToCountdownFinish() }}</p>
                    <div class="sticky-offer__timer-box">
                        <span class="sticky-offer__timer-text" id="lastChance">{{ 'landing.sticky_offer.timer.minutes'|resp_i11n_trans(domain=translations_domain) }}</span>
                        <span class="sticky-offer__timer-text">{{ 'landing.sticky_offer.timer.seconds'|resp_i11n_trans(domain=translations_domain) }}</span>
                    </div>
                </div>
            </div>
            <button class="sticky-offer__btn smooth-scroll" id="stickyBtn">
                <a href="#promocode" class="js-scroll"></a>
                <span></span>
                {{ 'landing.sticky_offer.get_my_plan'|resp_i11n_trans(domain=translations_domain) }}
            </button>
        </div>
    </div>
</aside>

<script>
    window.finishCountdownTimestamp = {{ getCountdown() }};
    window.getFormattedTimeToCountdownFinish = '{{ getFormattedTimeToCountdownFinish() }}';
</script>
