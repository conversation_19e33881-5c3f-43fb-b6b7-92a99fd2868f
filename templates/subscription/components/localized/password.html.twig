<aside class="password__container" id="password-container">
    <div class="password__inner">
        <div class="password__logo-wrapp">
            <img src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1709728092/paw-champ/logo/logo-square.svg" alt="" class="password__logo">
        </div>
        <h4 class="password__title">
            <img class="password__title-icon" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1671198919/dog-training/icons/good.png" alt="">
            {{ 'landing.password.payment_succeeded'|resp_i11n_trans(domain=translations_domain) }}
        </h4>
        <p class="password__subtitle">
            {{ 'landing.password.create_account'|resp_i11n_trans(domain=translations_domain) }}
        </p>
        <form class="password__form" name="password-form" id="password-form">
            <div class="password__field">
                <label class="password__field-email-label" for="name">
                    {{ 'landing.password.email_label'|resp_i11n_trans(domain=translations_domain) }}
                </label>
                <div class="password__field-input-wrapp">
                    <input class="password__field-input-email"
                           placeholder="{{ 'landing.password.email_placeholder'|resp_i11n_trans(domain=translations_domain) }}"
                           type="email"
                           id="email"
                           name="email"
                           value="<EMAIL>"
                           required
                    />
                </div>
            </div>
            <div class="password__field-email-error">
                <span id="email-error-text"></span>
            </div>
            <div class="password__field">
                <label class="password__field-label" for="name">{{ 'landing.password.create_password_label'|resp_i11n_trans(domain=translations_domain) }}</label>
                <div class="password__field-input-wrapp">
                    <input class="password__field-input"
                           type="password"
                           id="password"
                           name="password"
                           placeholder="{{ 'landing.password.new_password_placeholder'|resp_i11n_trans(domain=translations_domain) }}"
                           required
                    />
                    <img class="password__field-input-toggle-button"
                         src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1679667595/dog-training/icons/show-input.svg"
                         alt=""
                    >
                </div>
            </div>
            <div class="password__field">
                <label class="password__field-label" for="email">{{ 'landing.password.repeat_password_label'|resp_i11n_trans(domain=translations_domain) }}</label>
                <div class="password__field-input-wrapp">
                    <input class="password__field-input"
                           type="password"
                           id="password-confirm"
                           name="password-confirm"
                           placeholder="{{ 'landing.password.repeat_password_placeholder'|resp_i11n_trans(domain=translations_domain) }}"
                           required
                    />
                    <img class="password__field-input-toggle-button"
                         src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1679667595/dog-training/icons/show-input.svg"
                         alt=""
                    >
                </div>
            </div>

            <div class="password__error-message">
                <span id="password__error-text"></span>
            </div>
            <div class="password__button-wrapp">
                <p class="password__error-text" id="submit-error-text">
                    {{ 'landing.password.support_contact_error'|resp_i11n_trans({
                        '{supportEmail}': support_email,
                    }, domain=translations_domain) }}
                </p>
                <div class="password__loader-wrapp" id="password-loader">
                    <div class="password__loader">
                        <div class="preload"></div>
                    </div>
                </div>
                <button class="password__button"
                        type="submit"
                        id="submit-button"
                >
                    {{ 'landing.password.confirm_button'|resp_i11n_trans(domain=translations_domain) }}
                </button>
            </div>
        </form>
    </div>

</aside>
<script>
    window.setPasswordUrl = '{{ path('user_api_set_password', {'externalId': app.user.externalId}) }}'
    window.checkPasswordUrl = '{{ path('user_api_check_password', {'externalId': app.user.externalId}) }}'
    window.userEmail = '{{ app.user.email }}'
    window.userHasPassword = {{ app.user.hasPassword ? 'true' : 'false' }}
</script>

{% include 'subscription/components/success.html.twig' %}
