{% for subscription in subscriptionInfo %}
    {% if subscription.status == "active" %}
        <div class="membership-info_card">
            <div class="membership-info_card_title">
                {{ ("membership_info.membership_plan")|trans }}
            </div>
            <div class="membership-info_card_box">
                <div class="membership-info_card_list">
                    {% if subscription.status is not empty %}
                        <div class="membership-info_card_list_status">
                            <p class="membership-info_card_list_text">
                                {{ ("membership_info.status.title")|trans }}
                            </p>
                            <p class="membership-info_card_list_text membership-info_card_list_text-status active">
                                {{ ("membership_info.status.active")|trans }}
                            </p>
                        </div>
                    {% endif %}

                    {% set trialPeriod = subscription.trialPeriod.value ?? null %}

                    {% if subscription.trialPeriod != null %}
                        <p class="membership-info_card_list_text">
                            {{ ("membership_info.introductory_offer")|trans }}
                            <b>
                                {{ subscription.trialCurrency }} {{ subscription.trialPrice / 100 }}
                                {% if trialPeriod %}
                                    / {{ ("membership_info.period_value." ~ trialPeriod)|trans }}
                                {% endif %}
                            </b>
                        </p>
                    {% endif %}

                    {% set subscriptionPeriod = subscription.subscriptionPeriod.value ?? null %}
                    <p class="membership-info_card_list_text">
                        {{ ("membership_info.membership_price")|trans }}
                        <b>
                            {{ subscription.subscriptionCurrency }} {{ subscription.subscriptionPrice / 100 }}
                            {% if subscriptionPeriod %}
                                / {{ ("membership_info.period_value." ~ subscriptionPeriod)|trans }}
                            {% endif %}
                        </b>
                    </p>
                    <p class="membership-info_card_list_text">
                        {{ ("membership_info.next_charge")|trans }}
                        {% if subscription.nextCharge is defined or subscription.nextCharge is not empty %}
                            <b>
                                {% set day = subscription.nextCharge|date('d') %}
                                {% set month = subscription.nextCharge|date('M')|trans %}
                                {% set year = subscription.nextCharge|date('Y') %}

                                {{ day~' '~month~' '~year }}
                            </b>
                        {% endif %}
                    </p>
                </div>
                <button class="membership-info_card_link cancel-subscription-btn" data-subscription-id="{{ subscription.externalId }}">
                    {{ ("membership_info.turn_off_auto_renewal")|trans }}
                </button>
            </div>
        </div>
    {% elseif subscription.status == "cancel_in_progress" %}
        <div class="membership-info_card">
            <div class="membership-info_card_wrapp">
                <div class="membership-info_card_title">
                    {{ ("membership_info.membership_plan")|trans }}
                </div>
                <div>
                    <button class="membership-info_card_btn refresh-btn" disabled>
                        <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 15C7.325 15 5.90625 14.4188 4.74375 13.2563C3.58125 12.0938 3 10.675 3 9C3 7.325 3.58125 5.90625 4.74375 4.74375C5.90625 3.58125 7.325 3 9 3C9.8625 3 10.6875 3.178 11.475 3.534C12.2625 3.89 12.9375 4.3995 13.5 5.0625V3.75C13.5 3.5375 13.572 3.3595 13.716 3.216C13.86 3.0725 14.038 3.0005 14.25 3C14.462 2.9995 14.6402 3.0715 14.7847 3.216C14.9292 3.3605 15.001 3.5385 15 3.75V7.5C15 7.7125 14.928 7.89075 14.784 8.03475C14.64 8.17875 14.462 8.2505 14.25 8.25H10.5C10.2875 8.25 10.1095 8.178 9.966 8.034C9.8225 7.89 9.7505 7.712 9.75 7.5C9.7495 7.288 9.8215 7.11 9.966 6.966C10.1105 6.822 10.2885 6.75 10.5 6.75H12.9C12.5 6.05 11.9532 5.5 11.2597 5.1C10.5662 4.7 9.813 4.5 9 4.5C7.75 4.5 6.6875 4.9375 5.8125 5.8125C4.9375 6.6875 4.5 7.75 4.5 9C4.5 10.25 4.9375 11.3125 5.8125 12.1875C6.6875 13.0625 7.75 13.5 9 13.5C9.85 13.5 10.6283 13.2845 11.3348 12.8535C12.0413 12.4225 12.588 11.8443 12.975 11.1188C13.075 10.9438 13.2158 10.822 13.3973 10.7535C13.5788 10.685 13.763 10.6818 13.95 10.7438C14.15 10.8063 14.2937 10.9375 14.3812 11.1375C14.4687 11.3375 14.4625 11.525 14.3625 11.7C13.85 12.7 13.1188 13.5 12.1688 14.1C11.2188 14.7 10.1625 15 9 15Z"
                                  fill="#343434"/>
                        </svg>
                        {{ ("membership_info.refresh")|trans }}
                    </button>
                </div>
            </div>

            <span class="membership-info_card_line"></span>

            <div class="membership-info_card_list">
                <div class="membership-info_card_list_status">
                    <p class="membership-info_card_list_text">
                        {{ ("membership_info.status.title")|trans }}
                    </p>
                    <p class="membership-info_card_list_text membership-info_card_list_text-status cancel_in_progress">
                        {{ ("membership_info.status.cancel_in_progress")|trans }}
                    </p>
                </div>
            </div>
            <div class="membership-info_card_note-gray">
                {{ ("membership_info.membership_canceled_in_progress_note")|trans }}
            </div>
        </div>
    {% else %}
        <div class="membership-info_card">
            <div class="membership-info_card_title">
                {{ ("membership_info.membership_plan")|trans }}
            </div>
            <div class="membership-info_card_list">
                <div class="membership-info_card_list_status">
                    <p class="membership-info_card_list_text">
                        {{ ("membership_info.status.title")|trans }}
                    </p>
                    <p class="membership-info_card_list_text membership-info_card_list_text-status cancelled">
                        {{ ("membership_info.status.canceled")|trans }}
                    </p>
                </div>
                {% if subscription.unEnrollAt is defined and subscription.unEnrollAt is not empty %}
                    <p class="membership-info_card_list_text">
                        {{ ("membership_info.membership_access_until")|trans }}
                        <b>
                            {% set day = subscription.unEnrollAt|date('d') %}
                            {% set month = subscription.unEnrollAt|date('M')|trans %}
                            {% set year = subscription.unEnrollAt|date('Y') %}

                            {{ day~' '~month~' '~year }}
                        </b>
                    </p>
                {% endif %}
            </div>
            <div class="membership-info_card_note">
                {{ ("membership_info.membership_canceled_note")|trans }}
            </div>

            {% include 'subscription/components/membership-restore-popup.html.twig' with { subscription } %}
        </div>
    {% endif %}

    {% if subscription.status == "active" and subscription.subscriptionProduct50Discount is not null %}
        {% include 'subscription/components/membership_discount.html.twig' with { 
            subscriptionData: subscription,
            subscriptionDiscountData: subscription.subscriptionProduct50Discount,
            subscriptionDiscountPercents: '50',
            previousPlanTitle: "current",
            buttonActionType: "change_plan",
        } %}
    {% endif %}
{% endfor %}
