{% set hiddenLegal = hiddenLegal ?? false %}
{% set hiddenLinks = hiddenLinks ?? false %}

{% set clientCountry = getCountry()|upper %}
{% set countryCodesEEA = [
    "AT", "BE", "BG", "HR", "CY", "CZ", "DK", "EE", "FI",
    "FR", "DE", "GR", "HU", "IS", "IE", "IT", "LV", "LI",
    "LT", "LU", "MT", "NL", "NO", "PL", "PT", "RO", "SK",
    "SI", "ES", "SE", "GB"
] %}

<footer class="footer">
    <div class="footer__wrapper">
        <p class="footer__text">
            {% trans %}footer.copyright{% endtrans %} {{ "now"|date("Y") }}
        </p>
        {% if not hiddenLegal %}
            <p class="footer__legal">
                {% if clientCountry == 'US'|upper %}
                    {% trans %}footer.legal.US{% endtrans %}
                {% elseif clientCountry == 'HK' or clientCountry == 'CN' %}
                    {% trans %}footer.legal.HK{% endtrans %}
                {% elseif clientCountry in countryCodesEEA %}
                    FONTADELLA LIMITED, Limassol, Cyprus; Driftal Limited, Hong Kong
                {% else %}
                    {% trans %}footer.legal.other{% endtrans %}
                {% endif %}
            </p>
        {% endif %}
        {% if not hiddenLinks %}
            <div class="footer__links-wrapp">
                <ul class="footer__list">
                    <li class="footer__item">
                        <a class="footer__item-link" href="{{ path('quiz_main', app.request.query.all | merge({'page': null})) }}">
                            {% trans %}footer.links.home_page{% endtrans %}
                        </a>
                    </li>
                    <li class="footer__item">
                        <a class="footer__item-link" href="{{ path('static_page_about_us', app.request.query.all | merge({'page': null})) }}">
                            {% trans %}footer.links.about_us{% endtrans %}
                        </a>
                    </li>
                    <li class="footer__item">
                        <a class="footer__item-link" href="{{ path('static_page_pawchamp_methods', app.request.query.all | merge({'page': null})) }}">
                            {% trans %}footer.links.pawchamp_methods{% endtrans %}
                        </a>
                    </li>
                    <li class="footer__item">
                        <a class="footer__item-link" href="{{ path('static_page_our_dog_experts', app.request.query.all | merge({'page': null})) }}">
                            {% trans %}footer.links.dog_experts{% endtrans %}
                        </a>
                    </li>
                    <li class="footer__item">
                        <a class="footer__item-link" href="{{ path('static_page_reviews', app.request.query.all | merge({'page': null})) }}">
                            {% trans %}footer.links.reviews{% endtrans %}
                        </a>
                    </li>
                    <li class="footer__item">
                        <a class="footer__item-link" href="{{ path('static_page_join_us', app.request.query.all | merge({'page': null})) }}">
                            {% trans %}footer.links.join_us{% endtrans %}
                        </a>
                    </li>
                    <li class="footer__item">
                        <a class="footer__item-link" href="{{ path('static_page_contacts', app.request.query.all | merge({'page': null})) }}">
                            {% trans %}footer.links.contact_us{% endtrans %}
                        </a>
                    </li>
                    <li class="footer__item">
                        <a class="footer__item-link" href="{{ path('blog', app.request.query.all | merge({'page': null})) }}">
                            {% trans %}footer.links.blog{% endtrans %}
                        </a>
                    </li>
                    <li class="footer__item">
                        <a class="footer__item-link" href="{{ path('static_page_terms', app.request.query.all | merge({'page': null})) }}">
                            {% trans %}footer.links.terms_of_use{% endtrans %}
                        </a>
                    </li>
                    <li class="footer__item">
                        <a class="footer__item-link" href="{{ path('static_page_policy', app.request.query.all | merge({'page': null})) }}">
                            {% trans %}footer.links.privacy_policy{% endtrans %}
                        </a>
                    </li>
                </ul>
                <div class="footer__social">
                    <a href="https://www.instagram.com/pawchamp_club/" class="footer__social-item" target="_blank">
                        <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <g clip-path="url(#clip0_375_629)">
                                <path d="M16 32C24.8366 32 32 24.8366 32 16C32 7.16344 24.8366 0 16 0C7.16344 0 0 7.16344 0 16C0 24.8366 7.16344 32 16 32Z" fill="white"/>
                                <path d="M15.9912 6.66876C17.2688 6.66876 18.5464 6.66316 19.8248 6.66956C22.9776 6.68636 25.3056 8.99756 25.3224 12.1448C25.336 14.72 25.3384 17.2952 25.3224 19.8704C25.3016 22.9864 22.9744 25.3056 19.8568 25.3224C17.2832 25.336 14.7088 25.3368 12.1344 25.3224C8.9848 25.3048 6.6808 22.9776 6.6728 19.8192C6.6664 17.2624 6.6648 14.7056 6.6728 12.1488C6.6832 8.99996 9.0016 6.68716 12.1584 6.66956C13.436 6.66236 14.7136 6.66876 15.992 6.66876H15.9912ZM16.0288 23.6416C17.3256 23.6416 18.6216 23.648 19.9184 23.64C22.0728 23.6264 23.5856 22.112 23.5912 19.9536C23.5984 17.3408 23.5992 14.728 23.5912 12.1152C23.584 9.99116 22.0912 8.46556 19.9576 8.44476C17.3096 8.41916 14.66 8.41756 12.012 8.44556C9.8864 8.46796 8.3984 9.99516 8.388 12.1312C8.3744 14.744 8.3736 17.3568 8.388 19.9696C8.4008 22.124 9.9272 23.628 12.084 23.64C13.3992 23.6472 14.7144 23.6416 16.0288 23.6416Z"
                                      fill="#343434"/>
                                <path d="M16.0288 23.6416C14.7136 23.6416 13.3984 23.6472 12.084 23.64C9.9272 23.6288 8.4008 22.124 8.388 19.9696C8.3728 17.3568 8.3744 14.744 8.388 12.1312C8.3992 9.99595 9.8864 8.46795 12.012 8.44555C14.66 8.41755 17.3096 8.41915 19.9576 8.44475C22.0912 8.46555 23.584 9.99115 23.5912 12.1152C23.6 14.728 23.5992 17.3408 23.5912 19.9536C23.5848 22.1112 22.0728 23.6264 19.9184 23.64C18.6216 23.648 17.3256 23.6416 16.0288 23.6416ZM20.8304 16.04C20.8288 13.356 18.6696 11.196 15.9896 11.1976C13.316 11.1992 11.1456 13.3728 11.1472 16.0464C11.1488 18.7184 13.328 20.896 15.996 20.8912C18.6752 20.8864 20.8312 18.7232 20.8304 16.04ZM22.124 10.9936C22.1096 10.3711 21.5944 9.88155 20.968 9.89435C20.34 9.90715 19.852 10.416 19.8656 11.044C19.8792 11.668 20.3952 12.1624 21.0176 12.148C21.6352 12.1344 22.1384 11.6088 22.124 10.9936Z"
                                      fill="white"/>
                                <path d="M20.8304 16.0399C20.832 18.7231 18.676 20.8863 15.996 20.8911C13.328 20.8959 11.1488 18.7183 11.1472 16.0463C11.1456 13.3727 13.316 11.1991 15.9896 11.1975C18.6696 11.1959 20.8288 13.3559 20.8304 16.0399ZM15.988 12.9151C14.2696 12.9167 12.8776 14.3007 12.8656 16.0207C12.8536 17.7639 14.2528 19.1695 15.9992 19.1679C17.7176 19.1663 19.1136 17.7815 19.1256 16.0655C19.1384 14.3295 17.728 12.9143 15.988 12.9159V12.9151Z"
                                      fill="#343434"/>
                                <path d="M22.124 10.9936C22.1384 11.6096 21.6352 12.1344 21.0176 12.148C20.3952 12.1624 19.8784 11.6672 19.8656 11.044C19.852 10.416 20.34 9.90721 20.968 9.89441C21.5952 9.88161 22.1096 10.3712 22.124 10.9936Z"
                                      fill="#343434"/>
                                <path d="M15.988 12.9151C17.728 12.9135 19.1384 14.3295 19.1256 16.0647C19.1128 17.7807 17.7176 19.1655 15.9992 19.1671C14.2528 19.1687 12.8536 17.7631 12.8656 16.0199C12.8776 14.2999 14.2696 12.9159 15.988 12.9143V12.9151Z"
                                      fill="white"/>
                            </g>
                            <defs>
                                <clipPath id="clip0_375_629">
                                    <rect width="32" height="32" fill="white"/>
                                </clipPath>
                            </defs>
                        </svg>
                    </a>
                    <a href="https://www.facebook.com/pawchamp/" class="footer__social-item" target="_blank">
                        <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <g clip-path="url(#clip0_375_667)">
                                <path d="M16 32C24.8366 32 32 24.8366 32 16C32 7.16344 24.8366 0 16 0C7.16344 0 0 7.16344 0 16C0 24.8366 7.16344 32 16 32Z" fill="white"/>
                                <path d="M17.9184 22.1128H21.1264C21.304 20.788 21.4792 19.4856 21.6576 18.1536H17.956C17.928 18.1216 17.92 18.1168 17.92 18.1112C17.9208 17.1352 17.8256 16.1552 18.0016 15.1856C18.1472 14.3848 18.556 13.7744 19.38 13.5664C19.7456 13.4744 20.1392 13.4824 20.5216 13.4688C20.9392 13.4536 21.3584 13.4656 21.7888 13.4656C21.7976 13.3824 21.808 13.3264 21.808 13.2712C21.8088 12.2928 21.8056 11.3152 21.812 10.3368C21.8128 10.1816 21.7672 10.1296 21.6128 10.1024C20.3544 9.88241 19.1008 9.74961 17.8152 9.92481C14.9552 10.3144 13.9544 12.424 13.756 14.1864C13.6352 15.2608 13.6952 16.356 13.6752 17.4416C13.6712 17.6712 13.6752 17.9016 13.6752 18.1512H10.188V22.0952H13.6488V32H17.9184V22.1128Z"
                                      fill="#343434"/>
                            </g>
                            <defs>
                                <clipPath id="clip0_375_667">
                                    <rect width="32" height="32" fill="white"/>
                                </clipPath>
                            </defs>
                        </svg>
                    </a>
                </div>
            </div>
        {% endif %}
    </div>
</footer>
