{% set subscriptionCurrency = subscriptionData.subscriptionCurrency is defined ? subscriptionData.subscriptionCurrency : '' %}

{% set discountBillingPeriod = subscriptionDiscountData.getBillingPeriod is defined and subscriptionDiscountData.getBillingPeriod.value is defined
    ? subscriptionDiscountData.getBillingPeriod.value : '' %}

{% set discountTrialDailyAmount = subscriptionDiscountData.getTrialDailyAmount is defined
    ? subscriptionCurrency ~ ' ' ~ (subscriptionDiscountData.getTrialDailyAmount / 100)|number_format(2, '.', '')
    : '' %}

{% set dscountFullDailyAmount = subscriptionDiscountData.getDailyAmount is defined
    ? subscriptionCurrency ~ ' ' ~ (subscriptionDiscountData.getDailyAmount / 100)|number_format(2, '.', '')
    : '' %}

{% set discountFullPrice = subscriptionDiscountData.getAmount is defined
    ? subscriptionCurrency ~ ' ' ~ (subscriptionDiscountData.getAmount / 100)|number_format(2, '.', '')
    : '' %}

{% set cancelledBillingPeriod = subscriptionData.subscriptionPeriod is defined and subscriptionData.subscriptionPeriod.value is defined
    ? subscriptionData.subscriptionPeriod.value : '' %}

{% set cancelledFullPrice = subscriptionData.subscriptionPrice is defined
    ? subscriptionCurrency ~ ' ' ~ (subscriptionData.subscriptionPrice / 100)|number_format(2, '.', '')
    : '' %}

{% set cancelledPricePerDay = subscriptionData.trialPrice is defined
    ? subscriptionCurrency ~ ' ' ~ (subscriptionData.trialPrice / 100)|number_format(2, '.', '')
    : '' %}

{% set cancellTrialDailyAmount = subscriptionData.dailyAmount is defined
    ? subscriptionCurrency ~ ' ' ~ (subscriptionData.dailyAmount / 100)|number_format(2, '.', '')
    : '' %}


<div class="membership-discount" id="membership-discount" data-subscription-id="{{ subscriptionData.externalId }}">
    <div class="container">
        <div class="membership-subscription_inner">

            {% include 'subscription/components/membership-back-btn.html.twig' %}

            <h1 class="membership-discount__title">
                {{ ("membership_info.discount.title")|trans({'%percent%': subscriptionDiscountPercents})|raw }}
            </h1>

            <p class="membership-discount__subtitle">
                {{ ("membership_info.discount.subtitle")|trans({'%percent%': subscriptionDiscountPercents})|raw }}
            </p>

            <ul class="membership-discount__list">
                <li class="membership-discount__list-item">
                    {{ ("membership_info.discount.list.0")|trans }}
                </li>
                <li class="membership-discount__list-item">
                    {{ ("membership_info.discount.list.1")|trans }}
                </li>
                <li class="membership-discount__list-item">
                    {{ ("membership_info.discount.list.2")|trans }}
                </li>
                <li class="membership-discount__list-item">
                    {{ ("membership_info.discount.list.3")|trans }}
                </li>
            </ul>

            <ul class="membership-discount__offer-list">
                <li class="membership-discount__offer-item-inner disabled">
                    <h3 class="membership-discount__offer-item-title">
                        {{ ("membership_info.discount.offer." ~ previousPlanTitle ~ ".title")|trans }}
                    </h3>
                    <div class="membership-discount__offer-item">
                        <div class="membership-discount__offer-item-prices">
                            <div class="membership-discount__offer-item-prices-box">
                                <h4 class="membership-discount__offer-item-plan">
                                    {{ ("membership_info.period_value." ~ cancelledBillingPeriod)|trans ~ ' plan' }}
                                </h4>
                                <p class="membership-discount__offer-item-prices-cancelled-per-month">
                                    {{ cancelledFullPrice ~ '/' ~ ("membership_info.period_value." ~ cancelledBillingPeriod)|trans }}
                                </p>
                            </div>
                            <p class="membership-discount__offer-item-prices-per-day">
                                {{ cancellTrialDailyAmount ~ '/' ~ ('membership_info.period_value.day')|trans }}
                            </p>
                        </div>
                    </div>
                </li>
                <li class="membership-discount__offer-item-inner">
                    <h3 class="membership-discount__offer-item-title">
                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M7.55664 14.6738C4.78516 14.6738 2.95117 13.0801 2.95117 10.6836C2.95117 8.56836 3.95898 7.80078 3.95898 6.48828C3.95898 6.23047 3.91797 5.87305 3.91797 5.7207C3.91797 5.45703 4.08203 5.28125 4.32227 5.28125C4.86133 5.28125 5.46484 5.73828 5.78711 6.42383C5.82812 6.27148 5.83984 6.13086 5.83984 5.98438C5.82812 4.83008 5.13086 3.83984 4.43359 3.00781C4.31055 2.86719 4.24609 2.71484 4.24609 2.56836C4.24609 2.19336 4.59766 2 5.14844 2C8.24805 2 12.7129 4.18555 12.7129 9.42969C12.7129 12.582 10.6504 14.6738 7.55664 14.6738ZM7.69141 13.0273C9.08594 13.0273 9.76562 12.002 9.76562 10.8418C9.76562 9.69922 9.09766 8.42188 7.81445 7.81836C7.76172 7.80078 7.72656 7.83008 7.73242 7.88281C7.83203 8.84375 7.68555 9.73438 7.375 10.1562C7.22852 9.81641 7.05273 9.52344 6.7832 9.2832C6.74219 9.24805 6.70703 9.26562 6.69531 9.31836C6.60156 9.98047 5.86914 10.3496 5.86914 11.3809C5.86914 12.3652 6.5957 13.0273 7.69141 13.0273Z"
                                  fill="white"/>
                        </svg>
                        {{ ("membership_info.discount.offer.special.title")|trans({'%percent%': subscriptionDiscountPercents})|raw }}
                    </h3>
                    <div class="membership-discount__offer-item">
                        <div class="membership-discount__offer-item-prices">
                            <div class="membership-discount__offer-item-prices-box">
                                <p class="membership-discount__offer-item-prices-special-per-month">
                                    {{ discountFullPrice ~ '/' ~ ("membership_info.period_value." ~ discountBillingPeriod)|trans }}
                                </p>
                                <h4 class="membership-discount__offer-item-plan-special">
                                    {{ ("membership_info.period_value." ~ discountBillingPeriod)|trans ~ ' plan' }}
                                </h4>
                            </div>
                            <div class="membership-discount__offer-item-prices-box end">
                                <p class="membership-discount__offer-item-prices-special-per-day">{{ cancellTrialDailyAmount ~ '/' ~ ('membership_info.period_value.day')|trans }}</p>
                                <p class="membership-discount__offer-item-prices-special-hot">
                                    <svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M8.50122 16.5081C5.3833 16.5081 3.32007 14.7151 3.32007 12.019C3.32007 9.6394 4.45386 8.77588 4.45386 7.29932C4.45386 7.00928 4.40771 6.60718 4.40771 6.43579C4.40771 6.13916 4.59229 5.94141 4.86255 5.94141C5.46899 5.94141 6.14795 6.45557 6.5105 7.22681C6.55664 7.05542 6.56982 6.89722 6.56982 6.73242C6.55664 5.43384 5.77222 4.31982 4.98779 3.38379C4.84937 3.22559 4.77686 3.0542 4.77686 2.8894C4.77686 2.46753 5.17236 2.25 5.79199 2.25C9.27905 2.25 14.302 4.70874 14.302 10.6084C14.302 14.1548 11.9817 16.5081 8.50122 16.5081ZM8.65283 14.6558C10.2217 14.6558 10.9863 13.5022 10.9863 12.197C10.9863 10.9116 10.2349 9.47461 8.79126 8.79565C8.73193 8.77588 8.69238 8.80884 8.69897 8.86816C8.81104 9.94922 8.64624 10.9512 8.29688 11.4258C8.13208 11.0435 7.93433 10.7139 7.6311 10.4436C7.58496 10.4041 7.54541 10.4238 7.53223 10.4832C7.42676 11.228 6.60278 11.6433 6.60278 12.8035C6.60278 13.9109 7.42017 14.6558 8.65283 14.6558Z"
                                              fill="#FF574C"/>
                                    </svg>

                                    <b>
                                        {{ dscountFullDailyAmount }}
                                    </b>
                                </p>
                            </div>
                        </div>
                        <p class="membership-discount__offer-item-text">
                            {{ ("membership_info.discount.offer.special.text")|trans({'%plan%': discountFullPrice, '%months%': ("membership_info.period_value." ~ discountBillingPeriod)|trans, '%support_email%': support_email, '%termsLink%': path('static_page_terms') })|raw }}
                        </p>
                    </div>
                </li>
            </ul>

            <button
                    class="membership-discount__btn js-membership-discount__btn"
                    data-discount-product-id="{{ subscriptionDiscountData.id }}"
                    data-discount-external-id="{{ subscriptionData.externalId }}"
            >
                {{ ("membership_info.discount.btn_text_" ~ buttonActionType)|trans({'%percent%': subscriptionDiscountPercents})|raw }}
            </button>
        </div>
    </div>
</div>
