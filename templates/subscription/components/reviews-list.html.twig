<div class="reviews__list">
    {% for i in range(startIndex ?? 0, endIndex ?? 0) %}
        {% set item = ('reviews_page.review_items.'~i) %}

        <script type="application/ld+json">
            {
                "@context": "https://schema.org",
                "@type": "Review",
                "name": "PawChamp: Puppy & Dog Training",
                "itemReviewed": {
                    "@type": "LocalBusiness",
                    "name": "PawChamp: Puppy & Dog Training",
                    "image": "https://images.paw-champ.com/pc/images/common/event-image.jpg",
                    "telephone": "+1(478)217-7806",
                    "priceRange": "$19.99 - $119.99",
                    "address": {
                        "@type": "PostalAddress",
                        "streetAddress": "3753 Howard Hughes Pkwy Ste 200",
                        "addressLocality": "Las Vegas",
                        "addressRegion": "NV",
                        "postalCode": "89169-0952",
                        "addressCountry": "US"
                    },
                    "aggregateRating": {
                        "@type": "AggregateRating",
                        "ratingValue": "5",
                        "reviewCount": "18"
                    }
                },
                "reviewRating": {
                    "@type": "Rating",
                    "ratingValue": "5",
                    "bestRating": "5"
                },
                "author": {
                    "@type": "Person",
                    "name": "{{ (item~'.name')|trans }}"
                },
                "datePublished": "{{ (item~'.date')|trans }}",
                "reviewBody": "{{ (item~'.text')|trans|raw }}"
            }
        </script>
        <div class="reviews__item reviews__item-hide" itemscope itemtype="https://schema.org/Review">
            <div class="reviews__item-wrapp">
                <h3 class="reviews__item-title" itemprop="name">
                    {{ (item~'.title')|trans }}
                </h3>
                <div class="reviews__item-box">
                    <p class="reviews__item-date" itemprop="datePublished" content="{{ (item~'.date')|trans }}">
                        {{ (item~'.date')|trans }}
                    </p>
                    <p class="reviews__item-username" itemprop="author" itemscope itemtype="https://schema.org/Person">
                        <span itemprop="name">
                            {{ ( item~'.username')|trans }}
                        </span>
                    </p>
                </div>
            </div>
            <div itemprop="itemReviewed" itemscope itemtype="https://schema.org/LocalBusiness">
                <meta itemprop="name" content="PawChamp: Puppy & Dog Training">
                <meta itemprop="image" content="/api/placeholder/400/320">
                <meta itemprop="telephone" content="+1-XXX-XXX-XXXX">
                <meta itemprop="priceRange" content="$19.99 - $119.99">
                <div itemprop="address" itemscope itemtype="https://schema.org/PostalAddress">
                    <meta itemprop="streetAddress" content="3753 Howard Hughes Pkwy Ste 200">
                    <meta itemprop="addressLocality" content="Las Vegas">
                    <meta itemprop="addressRegion" content="NV">
                    <meta itemprop="postalCode" content="89169-0952">
                    <meta itemprop="addressCountry" content="US">
                </div>
            </div>
            <div class="reviews__item-stars" itemprop="reviewRating" itemscope itemtype="https://schema.org/Rating">
                <meta itemprop="ratingValue" content="5">
                <meta itemprop="bestRating" content="5">
                <meta itemprop="worstRating" content="1">
                <svg width="80" height="13" viewBox="0 0 80 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M5.85717 0.365473C5.98067 -0.0648592 6.59047 -0.0648577 6.71397 0.365475L7.78497 4.09736C7.8414 4.29397 8.02441 4.42693 8.22883 4.41984L12.109 4.28521C12.5565 4.26968 12.7449 4.84964 12.3738 5.10007L9.15551 7.27187C8.98596 7.38629 8.91606 7.60143 8.98597 7.79366L10.3131 11.4423C10.4661 11.8631 9.97275 12.2215 9.61989 11.9459L6.55989 9.55631C6.39867 9.43042 6.17246 9.43042 6.01125 9.55631L2.95125 11.9459C2.59839 12.2215 2.10505 11.8631 2.25808 11.4423L3.58516 7.79366C3.65508 7.60143 3.58518 7.38629 3.41563 7.27187L0.197352 5.10007C-0.173755 4.84964 0.0146859 4.26968 0.46212 4.28521L4.34231 4.41984C4.54673 4.42693 4.72974 4.29397 4.78617 4.09736L5.85717 0.365473Z"
                            fill="#4ECE52"/>
                    <path d="M22.6187 0.365473C22.7422 -0.0648592 23.352 -0.0648577 23.4755 0.365475L24.5465 4.09736C24.6029 4.29397 24.7859 4.42693 24.9903 4.41984L28.8705 4.28521C29.318 4.26968 29.5064 4.84964 29.1353 5.10007L25.917 7.27187C25.7475 7.38629 25.6776 7.60143 25.7475 7.79366L27.0746 11.4423C27.2276 11.8631 26.7343 12.2215 26.3814 11.9459L23.3214 9.55631C23.1602 9.43042 22.934 9.43042 22.7728 9.55631L19.7128 11.9459C19.3599 12.2215 18.8666 11.8631 19.0196 11.4423L20.3467 7.79366C20.4166 7.60143 20.3467 7.38629 20.1771 7.27187L16.9589 5.10007C16.5878 4.84964 16.7762 4.26968 17.2236 4.28521L21.1038 4.41984C21.3082 4.42693 21.4912 4.29397 21.5477 4.09736L22.6187 0.365473Z"
                            fill="#4ECE52"/>
                    <path d="M39.3802 0.365473C39.5037 -0.0648592 40.1135 -0.0648577 40.237 0.365475L41.308 4.09736C41.3644 4.29397 41.5474 4.42693 41.7519 4.41984L45.6321 4.28521C46.0795 4.26968 46.2679 4.84964 45.8968 5.10007L42.6786 7.27187C42.509 7.38629 42.4391 7.60143 42.509 7.79366L43.8361 11.4423C43.9891 11.8631 43.4958 12.2215 43.1429 11.9459L40.0829 9.55631C39.9217 9.43042 39.6955 9.43042 39.5343 9.55631L36.4743 11.9459C36.1214 12.2215 35.6281 11.8631 35.7811 11.4423L37.1082 7.79366C37.1781 7.60143 37.1082 7.38629 36.9387 7.27187L33.7204 5.10007C33.3493 4.84964 33.5377 4.26968 33.9852 4.28521L37.8653 4.41984C38.0698 4.42693 38.2528 4.29397 38.3092 4.09736L39.3802 0.365473Z"
                            fill="#4ECE52"/>
                    <path d="M56.1417 0.365473C56.2652 -0.0648592 56.875 -0.0648577 56.9985 0.365475L58.0695 4.09736C58.1259 4.29397 58.309 4.42693 58.5134 4.41984L62.3936 4.28521C62.841 4.26968 63.0294 4.84964 62.6583 5.10007L59.4401 7.27187C59.2705 7.38629 59.2006 7.60143 59.2705 7.79366L60.5976 11.4423C60.7506 11.8631 60.2573 12.2215 59.9044 11.9459L56.8444 9.55631C56.6832 9.43042 56.457 9.43042 56.2958 9.55631L53.2358 11.9459C52.8829 12.2215 52.3896 11.8631 52.5426 11.4423L53.8697 7.79366C53.9396 7.60143 53.8697 7.38629 53.7002 7.27187L50.4819 5.10007C50.1108 4.84964 50.2992 4.26968 50.7467 4.28521L54.6269 4.41984C54.8313 4.42693 55.0143 4.29397 55.0707 4.09736L56.1417 0.365473Z"
                            fill="#4ECE52"/>
                    <path d="M72.9032 0.365473C73.0267 -0.0648592 73.6365 -0.0648577 73.76 0.365475L74.831 4.09736C74.8874 4.29397 75.0705 4.42693 75.2749 4.41984L79.1551 4.28521C79.6025 4.26968 79.7909 4.84964 79.4198 5.10007L76.2016 7.27187C76.032 7.38629 75.9621 7.60143 76.032 7.79366L77.3591 11.4423C77.5121 11.8631 77.0188 12.2215 76.6659 11.9459L73.6059 9.55631C73.4447 9.43042 73.2185 9.43042 73.0573 9.55631L69.9973 11.9459C69.6444 12.2215 69.1511 11.8631 69.3041 11.4423L70.6312 7.79366C70.7011 7.60143 70.6312 7.38629 70.4617 7.27187L67.2434 5.10007C66.8723 4.84964 67.0607 4.26968 67.5082 4.28521L71.3884 4.41984C71.5928 4.42693 71.7758 4.29397 71.8322 4.09736L72.9032 0.365473Z"
                            fill="#4ECE52"/>
                </svg>
            </div>
            <p class="reviews__item-text" itemprop="reviewBody">
                {{ (item~'.review_body')|trans|replace({'\n': "<br>"})|raw }}
            </p>
        </div>
    {% endfor %}
</div>