<section class="landing__what" id="whatYouGetBlock">
    <div class="container">
        <h2 class="landing__what-title">
            What you get
        </h2>
        <ul class="landing__what-list">
            <li class="landing__what-item">
                <div class="landing__what-item-box">
                    Reduce
                    <span class="nameInsertSelector">
                            {{ 'shared.pet_name'|resp_i11n_trans(domain='shared') }}'s
                        </span>
                    anxiety fast with science-backed approach
                </div>
            </li>
            <li class="landing__what-item">
                <div class="landing__what-item-box">
                    Forget about <span id="secondProblemInsertSelector">outdated training methods</span>
                </div>
            </li>
            <li class="landing__what-item">
                <div class="landing__what-item-box">
                    Learn dog <b>calming</b> techniques
                </div>
            </li>
            <li class="landing__what-item">
                <div class="landing__what-item-box">
                    Deal with <span id="firstProblemInsertSelector">problem</span>
                </div>
            </li>
            <li class="landing__what-item">
                <div class="landing__what-item-box">
                    Maintain low anxiety level
                </div>
            </li>
            <li class="landing__what-item">
                <div class="landing__what-item-box">
                    50+ games to boost
                    <span class="nameInsertSelector">
                            {{ 'shared.pet_name'|resp_i11n_trans(domain='shared') }}'s
                        </span>
                    mental health as a bonus
                </div>
            </li>
            <li class="landing__what-item">
                <div class="landing__what-item-box">
                    Enjoy well-being with happy, confident and obedient
                    {% if app.request.query.get('age') == 'puppy' %}puppy{% else %}dog{% endif %}
                </div>
            </li>
        </ul>
    </div>
</section>
