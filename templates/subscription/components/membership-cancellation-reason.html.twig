<div class="membership-subscription" id="membership-cancellation-reason">
    <div class="container">
        <div class="membership-subscription_inner">
            {% include 'subscription/components/membership-back-btn.html.twig' %}

            <h1 class="membership-subscription-header">
                {{ ("membership_info.cancel_reason_question")|trans }}
            </h1>

            <div class="membership-subscription_reasons">
                {% for i in range(0, 4) %}
                    <label class="membership-subscription_reasons_item">
                        <p class="membership-subscription_reasons_item_container">
                            <input
                                    class="membership-subscription_reasons_item_checkbox"
                                    type="checkbox"
                                    data-reason={{ ("membership_info.cancel_reasons."~i~".attribute")|trans }}
                            >
                            <span class="membership-subscription_reasons_item_checkmark"></span>
                        </p>

                        <p class="membership-subscription_reasons_item_text">
                            {{ ("membership_info.cancel_reasons."~i~".text")|trans }}
                        </p>
                    </label>
                {% endfor %}
            </div>

            <div class="membership-subscription-btn">
                <div class="membership-info_support_btn btn-black" data-reset-action="next">
                    {{ ("membership_info.continue_button")|trans }}
                </div>
            </div>
        </div>
    </div>
</div>
