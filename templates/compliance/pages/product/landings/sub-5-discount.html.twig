<!DOCTYPE html>
<html lang="en">
<head>
    {% include 'subscription/components/head.html.twig' %}

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link rel="stylesheet" media="print" onload="this.onload=null;this.removeAttribute('media');" href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700;800&display=swap">

    <meta name="format-detection" content="telephone=no" />

    {% set countryNamesList = {
        'AU' : 'Australia',
        'CA' : 'Canada',
        'DE' : 'Germany',
        'CN' : 'Hong Kong',
        'NL' : 'Netherlands',
        'NZ' : 'New Zealand',
        'SG' : 'Singapore',
        'SE' : 'Sweden',
        'GB' : 'United Kingdom',
        'US': 'United States',
    } %}
    {% set clientCountry = getCountry()|upper %}
    {% set isCountryBR = clientCountry == 'BR' %}
    {% set countryCodesEEA = [
        "AT", "BE", "BG", "HR", "CY", "CZ", "DK", "EE", "FI",
        "FR", "DE", "GR", "HU", "IS", "IE", "IT", "LV", "LI",
        "LT", "LU", "MT", "NL", "NO", "PL", "PT", "RO", "SK",
        "SI", "ES", "SE", "GB"
    ] %}

    {% set deviceInfo = getDeviceNameAndModel() %}

    {% block additionalStyles %}
        <link rel="stylesheet" href="{{ asset("/assets/compliance/css/landing-5.css") }}">
    {% endblock %}

    {% block additionalScripts %}
        {% include 'common/hotjar.html.twig' %}

        <script defer src="{{ asset("/assets/compliance/js/landing5.js") }}"></script>
    {% endblock %}
</head>
<body class="{% if deviceInfo.os %} device-{{ deviceInfo.os.name | default('unknown') | lower }} {% endif %} {% if clientCountry == 'MX' %}three-letter-currency{% endif %}">

{% include 'subscription/components/google-tag-manager/google-tag-manager-body.html.twig' %}
<main class="main landing">
    <section class="landing__header">
        <div class="container">
            <div class="landing__header-logo-box">
                <svg width="144" height="41" viewBox="0 0 144 41" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <g filter="url(#filter0_d_1240_1731)">
                        <rect x="3.99976" y="2.1524" width="32.5553" height="32.5553" rx="7.17755" fill="url(#paint0_linear_1240_1731)"/>
                        <rect x="4.14381" y="2.29645" width="32.2672" height="32.2672" rx="7.0335" stroke="url(#paint1_linear_1240_1731)" stroke-width="0.2881"/>
                    </g>
                    <g filter="url(#filter1_d_1240_1731)">
                        <rect x="4" y="1" width="32.5553" height="32.5553" rx="7.17755" fill="url(#paint2_linear_1240_1731)"/>
                        <rect x="4.14405" y="1.14405" width="32.2672" height="32.2672" rx="7.0335" stroke="url(#paint3_linear_1240_1731)" stroke-width="0.2881"/>
                    </g>
                    <mask id="mask0_1240_1731" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="3" y="21" width="34" height="12">
                        <rect x="3.99976" y="21.8161" width="32.5553" height="10.6597" fill="#D9D9D9"/>
                    </mask>
                    <g mask="url(#mask0_1240_1731)">
                        <mask id="mask1_1240_1731" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="3" y="-1" width="34" height="34">
                            <rect x="4.14381" y="0.0645213" width="32.2672" height="32.2672" rx="7.0335" fill="url(#paint4_linear_1240_1731)" stroke="url(#paint5_linear_1240_1731)" stroke-width="0.2881"/>
                        </mask>
                        <g mask="url(#mask1_1240_1731)">
                            <g opacity="0.7">
                                <rect x="31.0811" y="-0.0795593" width="1.27917" height="35.2696" rx="0.2881" transform="rotate(30 31.0811 -0.0795593)" fill="#EF3A36"/>
                                <rect x="31.0811" y="-0.0795593" width="1.27917" height="35.2696" rx="0.2881" transform="rotate(30 31.0811 -0.0795593)" fill="#FF756B"/>
                                <rect x="31.0811" y="-0.0795593" width="1.27917" height="35.2696" rx="0.2881" transform="rotate(30 31.0811 -0.0795593)" fill="#FFB1AC"/>
                            </g>
                            <g opacity="0.3">
                                <rect x="29.6404" y="-0.0795593" width="1.27917" height="35.334" rx="0.2881" transform="rotate(30 29.6404 -0.0795593)" fill="#EF3A36"/>
                                <rect x="29.6404" y="-0.0795593" width="1.27917" height="35.334" rx="0.2881" transform="rotate(30 29.6404 -0.0795593)" fill="#FF756B"/>
                                <rect x="29.6404" y="-0.0795593" width="1.27917" height="35.334" rx="0.2881" transform="rotate(30 29.6404 -0.0795593)" fill="#FFB1AC"/>
                            </g>
                            <g opacity="0.7">
                                <rect x="28.2" y="-0.0795593" width="1.27917" height="35.2251" rx="0.2881" transform="rotate(30 28.2 -0.0795593)" fill="#EF3A36"/>
                                <rect x="28.2" y="-0.0795593" width="1.27917" height="35.2251" rx="0.2881" transform="rotate(30 28.2 -0.0795593)" fill="#FF756B"/>
                                <rect x="28.2" y="-0.0795593" width="1.27917" height="35.2251" rx="0.2881" transform="rotate(30 28.2 -0.0795593)" fill="#FFB1AC"/>
                            </g>
                            <g opacity="0.3">
                                <rect x="26.7595" y="-0.0795593" width="1.27917" height="34.17" rx="0.2881" transform="rotate(30 26.7595 -0.0795593)" fill="#EF3A36"/>
                                <rect x="26.7595" y="-0.0795593" width="1.27917" height="34.17" rx="0.2881" transform="rotate(30 26.7595 -0.0795593)" fill="#FF756B"/>
                                <rect x="26.7595" y="-0.0795593" width="1.27917" height="34.17" rx="0.2881" transform="rotate(30 26.7595 -0.0795593)" fill="#FFB1AC"/>
                            </g>
                            <g opacity="0.7">
                                <rect x="25.3191" y="-0.0795593" width="1.27917" height="32.7552" rx="0.2881" transform="rotate(30 25.3191 -0.0795593)" fill="#EF3A36"/>
                                <rect x="25.3191" y="-0.0795593" width="1.27917" height="32.7552" rx="0.2881" transform="rotate(30 25.3191 -0.0795593)" fill="#FF756B"/>
                                <rect x="25.3191" y="-0.0795593" width="1.27917" height="32.7552" rx="0.2881" transform="rotate(30 25.3191 -0.0795593)" fill="#FFB1AC"/>
                            </g>
                            <g opacity="0.7">
                                <rect width="1.27974" height="34.619" rx="0.2881" transform="matrix(-0.866025 0.5 0.5 0.866025 10.2937 0.576981)" fill="#EF3A36"/>
                                <rect width="1.27974" height="34.619" rx="0.2881" transform="matrix(-0.866025 0.5 0.5 0.866025 10.2937 0.576981)" fill="#FF756B"/>
                                <rect width="1.27974" height="34.619" rx="0.2881" transform="matrix(-0.866025 0.5 0.5 0.866025 10.2937 0.576981)" fill="#FFB1AC"/>
                            </g>
                            <g opacity="0.2">
                                <rect width="1.27974" height="34.7609" rx="0.2881" transform="matrix(-0.866025 0.5 0.5 0.866025 11.7344 0.576981)" fill="#EF3A36"/>
                                <rect width="1.27974" height="34.7609" rx="0.2881" transform="matrix(-0.866025 0.5 0.5 0.866025 11.7344 0.576981)" fill="#FF756B"/>
                                <rect width="1.27974" height="34.7609" rx="0.2881" transform="matrix(-0.866025 0.5 0.5 0.866025 11.7344 0.576981)" fill="#FFB1AC"/>
                            </g>
                            <g opacity="0.7">
                                <rect width="1.27974" height="34.6408" rx="0.2881" transform="matrix(-0.866025 0.5 0.5 0.866025 13.1746 0.576981)" fill="#EF3A36"/>
                                <rect width="1.27974" height="34.6408" rx="0.2881" transform="matrix(-0.866025 0.5 0.5 0.866025 13.1746 0.576981)" fill="#FF756B"/>
                                <rect width="1.27974" height="34.6408" rx="0.2881" transform="matrix(-0.866025 0.5 0.5 0.866025 13.1746 0.576981)" fill="#FFB1AC"/>
                            </g>
                            <g opacity="0.2">
                                <rect width="1.27974" height="33.4843" rx="0.2881" transform="matrix(-0.866025 0.5 0.5 0.866025 14.6152 0.576981)" fill="#EF3A36"/>
                                <rect width="1.27974" height="33.4843" rx="0.2881" transform="matrix(-0.866025 0.5 0.5 0.866025 14.6152 0.576981)" fill="#FF756B"/>
                                <rect width="1.27974" height="33.4843" rx="0.2881" transform="matrix(-0.866025 0.5 0.5 0.866025 14.6152 0.576981)" fill="#FFB1AC"/>
                            </g>
                            <g opacity="0.7">
                                <rect width="1.27974" height="32.2708" rx="0.2881" transform="matrix(-0.866025 0.5 0.5 0.866025 16.0557 0.576981)" fill="#EF3A36"/>
                                <rect width="1.27974" height="32.2708" rx="0.2881" transform="matrix(-0.866025 0.5 0.5 0.866025 16.0557 0.576981)" fill="#FF756B"/>
                                <rect width="1.27974" height="32.2708" rx="0.2881" transform="matrix(-0.866025 0.5 0.5 0.866025 16.0557 0.576981)" fill="#FFB1AC"/>
                            </g>
                        </g>
                    </g>
                    <g filter="url(#filter2_d_1240_1731)">
                        <mask id="path-17-inside-1_1240_1731" fill="white">
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M19.7101 6.2021C20.105 5.68579 20.8827 5.68579 21.2776 6.2021L22.0122 7.16235C22.301 7.53991 22.8179 7.6579 23.2419 7.44304L24.3204 6.8966C24.9003 6.60277 25.6009 6.94017 25.7327 7.57672L25.9778 8.76058C26.0742 9.22606 26.4888 9.55666 26.9641 9.54706L28.1728 9.52264C28.8227 9.50951 29.3075 10.1175 29.1501 10.7482L28.8574 11.9212C28.7422 12.3824 28.9723 12.8601 29.4047 13.0577L30.5043 13.5601C31.0955 13.8303 31.2686 14.5884 30.8531 15.0883L30.0804 16.0182C29.7765 16.3837 29.7765 16.914 30.0804 17.2796L30.8531 18.2094C31.2686 18.7093 31.0955 19.4674 30.5043 19.7376L29.4047 20.24C28.9723 20.4376 28.7422 20.9153 28.8574 21.3765L29.1501 22.5495C29.3075 23.1802 28.8227 23.7882 28.1728 23.7751L26.9641 23.7507C26.4888 23.7411 26.0742 24.0717 25.9778 24.5371L25.7327 25.721C25.6009 26.3575 24.9003 26.6949 24.3204 26.4011L23.2419 25.8547C22.8179 25.6398 22.301 25.7578 22.0122 26.1354L21.2776 27.0956C20.8827 27.6119 20.105 27.6119 19.7101 27.0956L18.9755 26.1354C18.6867 25.7578 18.1698 25.6398 17.7458 25.8547L16.6673 26.4011C16.0875 26.6949 15.3869 26.3575 15.255 25.721L15.0099 24.5371C14.9135 24.0717 14.4989 23.7411 14.0237 23.7507L12.8149 23.7751C12.165 23.7882 11.6802 23.1802 11.8376 22.5495L12.1304 21.3765C12.2455 20.9153 12.0154 20.4376 11.5831 20.24L10.4834 19.7376C9.89217 19.4674 9.71913 18.7093 10.1346 18.2094L10.9073 17.2796C11.2112 16.914 11.2112 16.3837 10.9073 16.0182L10.1346 15.0883C9.71913 14.5884 9.89217 13.8303 10.4834 13.5601L11.5831 13.0577C12.0154 12.8601 12.2455 12.3824 12.1304 11.9212L11.8376 10.7482C11.6802 10.1175 12.165 9.50951 12.8149 9.52264L14.0237 9.54706C14.4989 9.55666 14.9135 9.22606 15.0099 8.76058L15.255 7.57672C15.3869 6.94017 16.0875 6.60277 16.6673 6.8966L17.7458 7.44304C18.1698 7.6579 18.6867 7.53991 18.9755 7.16235L19.7101 6.2021ZM20.1336 23.2565C24.2705 23.2565 27.6242 19.9029 27.6242 15.7659C27.6242 11.629 24.2705 8.27531 20.1336 8.27531C15.9966 8.27531 12.643 11.629 12.643 15.7659C12.643 19.9029 15.9966 23.2565 20.1336 23.2565ZM20.1336 23.5446C24.4296 23.5446 27.9123 20.062 27.9123 15.7659C27.9123 11.4699 24.4296 7.98721 20.1336 7.98721C15.8375 7.98721 12.3549 11.4699 12.3549 15.7659C12.3549 20.062 15.8375 23.5446 20.1336 23.5446ZM19.4134 14.8624C20.2139 15.1661 21.1541 14.6442 21.5137 13.6968C21.8731 12.7493 21.5156 11.735 20.7152 11.4313C19.9147 11.1277 18.9744 11.6496 18.615 12.597C18.2555 13.5445 18.613 14.5587 19.4134 14.8624ZM20.7948 15.9431C20.7948 15.9431 18.8467 14.8856 16.7106 16.8251C14.7861 18.5722 15.7836 19.6926 16.5898 20.0996C17.1001 20.3572 17.664 20.3437 18.0663 20.334C18.1016 20.3331 18.1357 20.3323 18.1684 20.3317C18.4275 20.327 18.5415 20.4062 18.5415 20.4062C18.5415 20.4062 18.6728 20.4509 18.8229 20.6622C18.8418 20.689 18.8615 20.717 18.8819 20.746C19.1129 21.0754 19.4368 21.5372 19.9469 21.7947C20.7531 22.2017 22.2468 22.3392 22.5101 19.7533C22.8024 16.8829 20.7948 15.9432 20.7948 15.9432V15.9431ZM24.7816 15.7104C24.2326 16.5622 23.2042 16.8767 22.4846 16.413C21.765 15.9492 21.6267 14.8827 22.1756 14.0309C22.7246 13.1791 23.753 12.8645 24.4726 13.3283C25.1922 13.7921 25.3305 14.8585 24.7816 15.7104ZM25.689 17.3416C25.1778 16.7907 24.2348 16.8347 23.5826 17.4398C22.9305 18.0449 22.8164 18.9821 23.3277 19.533C23.8389 20.0839 24.782 20.0399 25.4341 19.4347C26.0861 18.8296 26.2004 17.8924 25.689 17.3415V17.3416ZM16.7635 12.8352C17.5103 12.9195 18.0349 13.7045 17.9351 14.5885C17.8353 15.4724 17.149 16.1207 16.4022 16.0364C15.6554 15.9521 15.1309 15.1672 15.2307 14.2831C15.3304 13.3992 16.0168 12.7509 16.7635 12.8352Z"/>
                        </mask>
                        <path fill-rule="evenodd" clip-rule="evenodd"
                              d="M19.7101 6.2021C20.105 5.68579 20.8827 5.68579 21.2776 6.2021L22.0122 7.16235C22.301 7.53991 22.8179 7.6579 23.2419 7.44304L24.3204 6.8966C24.9003 6.60277 25.6009 6.94017 25.7327 7.57672L25.9778 8.76058C26.0742 9.22606 26.4888 9.55666 26.9641 9.54706L28.1728 9.52264C28.8227 9.50951 29.3075 10.1175 29.1501 10.7482L28.8574 11.9212C28.7422 12.3824 28.9723 12.8601 29.4047 13.0577L30.5043 13.5601C31.0955 13.8303 31.2686 14.5884 30.8531 15.0883L30.0804 16.0182C29.7765 16.3837 29.7765 16.914 30.0804 17.2796L30.8531 18.2094C31.2686 18.7093 31.0955 19.4674 30.5043 19.7376L29.4047 20.24C28.9723 20.4376 28.7422 20.9153 28.8574 21.3765L29.1501 22.5495C29.3075 23.1802 28.8227 23.7882 28.1728 23.7751L26.9641 23.7507C26.4888 23.7411 26.0742 24.0717 25.9778 24.5371L25.7327 25.721C25.6009 26.3575 24.9003 26.6949 24.3204 26.4011L23.2419 25.8547C22.8179 25.6398 22.301 25.7578 22.0122 26.1354L21.2776 27.0956C20.8827 27.6119 20.105 27.6119 19.7101 27.0956L18.9755 26.1354C18.6867 25.7578 18.1698 25.6398 17.7458 25.8547L16.6673 26.4011C16.0875 26.6949 15.3869 26.3575 15.255 25.721L15.0099 24.5371C14.9135 24.0717 14.4989 23.7411 14.0237 23.7507L12.8149 23.7751C12.165 23.7882 11.6802 23.1802 11.8376 22.5495L12.1304 21.3765C12.2455 20.9153 12.0154 20.4376 11.5831 20.24L10.4834 19.7376C9.89217 19.4674 9.71913 18.7093 10.1346 18.2094L10.9073 17.2796C11.2112 16.914 11.2112 16.3837 10.9073 16.0182L10.1346 15.0883C9.71913 14.5884 9.89217 13.8303 10.4834 13.5601L11.5831 13.0577C12.0154 12.8601 12.2455 12.3824 12.1304 11.9212L11.8376 10.7482C11.6802 10.1175 12.165 9.50951 12.8149 9.52264L14.0237 9.54706C14.4989 9.55666 14.9135 9.22606 15.0099 8.76058L15.255 7.57672C15.3869 6.94017 16.0875 6.60277 16.6673 6.8966L17.7458 7.44304C18.1698 7.6579 18.6867 7.53991 18.9755 7.16235L19.7101 6.2021ZM20.1336 23.2565C24.2705 23.2565 27.6242 19.9029 27.6242 15.7659C27.6242 11.629 24.2705 8.27531 20.1336 8.27531C15.9966 8.27531 12.643 11.629 12.643 15.7659C12.643 19.9029 15.9966 23.2565 20.1336 23.2565ZM20.1336 23.5446C24.4296 23.5446 27.9123 20.062 27.9123 15.7659C27.9123 11.4699 24.4296 7.98721 20.1336 7.98721C15.8375 7.98721 12.3549 11.4699 12.3549 15.7659C12.3549 20.062 15.8375 23.5446 20.1336 23.5446ZM19.4134 14.8624C20.2139 15.1661 21.1541 14.6442 21.5137 13.6968C21.8731 12.7493 21.5156 11.735 20.7152 11.4313C19.9147 11.1277 18.9744 11.6496 18.615 12.597C18.2555 13.5445 18.613 14.5587 19.4134 14.8624ZM20.7948 15.9431C20.7948 15.9431 18.8467 14.8856 16.7106 16.8251C14.7861 18.5722 15.7836 19.6926 16.5898 20.0996C17.1001 20.3572 17.664 20.3437 18.0663 20.334C18.1016 20.3331 18.1357 20.3323 18.1684 20.3317C18.4275 20.327 18.5415 20.4062 18.5415 20.4062C18.5415 20.4062 18.6728 20.4509 18.8229 20.6622C18.8418 20.689 18.8615 20.717 18.8819 20.746C19.1129 21.0754 19.4368 21.5372 19.9469 21.7947C20.7531 22.2017 22.2468 22.3392 22.5101 19.7533C22.8024 16.8829 20.7948 15.9432 20.7948 15.9432V15.9431ZM24.7816 15.7104C24.2326 16.5622 23.2042 16.8767 22.4846 16.413C21.765 15.9492 21.6267 14.8827 22.1756 14.0309C22.7246 13.1791 23.753 12.8645 24.4726 13.3283C25.1922 13.7921 25.3305 14.8585 24.7816 15.7104ZM25.689 17.3416C25.1778 16.7907 24.2348 16.8347 23.5826 17.4398C22.9305 18.0449 22.8164 18.9821 23.3277 19.533C23.8389 20.0839 24.782 20.0399 25.4341 19.4347C26.0861 18.8296 26.2004 17.8924 25.689 17.3415V17.3416ZM16.7635 12.8352C17.5103 12.9195 18.0349 13.7045 17.9351 14.5885C17.8353 15.4724 17.149 16.1207 16.4022 16.0364C15.6554 15.9521 15.1309 15.1672 15.2307 14.2831C15.3304 13.3992 16.0168 12.7509 16.7635 12.8352Z"
                              fill="url(#paint6_linear_1240_1731)"/>
                        <path d="M21.2776 6.2021L21.452 6.06874V6.06874L21.2776 6.2021ZM19.7101 6.2021L19.8844 6.33547L19.7101 6.2021ZM22.0122 7.16235L22.1865 7.02899L22.1865 7.02899L22.0122 7.16235ZM23.2419 7.44304L23.1427 7.24724L23.2419 7.44304ZM24.3204 6.8966L24.2212 6.7008L24.3204 6.8966ZM25.7327 7.57672L25.5177 7.62123V7.62123L25.7327 7.57672ZM25.9778 8.76058L26.1928 8.71607V8.71607L25.9778 8.76058ZM26.9641 9.54706L26.9685 9.76651L26.9641 9.54706ZM28.1728 9.52264L28.1772 9.7421L28.1728 9.52264ZM29.1501 10.7482L29.3631 10.8013L29.1501 10.7482ZM28.8574 11.9212L28.6444 11.868V11.868L28.8574 11.9212ZM29.4047 13.0577L29.3134 13.2573L29.4047 13.0577ZM30.5043 13.5601L30.4131 13.7598V13.7598L30.5043 13.5601ZM30.8531 15.0883L30.6843 14.9481L30.8531 15.0883ZM30.0804 16.0182L30.2492 16.1584V16.1584L30.0804 16.0182ZM30.0804 17.2796L30.2492 17.1393L30.0804 17.2796ZM30.8531 18.2094L30.6843 18.3497L30.8531 18.2094ZM30.5043 19.7376L30.4131 19.5379L30.5043 19.7376ZM29.4047 20.24L29.4959 20.4397L29.4047 20.24ZM28.8574 21.3765L28.6444 21.4297V21.4297L28.8574 21.3765ZM29.1501 22.5495L29.3631 22.4964L29.1501 22.5495ZM28.1728 23.7751L28.1684 23.9945L28.1728 23.7751ZM26.9641 23.7507L26.9596 23.9701L26.9641 23.7507ZM25.9778 24.5371L26.1928 24.5816V24.5816L25.9778 24.5371ZM25.7327 25.721L25.5177 25.6765V25.6765L25.7327 25.721ZM24.3204 26.4011L24.2212 26.5969L24.3204 26.4011ZM23.2419 25.8547L23.1427 26.0505L23.2419 25.8547ZM22.0122 26.1354L21.8378 26.002L22.0122 26.1354ZM21.2776 27.0956L21.1033 26.9622L21.2776 27.0956ZM19.7101 27.0956L19.8844 26.9622L19.7101 27.0956ZM18.9755 26.1354L19.1499 26.002H19.1499L18.9755 26.1354ZM17.7458 25.8547L17.6466 25.6589H17.6466L17.7458 25.8547ZM16.6673 26.4011L16.5681 26.2053L16.6673 26.4011ZM15.255 25.721L15.47 25.6765L15.255 25.721ZM15.0099 24.5371L15.2248 24.4926L15.0099 24.5371ZM14.0237 23.7507L14.0281 23.9701H14.0281L14.0237 23.7507ZM12.8149 23.7751L12.8194 23.9945H12.8194L12.8149 23.7751ZM11.8376 22.5495L12.0506 22.6027L11.8376 22.5495ZM12.1304 21.3765L11.9174 21.3234L12.1304 21.3765ZM11.5831 20.24L11.4918 20.4397H11.4918L11.5831 20.24ZM10.4834 19.7376L10.3922 19.9372H10.3922L10.4834 19.7376ZM10.1346 18.2094L10.3034 18.3497L10.3034 18.3497L10.1346 18.2094ZM10.9073 17.2796L10.7385 17.1393H10.7385L10.9073 17.2796ZM10.9073 16.0182L10.7385 16.1584L10.7385 16.1584L10.9073 16.0182ZM10.1346 15.0883L10.3034 14.9481H10.3034L10.1346 15.0883ZM10.4834 13.5601L10.5747 13.7598H10.5747L10.4834 13.5601ZM11.5831 13.0577L11.4918 12.858L11.5831 13.0577ZM12.1304 11.9212L11.9174 11.9743L12.1304 11.9212ZM11.8376 10.7482L12.0506 10.695L11.8376 10.7482ZM12.8149 9.52264L12.8105 9.7421L12.8149 9.52264ZM14.0237 9.54706L14.0192 9.76651H14.0192L14.0237 9.54706ZM15.0099 8.76058L14.7949 8.71607L15.0099 8.76058ZM15.255 7.57672L15.0401 7.53221L15.255 7.57672ZM16.6673 6.8966L16.7665 6.7008L16.6673 6.8966ZM17.7458 7.44304L17.6466 7.63884L17.7458 7.44304ZM18.9755 7.16235L19.1499 7.29572V7.29572L18.9755 7.16235ZM21.5137 13.6968L21.7189 13.7746L21.7189 13.7746L21.5137 13.6968ZM19.4134 14.8624L19.3355 15.0676L19.3355 15.0676L19.4134 14.8624ZM18.615 12.597L18.4098 12.5191L18.4098 12.5191L18.615 12.597ZM16.7106 16.8251L16.8581 16.9876L16.8581 16.9876L16.7106 16.8251ZM20.7948 15.9431H21.0143V15.8125L20.8995 15.7502L20.7948 15.9431ZM16.5898 20.0996L16.4908 20.2955L16.4909 20.2955L16.5898 20.0996ZM18.0663 20.334L18.0716 20.5534H18.0716L18.0663 20.334ZM18.1684 20.3317L18.1643 20.1123H18.1643L18.1684 20.3317ZM18.5415 20.4062L18.4162 20.5864L18.4416 20.604L18.4708 20.614L18.5415 20.4062ZM18.8229 20.6622L18.6439 20.7893L18.6439 20.7893L18.8229 20.6622ZM18.8819 20.746L19.0616 20.62L19.0616 20.62L18.8819 20.746ZM19.9469 21.7947L20.0458 21.5988L20.0458 21.5988L19.9469 21.7947ZM22.5101 19.7533L22.7285 19.7756L22.7285 19.7756L22.5101 19.7533ZM20.7948 15.9432H20.5753V16.0828L20.7017 16.142L20.7948 15.9432ZM22.4846 16.413L22.6035 16.2285L22.4846 16.413ZM22.1756 14.0309L22.3602 14.1498L22.1756 14.0309ZM24.4726 13.3283L24.5915 13.1438L24.5915 13.1438L24.4726 13.3283ZM23.5826 17.4398L23.4333 17.2789L23.4333 17.2789L23.5826 17.4398ZM25.689 17.3416L25.5281 17.4909L25.9085 17.9008V17.3416H25.689ZM23.3277 19.533L23.4886 19.3837L23.4886 19.3837L23.3277 19.533ZM25.4341 19.4347L25.5834 19.5956L25.5834 19.5956L25.4341 19.4347ZM25.689 17.3415L25.8499 17.1922L25.4695 16.7823V17.3415H25.689ZM17.9351 14.5885L17.717 14.5638L17.717 14.5639L17.9351 14.5885ZM16.7635 12.8352L16.7389 13.0533H16.7389L16.7635 12.8352ZM16.4022 16.0364L16.3776 16.2545L16.4022 16.0364ZM15.2307 14.2831L15.0126 14.2585L15.0126 14.2585L15.2307 14.2831ZM21.452 6.06874C20.9691 5.43757 20.0186 5.43757 19.5357 6.06874L19.8844 6.33547C20.1915 5.934 20.7962 5.934 21.1033 6.33547L21.452 6.06874ZM22.1865 7.02899L21.452 6.06874L21.1033 6.33547L21.8378 7.29572L22.1865 7.02899ZM23.1427 7.24724C22.813 7.4143 22.4111 7.32256 22.1865 7.02899L21.8378 7.29572C22.1909 7.75726 22.8228 7.90149 23.3412 7.63884L23.1427 7.24724ZM24.2212 6.7008L23.1427 7.24724L23.3412 7.63884L24.4196 7.09239L24.2212 6.7008ZM25.9476 7.53221C25.7865 6.75406 24.93 6.34162 24.2212 6.7008L24.4196 7.09239C24.8705 6.86393 25.4152 7.12627 25.5177 7.62123L25.9476 7.53221ZM26.1928 8.71607L25.9476 7.53221L25.5177 7.62123L25.7629 8.80509L26.1928 8.71607ZM26.9596 9.3276C26.5901 9.33506 26.2677 9.078 26.1928 8.71607L25.7629 8.80509C25.8807 9.37411 26.3875 9.77825 26.9685 9.76651L26.9596 9.3276ZM28.1684 9.30318L26.9596 9.3276L26.9685 9.76651L28.1772 9.7421L28.1684 9.30318ZM29.3631 10.8013C29.5555 10.0303 28.9629 9.28714 28.1684 9.30318L28.1772 9.7421C28.6826 9.73189 29.0596 10.2046 28.9372 10.695L29.3631 10.8013ZM29.0703 11.9743L29.3631 10.8013L28.9372 10.695L28.6444 11.868L29.0703 11.9743ZM29.4959 12.858C29.1597 12.7044 28.9808 12.3329 29.0703 11.9743L28.6444 11.868C28.5037 12.4318 28.7849 13.0158 29.3134 13.2573L29.4959 12.858ZM30.5955 13.3605L29.4959 12.858L29.3134 13.2573L30.4131 13.7598L30.5955 13.3605ZM31.0219 15.2286C31.5298 14.6175 31.3183 13.6907 30.5955 13.3605L30.4131 13.7598C30.8728 13.9698 31.0073 14.5593 30.6843 14.9481L31.0219 15.2286ZM30.2492 16.1584L31.0219 15.2286L30.6843 14.9481L29.9116 15.8779L30.2492 16.1584ZM30.2492 17.1393C30.0129 16.855 30.0129 16.4427 30.2492 16.1584L29.9116 15.8779C29.5401 16.3248 29.5401 16.9729 29.9116 17.4199L30.2492 17.1393ZM31.0219 18.0691L30.2492 17.1393L29.9116 17.4199L30.6843 18.3497L31.0219 18.0691ZM30.5955 19.9372C31.3183 19.607 31.5298 18.6802 31.0219 18.0691L30.6843 18.3497C31.0073 18.7384 30.8728 19.3279 30.4131 19.5379L30.5955 19.9372ZM29.4959 20.4397L30.5955 19.9372L30.4131 19.5379L29.3134 20.0404L29.4959 20.4397ZM29.0703 21.3234C28.9808 20.9648 29.1597 20.5933 29.4959 20.4397L29.3134 20.0404C28.7849 20.2819 28.5037 20.8659 28.6444 21.4297L29.0703 21.3234ZM29.3631 22.4964L29.0703 21.3234L28.6444 21.4297L28.9372 22.6027L29.3631 22.4964ZM28.1684 23.9945C28.9628 24.0106 29.5555 23.2674 29.3631 22.4964L28.9372 22.6027C29.0596 23.0931 28.6826 23.5658 28.1772 23.5556L28.1684 23.9945ZM26.9596 23.9701L28.1684 23.9945L28.1772 23.5556L26.9685 23.5312L26.9596 23.9701ZM26.1928 24.5816C26.2677 24.2197 26.5901 23.9626 26.9596 23.9701L26.9685 23.5312C26.3875 23.5195 25.8807 23.9236 25.7629 24.4926L26.1928 24.5816ZM25.9476 25.7655L26.1928 24.5816L25.7629 24.4926L25.5177 25.6765L25.9476 25.7655ZM24.2212 26.5969C24.93 26.9561 25.7865 26.5437 25.9476 25.7655L25.5177 25.6765C25.4152 26.1714 24.8705 26.4338 24.4196 26.2053L24.2212 26.5969ZM23.1427 26.0505L24.2212 26.5969L24.4196 26.2053L23.3412 25.6589L23.1427 26.0505ZM22.1865 26.2687C22.4111 25.9752 22.813 25.8834 23.1427 26.0505L23.3412 25.6589C22.8228 25.3962 22.1909 25.5405 21.8378 26.002L22.1865 26.2687ZM21.452 27.229L22.1865 26.2687L21.8378 26.002L21.1033 26.9622L21.452 27.229ZM19.5357 27.229C20.0186 27.8601 20.9691 27.8601 21.452 27.229L21.1033 26.9622C20.7962 27.3637 20.1915 27.3637 19.8844 26.9622L19.5357 27.229ZM18.8012 26.2687L19.5357 27.229L19.8844 26.9622L19.1499 26.002L18.8012 26.2687ZM17.845 26.0505C18.1747 25.8834 18.5766 25.9752 18.8012 26.2687L19.1499 26.002C18.7968 25.5405 18.1649 25.3962 17.6466 25.6589L17.845 26.0505ZM16.7665 26.5969L17.845 26.0505L17.6466 25.6589L16.5681 26.2053L16.7665 26.5969ZM15.0401 25.7655C15.2012 26.5437 16.0577 26.9561 16.7665 26.5969L16.5681 26.2053C16.1172 26.4338 15.5725 26.1714 15.47 25.6765L15.0401 25.7655ZM14.7949 24.5816L15.0401 25.7655L15.47 25.6765L15.2248 24.4926L14.7949 24.5816ZM14.0281 23.9701C14.3976 23.9626 14.72 24.2197 14.7949 24.5816L15.2248 24.4926C15.107 23.9236 14.6002 23.5195 14.0192 23.5312L14.0281 23.9701ZM12.8194 23.9945L14.0281 23.9701L14.0192 23.5312L12.8105 23.5556L12.8194 23.9945ZM11.6246 22.4964C11.4322 23.2674 12.0249 24.0106 12.8194 23.9945L12.8105 23.5556C12.3051 23.5658 11.9281 23.0931 12.0506 22.6027L11.6246 22.4964ZM11.9174 21.3234L11.6246 22.4964L12.0506 22.6027L12.3433 21.4297L11.9174 21.3234ZM11.4918 20.4397C11.828 20.5933 12.0069 20.9648 11.9174 21.3234L12.3433 21.4297C12.484 20.8659 12.2028 20.2819 11.6743 20.0404L11.4918 20.4397ZM10.3922 19.9372L11.4918 20.4397L11.6743 20.0404L10.5747 19.5379L10.3922 19.9372ZM9.96581 18.0691C9.4579 18.6802 9.66942 19.607 10.3922 19.9372L10.5747 19.5379C10.1149 19.3279 9.98037 18.7384 10.3034 18.3497L9.96581 18.0691ZM10.7385 17.1393L9.96581 18.0691L10.3034 18.3497L11.0762 17.4199L10.7385 17.1393ZM10.7385 16.1584C10.9748 16.4427 10.9748 16.855 10.7385 17.1393L11.0762 17.4199C11.4476 16.9729 11.4476 16.3248 11.0762 15.8779L10.7385 16.1584ZM9.96581 15.2286L10.7385 16.1584L11.0762 15.8779L10.3034 14.9481L9.96581 15.2286ZM10.3922 13.3605C9.66942 13.6907 9.4579 14.6175 9.96581 15.2286L10.3034 14.9481C9.98037 14.5593 10.1149 13.9698 10.5747 13.7598L10.3922 13.3605ZM11.4918 12.858L10.3922 13.3605L10.5747 13.7598L11.6743 13.2573L11.4918 12.858ZM11.9174 11.9743C12.0069 12.3329 11.828 12.7044 11.4918 12.858L11.6743 13.2573C12.2028 13.0158 12.484 12.4318 12.3433 11.868L11.9174 11.9743ZM11.6246 10.8013L11.9174 11.9743L12.3433 11.868L12.0506 10.695L11.6246 10.8013ZM12.8194 9.30318C12.0249 9.28714 11.4322 10.0303 11.6246 10.8013L12.0506 10.695C11.9281 10.2046 12.3051 9.73189 12.8105 9.7421L12.8194 9.30318ZM14.0281 9.3276L12.8194 9.30318L12.8105 9.7421L14.0192 9.76651L14.0281 9.3276ZM14.7949 8.71607C14.72 9.07801 14.3976 9.33506 14.0281 9.3276L14.0192 9.76651C14.6002 9.77825 15.107 9.37411 15.2248 8.80509L14.7949 8.71607ZM15.0401 7.53221L14.7949 8.71607L15.2248 8.80509L15.47 7.62123L15.0401 7.53221ZM16.7665 6.7008C16.0577 6.34162 15.2012 6.75406 15.0401 7.53221L15.47 7.62123C15.5725 7.12627 16.1172 6.86393 16.5681 7.09239L16.7665 6.7008ZM17.845 7.24724L16.7665 6.7008L16.5681 7.09239L17.6466 7.63884L17.845 7.24724ZM18.8012 7.02899C18.5766 7.32256 18.1747 7.4143 17.845 7.24724L17.6466 7.63884C18.1649 7.90149 18.7968 7.75725 19.1499 7.29572L18.8012 7.02899ZM19.5357 6.06874L18.8012 7.02899L19.1499 7.29572L19.8844 6.33547L19.5357 6.06874ZM27.4047 15.7659C27.4047 19.7816 24.1493 23.037 20.1336 23.037V23.476C24.3918 23.476 27.8437 20.0241 27.8437 15.7659H27.4047ZM20.1336 8.49481C24.1493 8.49481 27.4047 11.7502 27.4047 15.7659H27.8437C27.8437 11.5077 24.3918 8.05581 20.1336 8.05581V8.49481ZM12.8625 15.7659C12.8625 11.7502 16.1179 8.49481 20.1336 8.49481V8.05581C15.8754 8.05581 12.4235 11.5077 12.4235 15.7659H12.8625ZM20.1336 23.037C16.1179 23.037 12.8625 19.7816 12.8625 15.7659H12.4235C12.4235 20.0241 15.8754 23.476 20.1336 23.476V23.037ZM27.6928 15.7659C27.6928 19.9408 24.3084 23.3251 20.1336 23.3251V23.7641C24.5509 23.7641 28.1318 20.1832 28.1318 15.7659H27.6928ZM20.1336 8.20671C24.3084 8.20671 27.6928 11.5911 27.6928 15.7659H28.1318C28.1318 11.3486 24.5509 7.76771 20.1336 7.76771V8.20671ZM12.5744 15.7659C12.5744 11.5911 15.9587 8.20671 20.1336 8.20671V7.76771C15.7163 7.76771 12.1354 11.3486 12.1354 15.7659H12.5744ZM20.1336 23.3251C15.9587 23.3251 12.5744 19.9408 12.5744 15.7659H12.1354C12.1354 20.1832 15.7163 23.7641 20.1336 23.7641V23.3251ZM21.3085 13.6189C20.9791 14.4868 20.1473 14.906 19.4913 14.6572L19.3355 15.0676C20.2804 15.4261 21.3292 14.8017 21.7189 13.7746L21.3085 13.6189ZM20.6373 11.6365C21.2934 11.8854 21.6377 12.7509 21.3084 13.6189L21.7189 13.7746C22.1085 12.7476 21.7379 11.5845 20.793 11.2261L20.6373 11.6365ZM18.8202 12.6748C19.1495 11.807 19.9812 11.3877 20.6373 11.6365L20.793 11.2261C19.8481 10.8676 18.7994 11.4921 18.4098 12.5191L18.8202 12.6748ZM19.4913 14.6572C18.8353 14.4083 18.4909 13.5428 18.8202 12.6748L18.4098 12.5191C18.0201 13.5461 18.3907 14.7091 19.3355 15.0676L19.4913 14.6572ZM16.8581 16.9876C17.8821 16.0579 18.8472 15.8558 19.5473 15.8762C19.9003 15.8865 20.1901 15.9535 20.3904 16.0173C20.4904 16.0492 20.5677 16.0802 20.6189 16.1026C20.6445 16.1137 20.6635 16.1228 20.6755 16.1287C20.6815 16.1316 20.6858 16.1338 20.6882 16.1351C20.6894 16.1357 20.6902 16.1361 20.6905 16.1363C20.6907 16.1363 20.6907 16.1364 20.6906 16.1363C20.6906 16.1363 20.6905 16.1363 20.6904 16.1362C20.6904 16.1362 20.6903 16.1361 20.6903 16.1361C20.6902 16.1361 20.6901 16.136 20.7948 15.9431C20.8995 15.7502 20.8994 15.7501 20.8993 15.7501C20.8992 15.75 20.8991 15.75 20.899 15.7499C20.8987 15.7498 20.8985 15.7496 20.8982 15.7495C20.8977 15.7492 20.897 15.7488 20.8962 15.7484C20.8946 15.7476 20.8926 15.7465 20.89 15.7452C20.885 15.7426 20.878 15.739 20.8692 15.7347C20.8517 15.7261 20.8267 15.7143 20.7949 15.7004C20.7312 15.6725 20.6396 15.636 20.5237 15.599C20.2919 15.5252 19.9612 15.4491 19.5601 15.4374C18.7522 15.4139 17.6752 15.6528 16.563 16.6626L16.8581 16.9876ZM16.6887 19.9037C16.3137 19.7143 15.9321 19.3764 15.8414 18.9219C15.7536 18.4818 15.922 17.8375 16.8581 16.9876L16.563 16.6626C15.5747 17.5598 15.2796 18.3493 15.4109 19.0078C15.5394 19.6521 16.0597 20.0779 16.4908 20.2955L16.6887 19.9037ZM18.061 20.1146C17.6528 20.1244 17.143 20.133 16.6887 19.9037L16.4909 20.2955C17.0571 20.5814 17.6753 20.563 18.0716 20.5534L18.061 20.1146ZM18.1643 20.1123C18.1309 20.1129 18.0962 20.1137 18.061 20.1146L18.0716 20.5534C18.107 20.5526 18.1404 20.5518 18.1724 20.5512L18.1643 20.1123ZM18.5415 20.4062C18.6668 20.2259 18.6667 20.2259 18.6666 20.2258C18.6665 20.2258 18.6664 20.2257 18.6664 20.2257C18.6663 20.2256 18.6661 20.2255 18.666 20.2254C18.6657 20.2252 18.6654 20.225 18.6651 20.2248C18.6646 20.2244 18.664 20.224 18.6633 20.2236C18.662 20.2227 18.6606 20.2218 18.6591 20.2208C18.656 20.2188 18.6525 20.2166 18.6484 20.2142C18.6402 20.2093 18.63 20.2036 18.6176 20.1975C18.5929 20.1851 18.5599 20.171 18.5181 20.1578C18.4343 20.1314 18.3173 20.1094 18.1643 20.1123L18.1724 20.5512C18.2786 20.5492 18.3482 20.5645 18.3861 20.5765C18.4052 20.5825 18.4169 20.5879 18.4216 20.5903C18.424 20.5915 18.4247 20.5919 18.4238 20.5914C18.4233 20.5911 18.4224 20.5906 18.4212 20.5897C18.4205 20.5893 18.4198 20.5889 18.419 20.5883C18.4186 20.588 18.4181 20.5877 18.4177 20.5874C18.4174 20.5872 18.4172 20.5871 18.4169 20.5869C18.4168 20.5868 18.4167 20.5867 18.4166 20.5867C18.4165 20.5866 18.4164 20.5866 18.4164 20.5865C18.4163 20.5865 18.4162 20.5864 18.5415 20.4062ZM19.0019 20.5352C18.9133 20.4104 18.8263 20.3293 18.7552 20.2775C18.7198 20.2517 18.6888 20.2336 18.6642 20.221C18.652 20.2147 18.6413 20.2099 18.6326 20.2062C18.6282 20.2043 18.6243 20.2028 18.6209 20.2015C18.6191 20.2008 18.6176 20.2003 18.6161 20.1997C18.6154 20.1995 18.6147 20.1992 18.614 20.199C18.6137 20.1989 18.6134 20.1988 18.6131 20.1987C18.6129 20.1986 18.6128 20.1986 18.6126 20.1985C18.6125 20.1985 18.6124 20.1985 18.6124 20.1984C18.6123 20.1984 18.6122 20.1984 18.5415 20.4062C18.4708 20.614 18.4707 20.6139 18.4706 20.6139C18.4705 20.6139 18.4704 20.6138 18.4704 20.6138C18.4702 20.6138 18.4701 20.6137 18.4699 20.6137C18.4696 20.6136 18.4694 20.6135 18.4691 20.6134C18.4686 20.6132 18.4681 20.613 18.4676 20.6129C18.4667 20.6125 18.4658 20.6122 18.4651 20.6119C18.4637 20.6114 18.4628 20.611 18.4623 20.6108C18.4613 20.6104 18.4621 20.6107 18.4645 20.6119C18.4692 20.6143 18.4804 20.6205 18.4966 20.6323C18.5287 20.6557 18.5824 20.7027 18.6439 20.7893L19.0019 20.5352ZM19.0616 20.62C19.0413 20.5911 19.0213 20.5625 19.0018 20.5352L18.6439 20.7893C18.6624 20.8155 18.6817 20.843 18.7022 20.8721L19.0616 20.62ZM20.0458 21.5988C19.5917 21.3695 19.296 20.9543 19.0616 20.62L18.7022 20.8721C18.9298 21.1966 19.2819 21.7049 19.848 21.9906L20.0458 21.5988ZM22.2917 19.7311C22.1636 20.989 21.7451 21.507 21.3388 21.6977C20.9193 21.8945 20.4209 21.7881 20.0458 21.5988L19.848 21.9906C20.2791 22.2083 20.9306 22.3742 21.5253 22.0951C22.1332 21.8098 22.5932 21.1036 22.7285 19.7756L22.2917 19.7311ZM20.7948 15.9432C20.7017 16.142 20.7016 16.142 20.7015 16.1419C20.7015 16.1419 20.7014 16.1419 20.7013 16.1418C20.7012 16.1418 20.7012 16.1418 20.7011 16.1417C20.7011 16.1417 20.7011 16.1417 20.7013 16.1418C20.7016 16.1419 20.7024 16.1423 20.7036 16.1429C20.7061 16.1441 20.7103 16.1463 20.7163 16.1493C20.7281 16.1555 20.7467 16.1654 20.7709 16.1794C20.8193 16.2073 20.8901 16.2511 20.9752 16.3127C21.1455 16.436 21.3715 16.6294 21.5893 16.9073C22.0214 17.4586 22.4318 18.3552 22.2917 19.7311L22.7285 19.7756C22.8806 18.2811 22.4334 17.2726 21.9348 16.6365C21.6872 16.3206 21.4296 16.0997 21.2326 15.9571C21.134 15.8857 21.0503 15.8337 20.9901 15.799C20.9599 15.7816 20.9356 15.7686 20.9183 15.7596C20.9096 15.7551 20.9026 15.7516 20.8975 15.7491C20.8949 15.7478 20.8928 15.7468 20.8912 15.746C20.8904 15.7456 20.8897 15.7453 20.8892 15.745C20.8889 15.7449 20.8886 15.7448 20.8884 15.7447C20.8883 15.7446 20.8882 15.7446 20.8881 15.7445C20.888 15.7445 20.8878 15.7444 20.7948 15.9432ZM20.5753 15.9431V15.9432H21.0143V15.9431H20.5753ZM22.3657 16.5975C23.2152 17.1449 24.371 16.7526 24.9661 15.8293L24.5971 15.5914C24.0942 16.3717 23.1933 16.6085 22.6035 16.2285L22.3657 16.5975ZM21.9911 13.912C21.3961 14.8353 21.5162 16.05 22.3657 16.5975L22.6035 16.2285C22.0137 15.8484 21.8573 14.9301 22.3602 14.1498L21.9911 13.912ZM24.5915 13.1438C23.742 12.5964 22.5862 12.9887 21.9911 13.912L22.3602 14.1498C22.863 13.3695 23.7639 13.1327 24.3537 13.5128L24.5915 13.1438ZM24.9661 15.8293C25.5611 14.9059 25.441 13.6913 24.5915 13.1438L24.3537 13.5128C24.9435 13.893 25.0999 14.8112 24.5971 15.5914L24.9661 15.8293ZM23.7319 17.6007C24.3217 17.0535 25.122 17.0532 25.5281 17.4909L25.8499 17.1923C25.2336 16.5281 24.1478 16.616 23.4333 17.2789L23.7319 17.6007ZM23.4886 19.3837C23.0823 18.946 23.1422 18.148 23.7319 17.6007L23.4333 17.2789C22.7188 17.9419 22.5504 19.0182 23.1668 19.6823L23.4886 19.3837ZM25.2848 19.2738C24.695 19.8211 23.8947 19.8214 23.4886 19.3837L23.1668 19.6823C23.7831 20.3465 24.8689 20.2586 25.5834 19.5956L25.2848 19.2738ZM25.5281 17.4908C25.9344 17.9285 25.8745 18.7265 25.2848 19.2738L25.5834 19.5956C26.2978 18.9326 26.4663 17.8563 25.8499 17.1922L25.5281 17.4908ZM25.9085 17.3416V17.3415H25.4695V17.3416H25.9085ZM18.1532 14.6131C18.2626 13.6446 17.6884 12.7187 16.7882 12.6171L16.7389 13.0533C17.3322 13.1203 17.8073 13.7645 17.717 14.5638L18.1532 14.6131ZM16.3776 16.2545C17.2779 16.3561 18.0439 15.5816 18.1532 14.6131L17.717 14.5639C17.6268 15.3632 17.0201 15.8853 16.4268 15.8183L16.3776 16.2545ZM15.0126 14.2585C14.9033 15.2271 15.4773 16.1529 16.3776 16.2545L16.4268 15.8183C15.8336 15.7513 15.3586 15.1073 15.4488 14.3078L15.0126 14.2585ZM16.7882 12.6171C15.8879 12.5155 15.1218 13.29 15.0126 14.2585L15.4488 14.3078C15.539 13.5084 16.1456 12.9864 16.7389 13.0533L16.7882 12.6171Z"
                              fill="white" mask="url(#path-17-inside-1_1240_1731)"/>
                    </g>
                    <g filter="url(#filter3_d_1240_1731)">
                        <mask id="path-19-inside-2_1240_1731" fill="white">
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                  d="M19.4706 5.7087C19.8655 5.19238 20.6432 5.19238 21.0381 5.7087L21.7727 6.66894C22.0615 7.0465 22.5784 7.16449 23.0024 6.94963L24.0809 6.40319C24.6607 6.10937 25.3614 6.44676 25.4932 7.08331L25.7383 8.26717C25.8347 8.73265 26.2493 9.06325 26.7246 9.05365L27.9333 9.02923C28.5832 9.0161 29.068 9.62407 28.9106 10.2548L28.6179 11.4278C28.5027 11.889 28.7328 12.3667 29.1652 12.5643L30.2648 13.0667C30.856 13.3369 31.0291 14.095 30.6136 14.5949L29.8409 15.5247C29.537 15.8903 29.537 16.4206 29.8409 16.7862L30.6136 17.716C31.0291 18.2159 30.856 18.974 30.2648 19.2442L29.1652 19.7466C28.7328 19.9442 28.5027 20.4219 28.6179 20.8831L28.9106 22.0561C29.068 22.6868 28.5832 23.2948 27.9333 23.2817L26.7246 23.2572C26.2493 23.2476 25.8347 23.5782 25.7383 24.0437L25.4932 25.2276C25.3614 25.8641 24.6607 26.2015 24.0809 25.9077L23.0024 25.3613C22.5784 25.1464 22.0615 25.2644 21.7727 25.642L21.0381 26.6022C20.6432 27.1185 19.8655 27.1185 19.4706 26.6022L18.736 25.642C18.4472 25.2644 17.9303 25.1464 17.5063 25.3613L16.4278 25.9077C15.848 26.2015 15.1474 25.8641 15.0155 25.2276L14.7704 24.0437C14.674 23.5782 14.2594 23.2476 13.7842 23.2572L12.5754 23.2817C11.9255 23.2948 11.4407 22.6868 11.5981 22.0561L11.8909 20.8831C12.006 20.4219 11.7759 19.9442 11.3436 19.7466L10.2439 19.2442C9.65267 18.974 9.47963 18.2159 9.89512 17.716L10.6678 16.7862C10.9717 16.4206 10.9717 15.8903 10.6678 15.5247L9.89512 14.5949C9.47963 14.095 9.65267 13.3369 10.2439 13.0667L11.3436 12.5643C11.7759 12.3667 12.006 11.889 11.8909 11.4278L11.5981 10.2548C11.4407 9.62407 11.9255 9.0161 12.5754 9.02923L13.7842 9.05365C14.2594 9.06325 14.674 8.73265 14.7704 8.26717L15.0155 7.08331C15.1474 6.44676 15.848 6.10937 16.4278 6.40319L17.5063 6.94963C17.9303 7.16449 18.4472 7.0465 18.736 6.66894L19.4706 5.7087ZM20.4213 23.5446C24.5582 23.5446 27.9119 20.191 27.9119 16.054C27.9119 11.9171 24.5582 8.56343 20.4213 8.56343C16.2843 8.56343 12.9307 11.9171 12.9307 16.054C12.9307 20.191 16.2843 23.5446 20.4213 23.5446ZM20.4213 23.8327C24.7173 23.8327 28.2 20.3501 28.2 16.054C28.2 11.758 24.7173 8.27533 20.4213 8.27533C16.1252 8.27533 12.6426 11.758 12.6426 16.054C12.6426 20.3501 16.1252 23.8327 20.4213 23.8327ZM19.1735 14.369C19.974 14.6726 20.9143 14.1508 21.2738 13.2033C21.6333 12.2558 21.2758 11.2416 20.4753 10.9379C19.6748 10.6342 18.7346 11.1562 18.3751 12.1035C18.0157 13.051 18.3731 14.0653 19.1735 14.369ZM20.5549 15.4497C20.5549 15.4497 18.6068 14.3922 16.4707 16.3317C14.5462 18.0788 15.5437 19.1992 16.3499 19.6062C16.8602 19.8638 17.4242 19.8503 17.8264 19.8406C17.8617 19.8397 17.8958 19.8389 17.9285 19.8383C18.1877 19.8335 18.3016 19.9128 18.3016 19.9128C18.3016 19.9128 18.433 19.9574 18.583 20.1688C18.602 20.1956 18.6216 20.2236 18.642 20.2526C18.8731 20.582 19.1969 21.0438 19.707 21.3013C20.5132 21.7083 22.0069 21.8458 22.2702 19.2599C22.5625 16.3895 20.5549 15.4498 20.5549 15.4498V15.4497ZM24.5417 15.2169C23.9927 16.0688 22.9644 16.3833 22.2447 15.9196C21.5251 15.4558 21.3868 14.3893 21.9358 13.5375C22.4847 12.6857 23.5131 12.3711 24.2327 12.8349C24.9523 13.2987 25.0907 14.3651 24.5417 15.2169ZM25.4492 16.8482C24.9379 16.2973 23.9949 16.3413 23.3428 16.9464C22.6906 17.5515 22.5765 18.4887 23.0878 19.0396C23.5991 19.5905 24.5421 19.5465 25.1942 18.9413C25.8462 18.3362 25.9605 17.399 25.4492 16.8481V16.8482ZM16.5237 12.3418C17.2704 12.4261 17.795 13.2111 17.6952 14.0951C17.5955 14.979 16.9091 15.6273 16.1624 15.543C15.4156 15.4587 14.8911 14.6738 14.9908 13.7897C15.0905 12.9058 15.7769 12.2575 16.5237 12.3418Z"/>
                        </mask>
                        <path fill-rule="evenodd" clip-rule="evenodd"
                              d="M19.4706 5.7087C19.8655 5.19238 20.6432 5.19238 21.0381 5.7087L21.7727 6.66894C22.0615 7.0465 22.5784 7.16449 23.0024 6.94963L24.0809 6.40319C24.6607 6.10937 25.3614 6.44676 25.4932 7.08331L25.7383 8.26717C25.8347 8.73265 26.2493 9.06325 26.7246 9.05365L27.9333 9.02923C28.5832 9.0161 29.068 9.62407 28.9106 10.2548L28.6179 11.4278C28.5027 11.889 28.7328 12.3667 29.1652 12.5643L30.2648 13.0667C30.856 13.3369 31.0291 14.095 30.6136 14.5949L29.8409 15.5247C29.537 15.8903 29.537 16.4206 29.8409 16.7862L30.6136 17.716C31.0291 18.2159 30.856 18.974 30.2648 19.2442L29.1652 19.7466C28.7328 19.9442 28.5027 20.4219 28.6179 20.8831L28.9106 22.0561C29.068 22.6868 28.5832 23.2948 27.9333 23.2817L26.7246 23.2572C26.2493 23.2476 25.8347 23.5782 25.7383 24.0437L25.4932 25.2276C25.3614 25.8641 24.6607 26.2015 24.0809 25.9077L23.0024 25.3613C22.5784 25.1464 22.0615 25.2644 21.7727 25.642L21.0381 26.6022C20.6432 27.1185 19.8655 27.1185 19.4706 26.6022L18.736 25.642C18.4472 25.2644 17.9303 25.1464 17.5063 25.3613L16.4278 25.9077C15.848 26.2015 15.1474 25.8641 15.0155 25.2276L14.7704 24.0437C14.674 23.5782 14.2594 23.2476 13.7842 23.2572L12.5754 23.2817C11.9255 23.2948 11.4407 22.6868 11.5981 22.0561L11.8909 20.8831C12.006 20.4219 11.7759 19.9442 11.3436 19.7466L10.2439 19.2442C9.65267 18.974 9.47963 18.2159 9.89512 17.716L10.6678 16.7862C10.9717 16.4206 10.9717 15.8903 10.6678 15.5247L9.89512 14.5949C9.47963 14.095 9.65267 13.3369 10.2439 13.0667L11.3436 12.5643C11.7759 12.3667 12.006 11.889 11.8909 11.4278L11.5981 10.2548C11.4407 9.62407 11.9255 9.0161 12.5754 9.02923L13.7842 9.05365C14.2594 9.06325 14.674 8.73265 14.7704 8.26717L15.0155 7.08331C15.1474 6.44676 15.848 6.10937 16.4278 6.40319L17.5063 6.94963C17.9303 7.16449 18.4472 7.0465 18.736 6.66894L19.4706 5.7087ZM20.4213 23.5446C24.5582 23.5446 27.9119 20.191 27.9119 16.054C27.9119 11.9171 24.5582 8.56343 20.4213 8.56343C16.2843 8.56343 12.9307 11.9171 12.9307 16.054C12.9307 20.191 16.2843 23.5446 20.4213 23.5446ZM20.4213 23.8327C24.7173 23.8327 28.2 20.3501 28.2 16.054C28.2 11.758 24.7173 8.27533 20.4213 8.27533C16.1252 8.27533 12.6426 11.758 12.6426 16.054C12.6426 20.3501 16.1252 23.8327 20.4213 23.8327ZM19.1735 14.369C19.974 14.6726 20.9143 14.1508 21.2738 13.2033C21.6333 12.2558 21.2758 11.2416 20.4753 10.9379C19.6748 10.6342 18.7346 11.1562 18.3751 12.1035C18.0157 13.051 18.3731 14.0653 19.1735 14.369ZM20.5549 15.4497C20.5549 15.4497 18.6068 14.3922 16.4707 16.3317C14.5462 18.0788 15.5437 19.1992 16.3499 19.6062C16.8602 19.8638 17.4242 19.8503 17.8264 19.8406C17.8617 19.8397 17.8958 19.8389 17.9285 19.8383C18.1877 19.8335 18.3016 19.9128 18.3016 19.9128C18.3016 19.9128 18.433 19.9574 18.583 20.1688C18.602 20.1956 18.6216 20.2236 18.642 20.2526C18.8731 20.582 19.1969 21.0438 19.707 21.3013C20.5132 21.7083 22.0069 21.8458 22.2702 19.2599C22.5625 16.3895 20.5549 15.4498 20.5549 15.4498V15.4497ZM24.5417 15.2169C23.9927 16.0688 22.9644 16.3833 22.2447 15.9196C21.5251 15.4558 21.3868 14.3893 21.9358 13.5375C22.4847 12.6857 23.5131 12.3711 24.2327 12.8349C24.9523 13.2987 25.0907 14.3651 24.5417 15.2169ZM25.4492 16.8482C24.9379 16.2973 23.9949 16.3413 23.3428 16.9464C22.6906 17.5515 22.5765 18.4887 23.0878 19.0396C23.5991 19.5905 24.5421 19.5465 25.1942 18.9413C25.8462 18.3362 25.9605 17.399 25.4492 16.8481V16.8482ZM16.5237 12.3418C17.2704 12.4261 17.795 13.2111 17.6952 14.0951C17.5955 14.979 16.9091 15.6273 16.1624 15.543C15.4156 15.4587 14.8911 14.6738 14.9908 13.7897C15.0905 12.9058 15.7769 12.2575 16.5237 12.3418Z"
                              fill="url(#paint7_linear_1240_1731)"/>
                        <path d="M21.0381 5.7087L21.2125 5.57533L21.2125 5.57533L21.0381 5.7087ZM19.4706 5.7087L19.6449 5.84206L19.6449 5.84206L19.4706 5.7087ZM21.7727 6.66894L21.947 6.53558V6.53558L21.7727 6.66894ZM23.0024 6.94963L22.9032 6.75383L23.0024 6.94963ZM24.0809 6.40319L23.9817 6.20739L24.0809 6.40319ZM25.4932 7.08331L25.2782 7.12782V7.12782L25.4932 7.08331ZM25.7383 8.26717L25.9533 8.22266L25.7383 8.26717ZM26.7246 9.05365L26.729 9.2731L26.7246 9.05365ZM27.9333 9.02923L27.9377 9.24869L27.9333 9.02923ZM28.9106 10.2548L28.6977 10.2016V10.2016L28.9106 10.2548ZM28.6179 11.4278L28.8308 11.4809V11.4809L28.6179 11.4278ZM29.1652 12.5643L29.0739 12.7639L29.1652 12.5643ZM30.2648 13.0667L30.356 12.8671L30.2648 13.0667ZM30.6136 14.5949L30.7824 14.7352L30.6136 14.5949ZM29.8409 15.5247L29.6721 15.3845L29.8409 15.5247ZM29.8409 16.7862L29.6721 16.9264L29.8409 16.7862ZM30.6136 17.716L30.7824 17.5757L30.6136 17.716ZM30.2648 19.2442L30.1736 19.0445L30.2648 19.2442ZM29.1652 19.7466L29.0739 19.547V19.547L29.1652 19.7466ZM28.6179 20.8831L28.8308 20.83V20.83L28.6179 20.8831ZM28.9106 22.0561L28.6977 22.1093L28.9106 22.0561ZM27.9333 23.2817L27.9377 23.0622L27.9333 23.2817ZM26.7246 23.2572L26.7201 23.4767L26.7246 23.2572ZM25.7383 24.0437L25.9533 24.0882V24.0882L25.7383 24.0437ZM25.4932 25.2276L25.2782 25.1831L25.4932 25.2276ZM24.0809 25.9077L23.9817 26.1035L24.0809 25.9077ZM23.0024 25.3613L23.1017 25.1655H23.1017L23.0024 25.3613ZM21.7727 25.642L21.947 25.7753H21.947L21.7727 25.642ZM21.0381 26.6022L20.8638 26.4688L21.0381 26.6022ZM19.4706 26.6022L19.6449 26.4688H19.6449L19.4706 26.6022ZM18.736 25.642L18.5617 25.7753L18.736 25.642ZM17.5063 25.3613L17.6055 25.5571H17.6055L17.5063 25.3613ZM16.4278 25.9077L16.3286 25.7119H16.3286L16.4278 25.9077ZM15.0155 25.2276L14.8006 25.2721H14.8006L15.0155 25.2276ZM14.7704 24.0437L14.5554 24.0882V24.0882L14.7704 24.0437ZM13.7842 23.2572L13.7886 23.4767H13.7886L13.7842 23.2572ZM12.5754 23.2817L12.571 23.0622H12.571L12.5754 23.2817ZM11.5981 22.0561L11.8111 22.1093H11.8111L11.5981 22.0561ZM11.8909 20.8831L12.1038 20.9363L11.8909 20.8831ZM11.3436 19.7466L11.4348 19.547L11.4348 19.547L11.3436 19.7466ZM10.2439 19.2442L10.1527 19.4438H10.1527L10.2439 19.2442ZM9.89512 17.716L9.7263 17.5757H9.7263L9.89512 17.716ZM10.6678 16.7862L10.499 16.6459H10.499L10.6678 16.7862ZM10.6678 15.5247L10.499 15.665H10.499L10.6678 15.5247ZM9.89512 14.5949L10.0639 14.4546H10.0639L9.89512 14.5949ZM10.2439 13.0667L10.3352 13.2664L10.2439 13.0667ZM11.3436 12.5643L11.2523 12.3646H11.2523L11.3436 12.5643ZM11.8909 11.4278L12.1038 11.3746L11.8909 11.4278ZM11.5981 10.2548L11.8111 10.2016L11.5981 10.2548ZM12.5754 9.02923L12.571 9.24869L12.5754 9.02923ZM13.7842 9.05365L13.7797 9.2731L13.7842 9.05365ZM14.7704 8.26717L14.5554 8.22266L14.7704 8.26717ZM15.0155 7.08331L14.8006 7.0388L15.0155 7.08331ZM16.4278 6.40319L16.3286 6.59899L16.4278 6.40319ZM17.5063 6.94963L17.4071 7.14543L17.5063 6.94963ZM18.736 6.66894L18.5617 6.53558V6.53558L18.736 6.66894ZM21.2738 13.2033L21.479 13.2812L21.479 13.2812L21.2738 13.2033ZM19.1735 14.369L19.0957 14.5742L19.0957 14.5742L19.1735 14.369ZM20.4753 10.9379L20.5531 10.7327L20.4753 10.9379ZM18.3751 12.1035L18.1699 12.0257L18.1699 12.0257L18.3751 12.1035ZM16.4707 16.3317L16.6182 16.4942L16.6182 16.4942L16.4707 16.3317ZM20.5549 15.4497H20.7744V15.3191L20.6596 15.2568L20.5549 15.4497ZM16.3499 19.6062L16.251 19.8021L16.251 19.8021L16.3499 19.6062ZM17.8264 19.8406L17.8317 20.06H17.8317L17.8264 19.8406ZM17.9285 19.8383L17.9244 19.6189H17.9244L17.9285 19.8383ZM18.3016 19.9128L18.1763 20.093L18.2017 20.1106L18.2309 20.1206L18.3016 19.9128ZM18.583 20.1688L18.404 20.2959L18.404 20.2959L18.583 20.1688ZM18.642 20.2526L18.4623 20.3787V20.3787L18.642 20.2526ZM19.707 21.3013L19.806 21.1054L19.806 21.1054L19.707 21.3013ZM22.2702 19.2599L22.4886 19.2821L22.4886 19.2821L22.2702 19.2599ZM20.5549 15.4498H20.3354V15.5894L20.4619 15.6486L20.5549 15.4498ZM22.2447 15.9196L22.1258 16.1041L22.2447 15.9196ZM24.5417 15.2169L24.3572 15.098L24.5417 15.2169ZM21.9358 13.5375L22.1203 13.6564L21.9358 13.5375ZM24.2327 12.8349L24.3517 12.6504L24.3516 12.6504L24.2327 12.8349ZM23.3428 16.9464L23.1935 16.7855L23.1935 16.7855L23.3428 16.9464ZM25.4492 16.8482L25.2883 16.9975L25.6687 17.4074V16.8482H25.4492ZM23.0878 19.0396L23.2487 18.8903L23.2487 18.8903L23.0878 19.0396ZM25.1942 18.9413L25.3435 19.1022L25.3435 19.1022L25.1942 18.9413ZM25.4492 16.8481L25.6101 16.6988L25.2297 16.2889V16.8481H25.4492ZM17.6952 14.0951L17.4771 14.0704L17.4771 14.0705L17.6952 14.0951ZM16.5237 12.3418L16.499 12.5599H16.499L16.5237 12.3418ZM16.1624 15.543L16.187 15.3249L16.1624 15.543ZM14.9908 13.7897L14.7727 13.7651L14.7727 13.7651L14.9908 13.7897ZM21.2125 5.57533C20.7296 4.94416 19.7791 4.94416 19.2962 5.57533L19.6449 5.84206C19.952 5.44059 20.5567 5.44059 20.8638 5.84206L21.2125 5.57533ZM21.947 6.53558L21.2125 5.57533L20.8638 5.84206L21.5983 6.80231L21.947 6.53558ZM22.9032 6.75383C22.5735 6.9209 22.1716 6.82915 21.947 6.53558L21.5983 6.80231C21.9514 7.26385 22.5833 7.40808 23.1017 7.14543L22.9032 6.75383ZM23.9817 6.20739L22.9032 6.75383L23.1017 7.14543L24.1801 6.59899L23.9817 6.20739ZM25.7081 7.0388C25.547 6.26065 24.6905 5.84821 23.9817 6.20739L24.1801 6.59899C24.631 6.37052 25.1757 6.63287 25.2782 7.12782L25.7081 7.0388ZM25.9533 8.22266L25.7081 7.0388L25.2782 7.12782L25.5234 8.31169L25.9533 8.22266ZM26.7201 8.83419C26.3506 8.84166 26.0282 8.5846 25.9533 8.22266L25.5234 8.31169C25.6412 8.8807 26.148 9.28484 26.729 9.2731L26.7201 8.83419ZM27.9289 8.80978L26.7201 8.83419L26.729 9.2731L27.9377 9.24869L27.9289 8.80978ZM29.1236 10.3079C29.316 9.53692 28.7233 8.79373 27.9289 8.80978L27.9377 9.24869C28.4431 9.23848 28.8201 9.71121 28.6977 10.2016L29.1236 10.3079ZM28.8308 11.4809L29.1236 10.3079L28.6977 10.2016L28.4049 11.3746L28.8308 11.4809ZM29.2564 12.3646C28.9202 12.211 28.7413 11.8395 28.8308 11.4809L28.4049 11.3746C28.2642 11.9384 28.5454 12.5224 29.0739 12.7639L29.2564 12.3646ZM30.356 12.8671L29.2564 12.3646L29.0739 12.7639L30.1736 13.2664L30.356 12.8671ZM30.7824 14.7352C31.2903 14.1241 31.0788 13.1973 30.356 12.8671L30.1736 13.2664C30.6333 13.4764 30.7678 14.0659 30.4448 14.4546L30.7824 14.7352ZM30.0097 15.665L30.7824 14.7352L30.4448 14.4546L29.6721 15.3845L30.0097 15.665ZM30.0097 16.6459C29.7734 16.3616 29.7734 15.9493 30.0097 15.665L29.6721 15.3845C29.3006 15.8314 29.3006 16.4795 29.6721 16.9264L30.0097 16.6459ZM30.7824 17.5757L30.0097 16.6459L29.6721 16.9264L30.4448 17.8562L30.7824 17.5757ZM30.356 19.4438C31.0788 19.1136 31.2903 18.1868 30.7824 17.5757L30.4448 17.8562C30.7678 18.245 30.6333 18.8345 30.1736 19.0445L30.356 19.4438ZM29.2564 19.9463L30.356 19.4438L30.1736 19.0445L29.0739 19.547L29.2564 19.9463ZM28.8308 20.83C28.7413 20.4714 28.9202 20.0999 29.2564 19.9463L29.0739 19.547C28.5454 19.7885 28.2642 20.3725 28.4049 20.9363L28.8308 20.83ZM29.1236 22.003L28.8308 20.83L28.4049 20.9363L28.6977 22.1093L29.1236 22.003ZM27.9289 23.5011C28.7233 23.5172 29.316 22.774 29.1236 22.003L28.6977 22.1093C28.8201 22.5997 28.4431 23.0724 27.9377 23.0622L27.9289 23.5011ZM26.7201 23.4767L27.9289 23.5011L27.9377 23.0622L26.729 23.0378L26.7201 23.4767ZM25.9533 24.0882C26.0282 23.7263 26.3506 23.4692 26.7201 23.4767L26.729 23.0378C26.148 23.0261 25.6412 23.4302 25.5234 23.9992L25.9533 24.0882ZM25.7081 25.2721L25.9533 24.0882L25.5234 23.9992L25.2782 25.1831L25.7081 25.2721ZM23.9817 26.1035C24.6905 26.4627 25.547 26.0502 25.7081 25.2721L25.2782 25.1831C25.1757 25.678 24.631 25.9404 24.1801 25.7119L23.9817 26.1035ZM22.9032 25.5571L23.9817 26.1035L24.1801 25.7119L23.1017 25.1655L22.9032 25.5571ZM21.947 25.7753C22.1716 25.4817 22.5735 25.39 22.9032 25.5571L23.1017 25.1655C22.5833 24.9028 21.9514 25.047 21.5983 25.5086L21.947 25.7753ZM21.2125 26.7356L21.947 25.7753L21.5983 25.5086L20.8638 26.4688L21.2125 26.7356ZM19.2962 26.7356C19.7791 27.3667 20.7296 27.3667 21.2125 26.7356L20.8638 26.4688C20.5567 26.8703 19.952 26.8703 19.6449 26.4688L19.2962 26.7356ZM18.5617 25.7753L19.2962 26.7356L19.6449 26.4688L18.9104 25.5086L18.5617 25.7753ZM17.6055 25.5571C17.9352 25.39 18.3371 25.4817 18.5617 25.7753L18.9104 25.5086C18.5573 25.047 17.9254 24.9028 17.407 25.1655L17.6055 25.5571ZM16.527 26.1035L17.6055 25.5571L17.4071 25.1655L16.3286 25.7119L16.527 26.1035ZM14.8006 25.2721C14.9617 26.0502 15.8182 26.4627 16.527 26.1035L16.3286 25.7119C15.8777 25.9404 15.333 25.678 15.2305 25.1831L14.8006 25.2721ZM14.5554 24.0882L14.8006 25.2721L15.2305 25.1831L14.9853 23.9992L14.5554 24.0882ZM13.7886 23.4767C14.1581 23.4692 14.4805 23.7263 14.5554 24.0882L14.9853 23.9992C14.8675 23.4302 14.3607 23.0261 13.7797 23.0378L13.7886 23.4767ZM12.5799 23.5011L13.7886 23.4767L13.7797 23.0378L12.571 23.0622L12.5799 23.5011ZM11.3851 22.003C11.1927 22.774 11.7854 23.5172 12.5799 23.5011L12.571 23.0622C12.0656 23.0724 11.6886 22.5997 11.8111 22.1093L11.3851 22.003ZM11.6779 20.83L11.3851 22.003L11.8111 22.1093L12.1038 20.9363L11.6779 20.83ZM11.2523 19.9463C11.5885 20.0999 11.7674 20.4714 11.6779 20.83L12.1038 20.9363C12.2445 20.3725 11.9633 19.7885 11.4348 19.547L11.2523 19.9463ZM10.1527 19.4438L11.2523 19.9463L11.4348 19.547L10.3352 19.0445L10.1527 19.4438ZM9.7263 17.5757C9.2184 18.1868 9.42992 19.1136 10.1527 19.4438L10.3352 19.0445C9.87541 18.8345 9.74087 18.245 10.0639 17.8562L9.7263 17.5757ZM10.499 16.6459L9.7263 17.5757L10.0639 17.8562L10.8367 16.9264L10.499 16.6459ZM10.499 15.665C10.7353 15.9493 10.7353 16.3616 10.499 16.6459L10.8367 16.9264C11.2081 16.4795 11.2081 15.8314 10.8367 15.3845L10.499 15.665ZM9.7263 14.7352L10.499 15.665L10.8367 15.3845L10.0639 14.4546L9.7263 14.7352ZM10.1527 12.8671C9.42992 13.1973 9.2184 14.1241 9.7263 14.7352L10.0639 14.4546C9.74087 14.0659 9.87541 13.4764 10.3352 13.2664L10.1527 12.8671ZM11.2523 12.3646L10.1527 12.8671L10.3352 13.2664L11.4348 12.7639L11.2523 12.3646ZM11.6779 11.4809C11.7674 11.8395 11.5885 12.211 11.2523 12.3646L11.4348 12.7639C11.9633 12.5224 12.2445 11.9384 12.1038 11.3746L11.6779 11.4809ZM11.3851 10.3079L11.6779 11.4809L12.1038 11.3746L11.8111 10.2016L11.3851 10.3079ZM12.5799 8.80978C11.7854 8.79373 11.1927 9.53692 11.3851 10.3079L11.8111 10.2016C11.6886 9.71121 12.0656 9.23848 12.571 9.24869L12.5799 8.80978ZM13.7886 8.83419L12.5799 8.80978L12.571 9.24869L13.7797 9.2731L13.7886 8.83419ZM14.5554 8.22266C14.4805 8.5846 14.1581 8.84166 13.7886 8.83419L13.7797 9.2731C14.3607 9.28484 14.8675 8.8807 14.9853 8.31169L14.5554 8.22266ZM14.8006 7.0388L14.5554 8.22266L14.9853 8.31169L15.2305 7.12782L14.8006 7.0388ZM16.527 6.20739C15.8182 5.84821 14.9617 6.26065 14.8006 7.0388L15.2305 7.12782C15.333 6.63287 15.8777 6.37052 16.3286 6.59899L16.527 6.20739ZM17.6055 6.75383L16.527 6.20739L16.3286 6.59899L17.4071 7.14543L17.6055 6.75383ZM18.5617 6.53558C18.3371 6.82915 17.9352 6.9209 17.6055 6.75383L17.4071 7.14543C17.9254 7.40808 18.5573 7.26385 18.9104 6.80231L18.5617 6.53558ZM19.2962 5.57533L18.5617 6.53558L18.9104 6.80231L19.6449 5.84206L19.2962 5.57533ZM27.6924 16.054C27.6924 20.0698 24.437 23.3251 20.4213 23.3251V23.7641C24.6794 23.7641 28.1314 20.3122 28.1314 16.054H27.6924ZM20.4213 8.78293C24.437 8.78293 27.6924 12.0383 27.6924 16.054H28.1314C28.1314 11.7959 24.6794 8.34392 20.4213 8.34392V8.78293ZM13.1502 16.054C13.1502 12.0383 16.4055 8.78293 20.4213 8.78293V8.34392C16.1631 8.34392 12.7112 11.7959 12.7112 16.054H13.1502ZM20.4213 23.3251C16.4055 23.3251 13.1502 20.0698 13.1502 16.054H12.7112C12.7112 20.3122 16.1631 23.7641 20.4213 23.7641V23.3251ZM27.9805 16.054C27.9805 20.2289 24.5961 23.6132 20.4213 23.6132V24.0522C24.8386 24.0522 28.4195 20.4713 28.4195 16.054H27.9805ZM20.4213 8.49483C24.5961 8.49483 27.9805 11.8792 27.9805 16.054H28.4195C28.4195 11.6367 24.8386 8.05582 20.4213 8.05582V8.49483ZM12.8621 16.054C12.8621 11.8792 16.2464 8.49483 20.4213 8.49483V8.05582C16.004 8.05582 12.4231 11.6367 12.4231 16.054H12.8621ZM20.4213 23.6132C16.2464 23.6132 12.8621 20.2289 12.8621 16.054H12.4231C12.4231 20.4713 16.004 24.0522 20.4213 24.0522V23.6132ZM21.0686 13.1255C20.7392 13.9934 19.9075 14.4126 19.2514 14.1637L19.0957 14.5742C20.0406 14.9327 21.0893 14.3083 21.479 13.2812L21.0686 13.1255ZM20.3974 11.1431C21.0535 11.392 21.3979 12.2575 21.0686 13.1255L21.479 13.2812C21.8687 12.2542 21.4981 11.0911 20.5531 10.7327L20.3974 11.1431ZM18.5803 12.1814C18.9096 11.3136 19.7414 10.8943 20.3974 11.1431L20.5531 10.7327C19.6082 10.3742 18.5595 10.9987 18.1699 12.0257L18.5803 12.1814ZM19.2514 14.1637C18.5954 13.9149 18.2511 13.0494 18.5803 12.1814L18.1699 12.0257C17.7803 13.0527 18.1509 14.2157 19.0957 14.5742L19.2514 14.1637ZM16.6182 16.4942C17.6422 15.5645 18.6073 15.3624 19.3075 15.3828C19.6604 15.3931 19.9502 15.4601 20.1505 15.5239C20.2506 15.5558 20.3279 15.5868 20.3791 15.6091C20.4046 15.6203 20.4237 15.6294 20.4357 15.6353C20.4417 15.6382 20.4459 15.6404 20.4484 15.6417C20.4496 15.6423 20.4503 15.6427 20.4507 15.6429C20.4508 15.6429 20.4508 15.643 20.4508 15.6429C20.4507 15.6429 20.4507 15.6429 20.4506 15.6428C20.4505 15.6428 20.4504 15.6427 20.4504 15.6427C20.4503 15.6427 20.4502 15.6426 20.5549 15.4497C20.6596 15.2568 20.6595 15.2567 20.6594 15.2567C20.6593 15.2566 20.6592 15.2565 20.6591 15.2565C20.6589 15.2564 20.6586 15.2562 20.6583 15.2561C20.6578 15.2558 20.6571 15.2554 20.6563 15.255C20.6548 15.2542 20.6527 15.2531 20.6502 15.2518C20.6451 15.2492 20.6382 15.2456 20.6294 15.2413C20.6118 15.2327 20.5869 15.2209 20.555 15.2069C20.4913 15.1791 20.3998 15.1426 20.2838 15.1056C20.0521 15.0318 19.7214 14.9556 19.3202 14.944C18.5123 14.9205 17.4353 15.1594 16.3231 16.1692L16.6182 16.4942ZM16.4488 19.4102C16.0738 19.2209 15.6922 18.883 15.6016 18.4285C15.5138 17.9884 15.6821 17.3441 16.6182 16.4942L16.3231 16.1692C15.3348 17.0664 15.0397 17.8559 15.1711 18.5144C15.2996 19.1587 15.8198 19.5844 16.251 19.8021L16.4488 19.4102ZM17.8211 19.6212C17.4129 19.631 16.9032 19.6396 16.4488 19.4102L16.251 19.8021C16.8172 20.088 17.4354 20.0695 17.8317 20.06L17.8211 19.6212ZM17.9244 19.6189C17.891 19.6195 17.8563 19.6203 17.8211 19.6212L17.8317 20.06C17.8671 20.0592 17.9006 20.0584 17.9325 20.0578L17.9244 19.6189ZM18.3016 19.9128C18.4269 19.7325 18.4268 19.7325 18.4267 19.7324C18.4267 19.7324 18.4266 19.7323 18.4265 19.7323C18.4264 19.7322 18.4262 19.7321 18.4261 19.732C18.4258 19.7318 18.4256 19.7316 18.4253 19.7314C18.4247 19.731 18.4241 19.7306 18.4234 19.7302C18.4222 19.7293 18.4208 19.7284 18.4192 19.7274C18.4162 19.7254 18.4126 19.7232 18.4085 19.7208C18.4003 19.7159 18.3901 19.7102 18.3778 19.7041C18.3531 19.6917 18.32 19.6776 18.2783 19.6644C18.1944 19.638 18.0774 19.616 17.9244 19.6189L17.9325 20.0578C18.0387 20.0558 18.1083 20.0711 18.1462 20.0831C18.1653 20.0891 18.177 20.0945 18.1818 20.0969C18.1841 20.0981 18.1848 20.0985 18.1839 20.098C18.1834 20.0977 18.1826 20.0972 18.1813 20.0963C18.1807 20.0959 18.1799 20.0954 18.1791 20.0949C18.1787 20.0946 18.1783 20.0943 18.1778 20.094C18.1776 20.0938 18.1773 20.0937 18.1771 20.0935C18.177 20.0934 18.1768 20.0933 18.1767 20.0933C18.1766 20.0932 18.1765 20.0931 18.1765 20.0931C18.1764 20.0931 18.1763 20.093 18.3016 19.9128ZM18.762 20.0418C18.6735 19.917 18.5864 19.8359 18.5153 19.7841C18.4799 19.7583 18.449 19.7402 18.4244 19.7276C18.4121 19.7213 18.4015 19.7165 18.3927 19.7128C18.3883 19.7109 18.3844 19.7094 18.381 19.7081C18.3793 19.7074 18.3777 19.7069 18.3763 19.7063C18.3755 19.7061 18.3748 19.7058 18.3742 19.7056C18.3738 19.7055 18.3735 19.7054 18.3732 19.7053C18.3731 19.7052 18.3729 19.7052 18.3728 19.7051C18.3727 19.7051 18.3726 19.705 18.3725 19.705C18.3724 19.705 18.3723 19.705 18.3016 19.9128C18.2309 20.1206 18.2308 20.1205 18.2307 20.1205C18.2307 20.1205 18.2306 20.1204 18.2305 20.1204C18.2303 20.1204 18.2302 20.1203 18.2301 20.1203C18.2298 20.1202 18.2295 20.1201 18.2292 20.12C18.2287 20.1198 18.2282 20.1196 18.2277 20.1195C18.2268 20.1191 18.226 20.1188 18.2253 20.1185C18.2239 20.118 18.2229 20.1176 18.2224 20.1174C18.2214 20.117 18.2222 20.1173 18.2246 20.1185C18.2293 20.1209 18.2406 20.1271 18.2568 20.1389C18.2889 20.1623 18.3425 20.2092 18.404 20.2959L18.762 20.0418ZM18.8217 20.1266C18.8014 20.0976 18.7814 20.0691 18.762 20.0418L18.404 20.2959C18.4226 20.3221 18.4419 20.3495 18.4623 20.3787L18.8217 20.1266ZM19.806 21.1054C19.3518 20.8761 19.0562 20.4609 18.8217 20.1266L18.4623 20.3787C18.6899 20.7032 19.0421 21.2115 19.6081 21.4972L19.806 21.1054ZM22.0519 19.2377C21.9238 20.4955 21.5053 21.0136 21.099 21.2042C20.6795 21.4011 20.181 21.2947 19.806 21.1054L19.6081 21.4972C20.0393 21.7149 20.6908 21.8808 21.2855 21.6017C21.8934 21.3164 22.3534 20.6102 22.4886 19.2821L22.0519 19.2377ZM20.5549 15.4498C20.4619 15.6486 20.4617 15.6486 20.4616 15.6485C20.4616 15.6485 20.4615 15.6484 20.4615 15.6484C20.4614 15.6484 20.4613 15.6483 20.4613 15.6483C20.4612 15.6483 20.4612 15.6483 20.4614 15.6484C20.4617 15.6485 20.4625 15.6489 20.4637 15.6495C20.4662 15.6507 20.4704 15.6529 20.4764 15.6559C20.4883 15.6621 20.5068 15.672 20.531 15.686C20.5794 15.7139 20.6503 15.7577 20.7353 15.8193C20.9056 15.9426 21.1316 16.136 21.3494 16.4139C21.7815 16.9652 22.192 17.8618 22.0519 19.2377L22.4886 19.2821C22.6408 17.7877 22.1935 16.7792 21.6949 16.1431C21.4474 15.8272 21.1897 15.6063 20.9928 15.4637C20.8942 15.3923 20.8104 15.3403 20.7502 15.3056C20.7201 15.2882 20.6958 15.2752 20.6784 15.2662C20.6697 15.2617 20.6627 15.2582 20.6576 15.2557C20.6551 15.2544 20.653 15.2534 20.6514 15.2526C20.6505 15.2522 20.6499 15.2519 20.6493 15.2516C20.649 15.2515 20.6488 15.2514 20.6485 15.2513C20.6484 15.2512 20.6483 15.2512 20.6482 15.2511C20.6481 15.2511 20.648 15.251 20.5549 15.4498ZM20.3354 15.4497V15.4498H20.7744V15.4497H20.3354ZM22.1258 16.1041C22.9753 16.6515 24.1311 16.2592 24.7262 15.3359L24.3572 15.098C23.8543 15.8783 22.9534 16.1151 22.3636 15.7351L22.1258 16.1041ZM21.7513 13.4186C21.1562 14.3419 21.2764 15.5566 22.1258 16.1041L22.3636 15.7351C21.7739 15.355 21.6174 14.4367 22.1203 13.6564L21.7513 13.4186ZM24.3516 12.6504C23.5022 12.103 22.3463 12.4953 21.7513 13.4186L22.1203 13.6564C22.6232 12.8761 23.524 12.6393 24.1138 13.0194L24.3516 12.6504ZM24.7262 15.3359C25.3212 14.4125 25.2011 13.1979 24.3517 12.6504L24.1138 13.0194C24.7036 13.3995 24.8601 14.3177 24.3572 15.098L24.7262 15.3359ZM23.4921 17.1073C24.0819 16.5601 24.8821 16.5598 25.2883 16.9975L25.6101 16.6989C24.9938 16.0347 23.9079 16.1226 23.1935 16.7855L23.4921 17.1073ZM23.2487 18.8903C22.8425 18.4526 22.9023 17.6545 23.4921 17.1073L23.1935 16.7855C22.479 17.4485 22.3105 18.5248 22.9269 19.1889L23.2487 18.8903ZM25.0449 18.7804C24.4551 19.3277 23.6549 19.328 23.2487 18.8903L22.9269 19.1889C23.5432 19.8531 24.6291 19.7652 25.3435 19.1022L25.0449 18.7804ZM25.2883 16.9974C25.6945 17.4351 25.6346 18.2331 25.0449 18.7804L25.3435 19.1022C26.0579 18.4392 26.2265 17.3629 25.6101 16.6988L25.2883 16.9974ZM25.6687 16.8482V16.8481H25.2297V16.8482H25.6687ZM17.9133 14.1197C18.0227 13.1512 17.4486 12.2253 16.5483 12.1237L16.499 12.5599C17.0923 12.6269 17.5674 13.271 17.4771 14.0704L17.9133 14.1197ZM16.1377 15.7611C17.038 15.8627 17.804 15.0882 17.9133 14.1197L17.4771 14.0705C17.3869 14.8698 16.7803 15.3919 16.187 15.3249L16.1377 15.7611ZM14.7727 13.7651C14.6634 14.7337 15.2375 15.6595 16.1377 15.7611L16.187 15.3249C15.5937 15.2579 15.1187 14.6139 15.2089 13.8143L14.7727 13.7651ZM16.5483 12.1237C15.648 12.0221 14.882 12.7966 14.7727 13.7651L15.2089 13.8143C15.2991 13.015 15.9057 12.4929 16.499 12.5599L16.5483 12.1237Z"
                              fill="white" mask="url(#path-19-inside-2_1240_1731)"/>
                    </g>
                    <path d="M42.9528 24.6096V11.2601H49.3719C50.8868 11.2601 52.0545 11.6515 52.875 12.4341C53.7082 13.2042 54.1247 14.2709 54.1247 15.6342C54.1247 16.9976 53.7082 18.0706 52.875 18.8532C52.0545 19.6233 50.8868 20.0083 49.3719 20.0083H46.437V24.6096H42.9528ZM46.437 17.3384H48.766C50.1294 17.3384 50.811 16.7703 50.811 15.6342C50.811 14.4981 50.1294 13.93 48.766 13.93H46.437V17.3384ZM57.5955 24.8179C56.876 24.8179 56.2385 24.6853 55.6831 24.4202C55.1402 24.1552 54.711 23.7954 54.3955 23.3409C54.0925 22.8739 53.941 22.3437 53.941 21.7504C53.941 21.0813 54.1177 20.5511 54.4712 20.1598C54.8247 19.7558 55.3927 19.4718 56.1754 19.3077C56.958 19.131 57.9995 19.0426 59.2997 19.0426H60.0193V18.7775C60.0193 18.323 59.8994 18.0011 59.6595 17.8118C59.4323 17.6224 59.0346 17.5278 58.4666 17.5278C57.9869 17.5278 57.4567 17.6035 56.876 17.755C56.2953 17.9065 55.7272 18.14 55.1718 18.4556L54.3197 16.2401C54.6353 16.0129 55.0393 15.8109 55.5316 15.6342C56.0239 15.4575 56.5415 15.3249 57.0843 15.2366C57.6271 15.1356 58.1384 15.0851 58.6181 15.0851C60.1708 15.0851 61.3195 15.4196 62.0643 16.0887C62.8217 16.7577 63.2004 17.8118 63.2004 19.2509V24.6096H60.0571V23.2841C59.893 23.7638 59.5964 24.1425 59.1672 24.4202C58.738 24.6853 58.2141 24.8179 57.5955 24.8179ZM58.353 22.6214C58.82 22.6214 59.2114 22.4699 59.527 22.1669C59.8552 21.864 60.0193 21.4663 60.0193 20.974V20.6332H59.2997C58.5549 20.6332 58.0058 20.7152 57.6523 20.8793C57.3115 21.0308 57.1411 21.277 57.1411 21.6178C57.1411 21.9081 57.2421 22.148 57.4441 22.3374C57.6587 22.5267 57.9616 22.6214 58.353 22.6214ZM66.9397 24.6096L63.0201 15.2934H66.4285L68.4924 20.69L70.5375 15.2934H72.6393L74.7033 20.7846L76.7672 15.2934H79.9673L76.0855 24.6096H73.302L71.4653 20.0272L69.7232 24.6096H66.9397Z"
                          fill="#16191E"/>
                    <path d="M88.9631 24.8179C87.4862 24.8179 86.2175 24.5339 85.1571 23.9658C84.0967 23.3977 83.2825 22.5961 82.7144 21.561C82.159 20.5259 81.8813 19.314 81.8813 17.9254C81.8813 16.5368 82.159 15.3312 82.7144 14.3087C83.2825 13.2736 84.0967 12.472 85.1571 11.9039C86.2175 11.3359 87.4862 11.0518 88.9631 11.0518C89.8468 11.0518 90.6989 11.1907 91.5194 11.4684C92.34 11.7335 93.0027 12.087 93.5076 12.5288L92.4662 15.2744C91.9107 14.8705 91.3553 14.5738 90.7999 14.3845C90.2444 14.1825 89.6764 14.0815 89.0957 14.0815C87.9217 14.0815 87.038 14.416 86.4447 15.0851C85.8514 15.7415 85.5548 16.6883 85.5548 17.9254C85.5548 19.1751 85.8514 20.1345 86.4447 20.8036C87.038 21.46 87.9217 21.7882 89.0957 21.7882C89.6764 21.7882 90.2444 21.6935 90.7999 21.5042C91.3553 21.3022 91.9107 20.9992 92.4662 20.5953L93.5076 23.3409C93.0027 23.7828 92.34 24.1425 91.5194 24.4202C90.6989 24.6853 89.8468 24.8179 88.9631 24.8179ZM94.2371 24.6096V11.2601H97.6076V16.4484C98.2767 15.5395 99.2487 15.0851 100.524 15.0851C101.635 15.0851 102.455 15.4133 102.985 16.0697C103.528 16.7262 103.799 17.736 103.799 19.0994V24.6096H100.429V19.2319C100.429 18.6639 100.328 18.2599 100.126 18.0201C99.9367 17.7802 99.6526 17.6603 99.2739 17.6603C98.769 17.6603 98.365 17.8244 98.062 18.1526C97.7591 18.4682 97.6076 18.8974 97.6076 19.4402V24.6096H94.2371ZM108.201 24.8179C107.481 24.8179 106.844 24.6853 106.288 24.4202C105.746 24.1552 105.316 23.7954 105.001 23.3409C104.698 22.8739 104.546 22.3437 104.546 21.7504C104.546 21.0813 104.723 20.5511 105.077 20.1598C105.43 19.7558 105.998 19.4718 106.781 19.3077C107.563 19.131 108.605 19.0426 109.905 19.0426H110.625V18.7775C110.625 18.323 110.505 18.0011 110.265 17.8118C110.038 17.6224 109.64 17.5278 109.072 17.5278C108.592 17.5278 108.062 17.6035 107.481 17.755C106.901 17.9065 106.333 18.14 105.777 18.4556L104.925 16.2401C105.241 16.0129 105.645 15.8109 106.137 15.6342C106.629 15.4575 107.147 15.3249 107.69 15.2366C108.232 15.1356 108.744 15.0851 109.223 15.0851C110.776 15.0851 111.925 15.4196 112.67 16.0887C113.427 16.7577 113.806 17.8118 113.806 19.2509V24.6096H110.663V23.2841C110.498 23.7638 110.202 24.1425 109.773 24.4202C109.343 24.6853 108.819 24.8179 108.201 24.8179ZM108.958 22.6214C109.425 22.6214 109.817 22.4699 110.132 22.1669C110.461 21.864 110.625 21.4663 110.625 20.974V20.6332H109.905C109.16 20.6332 108.611 20.7152 108.258 20.8793C107.917 21.0308 107.746 21.277 107.746 21.6178C107.746 21.9081 107.847 22.148 108.049 22.3374C108.264 22.5267 108.567 22.6214 108.958 22.6214ZM115.006 24.6096V15.2934H118.281V16.5431C118.572 16.1013 118.957 15.7478 119.436 15.4827C119.929 15.2176 120.491 15.0851 121.122 15.0851C121.753 15.0851 122.296 15.2239 122.75 15.5017C123.217 15.7794 123.571 16.2086 123.811 16.7893C124.126 16.2465 124.555 15.8299 125.098 15.5395C125.641 15.2366 126.241 15.0851 126.897 15.0851C127.983 15.0851 128.784 15.4133 129.302 16.0697C129.832 16.7135 130.097 17.7234 130.097 19.0994V24.6096H126.727V19.1941C126.727 18.6513 126.645 18.2599 126.48 18.0201C126.316 17.7802 126.045 17.6603 125.666 17.6603C125.199 17.6603 124.839 17.8244 124.587 18.1526C124.347 18.4808 124.227 18.9479 124.227 19.5538V24.6096H120.857V19.1941C120.857 18.6513 120.775 18.2599 120.61 18.0201C120.446 17.7802 120.175 17.6603 119.796 17.6603C119.342 17.6603 118.988 17.8244 118.736 18.1526C118.496 18.4808 118.376 18.9479 118.376 19.5538V24.6096H115.006ZM131.297 28.018V15.2934H134.572V16.562C134.825 16.1076 135.21 15.7478 135.727 15.4827C136.245 15.2176 136.813 15.0851 137.432 15.0851C138.227 15.0851 138.928 15.2807 139.533 15.6721C140.152 16.0634 140.632 16.6252 140.973 17.3573C141.326 18.0769 141.503 18.9416 141.503 19.9515C141.503 20.9488 141.332 21.8135 140.991 22.5456C140.651 23.2778 140.171 23.8396 139.552 24.2309C138.946 24.6222 138.24 24.8179 137.432 24.8179C136.851 24.8179 136.308 24.698 135.803 24.4581C135.311 24.2183 134.932 23.8964 134.667 23.4924V28.018H131.297ZM136.371 22.3374C136.876 22.3374 137.293 22.148 137.621 21.7693C137.949 21.3906 138.113 20.7846 138.113 19.9515C138.113 19.1057 137.949 18.4998 137.621 18.1337C137.293 17.755 136.876 17.5656 136.371 17.5656C135.854 17.5656 135.431 17.755 135.103 18.1337C134.774 18.4998 134.61 19.1057 134.61 19.9515C134.61 20.7846 134.774 21.3906 135.103 21.7693C135.431 22.148 135.854 22.3374 136.371 22.3374Z"
                          fill="#16191E"/>
                    <defs>
                        <filter id="filter0_d_1240_1731" x="0.667321" y="1.38338" width="39.2203" height="39.2202" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                            <feOffset dy="2.56341"/>
                            <feGaussianBlur stdDeviation="1.66622"/>
                            <feComposite in2="hardAlpha" operator="out"/>
                            <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 0.550639 0 0 0 0 0.545833 0 0 0 0.53 0"/>
                            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1240_1731"/>
                            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1240_1731" result="shape"/>
                        </filter>
                        <filter id="filter1_d_1240_1731" x="0.667565" y="0.230977" width="39.2203" height="39.2202" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                            <feOffset dy="2.56341"/>
                            <feGaussianBlur stdDeviation="1.66622"/>
                            <feComposite in2="hardAlpha" operator="out"/>
                            <feColorMatrix type="matrix" values="0 0 0 0 0.878288 0 0 0 0 0.154183 0 0 0 0 0.146438 0 0 0 0.53 0"/>
                            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1240_1731"/>
                            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1240_1731" result="shape"/>
                        </filter>
                        <filter id="filter2_d_1240_1731" x="7.54002" y="4.23722" width="25.9078" height="26.4009" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                            <feOffset dy="0.788824"/>
                            <feGaussianBlur stdDeviation="1.18324"/>
                            <feComposite in2="hardAlpha" operator="out"/>
                            <feColorMatrix type="matrix" values="0 0 0 0 0.575 0 0 0 0 0.110208 0 0 0 0 0.110208 0 0 0 0.67 0"/>
                            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1240_1731"/>
                            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1240_1731" result="shape"/>
                        </filter>
                        <filter id="filter3_d_1240_1731" x="7.30052" y="3.74381" width="25.9078" height="26.4009" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                            <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                            <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                            <feOffset dy="0.788824"/>
                            <feGaussianBlur stdDeviation="1.18324"/>
                            <feComposite in2="hardAlpha" operator="out"/>
                            <feColorMatrix type="matrix" values="0 0 0 0 0.575 0 0 0 0 0.110208 0 0 0 0 0.110208 0 0 0 0.67 0"/>
                            <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1240_1731"/>
                            <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_1240_1731" result="shape"/>
                        </filter>
                        <linearGradient id="paint0_linear_1240_1731" x1="15.709" y1="0.358321" x2="28.2358" y2="33.7638" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#FF765A"/>
                            <stop offset="1" stop-color="#FF4040"/>
                        </linearGradient>
                        <linearGradient id="paint1_linear_1240_1731" x1="15.709" y1="0.358321" x2="28.2358" y2="33.7638" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#F56C50"/>
                            <stop offset="1" stop-color="#F83535"/>
                        </linearGradient>
                        <linearGradient id="paint2_linear_1240_1731" x1="15.7092" y1="-0.794083" x2="28.236" y2="32.6114" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#FF765A"/>
                            <stop offset="1" stop-color="#FF4040"/>
                        </linearGradient>
                        <linearGradient id="paint3_linear_1240_1731" x1="15.7092" y1="-0.794083" x2="28.236" y2="32.6114" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#F56C50"/>
                            <stop offset="1" stop-color="#F83535"/>
                        </linearGradient>
                        <linearGradient id="paint4_linear_1240_1731" x1="15.709" y1="-1.87361" x2="28.2358" y2="31.5319" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#FF765A"/>
                            <stop offset="1" stop-color="#FF4040"/>
                        </linearGradient>
                        <linearGradient id="paint5_linear_1240_1731" x1="15.709" y1="-1.87361" x2="28.2358" y2="31.5319" gradientUnits="userSpaceOnUse">
                            <stop stop-color="#F56C50"/>
                            <stop offset="1" stop-color="#F83535"/>
                        </linearGradient>
                        <linearGradient id="paint6_linear_1240_1731" x1="22.0116" y1="5.22455" x2="22.0116" y2="26.9086" gradientUnits="userSpaceOnUse">
                            <stop stop-color="white"/>
                            <stop offset="1" stop-color="#FFE0E0"/>
                        </linearGradient>
                        <linearGradient id="paint7_linear_1240_1731" x1="21.6694" y1="4.72204" x2="21.6694" y2="26.39" gradientUnits="userSpaceOnUse">
                            <stop stop-color="white"/>
                            <stop offset="1" stop-color="#FFE0E0"/>
                        </linearGradient>
                    </defs>
                </svg>
            </div>
        </div>
    </section>
    <section class="landing__timer" id="timerView">
        <div class="landing__timer-text">
            Reserved price for: <span id="countdown1">{{ getFormattedTimeToCountdownFinish() }}</span>
        </div>
    </section>
    <section class="landing__discount" id="discountBlockView">
        <div class="container">
            <div class="landing__discount-wrapp">
                <div class="landing__discount-title-box">
                    <p class="landing__discount-prev">
                        Previous discount:
                        <span>60%</span>
                    </p>
                    <h2 class="landing__discount-title">
                        Get
                        <span class="nameInsertSelector">
                            {{ 'shared.pet_name'|resp_i11n_trans(domain='shared') }}'s
                        </span>
                        obedience plan with up to <span>64%</span> discount
                    </h2>
                </div>
                <div class="landing__discount-img-box">
                    <img src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1685119143/dog-training/img/better/gift.png" alt="" class="landing__discount-img">
                </div>
            </div>
        </div>
    </section>
    <section class="landing__goal" id="goalView">
        <div class="landing__goal-wrapp">
            <div class="landing__goal-state">
                <div class="landing__goal-state-box">
                    <p class="landing__goal-state-item white">
                        Now
                    </p>
                </div>
                <div class="landing__hr"></div>
                <div class="landing__goal-state-box">
                    <p class="landing__goal-state-item">
                        Your Goal
                    </p>
                </div>
            </div>
            {% if app.request.query.has('ml_img') %}
                <div class="landing__goal-img-inner man">
                    <div class="landing__goal-img-box">
                        <picture>
                            <source srcset="https://res.cloudinary.com/dr0cx27xo/image/upload/v1718205174/dog-training/img/landing/man-problem.webp" type="image/webp">
                            <img class="landing__goal-img" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1689582634/dog-training/img/man-problem.png" alt="man problem">
                        </picture>
                    </div>
                    <div class="landing__goal-img-arrow-box">
                        <svg width="77" height="185" viewBox="0 0 77 185" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path class="landing__goal-img-arrow" d="M2 2L45.6756 80.9691C49.3048 87.5309 49.3418 95.4894 45.7739 102.085L2 183" stroke="#1998CD" stroke-opacity="0.13" stroke-width="4" stroke-linecap="round"/>
                            <path class="landing__goal-img-arrow" d="M14.5 2L58.1756 80.9691C61.8048 87.5309 61.8418 95.4894 58.2739 102.085L14.5 183" stroke="#1998CD" stroke-opacity="0.13" stroke-width="4"
                                  stroke-linecap="round"/>
                            <path class="landing__goal-img-arrow" d="M27 2L70.6756 80.9691C74.3048 87.5309 74.3418 95.4894 70.7739 102.085L27 183" stroke="#1998CD" stroke-opacity="0.13" stroke-width="4" stroke-linecap="round"/>
                        </svg>
                    </div>
                    <div class="landing__goal-img-box">
                        <picture>
                            <source srcset="https://res.cloudinary.com/dr0cx27xo/image/upload/v1718205174/dog-training/img/landing/man-resolved.webp" type="image/webp">
                            <img class="landing__goal-img" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1689582633/dog-training/img/man-resolved.png" alt="man resolved">
                        </picture>
                    </div>
                </div>
            {% else %}
                <div class="landing__goal-img-inner">
                    <div class="landing__goal-img-box">
                        <picture>
                            <source srcset="https://res.cloudinary.com/dr0cx27xo/image/upload/v1718202862/dog-training/img/landing/girl-problem.webp" type="image/webp">
                            <img class="landing__goal-img" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1689601188/dog-training/img/girl-problem.png" alt="girl problem">
                        </picture>
                    </div>
                    <div class="landing__goal-img-arrow-box">
                        <svg width="77" height="185" viewBox="0 0 77 185" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path class="landing__goal-img-arrow" d="M2 2L45.6756 80.9691C49.3048 87.5309 49.3418 95.4894 45.7739 102.085L2 183" stroke="#1998CD" stroke-opacity="0.13" stroke-width="4" stroke-linecap="round"/>
                            <path class="landing__goal-img-arrow" d="M14.5 2L58.1756 80.9691C61.8048 87.5309 61.8418 95.4894 58.2739 102.085L14.5 183" stroke="#1998CD" stroke-opacity="0.13" stroke-width="4"
                                  stroke-linecap="round"/>
                            <path class="landing__goal-img-arrow" d="M27 2L70.6756 80.9691C74.3048 87.5309 74.3418 95.4894 70.7739 102.085L27 183" stroke="#1998CD" stroke-opacity="0.13" stroke-width="4" stroke-linecap="round"/>
                        </svg>
                    </div>
                    <div class="landing__goal-img-box">
                        <picture>
                            <source srcset="https://res.cloudinary.com/dr0cx27xo/image/upload/v1718202862/dog-training/img/landing/girl-resolved.webp" type="image/webp">
                            <img class="landing__goal-img" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1689582634/dog-training/img/girl-resolved.png" alt="girl resolved">
                        </picture>
                    </div>
                </div>
            {% endif %}
            <div class="container landing__goal-values">
                <div class="landing__goal-values-box">
                    <div class="landing__goal-values-item">
                        <h3 class="landing__goal-values-title">
                            <span class="nameInsertSelector">
                                {{ 'shared.pet_name'|resp_i11n_trans(domain='shared') }}'s
                            </span> obedience
                        </h3>
                        <p class="landing__goal-values-text">
                            Low
                        </p>
                    </div>
                    <div class="landing__goal-values-item">
                        <h3 class="landing__goal-values-title">
                            Training Level
                        </h3>
                        <p class="landing__goal-values-text">
                            Intermediate
                        </p>
                        <svg width="156" height="7" viewBox="0 0 156 7" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect width="49.3333" height="7" rx="2" fill="#FF574C"/>
                            <path opacity="0.3"
                                  d="M53.3333 2C53.3333 0.89543 54.2287 0 55.3333 0H100.667C101.771 0 102.667 0.895431 102.667 2V5C102.667 6.10457 101.771 7 100.667 7H55.3333C54.2287 7 53.3333 6.10457 53.3333 5V2Z"
                                  fill="#FF574C"/>
                            <rect opacity="0.3" x="106.667" width="49.3333" height="7" rx="2" fill="#FF574C"/>
                        </svg>

                    </div>
                </div>
                <div class="landing__goal-values-box">
                    <div class="landing__goal-values-item">
                        <h3 class="landing__goal-values-title">
                            <span class="nameInsertSelector">
                                {{ 'shared.pet_name'|resp_i11n_trans(domain='shared') }}'s
                            </span> obedience
                        </h3>
                        <p class="landing__goal-values-text">
                            Normal
                        </p>
                    </div>
                    <div class="landing__goal-values-item">
                        <h3 class="landing__goal-values-title">
                            Training Level
                        </h3>
                        <p class="landing__goal-values-text">
                            Advanced
                        </p>
                        <svg width="156" height="7" viewBox="0 0 156 7" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect width="49.3333" height="7" rx="2" fill="#1998CD"/>
                            <rect x="53.3333" width="49.3333" height="7" rx="2" fill="#1998CD"/>
                            <rect x="106.667" width="49.3333" height="7" rx="2" fill="#1998CD"/>
                        </svg>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <section class="landing__offer" id="getOffer">
        <div class="container">
            <h2 class="landing__offer-title">
                Your personalized
                <span class="nameInsertSelector">
                    {{ 'shared.pet_name'|resp_i11n_trans(domain='shared') }}'s
                </span> obedience plan is ready!
            </h2>
            <div class="landing__offer-problems">
                <div class="landing__offer-problems-list-item">
                    <div class="landing__offer-problems-list-icon-wrapp">
                        <img class="landing__offer-problems-list-icon" src="https://images.paw-champ.com/landings/goal.png" alt="">
                    </div>
                    <div class="landing__offer-problems-list-text-wrapp goalsInsertSelector">
                        <h4 class="landing__offer-problems-list-title">
                            Goal
                        </h4>
                    </div>
                </div>
                <div class="landing__offer-problems-list-item">
                    <div class="landing__offer-problems-list-icon-wrapp">
                        <img class="landing__offer-problems-list-icon" src="https://images.paw-champ.com/landings/behavioral_problems.png" alt="">
                    </div>
                    <div class="landing__offer-problems-list-text-wrapp problemsInsertSelector">
                        <h4 class="landing__offer-problems-list-title">
                            Behavioral problems
                        </h4>
                    </div>
                </div>
            </div>
            <div class="landing__offer-elements">
                <ul>
                    <li class="landing__offer-item active most-popular" data-offer-value="1-Month">
                        <div class="landing__offer-item-inner">
                            <div class="landing__offer-item-box">
                                <input class="paymentInfo"
                                       type="radio"
                                       checked
                                       id="dog-breed-radio-1"
                                       name="dog-breed-radio"
                                       data-payment-info='{"type": "1m_lower_intro_full_monthly_discount", "full_price": "49.99", "total_price": "17.99", "discount": "253.99"}'
                                >
                                <label for="dog-breed-radio-1"></label>
                                <h4 class="landing__offer-item-title">
                                    1-MONTH PLAN
                                </h4>
                            </div>
                            <div class="landing__offer-item-price-wrapper">
                                <p class="landing__offer-item-price-full">
                                    <span>{{ userCurrencySign() }}1.67</span>
                                </p>
                                <div class="landing__offer-item-price-box">
                                    <div class="landing__offer-item-price">
                                        <div class="landing__offer-item-price-sign">
                                            <span>{{ userCurrencySign() }}</span>
                                            <p>0</p>
                                        </div>
                                        <div class="landing__offer-item-price-value">
                                            <span>60</span>
                                            <p class="landing__offer-item-price-text">per day</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </li>
                    <li class="landing__offer-item" data-offer-value="3-Month">
                        <div class="landing__offer-item-inner">
                            <div class="landing__offer-item-box">
                                <input class="paymentInfo"
                                       type="radio"
                                       id="dog-breed-radio-2"
                                       name="dog-breed-radio"
                                       data-payment-info='{"type": "3m_lower_intro_full_quarterly_discount", "full_price": "79.99", "total_price": "29.99", "discount": "271.99"}'
                                >
                                <label for="dog-breed-radio-2"></label>
                                <h4 class="landing__offer-item-title">
                                    3-MONTH PLAN
                                </h4>
                            </div>
                            <div class="landing__offer-item-price-wrapper">
                                <p class="landing__offer-item-price-full">
                                    <span>{{ userCurrencySign() }}0.89</span>
                                </p>
                                <div class="landing__offer-item-price-box">
                                    <div class="landing__offer-item-price">
                                        <div class="landing__offer-item-price-sign">
                                            <span>{{ userCurrencySign() }}</span>
                                            <p>0</p>
                                        </div>
                                        <div class="landing__offer-item-price-value">
                                            <span>33</span>
                                            <p class="landing__offer-item-price-text">per day</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </li>
                    <li class="landing__offer-item" data-offer-value="6-Month">
                        <div class="landing__offer-item-inner">
                            <div class="landing__offer-item-box">
                                <input class="paymentInfo"
                                       type="radio"
                                       id="dog-breed-radio-3"
                                       name="dog-breed-radio"
                                       data-payment-info='{"type": "6m_lower_intro_full_halfyear_discount", "full_price": "119.99", "total_price": "43.99", "discount": "296.99"}'
                                >
                                <label for="dog-breed-radio-3"></label>
                                <h4 class="landing__offer-item-title">
                                    6-MONTH PLAN
                                </h4>
                            </div>
                            <div class="landing__offer-item-price-wrapper">
                                <p class="landing__offer-item-price-full">
                                    <span>{{ userCurrencySign() }}0.67</span>
                                </p>
                                <div class="landing__offer-item-price-box">
                                    <div class="landing__offer-item-price">
                                        <div class="landing__offer-item-price-sign">
                                            <span>{{ userCurrencySign() }}</span>
                                            <p>0</p>
                                        </div>
                                        <div class="landing__offer-item-price-value">
                                            <span>24</span>
                                            <p class="landing__offer-item-price-text">per day</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </li>
                </ul>
                {% if clientCountry == 'HK'|upper %}
                    <div class="landing__offer-text-wrapp">
                        <input class="landing__offer-text-wrapp-checkbox" type="checkbox">
                        <p class="smaller landing__offer-text textUnderOffer "></p>
                    </div>
                {% else %}
                    <p class="smaller landing__offer-text textUnderOffer borderBox"></p>
                {% endif %}
                <button class="landing__offer-btn popupBtn set-payment-info" data-amplitude-click-event="Get my plan 2" id="viewportAnchor">
                    <span></span>
                    Get my plan
                </button>
                <div class="landing__offer-guarantee underButton">
                    <svg width="13" height="15" viewBox="0 0 13 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M6.45368 2.62357e-05C8.68687 0.00473825 10.6315 0.626913 12.5873 1.61936C12.7247 1.6891 12.811 1.81076 12.8307 1.96249C13.158 4.48285 13.0886 6.99313 11.9197 9.31842C10.7803 11.5851 8.95055 12.9905 6.69292 14.0393C6.5665 14.098 6.43371 14.098 6.30719 14.0393C4.04956 12.9905 2.21979 11.5851 1.08042 9.31842C-0.0879722 6.99408 -0.158363 4.48219 0.169429 1.96249C0.188739 1.81378 0.271875 1.69428 0.405237 1.62341C2.33032 0.60062 4.23201 -0.00459154 6.45368 2.62357e-05ZM6.45368 0.943655C4.65387 0.939885 2.86291 1.40016 1.08099 2.33502C0.755097 4.91513 1.03533 7.10537 1.93575 8.8966C2.83048 10.6765 4.3473 12.0766 6.50001 13.0886C8.65272 12.0766 10.1696 10.6765 11.0643 8.8966C11.964 7.1066 12.2445 4.91824 11.9197 2.34039C10.078 1.41665 8.25597 0.947519 6.45368 0.943655Z"
                              fill="#787878"/>
                        <path fill-rule="evenodd" clip-rule="evenodd"
                              d="M6.45368 0.943655C4.65387 0.939885 2.86291 1.40016 1.08099 2.33502C0.755097 4.91513 1.03533 7.10537 1.93575 8.8966C2.83048 10.6765 4.3473 12.0766 6.50001 13.0886C8.65272 12.0766 10.1696 10.6765 11.0643 8.8966C11.964 7.1066 12.2445 4.91824 11.9197 2.34039C10.078 1.41665 8.25597 0.947519 6.45368 0.943655ZM5.88286 7.08813L4.59176 5.83229L3.58889 6.84245L5.99967 9.18734L9.68004 4.73778L8.57073 3.83863L5.88286 7.08813Z"
                              fill="#787878"/>
                    </svg>
                    <p class="landing__offer-guarantee-text">
                        30-DAY MONEY-BACK GUARANTEE
                    </p>
                </div>
            </div>
        </div>
    </section>
    <section class="landing__what" id="whatYouGetBlock">
        <div class="container">
            <h2 class="landing__what-title">
                What you get
            </h2>
            <ul class="landing__what-list">
                <li class="landing__what-item">
                    <div class="landing__what-item-box">
                        <span class="goalsInsertSelector"></span> fast
                    </div>
                </li>
                <li class="landing__what-item">
                    <div class="landing__what-item-box">
                        Forget about <span id="secondProblemInsertSelector">ineffective dog training</span>
                    </div>
                </li>
                <li class="landing__what-item">
                    <div class="landing__what-item-box">
                        Learn new dog training techniques
                    </div>
                </li>
                <li class="landing__what-item">
                    <div class="landing__what-item-box">
                        Deal with <span id="firstProblemInsertSelector">problem</span>
                    </div>
                </li>
                <li class="landing__what-item">
                    <div class="landing__what-item-box">
                        Maintain high obedience level
                    </div>
                </li>
                <li class="landing__what-item">
                    <div class="landing__what-item-box">
                        50+ games to boost
                        <span class="nameInsertSelector">
                            {{ 'shared.pet_name'|resp_i11n_trans(domain='shared') }}'s
                        </span>
                        mental health as a bonus
                    </div>
                </li>
                <li class="landing__what-item">
                    <div class="landing__what-item-box">
                        Enjoy well-being with happy and obedient {% if app.request.query.get('age') == 'puppy' %}puppy{% else %}dog{% endif %}
                    </div>
                </li>
            </ul>
        </div>
    </section>
    <section class="landing__featured" id="trustedBlock">
        <div class="container">

            <div class="landing__featured-inner">
                <ul class="landing__featured-list swiper-wrapper">
                    <li class="landing__featured-item swiper-slide">
                        <h3 class="landing__featured-item-title">
                            People just like you achieved great results using our
                            <br>
                            <span>Dog Obedience Plans</span>
                        </h3>
                        <div class="landing__featured-item-img-wrapp">
                            <img src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1685108802/dog-training/img/better/before-after01.png" alt="" class="landing__featured-item-img">
                        </div>
                        <div class="landing__featured-item-inner">
                            <div class="landing__featured-item-text-wrapp">
                                <p class="landing__featured-item-name">
                                    Jupiter
                                </p>
                                <p class="landing__featured-item-problem">
                                    Problem: <span>Leash pulling</span>
                                </p>
                            </div>
                            <p class="landing__featured-item-text">
                                My dog had a persistent habit of pulling on the leash during walks, making our outings a frustrating experience. However, after PawChamp lessons, walks with my doggo have become a pleasure rather
                                than a struggle. That annoying pulling habit is completely gone, and my doggo now strolls around all calm and composed...
                            </p>
                        </div>
                    </li>
                    <li class="landing__featured-item swiper-slide">
                        <h3 class="landing__featured-item-title">
                            People just like you achieved great results using our
                            <br>
                            <span>Dog Obedience Plans</span>
                        </h3>
                        <div class="landing__featured-item-img-wrapp">
                            <img src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1685108802/dog-training/img/better/before-after02.png" alt="" class="landing__featured-item-img">
                        </div>
                        <div class="landing__featured-item-inner">
                            <div class="landing__featured-item-text-wrapp">
                                <p class="landing__featured-item-name">
                                    Cooper
                                </p>
                                <p class="landing__featured-item-problem">
                                    Problem: <span>Aggression</span>
                                </p>
                            </div>
                            <p class="landing__featured-item-text">
                                Before the course, my dog had an intense fear aggression and freak out around people, making our lives pretty stressful. We followed the course's tips and tricks, and guess what? Slowly but surely,
                                my dog started getting over this problem. His fear melted away, and his aggression turned into curiosity and friendliness...
                            </p>
                        </div>
                    </li>
                    <li class="landing__featured-item swiper-slide">
                        <h3 class="landing__featured-item-title">
                            People just like you achieved great results using our
                            <br>
                            <span>Dog Obedience Plans</span>
                        </h3>
                        <div class="landing__featured-item-img-wrapp">
                            <img src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1685108802/dog-training/img/better/before-after03.png" alt="" class="landing__featured-item-img">
                        </div>
                        <div class="landing__featured-item-inner">
                            <div class="landing__featured-item-text-wrapp">
                                <p class="landing__featured-item-name">
                                    Luna
                                </p>
                                <p class="landing__featured-item-problem">
                                    Problem: <span>Hyperactivity</span>
                                </p>
                            </div>
                            <p class="landing__featured-item-text">
                                Walking my dog used to be a chaotic mess, with constant mischief and mayhem. But thanks to this course, we've experienced a complete turnaround. With each lesson, I see a remarkable progress in her
                                ability to calm down during walks and she became more chill around other dogs. I learned the reasons behind her hyperactivity and...
                            </p>
                        </div>
                    </li>
                </ul>
                <div class="landing__featured-btns">
                    <div class="swiper-button-prev"></div>
                    <div class="swiper-button-next"></div>
                </div>
            </div>
            <div class="landing__featured-disclamer">
                <b>Disclaimer:</b> Following the exercises and obedience plan is the key in your journey and greatly impacts the results.
            </div>
        </div>
    </section>
    <section class="landing__ask spacing" id="FAQView">
        <div class="container">
            <div class="landing__ask-wrapp">
                <h2 class="landing__ask-title">
                    People Often Ask
                </h2>
                <ul class="landing__ask-list">
                    <li class="landing__ask-item">
                        <h3 class="landing__ask-item-title">
                            I have tried other trainings before, how is this different?
                        </h3>
                        <div class="landing__ask-item-box">
                            PawChamp is not a regular training stuff. We offer a unique and scientifically-backed approach to dog training that focuses on the most effective methods. We say no to prong collars, punishments,
                            yelling, and dominance-based training, as these methods can harm your <span class="puppyInsertSelector">dog</span> and are not effective in the long term. Our goal is to help you train your <span
                                    class="puppyInsertSelector">dog</span> in a comfortable and healthy manner, so that both you and your furry friend can be happy.
                        </div>
                    </li>
                    <li class="landing__ask-item">
                        <h3 class="landing__ask-item-title">
                            My <span class="puppyInsertSelector">dog</span> is not food-motivated
                        </h3>
                        <div class="landing__ask-item-box">
                            It’s possible that your dog finds toys or praise more interesting than treats. It’s also important to mention that sometimes dogs refuse treats because they are too stressed to eat. To all of the above,
                            we
                            have a separate module that focuses on dog’s motivation.
                        </div>
                    </li>
                    <li class="landing__ask-item">
                        <h3 class="landing__ask-item-title">
                            I don’t have time to train my <span class="puppyInsertSelector">dog</span>
                        </h3>
                        <div class="landing__ask-item-box">
                            Ask yourself, how much time are you spending dealing with your dog’s
                            behavior issues right now? It’s possible that by investing just a little bit of time upfront in dog training courses, you could save
                            yourself a lot of time and hassle in the long run. We keep our training lessons short (up to 15 min) and easy!
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </section>
    {% include 'compliance/components/feeds-main.html.twig' %}
    <section class="landing__offer">
        <div class="container">
            <h2 class="landing__offer-title">
                Your personalized
                <span class="nameInsertSelector">
                    {{ 'shared.pet_name'|resp_i11n_trans(domain='shared') }}'s
                </span>
                obedience plan is ready!
            </h2>
            <div class="landing__offer-problems">
                <div class="landing__offer-problems-list-item">
                    <div class="landing__offer-problems-list-icon-wrapp">
                        <img class="landing__offer-problems-list-icon" src="https://images.paw-champ.com/landings/goal.png" alt="">
                    </div>
                    <div class="landing__offer-problems-list-text-wrapp goalsInsertSelector">
                        <h4 class="landing__offer-problems-list-title">
                            Goal
                        </h4>
                    </div>
                </div>
                <div class="landing__offer-problems-list-item">
                    <div class="landing__offer-problems-list-icon-wrapp">
                        <img class="landing__offer-problems-list-icon" src="https://images.paw-champ.com/landings/behavioral_problems.png" alt="">
                    </div>
                    <div class="landing__offer-problems-list-text-wrapp problemsInsertSelector">
                        <h4 class="landing__offer-problems-list-title">
                            Top priority
                        </h4>
                    </div>
                </div>
            </div>
            <div class="landing__offer-elements" id="getOffer2">
                <ul>
                    <li class="landing__offer-item active most-popular" data-offer-value="1-Month">
                        <div class="landing__offer-item-inner">
                            <div class="landing__offer-item-box">
                                <input class="paymentInfo"
                                       type="radio"
                                       checked
                                       id="dog-breed-radio-1"
                                       name="dog-breed-radio"
                                       data-payment-info='{"type": "1m_lower_intro_full_monthly_discount", "full_price": "49.99", "total_price": "17.99", "discount": "253.99"}'
                                >
                                <label for="dog-breed-radio-1"></label>
                                <h4 class="landing__offer-item-title">
                                    1-MONTH PLAN
                                </h4>
                            </div>
                            <div class="landing__offer-item-price-wrapper">
                                <p class="landing__offer-item-price-full">
                                    <span>{{ userCurrencySign() }}1.67</span>
                                </p>
                                <div class="landing__offer-item-price-box">
                                    <div class="landing__offer-item-price">
                                        <div class="landing__offer-item-price-sign">
                                            <span>{{ userCurrencySign() }}</span>
                                            <p>0</p>
                                        </div>
                                        <div class="landing__offer-item-price-value">
                                            <span>60</span>
                                            <p class="landing__offer-item-price-text">per day</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </li>
                    <li class="landing__offer-item" data-offer-value="3-Month">
                        <div class="landing__offer-item-inner">
                            <div class="landing__offer-item-box">
                                <input class="paymentInfo"
                                       type="radio"
                                       id="dog-breed-radio-2"
                                       name="dog-breed-radio"
                                       data-payment-info='{"type": "3m_lower_intro_full_quarterly_discount", "full_price": "79.99", "total_price": "29.99", "discount": "271.99"}'
                                >
                                <label for="dog-breed-radio-2"></label>
                                <h4 class="landing__offer-item-title">
                                    3-MONTH PLAN
                                </h4>
                            </div>
                            <div class="landing__offer-item-price-wrapper">
                                <p class="landing__offer-item-price-full">
                                    <span>{{ userCurrencySign() }}0.89</span>
                                </p>
                                <div class="landing__offer-item-price-box">
                                    <div class="landing__offer-item-price">
                                        <div class="landing__offer-item-price-sign">
                                            <span>{{ userCurrencySign() }}</span>
                                            <p>0</p>
                                        </div>
                                        <div class="landing__offer-item-price-value">
                                            <span>33</span>
                                            <p class="landing__offer-item-price-text">per day</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </li>
                    <li class="landing__offer-item" data-offer-value="6-Month">
                        <div class="landing__offer-item-inner">
                            <div class="landing__offer-item-box">
                                <input class="paymentInfo"
                                       type="radio"
                                       id="dog-breed-radio-3"
                                       name="dog-breed-radio"
                                       data-payment-info='{"type": "6m_lower_intro_full_halfyear_discount", "full_price": "119.99", "total_price": "43.99", "discount": "296.99"}'
                                >
                                <label for="dog-breed-radio-3"></label>
                                <h4 class="landing__offer-item-title">
                                    6-MONTH PLAN
                                </h4>
                            </div>
                            <div class="landing__offer-item-price-wrapper">
                                <p class="landing__offer-item-price-full">
                                    <span>{{ userCurrencySign() }}0.67</span>
                                </p>
                                <div class="landing__offer-item-price-box">
                                    <div class="landing__offer-item-price">
                                        <div class="landing__offer-item-price-sign">
                                            <span>{{ userCurrencySign() }}</span>
                                            <p>0</p>
                                        </div>
                                        <div class="landing__offer-item-price-value">
                                            <span>24</span>
                                            <p class="landing__offer-item-price-text">per day</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </li>
                </ul>
                {% if clientCountry == 'HK'|upper %}
                    <div class="landing__offer-text-wrapp">
                        <input class="landing__offer-text-wrapp-checkbox" type="checkbox">
                        <p class="smaller landing__offer-text textUnderOffer "></p>
                    </div>
                {% else %}
                    <p class="smaller landing__offer-text textUnderOffer borderBox"></p>
                {% endif %}
                <button class="landing__offer-btn popupBtn set-payment-info " data-amplitude-click-event="Get my plan 4" id="secondOffer">
                    <span></span>
                    Get my plan
                </button>
                <div class="landing__offer-guarantee underButton">
                    <svg width="13" height="15" viewBox="0 0 13 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M6.45368 2.62357e-05C8.68687 0.00473825 10.6315 0.626913 12.5873 1.61936C12.7247 1.6891 12.811 1.81076 12.8307 1.96249C13.158 4.48285 13.0886 6.99313 11.9197 9.31842C10.7803 11.5851 8.95055 12.9905 6.69292 14.0393C6.5665 14.098 6.43371 14.098 6.30719 14.0393C4.04956 12.9905 2.21979 11.5851 1.08042 9.31842C-0.0879722 6.99408 -0.158363 4.48219 0.169429 1.96249C0.188739 1.81378 0.271875 1.69428 0.405237 1.62341C2.33032 0.60062 4.23201 -0.00459154 6.45368 2.62357e-05ZM6.45368 0.943655C4.65387 0.939885 2.86291 1.40016 1.08099 2.33502C0.755097 4.91513 1.03533 7.10537 1.93575 8.8966C2.83048 10.6765 4.3473 12.0766 6.50001 13.0886C8.65272 12.0766 10.1696 10.6765 11.0643 8.8966C11.964 7.1066 12.2445 4.91824 11.9197 2.34039C10.078 1.41665 8.25597 0.947519 6.45368 0.943655Z"
                              fill="#787878"/>
                        <path fill-rule="evenodd" clip-rule="evenodd"
                              d="M6.45368 0.943655C4.65387 0.939885 2.86291 1.40016 1.08099 2.33502C0.755097 4.91513 1.03533 7.10537 1.93575 8.8966C2.83048 10.6765 4.3473 12.0766 6.50001 13.0886C8.65272 12.0766 10.1696 10.6765 11.0643 8.8966C11.964 7.1066 12.2445 4.91824 11.9197 2.34039C10.078 1.41665 8.25597 0.947519 6.45368 0.943655ZM5.88286 7.08813L4.59176 5.83229L3.58889 6.84245L5.99967 9.18734L9.68004 4.73778L8.57073 3.83863L5.88286 7.08813Z"
                              fill="#787878"/>
                    </svg>
                    <p class="landing__offer-guarantee-text">
                        30-DAY MONEY-BACK GUARANTEE
                    </p>
                </div>
            </div>
        </div>
    </section>
    <section class="landing__moneyback {{ isCountryBR ? 'pb-40' }}" id="moneybackBlock">
        <div class="container">
            <div class="landing__moneyback-wrapper">
                <div class="landing__moneyback-box">
                    <h2 class="landing__moneyback-title">
                        30-Day Money-Back Guarantee
                    </h2>
                    <p class="landing__moneyback-text">
                        We believe that our plan may work for you and you’ll get visible results in 4 weeks! We even are ready to return your money back if you don’t see visible results and can demonstrate that you followed our
                        plan.
                        <br>
                        <br>
                        Find more about applicable limitations in our
                        <a href="{{ path('static_page_terms') }}">terms of use</a>.
                    </p>
                    <img class="landing__moneyback-img" src="https://res.cloudinary.com/dr0cx27xo/image/upload/v1685114587/dog-training/img/better/guarr.svg" alt="">
                </div>
            </div>
        </div>
    </section>
    <section class="landing__footer">
        {% include 'subscription/components/footer-disclaimer.html.twig' %}
        <div class="landing__footer-mob">
            <div class="container">
                <div class="landing__footer-box">
                    <p class="landing__footer-copyright">
                        All rights reserved. {{ "now"|date("Y") }}
                    </p>
                </div>
                <div class="landing__footer-box">
                    <p class="landing__footer-copyright">
                        {% if clientCountry == 'US'|upper %}
                            Dogitality Corp., 3753 Howard Hughes Parkway, Suite 200, Las Vegas, NV 89169, USA
                        {% elseif clientCountry == 'HK'|upper  or clientCountry == 'CN'|upper %}
                            Driftal Limited, 14/F, China Hong Kong Tower, 8 Hennessy Road, Wanchai, Hong Kong
                        {% elseif clientCountry in countryCodesEEA %}
                            FONTADELLA LIMITED, Limassol, Cyprus; Driftal Limited, Hong Kong
                        {% else %}
                            FONTADELLA LIMITED, Limassol, Cyprus
                        {% endif %}
                    </p>
                </div>
            </div>
            <ul class="landing__footer-mob-list">
                <div class="container">
                    <div class="landing__footer-box">
                        <a class="landing__footer-contact" href="{{ path('static_page_contacts') }}">Contact Us</a>
                        <a class="landing__footer-contact" href="{{ path('static_page_policy') }}">Privacy Policy</a>
                        <a class="landing__footer-contact" href="{{ path('static_page_terms') }}">Terms of Use</a>
                    </div>
                </div>
            </ul>
        </div>

        <div class="landing__footer-desk">
            <div class="container">
                <div class="landing__footer-box">
                    <ul class="landing__footer-desk-list">
                        <li class="landing__footer-desk-item">
                            <a class="landing__footer-contact" href="{{ path('static_page_contacts') }}">Contact Us</a>
                        </li>
                        <li class="landing__footer-desk-item">
                            <a class="landing__footer-contact" href="{{ path('static_page_policy') }}">Privacy Policy</a>
                        </li>
                        <li class="landing__footer-desk-item">
                            <a class="landing__footer-contact" href="{{ path('static_page_terms') }}">Terms of Use</a>
                        </li>
                    </ul>
                    <p class="landing__footer-copyright">
                        All rights reserved. {{ "now"|date("Y") }}
                    </p>
                </div>
                {% if clientCountry == 'US'|upper %}
                    <p class="landing__footer-copyright big">
                        Dogitality Corp., 3753 Howard Hughes Parkway, Suite 200, Las Vegas, NV 89169, USA
                    </p>
                {% elseif clientCountry == 'HK'|upper or clientCountry == 'CN'|upper %}
                    <p class="landing__footer-copyright big">
                        Driftal Limited, 14/F, China Hong Kong Tower, 8 Hennessy Road, Wanchai, Hong Kong
                    </p>
                {% elseif clientCountry in countryCodesEEA %}
                    <p class="landing__footer-copyright big">
                        FONTADELLA LIMITED, Limassol, Cyprus; Driftal Limited, Hong Kong
                    </p>
                {% else %}
                    <p class="landing__footer-copyright big">
                        FONTADELLA LIMITED, Limassol, Cyprus
                    </p>
                {% endif %}
            </div>
        </div>
    </section>
</main>

{% include 'subscription/components/quiz-responses.html.twig' %}

<script>
    window.language = 'en';
    window.paymentFormDiscount = '75';
    window.currentPaymentPlanInfo = '';
    window.currencySign = '{{ userCurrencySign() }}';
    window.paymentInfoId = '6';
    window.currencyPayment = '{{ getUserCurrencyCode() }}';
    window.userId = '{{ app.user.externalId }}';
    window.getDeviceName = {{ getDeviceNameAndModel()|json_encode()|raw }};
    window.termsLink = '{{ path('static_page_terms') }}';
    window.enableFbTracking = false;
    window.getCountry = '{{ getCountry()|upper }}';
</script>

{% include 'subscription/components/payment-popups/payment-popup-bank-funnel.html.twig' with {discountPercent: '40'} %}

{% include 'subscription/components/thank-you-popup.html.twig' %}
{% include 'subscription/components/password.html.twig' %}
{% include 'subscription/components/upsale-one-agression.html.twig' %}
{% include 'subscription/components/sticky-offer-bank.html.twig' %}

</body>
</html>
