{# Default modal #}
<div class="modal fade" id="exportModal" tabindex="-1" role="dialog" aria-labelledby="exportModalLabel"
     aria-hidden="true">
    <div class="modal-dialog" id="defaultModalLayout" role="document">
        <form id="exportCsvForm" action="{{ path('admin_export_data_for_lms_enroll') }}" method="POST">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exportModalLabel">Export CSV Users for LMS enroll</h5>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label for="courseIDs">Course IDs:</label>
                        <input type="text"
                               title="Newlines, commas, and double quotes are not allowed." name="course_ids"
                               class="form-control" id="courseIDs" placeholder="Enter Course IDs separated by ';'">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="submit" class="btn btn-primary">Export</button>
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                </div>
            </div>
        </form>
    </div>
</div>
{# Default modal #}


{# Success modal #}
<div id="successModalLayout" class="d-none">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Success</h5>
            </div>
            <div class="modal-body">
                <p>Your export has started. Your file should download shortly.</p>
            </div>
            <div class="modal-footer">
                <button id="closeModalBtn" type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{# Success modal #}

{# Erorr modal #}
<div id="errorModalLayout" class="d-none">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Error</h5>
            </div>
            <div class="modal-body">
                <p>Error downloading the file. Please try again later.</p>
            </div>
            <div class="modal-footer">
                <button id="closeModalBtn" type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{# Erorr modal #}

{# Generating modal #}
<div id="generatingModalLayout" class="d-none">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-body text-center">
                <h5>Generating<span id="dots">.</span></h5>
            </div>
        </div>
    </div>
</div>
{# Generating modal #}