<?php

declare(strict_types=1);

$finder = PhpCsFixer\Finder::create()
    ->exclude(['Enum'])
    ->in([__DIR__ . '/src', __DIR__ . '/tests']);

return (new PhpCsFixer\Config())
    ->setRules([
        '@Symfony' => true,
        '@PSR12' => true,
        '@PHP82Migration' => true,
        'array_syntax' => ['syntax' => 'short'],
        'single_line_throw' => false,
        'yoda_style' => [
            'equal' => false,
            'identical' => false,
            'less_and_greater' => false,
        ],
        'trailing_comma_in_multiline' => true,
        'single_trait_insert_per_statement' => false,
        'concat_space' => ['spacing' => 'one'],
        'array_indentation' => true,
        'global_namespace_import' => false,
        'types_spaces' => ['space' => 'single'],
        'global_namespace_import' => true,
    ])
    ->setFinder($finder);
