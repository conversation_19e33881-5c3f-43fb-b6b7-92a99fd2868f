@import "abstracts/variables"
@import "abstracts/mixins"
@import "base/reset"
@import "base/base"

@import "components/header"
@import "components/sticky-offer"
@import "components/payment-popup-main"
@import "components/thank-you-popup"
@import "components/email-popup"
@import "components/upsale-progressbar"
@import "components/stacked-paper"
@import "components/checkout"
@import "components/user-reviews"
@import "components/upsale"
@import "components/upsale-list"
@import "components/swiper"
@import "components/password"
@import "components/success"

//for split
$maxContainer: 504px

@mixin videoFrameBorderStyle
  border: 6px solid #4D4D4D
  box-shadow: 0 6px 6px rgba(0, 0, 0, 0.25)
  border-radius: 16px

.payment-page
  &__report
    width: 100%
    position: relative
    max-width: 504px
    margin: 56px auto 0
    padding-bottom: 48px

    &-title
      font-size: 26px
      font-weight: 800
      line-height: 120%
      display: flex
      align-items: center
      justify-content: center
      padding-bottom: 16px
      margin-left: 8px
      color: $black

      & svg
        margin-right: 8px

      &-small
        font-weight: 800
        line-height: 120%
        font-size: 12px
        padding: 8px 0

    &-wrapp
      background: #FFF6EE
      border: 1px solid #FFEBD9
      border-radius: 16px
      position: relative
      padding: 24px 16px 16px

    &-info
      padding: 0 4px
      position: relative
      margin-top: 24px

      &-avatar
        font-size: 0
        margin-right: 16px
        width: 84px

      &-wrapp
        display: flex
        padding: 0 8px

      &-inner
        display: flex
        align-items: center

      &-box
        display: flex
        flex-direction: column
        justify-content: center
        width: 100%

      &-item
        width: 100%
        display: flex
        align-items: center

        &:first-of-type
          margin-right: 12px
          max-width: max-content

        &-title
          color: #9A9A9A
          font-weight: 400
          font-size: 10px
          line-height: 120%
          padding-bottom: 2px
          padding-left: 4px

        &-value
          font-size: 14px
          line-height: 110%
          min-height: 20px
          padding-left: 4px
          display: flex
          align-items: center

        &-icon
          width: 24px
          height: 24px

      &-together
        padding: 4px 8px
        background: #F6BC87
        border-radius: 24px
        color: $white
        font-size: 10px

    &-list
      margin: 16px 0 80px

      &-title
        font-size: 32px
        font-weight: 800
        line-height: 120%
        margin: 80px 0 0
        color: #523636
        width: 100%
        text-align: center

      &-box
        display: flex
        align-items: center
        font-weight: 800
        font-size: 20px
        line-height: 110%
        padding: 24px 0
        max-width: 263px
        width: 100%
        margin: 0 auto

      &-item
        display: flex
        flex-direction: column
        align-items: center
        background: linear-gradient(180deg, rgba(254, 243, 233, 0) 0%, #FEF0E4 100%)
        width: calc(100% + 32px)
        position: relative
        left: -16px
        margin-top: 32px

        &-number
          background: $black
          color: $white
          border-radius: 100%
          min-width: 40px
          min-height: 40px
          display: flex
          align-items: center
          justify-content: center
          font-size: 29px
          margin-right: 16px

        &-img
          max-width: 232px
          width: 100%
          margin: 0 auto

    &-hr
      margin: 0
      padding: 0
      border: none
      border-bottom: 1px solid #FFEBD9
      width: 100%

      &.top
        margin: 12px 0

      &.middle
        margin: 16px 0

    &-obedience
      position: relative
      width: 100%
      box-sizing: border-box
      border-radius: 16px
      padding: 0 8px

      &-img
        & img
          width: 100%
          position: relative

      &-title
        text-align: center
        width: 100%
        font-weight: 600
        font-size: 18px

      &-slider
        display: flex
        flex-direction: column
        width: 100%

      &-wrapper
        display: block
        padding: 0 16px 24px
        position: relative

      &-numbers
        display: flex
        justify-content: space-between
        width: 100%
        padding: 0 4px
        margin-top: -48px

        &-item
          color: $input-title
          font-size: 14px
          line-height: 135%
          display: flex
          justify-content: center

      &-hidden
        display: none !important

    &-explore
      &-title
        font-weight: 800
        font-size: 18px
        text-align: center
        padding-bottom: 8px

        & span
          @include textGradient($gradient-red)

      &-list
        padding: 0 24px
        max-width: 300px
        width: 100%
        margin: 0 auto

      &-item
        display: flex
        align-items: center
        padding-bottom: 4px

        &:last-of-type
          margin-bottom: 0

      &-text
        font-size: 16px
        font-weight: 600
        line-height: 120%

      &-icon
        margin-right: 8px
        width: 20px
        min-width: 20px

    &-teach
      &-title
        font-weight: 800
        font-size: 18px
        text-align: center
        padding-bottom: 8px

      &-list
        display: flex
        align-items: center
        justify-content: center
        flex-wrap: wrap

      &-item
        display: flex
        align-items: center
        justify-content: center
        margin-right: 8px
        margin-bottom: 8px

        &:last-of-type
          margin-right: 0

  &__based
    position: relative
    padding: 64px 0
    width: 100%

    &:before
      content: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1628064914/dog-training/img/based-bg-desk.svg")
      z-index: -1
      position: absolute
      width: 100%
      height: 100%
      display: flex
      justify-content: center
      align-items: center
      transform: scale(0.8)
      margin: -64px auto 0

    &-new
      position: relative
      padding-bottom: 64px
      width: 100%
      margin-top: 32px

      &:before
        content: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1628064914/dog-training/img/based-bg-desk.svg")
        z-index: -1
        position: absolute
        width: 100%
        height: 100%
        display: flex
        justify-content: center
        align-items: center
        transform: scale(1.4)
        margin: -16px auto 0

    &-improve
      background: #FAE7D6
      border: 1px solid #F3DCC7
      border-radius: 16px
      padding: 16px
      margin-bottom: 40px

      &-wrapper
        max-width: 504px
        width: 100%
        margin: 0 auto

      &-percents
        color: #CE9C70
        font-size: 82px
        font-weight: 800
        line-height: 120%
        text-align: center

      &-box
        background: #FFF6EE
        border: 1px solid #F2DDCB
        border-radius: 16px
        padding: 16px
        text-align: center
        font-size: 14px
        color: #523A25
        line-height: 140%
        font-weight: 600

      &-subtitle
        font-size: 16px
        line-height: 140%
        font-weight: 600
        color: #523A25
        text-align: center

    &-inner
      max-width: 414px
      width: 100%
      margin: 0 auto

    &-title
      font-size: 26px
      font-weight: 800
      line-height: 120%
      text-align: center
      max-width: 202px
      width: 100%
      margin: 8px auto 16px

      &-gradient
        @include textGradient($gradient-red)
        font-size: 24px
        text-align: center
        width: 100%
        margin-bottom: 8px

      &-small
        font-size: 20px
        font-weight: 800
        line-height: 120%
        text-align: center
        max-width: 202px
        width: 100%
        margin: 20px auto 16px

    &-subtitle
      font-size: 16px
      line-height: 140%
      font-weight: 600
      color: #16191E
      text-align: center
      margin-bottom: 16px

    &-graph
      background: $white
      border-radius: 16px
      padding: 24px 8px 16px
      border: 1px solid #FAEADC
      box-shadow: 0 4px 16px rgba(220, 205, 192, 0.47)
      margin-bottom: 16px

      &-health
        width: calc(100% + 32px)
        position: relative
        left: -16px
        padding-bottom: 32px

        &-box
          font-size: 12px
          font-weight: 700
          color: #A49F9A
          display: flex
          justify-content: space-between
          align-items: center
          padding: 0 16px
          margin-top: -40px

      &-text
        font-size: 16px
        font-weight: 600
        line-height: 130%
        margin-left: 8px
        display: flex

      & svg
        margin-top: -16px

        & path
          width: 100%

      &-line
        stroke-dasharray: 1000
        animation: dash 12s
        animation-play-state: unset

      @keyframes dash
        from
          stroke-dashoffset: 1000
        to
          stroke-dashoffset: 0

    &-list
      display: flex

      &-item
        display: flex
        flex-direction: column
        background: $white
        font-weight: 600
        border-radius: 16px
        padding: 16px
        border: 1px solid #FAEADC
        box-shadow: 0 4px 16px rgba(220, 205, 192, 0.47)
        width: 50%

        & span
          color: $red
          font-weight: 800

        &:first-child
          margin-right: 16px

  &__progress-line
    margin: 0 auto
    width: 100%
    position: relative
    max-width: 504px

    &-wrapp
      padding: 24px 16px
      max-width: 504px
      width: 100%
      margin: 0 auto
      border-radius: 16px
      background: #EBFFE8
      border: 1px solid #BEECB7

    &-title
      color: $black
      font-weight: 700
      font-size: 14px
      line-height: 140%

      &-box
        display: flex
        justify-content: space-between
        align-items: center
        margin-bottom: -12px

      &-main
        color: $black
        font-weight: 800
        font-size: 25px
        line-height: 140%
        text-align: center

    &-subtitle
      font-size: 14px
      text-align: center
      line-height: 120%
      margin: 8px 0

    & .blue
      color: #2995F8
      font-weight: 500

    & .red
      color: #FF7A5C
      font-weight: 700

      & svg
        border: 0.3px solid #FB5A48

    & .green
      color: #49D64E
      font-weight: 500

    &-text
      font-size: 12px
      margin-top: -4px
      color: #646464

    &-item
      margin-bottom: 16px

      &:last-of-type
        margin-bottom: 0

      & svg
        border: 0.3px solid #9ADC90

  &__improvements
    border-radius: 16px
    display: flex
    flex-direction: column
    justify-content: center
    align-items: center
    margin: 0 auto
    width: 100%
    padding-top: 24px

    &-text
      font-size: 12px
      color: #646464

      &-box
        display: flex
        justify-content: space-between
        align-items: center
        margin: -24px 16px
        width: 100%
        padding: 0 16px

    &-title
      font-weight: 700
      font-size: 14px
      line-height: 110%
      width: 55%
      margin-bottom: -24px
      padding-left: 16px

      &-wrapp
        width: 100%
        display: flex

  &__percents
    padding: 48px 0

    &-similar
      background: #FFFFFF
      border: 1px solid #E1E1E1
      border-radius: 16px
      padding: 16px
      margin-bottom: 16px
      display: flex
      align-items: center
      justify-content: space-between

      &-text
        font-size: 16px
        max-width: 226px
        text-align: right

      &-percents
        font-weight: 800
        font-size: 26px
        line-height: 100%

    &-readiness
      background: #EBFFE8
      border: 1px solid #BEECB7
      border-radius: 16px
      padding: 16px
      display: flex
      align-items: center
      justify-content: space-between

      &-text
        font-weight: 500
        font-size: 17px
        line-height: 110%
        max-width: 226px
        text-align: right

      &-percents
        font-weight: 800
        font-size: 26px
        line-height: 100%
        color: #2FC339

  &__summary
    width: 100%
    padding-top: 88px

    &-wrapper
      display: grid
      grid-template-columns: 1fr 1fr
      grid-column-gap: 16px
      max-height: 135px
      max-width: 414px
      width: 100%

    &-inner
      max-width: 840px
      width: 100%
      margin: 0 auto
      display: flex
      flex-direction: column
      align-items: center
      flex-wrap: wrap

      &.healthy
        max-width: 504px
        width: 100%
        margin: 0 auto

    &-title
      text-align: center
      width: 100%
      font-weight: 800
      font-size: 26px
      line-height: 120%
      padding: 16px 0

  &__problems
    position: relative
    width: 100%
    max-width: 414px
    background: #FFFFFF
    border: 1px solid #F1F1F1
    box-sizing: border-box
    box-shadow: 0 4px 16px rgba(219, 219, 219, 0.47)
    border-radius: 16px
    margin-bottom: 16px
    padding: 16px 12px

    &-title
      text-align: center
      width: 100%
      font-weight: 600
      font-size: 18px
      padding-bottom: 16px

    &-box
      display: flex
      justify-content: center
      padding: 8px 0

    &-value
      font-weight: 700
      font-size: 64px
      line-height: 100%
      display: flex

      &-total
        font-weight: 400
        font-size: 12px
        line-height: 200%
        display: flex
        flex-direction: column

    &-active
      display: flex
      flex-wrap: wrap
      align-items: center
      justify-content: center
      list-style: none

      &-item
        display: flex

    &-inactive
      display: flex
      flex-wrap: wrap
      align-items: center
      justify-content: center

      &-item
        display: flex

    &-text
      font-weight: 800
      font-size: 18px
      line-height: 120%
      max-width: 206px
      width: 100%
      margin: 0 auto
      text-align: center
      padding-top: 16px

  &__obedience
    position: relative
    width: 100%
    max-width: 414px
    background: #FFFFFF
    border: 1px solid #F1F1F1
    box-sizing: border-box
    box-shadow: 0 4px 16px rgba(219, 219, 219, 0.47)
    border-radius: 16px
    padding: 12px 12px 24px
    margin-bottom: 16px

    &-img
      & img
        width: 100%

    &-title
      text-align: center
      width: 100%
      padding-top: 10px
      font-weight: 600
      font-size: 18px

    &-slider
      display: flex
      flex-direction: column
      width: 100%

    &-wrapper
      display: block
      padding: 0 16px 24px
      position: relative

    &-numbers
      display: flex
      justify-content: space-between
      width: 100%
      padding: 0 4px
      margin-top: -32px

      &-item
        color: $input-title
        font-size: 14px
        line-height: 135%
        display: flex
        justify-content: center

    &-hidden
      display: none !important

  &__basics
    position: relative
    width: 100%
    max-width: 414px
    background: linear-gradient(159.44deg, #FF765A 5.8%, #FF4040 90.92%)
    border: 1px solid #DD4A40
    box-shadow: 0 4px 16px rgba(255, 82, 73, 0.46)
    border-radius: 16px
    box-sizing: border-box
    padding: 8px 8px 16px
    margin-bottom: 16px
    &-title
      color: $white
      text-align: center
      width: 100%
      font-weight: 800
      font-size: 18px
      padding: 10px 0
    &-list
      min-height: 76px
      display: flex
      justify-content: center

  &__bonuses
    padding: 48px 0 64px

    &-title
      display: flex
      flex-direction: column
      align-items: center
      justify-content: center
      font-size: 26px
      font-weight: 800
      line-height: 120%
      text-align: center
      padding-bottom: 16px
      color: $black

      & svg
        margin-top: 4px

    &-slider
      &-item
        background: #CFE8FF
        border: 1px solid #B1D5F5
        border-radius: 16px
        padding: 16px 8px

        &.adult
          background: #ffffff

        &-top
          display: flex
          align-items: center
          padding-bottom: 8px

          &-bonus
            background: $gradient-red
            border: 1px solid #F4453F
            border-radius: 16px
            font-weight: 800
            font-size: 14px
            line-height: 100%
            color: #ffffff
            width: max-content
            padding: 4px 8px
            margin-right: 8px

          &-value
            background: #FFFFFF
            border: 1px solid #B8D8F5
            border-radius: 16px
            font-weight: 600
            font-size: 14px
            line-height: 100%
            color: $black
            width: max-content
            padding: 4px 8px

            & b
              font-weight: 800

            & span
              text-decoration: line-through

        &-main
          display: flex

        &-img
          max-width: 115px
          width: 100%
          height: 100%

        &-bullets
          list-style: disc
          margin-left: 16px

        &-bullet
          font-size: 14px
          font-weight: 600
          line-height: 130%

        &-title
          font-size: 20px
          font-weight: 800
          padding-bottom: 8px

        &-subtitle
          font-size: 14px
          font-weight: 600
          padding-bottom: 8px

        &-box
          margin-left: 8px

      & .swiper-pagination
        padding-top: 16px

        &-bullet-active
          background: $black

  &__trained
    padding: 24px 0 48px
    background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1674998593/dog-training/img/trained-bg.png") center no-repeat
    background-size: cover
    position: relative

    &:after
      content: ''
      position: absolute
      top: 0
      left: 0
      right: 0
      height: 100px
      width: 100%
      background: linear-gradient(0deg, rgba(255, 255, 255, 0) 25%, #FFFFFF 40%)
      z-index: 0

    &:before
      content: ''
      position: absolute
      bottom: 0
      left: 0
      right: 0
      height: 100px
      width: 100%
      background: linear-gradient(180deg, rgba(255, 255, 255, 0) 5%, #FFFFFF 40%)

    &-title
      font-size: 26px
      font-weight: 800
      line-height: 120%
      text-align: center
      padding-bottom: 8px
      z-index: 1
      position: relative

    &-subtitle
      font-size: 16px
      font-weight: 500
      line-height: 120%
      text-align: center
      padding-bottom: 16px
      position: relative
      z-index: 1
      max-width: 800px
      margin: 0 auto

    &-img
      max-width: 290px
      width: 100%
      position: relative
      z-index: 1

      &-wrapp
        width: 100%
        display: flex
        align-items: center
        justify-content: center
        padding-bottom: 16px

    &-box
      display: flex
      align-items: center
      font-weight: 600
      font-size: 18px

    &-list
      max-width: 300px
      width: 100%
      margin: 0 auto
      position: relative

    &-item
      padding-bottom: 8px

      &:last-of-type
        padding-bottom: 0

      &-img
        max-width: 24px
        min-width: 24px
        padding-right: 8px

  &__plan
    background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/f_auto/v1625737854/dog-training/img/selling-plan-bg.png") no-repeat top center
    background-size: cover
    position: relative
    z-index: -1
    width: 100%
    max-width: 414px

    &:after
      content: ''
      position: absolute
      top: 0
      left: 0
      right: 0
      height: 100px
      width: 100%
      background: linear-gradient(0deg, rgba(255, 255, 255, 0) 25%, #FFFFFF 40%)
      z-index: -1

    &:before
      content: ''
      position: absolute
      bottom: 0
      left: 0
      right: 0
      height: 200px
      width: 100%
      background: linear-gradient(180deg, rgba(255, 255, 255, 0) 25%, #FFFFFF 40%)

    &-easier-bg
      background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1647607402/dog-training/img/easier-landing-mob-bg.jpg") no-repeat top center
      background-size: 100%

    &-title
      font-size: 26px
      line-height: 120%
      text-align: center
      font-weight: 800

      & span
        @include textGradient(linear-gradient(159.44deg, #FF765A 5.8%, #FF4040 90.92%))

    &-subtitle
      font-size: 20px
      line-height: 120%
      text-align: center
      font-weight: 600
      padding-top: 4px

      & span
        @include textGradient(linear-gradient(159.44deg, #FF765A 5.8%, #FF4040 90.92%))

    &-box
      position: relative
      max-width: 230px
      margin: 8px auto 16px
      left: 16px

    &-img
      width: 100%
      border-radius: 16px
      border: 7px solid $red
      box-shadow: 0 4px 16px rgba(170, 105, 91, 0.47)

    &-smile
      position: absolute
      bottom: 40px
      left: -48px
      max-width: 80px
      width: 100%
      display: flex

    &-calendar
      position: absolute
      bottom: 24px
      left: -48px
      box-shadow: 0 1.44024px 5.76098px rgba(220, 205, 192, 0.47)
      width: 92px
      height: 88px
      background: $white
      border-radius: 10px
      overflow: hidden

      &-top
        background: linear-gradient(159.44deg, #FF765A 5.8%, #FF4040 90.92%)
        font-size: 12px
        font-weight: 800
        line-height: 100%
        color: $white
        width: 100%
        text-align: center
        padding: 4px

      &-days
        font-size: 54px
        line-height: 100%
        font-weight: 400
        color: $black
        width: 100%
        height: 100%
        margin-top: 8px
        text-align: center

    &-icon
      max-width: 36px

      &:nth-child(1)
        position: absolute
        left: -64px
        bottom: 120px

      &:nth-child(2)
        position: absolute
        left: -44px
        bottom: 164px

  &__easier
    position: relative
    width: 100%
    background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1647607402/dog-training/img/easier-landing-mob-bg.jpg") top center no-repeat
    background-size: 100%

    &:after
      content: ''
      position: absolute
      top: 0
      left: 0
      right: 0
      height: 100px
      width: 100%
      background: linear-gradient(0deg, rgba(255, 255, 255, 0) 60%, #FFFFFF 100%)
      z-index: 0

    &:before
      content: ''
      position: absolute
      bottom: 0
      left: 0
      right: 0
      height: 200px
      width: 100%
      background: linear-gradient(180deg, rgba(255, 255, 255, 0) 25%, #FFFFFF 40%)

    &.labradorBg
      background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1668077479/dog-training/img/breedImg/labrador/trainingBg.jpg") top center no-repeat
      background-size: 100%

    &.labradorBgAlexRecs
      background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1676558056/dog-training/img/breedImg/labrador/puppy/land/trainingBg.jpg") top center no-repeat
      background-size: 100%

    &.pitBullBg
      background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1668085920/dog-training/img/breedImg/american-pit-bull-terrier/trainingBg.jpg") top center no-repeat
      background-size: 100%

    &.borderCollie
      background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1668085922/dog-training/img/breedImg/border-collie/trainingBg.jpg") top center no-repeat
      background-size: 100%

    &.chihuahua
      background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1668085923/dog-training/img/breedImg/chihuahua/trainingBg.jpg") top center no-repeat
      background-size: 100%

    &.cockerSpaniel
      background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1668085925/dog-training/img/breedImg/cocker-spaniel/trainingBg.jpg") top center no-repeat
      background-size: 100%

    &.cockapoo
      background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1676539675/dog-training/img/breedImg/cockapoo/trainingBg.jpg") top center no-repeat
      background-size: 100%

    &.frenchBulldog
      background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1668085926/dog-training/img/breedImg/french-bulldog/trainingBg.jpg") top center no-repeat
      background-size: 100%

    &.germanShepherdDog
      background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1676282816/dog-training/img/breedImg/german-shepherd/trainingBg.jpg") top center no-repeat
      background-size: 100%

    &.germanShepherdDogAlexRecs
      background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1676557259/dog-training/img/breedImg/german-shepherd/puppy/land/trainingBg.jpg") top center no-repeat
      background-size: 100%

    &.goldenRetriever
      background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1676282816/dog-training/img/breedImg/german-shepherd/trainingBg.jpg") top center no-repeat
      background-size: 100%

    &.jackRussellTerrier
      background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1668085931/dog-training/img/breedImg/jack-russel/trainingBg.jpg") top center no-repeat
      background-size: 100%

    &.labrador
      background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1668085933/dog-training/img/breedImg/labrador/trainingBg.jpg") top center no-repeat
      background-size: 100%

    &.shihTzu
      background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1668085934/dog-training/img/breedImg/shi-tsu/trainingBg.jpg") top center no-repeat
      background-size: 100%

    &.staffordshireBullTerrier
      background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1668085935/dog-training/img/breedImg/staffordshire-bull-terrier/trainingBg.jpg") top center no-repeat
      background-size: 100%

    &-title
      font-size: 26px
      line-height: 120%
      text-align: center
      font-weight: 800
      padding-bottom: 16px
      position: relative
      z-index: 1

    &-box
      position: relative
      max-width: 160px
      margin: 8px 8px 16px auto
      right: auto
      display: flex

    &-img
      width: 100%
      height: 100%
      border-radius: 16px
      border: 5px solid $red
      box-shadow: 0 4px 16px rgba(170, 105, 91, 0.47)

    &-smile
      position: absolute
      bottom: 8px
      left: -118px
      max-width: 80px
      width: 100%
      display: flex

    &-icon
      max-width: 36px

      &:nth-child(1)
        position: absolute
        left: -60px
        bottom: 82px
        max-width: 40px

      &:nth-child(2)
        position: absolute
        left: -180px
        bottom: 75px
        max-width: 72px

  &__try
    padding: 48px 0 32px
  &__profit
    padding: 48px 0
    max-width: 522px

    &-title
      font-size: 26px
      line-height: 120%
      text-align: center
      font-weight: 800
      margin-bottom: 16px

      & span
        @include textGradient(linear-gradient(159.44deg, #FF765A 5.8%, #FF4040 90.92%))

    &-list
      display: flex
      flex-direction: column

      &-new
        display: grid
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr))
        grid-column-gap: 16px
        grid-row-gap: 16px
      &.custom
        & .payment-page
          &__profit
            &-item
              padding: 0
              background: #FAFAFA
              border: 1px solid #E2E2E2
              border-radius: 118px 40px 40px 118px
              box-shadow: none
              & p
                font-weight: 500
                font-size: 18px
                line-height: 120%
                & .capitalize
                  text-transform: capitalize
            &-number
              width: 56px
              min-width: 56px
              height: 56px
              background: #44D14B
              border: 1.17964px solid #2E9F33
              box-shadow: 2px 4px 4px rgba(65, 207, 73, 0.38)
    &-item
      display: flex
      align-items: center
      padding: 16px
      font-size: 20px
      line-height: 120%
      color: $black
      font-weight: 800
      background: #FFFFFF
      border: 1px solid #E7E7E7
      box-sizing: border-box
      box-shadow: 0 8px 24px rgba(185, 181, 181, 0.47)
      border-radius: 16px
      margin-bottom: 16px

      &:last-child
        margin-bottom: 48px
      &.capitalize
        text-transform: capitalize

      &-box
        display: flex
        align-items: center
        padding: 16px
        width: 100%

      &-wrapp
        display: flex
        flex-direction: column
        padding: 0
        height: 100%

        &:nth-child(3)
          max-width: 100%
          margin: 0 auto
          width: 100%
          display: flex
          align-items: center
          justify-content: center

    &-number
      min-width: 55px
      height: 55px
      display: flex
      align-items: center
      justify-content: center
      font-weight: 800
      line-height: 100%
      color: $white
      font-size: 32px
      background: $gradient-green
      border-radius: 100%
      margin-right: 16px

      &.blue
        background: linear-gradient(159.44deg, #51BAF1 5.8%, #1996D2 90.92%)

    &-calendar
      box-shadow: 0 1.44024px 5.76098px rgba(220, 205, 192, 0.47)
      min-width: 123px
      height: 115px
      background: $white
      border-radius: 10px
      overflow: hidden

      &-top
        background: linear-gradient(159.44deg, #FF765A 5.8%, #FF4040 90.92%)
        font-size: 16px
        font-weight: 800
        line-height: 100%
        color: $white
        width: 100%
        text-align: center
        padding: 4px

      &-days
        font-size: 74px
        line-height: 100%
        font-weight: 800
        display: flex
        align-items: center
        justify-content: center
        color: $black
        width: 100%
        height: 70%
        margin-top: 8px
        text-align: center

      &-wrapp
        padding: 16px 32px
        display: flex
        align-items: center
        background: linear-gradient(72.45deg, #FFBD5A -36.25%, #FFEC40 72.37%)
        width: calc(100% + 32px)
        position: relative
        left: -16px

      &-text
        font-size: 18px
        line-height: 120%
        color: $black
        font-weight: 800
        padding-left: 16px

    &-content
      background: #DCEDF6
      border: 1px solid #9BD0EC
      border-top: 1px solid #E7E7E7
      padding: 16px
      border-radius: 0 0 16px 16px
      height: 100%
      display: flex
      justify-content: center
      align-items: center
      flex-direction: column

      &-text
        color: $black
        font-size: 16px
        line-height: 120%
        font-weight: 600
        text-align: center
        margin-top: 16px

      &-video
        width: 100%
        border-radius: 12px

      &-img
        &-plan
          max-width: 228px
          display: flex
          justify-content: center
          margin: 0 auto
          position: relative
          left: 8px

        &-training
          width: 100%
          padding: 40px 0

  &__care
    padding: 48px 0
    width: 100%

    &-wrapper
      width: 100%
      max-width: 504px
      margin: 0 auto

    &-title
      font-size: 24px
      line-height: 120%
      text-align: center
      font-weight: 800
      margin-bottom: 8px

    &-subtitle
      font-size: 20px
      line-height: 120%
      text-align: center
      font-weight: 600
      margin-bottom: 16px

    &-list
      display: flex
      flex-direction: column
      width: 100%

    &-item
      display: flex
      align-items: center
      padding: 16px
      font-size: 20px
      line-height: 120%
      color: $black
      font-weight: 800
      background: #FFFFFF
      border: 1px solid #E7E7E7
      box-sizing: border-box
      border-radius: 16px
      margin-bottom: 8px

      &:last-child
        margin-bottom: 48px

      &-img
        min-width: 62px
        width: 62px
        margin-right: 16px

    &-number
      min-width: 55px
      height: 55px
      display: flex
      align-items: center
      justify-content: center
      font-weight: 800
      line-height: 100%
      color: $white
      font-size: 32px
      background: $gradient-red
      border-radius: 100%
      margin-right: 16px

  &__discount
    position: relative
    display: flex
    justify-content: center
    margin-bottom: -80px

    &-desk
      display: block

      &-box
        display: flex
        flex-direction: column
        max-width: 687px
        margin: -64px auto
        padding-bottom: 64px

      &-img
        width: 100%
        object-fit: cover
        height: 552px

      &-sale
        position: absolute
        left: 0
        bottom: 45%
        display: block
        max-width: 186px

      &-title
        font-weight: 800
        font-size: 36px
        color: $black
        line-height: 130%
        padding: 0 16px 16px 32px

        & span
          @include textGradient(linear-gradient(159.44deg, #67EF65 5.8%, #27B935 90.92%))
        & .green
          @include textGradient(linear-gradient(159.44deg, #00B67A 5.8%, #00B67A 90.92%))

      &-previously
        height: 40px
        width: 100%
        position: relative
        background: $pure
        display: flex
        align-items: center

        & span
          @include textGradient(linear-gradient(159.44deg, #FF765A 5.8%, #FF4040 90.92%))

        &-box
          width: fit-content
          position: relative
          color: $black
          font-size: 18px
          line-height: 100%
          font-weight: 600
          margin-left: 32px

          &:before
            content: ''
            position: absolute
            background: #16191E
            width: 100%
            height: 1px
            bottom: 8px

    &-mob
      max-width: 414px
      width: 100%
      position: relative
      margin-top: 40px

      &-img
        width: 100%

      &-sale
        position: absolute
        left: 0
        bottom: 45%
        max-width: 186px

      &-title
        font-weight: 800
        font-size: 26px
        color: $black
        line-height: 130%
        position: absolute
        left: 44px
        bottom: 80px

        & .green
          @include textGradient(linear-gradient(159.44deg, #00B67A 5.8%, #00B67A 90.92%))

        & span
          @include textGradient(linear-gradient(159.44deg, #67EF65 5.8%, #27B935 90.92%))

      &-previously
        color: $black
        position: absolute
        left: 44px
        bottom: 40px
        font-size: 18px
        line-height: 100%
        font-weight: 600

        & span
          @include textGradient(linear-gradient(159.44deg, #FF765A 5.8%, #FF4040 90.92%))

        &:before
          content: ''
          position: absolute
          background: #16191E
          width: 100%
          height: 1px
          bottom: 8px

    &-desk
      display: none

    &-mob
      display: block

  &__what
    margin-bottom: 64px

    &-title
      font-size: 26px
      line-height: 120%
      font-weight: 800
      width: 100%
      text-align: center
      margin-bottom: 8px

    &-subtitle
      font-size: 16px
      text-align: center
      font-weight: 600
      margin-bottom: 24px

    &-img
      width: calc(100% + 16px)
      position: relative
      left: -8px
      margin-bottom: 16px

    &-text
      font-size: 16px
      text-align: center
      font-weight: 600
      margin-bottom: 16px

    &-inner
      display: flex
      align-items: center

      &-img
        max-width: 210px

    &-box
      display: flex
      flex-direction: column
      margin-bottom: 16px
      margin-left: 8px
      width: 100%
      max-width: 414px

      &-title
        font-size: 20px
        font-weight: 800
        line-height: 100%
        margin-bottom: 4px

      &-subtitle
        font-weight: 600
        line-height: 120%

    &-list
      list-style: none
      max-width: 270px
      width: 100%
      margin: 0 auto

    &-item
      font-weight: 600
      font-size: 16px
      color: #16191E
      margin-bottom: 16px
      position: relative

      &:before
        content: url('https://res.cloudinary.com/dr0cx27xo/image/upload/v1628690959/dog-training/icons/done-red.svg')
        position: absolute
        left: -28px
        top: -2px

  &__features
    margin: 16px 0 0
    display: flex
    justify-content: center

    &-title
      font-size: 20px
      line-height: 140%
      margin-bottom: 16px
      margin-top: 16px

    &-list
      margin-left: 32px
      list-style: none

    &-item
      font-weight: 600
      font-size: 16px
      color: $black
      margin-bottom: 16px
      position: relative

      &:last-child
        margin-bottom: 0

      &:before
        content: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1624963407/dog-training/icons/done-small.svg")
        position: absolute
        left: -28px
        top: -2px

      & .bigger-font
        font-size: 19px
        font-weight: 800
        padding-bottom: 8px

      &-okay
        &:before
          content: ''
          background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1647516889/dog-training/icons/okay-icon.png") center no-repeat
          background-size: 100%
          position: absolute
          width: 24px
          height: 24px
          left: -28px
          top: -2px

      &-play
        &:before
          content: ''
          background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1669392447/dog-training/icons/playicon.png") center no-repeat
          background-size: 100%
          position: absolute
          width: 24px
          height: 24px
          left: -33px
          top: -2px

      & span
        @include textGradient($gradient-red)

    &-health
      display: flex
      max-width: 400px
      width: 100%
      margin: 0 auto

      &-item
        font-size: 19px
        font-weight: 600
        color: $black
        margin-bottom: 16px
        position: relative

        &:last-child
          margin-bottom: 0

        &:before
          content: ''
          background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1647516889/dog-training/icons/okay-icon.png") center no-repeat
          background-size: 100%
          position: absolute
          width: 24px
          height: 24px
          left: -28px
          top: -4px

  &__guarantee
    background: linear-gradient(159.44deg, #67EF65 5.8%, #27B935 90.92%)
    padding: 8px 0
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.16)
    font-size: 18px
    position: relative
    color: $white
    font-weight: 800
    margin-top: 48px

    &-box
      max-width: 340px
      width: 100%
      margin: 0 auto
      position: relative

    &-img
      position: absolute
      max-width: 56px
      right: 16px
      top: -22px

    & span
      color: #ffe69b

  &__get
    padding-bottom: 48px
    overflow: hidden

    &:before
      content: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1628064914/dog-training/img/based-bg-desk.svg")
      z-index: -1
      position: absolute
      margin: 0 auto
      width: 100%
      height: 100%
      display: flex
      justify-content: center
      align-items: center
      transform: scale(0.95)

    &-third
      padding-bottom: 40px
      margin-bottom: 40px
      overflow-x: hidden

      &:before
        content: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1628064914/dog-training/img/based-bg-desk.svg")
        z-index: -1
        position: absolute
        margin: 0 auto
        width: 100%
        height: 100%
        display: flex
        justify-content: center
        align-items: center
        transform: scale(1)

      &-text
        position: relative
        z-index: 1

    &-title
      font-size: 26px
      line-height: 120%
      text-align: center
      margin: 56px 0 16px
      font-weight: 800

      & span
        @include textGradient(linear-gradient(159.44deg, #FF765A 5.8%, #FF4040 90.92%))

    &-img
      width: 100%
      display: none

      &-mob
        display: block
        position: relative
        width: 100%
        max-width: 260px
        margin: 0 auto -40px

    &-price
      position: relative

      &-second
        display: flex
        flex-direction: column
        align-items: center
        justify-content: center

      &-percents
        background: linear-gradient(159.44deg, #67EF65 5.8%, #27B935 90.92%)
        border-radius: 16px
        font-size: 12px
        font-weight: 800
        line-height: 120%
        color: white
        padding: 0 6px
        margin-right: 8px

      &-value
        font-size: 22px
        line-height: 100%
        font-weight: 400
        text-decoration: line-through

      &-box
        display: flex
        justify-content: flex-end
        align-items: center
        margin-bottom: -4px

      &-wrapper
        font-size: 54px
        font-weight: 800
        line-height: 100%
        margin: 0 0 16px
        position: relative
        display: flex
        align-items: center
        justify-content: center

      &-main
        font-size: 54px
        line-height: 120%
        position: relative

        &-second
          font-size: 36px
          line-height: 120%
          position: relative

          & span
            font-size: 24px
            font-weight: 400

      &-old-value
        font-size: 22px
        line-height: 120%
        font-weight: 400
        text-decoration: line-through

    &-feed
      display: flex
      justify-content: center
      margin-bottom: 48px

      &-text
        display: inline-block
        text-align: center
        font-weight: 700
        font-size: 16px
        color: $black
        max-width: 284px
        width: 100%
        margin: 0 auto
        position: relative

        &-red
          @include textGradient(linear-gradient(159.44deg, #FF765A 5.8%, #FF4040 90.92%))
          padding-right: 4px
          position: relative

          &:before
            content: ''
            position: absolute
            left: -64px
            top: -8px
            width: 56px
            height: 32px
            background-image: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1625752497/dog-training/icons/clients-icon.png")
            background-repeat: no-repeat
            background-size: contain

    &-btn
      @include btn-red(24px)
      max-width: 414px
      margin: 0 auto

    &-second
      &-text
        font-size: 18px
        color: #8B7867
        margin-left: 16px

      &-billed
        font-size: 14px
        color: #8B7867
        text-align: center
        margin: 24px 0

      &-list
        padding: 12px 28px
        border: 1px solid #8B7867
        border-radius: 16px
        max-width: 280px
        width: 100%
        margin: 0 auto

      &-item
        display: flex
        align-items: center
        margin-bottom: 6px

        &:last-child
          margin-bottom: 0

    &-box

      &.memberships
        &:before
          transform: scale(1.3) translateY(-110px)

      &-img
        position: relative

        &:before
          transform: translateY(-40px) scale(1.2)

  &__feed
    padding: 40px 0 56px

    &-title
      font-size: 26px
      line-height: 120%
      text-align: center
      position: relative
      margin-bottom: 16px
      font-weight: 800

      & span
        @include textGradient(linear-gradient(159.44deg, #FF765A 5.8%, #FF4040 90.92%))
        position: relative

        &:before
          content: ''
          position: absolute
          right: -36px
          width: 32px
          height: 32px
          background-image: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1625745365/dog-training/icons/dog-icon.png")
          background-size: contain
          background-repeat: no-repeat
          transform: rotate(-12deg)

    &-box
      display: block

    &-img
      width: 100%
      border-radius: 16px 16px 0 0
      margin-bottom: 16px

    &-info
      display: flex
      justify-content: space-between

      &-name
        font-weight: 800
        font-size: 20px
        margin-bottom: 8px

      &-location
        font-weight: 400
        display: flex

        &:before
          content: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1625757223/dog-training/icons/location.svg")
          margin-right: 8px

      &-box
        display: flex
        flex-direction: column
        justify-content: space-evenly
        margin-bottom: 16px

      &-tricks
        font-weight: 700
        display: flex
        align-items: center
        line-height: 100%

        & span
          font-weight: 800
          background: linear-gradient(159.44deg, #67EF65 5.8%, #27B935 90.92%)
          width: 24px
          height: 24px
          border-radius: 100%
          text-align: center
          color: $white
          line-height: 170%
          font-size: 14px
          margin-right: 4px

      &-habits
        font-weight: 700
        display: flex
        align-items: center
        line-height: 100%
        margin-bottom: 4px

        & span
          font-weight: 800
          background: linear-gradient(159.44deg, #FF765A 5.8%, #FF4040 90.92%)
          width: 24px
          height: 24px
          border-radius: 100%
          text-align: center
          color: $white
          line-height: 170%
          font-size: 14px
          margin-right: 4px

    &-text
      font-weight: 400
      font-size: 16px
      line-height: 150%

  &__trusted
    background: linear-gradient(0deg, #AAD6FF 0%, rgba(170, 214, 255, 0) 65.1%)
    width: 100%

    &-title
      color: $black
      font-weight: 800
      font-size: 26px
      line-height: 150%
      text-align: center

      & span
        color: #97C6F0
        font-size: 42px

    &-img
      max-width: 424px

    &-wrapper
      display: flex
      flex-direction: column
      align-items: center

  &__users
    margin: 24px auto
    max-width: 730px
    width: 100%

    &.default
      max-width: none

      & .payment-page__users-wrapper
        justify-content: space-between
        align-items: inherit

      & .payment-page__users-box
        flex-direction: row
        align-items: center

    &-title
      font-size: 26px
      line-height: 120%
      font-weight: 800
      text-align: center
      margin: 0 0 16px

      & span
        width: 32px
        height: 32px
        background-image: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1625758450/dog-training/icons/love.svg")
        background-repeat: no-repeat
        background-size: contain
        display: inline-block

    &-item
      background: $white
      border: 1px solid #E7E7E7
      box-shadow: 0 8px 24px rgba(185, 181, 181, 0.47)
      border-radius: 16px
      padding: 16px 8px
      margin-bottom: 16px

      &:last-child
        margin-bottom: 0

    &-avatar
      max-width: 56px
      width: 100%
      border-radius: 100%
      margin-right: 8px

    &-nickname
      font-size: 16px
      font-weight: 800

    &-box
      display: flex
      flex-direction: column
      align-items: flex-start

    &-insta
      display: block
      max-height: 28px
      margin-bottom: 4px

    &-wrapper
      display: flex
      align-items: center
      padding: 0 8px
      margin-bottom: 16px

    &-text
      font-size: 16px
      line-height: 150%
      font-weight: 400

    & .swiper-pagination-users
      display: none

      & .swiper-pagination-bullet
        background: $gradient-red

        &-active
          background: $gradient-red

  &__moneyback
    padding: 64px 0
    background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1625759533/dog-training/img/green-bg.svg") no-repeat
    background-size: cover

    &.gradient
      margin-top: -80px
      background: #D2F0D4
      position: relative
      padding: 32px 0 80px

      &:after
        content: ''
        position: absolute
        bottom: 0
        height: 64px
        width: 100%
        background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #FFFFFF 100%)

    &-third
      padding: 64px 0
      display: flex
      align-items: center
      justify-content: center
      margin: 24px 0 40px
      position: relative

      &:before
        content: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1629107090/dog-training/img/full-green-bg-mob.svg")
        position: absolute
        z-index: -1

    &-title
      font-size: 24px
      line-height: 120%
      text-align: center
      position: relative
      margin: 24px auto 16px
      font-weight: 800
      max-width: 254px

    &-wrapper
      background: $white
      border-radius: 16px
      padding: 12px
      max-width: 522px
      margin: 0 auto

    &-box
      border: 3px solid #62EB61
      border-radius: 10px
      position: relative

    &-img
      position: absolute
      bottom: -45px
      left: 43%
      max-width: 56px

      &-agress
        position: absolute
        top: -45px
        left: 43%
        max-width: 56px

    &-text
      font-size: 14px
      line-height: 130%
      font-weight: 400
      padding: 0 16px 40px

      & a
        @include textGradient(linear-gradient(159.44deg, #67EF65 5.8%, #27B935 90.92%))
        font-weight: 800
        position: relative

        &:before
          content: ''
          width: 100%
          position: absolute
          bottom: 2px
          background: linear-gradient(159.44deg, #67EF65 5.8%, #27B935 90.92%)
          height: 1px

        &:checked
          @include textGradient(linear-gradient(159.44deg, #67EF65 5.8%, #27B935 90.92%))
          font-weight: 800
          position: relative

          &:before
            content: ''
            width: 100%
            position: absolute
            bottom: 2px
            background: linear-gradient(159.44deg, #67EF65 5.8%, #27B935 90.92%)
            height: 1px

  &__footer
    width: 100%

    &-box
      display: flex
      align-items: center
      justify-content: space-between
      width: 100%
      padding: 8px 0

    &-mob
      display: flex
      flex-direction: column
      justify-content: space-between
      align-items: center

      &-list
        width: 100%
        background: #F6F6F6
        line-height: 100%

    &-desk
      display: none

    &-contact
      color: #878787
      font-size: 14px
      font-weight: 700
      line-height: 150%
      text-decoration: underline

    &-copyright
      color: #878787
      font-size: 14px
      font-weight: 400
      line-height: 130%
      width: 100%
      text-align: center

    &.black
      & .payment-page__footer-mob-list
        background: #000000

  &__accordion
    padding-top: 16px
    max-width: 589px
    margin-bottom: 64px

    &-title
      font-size: 26px
      line-height: 120%
      font-weight: 800
      text-align: center
      margin-bottom: 16px

    &-item
      border: 1px solid #8DEAFD
      border-radius: 16px
      margin-bottom: 16px

      &:last-child
        margin-bottom: 0

      &-box
        display: flex
        max-width: 90%
        width: 100%

      &-heading
        display: flex
        align-items: center
        justify-content: space-between
        line-height: 150%
        font-size: 15px
        font-weight: 700
        padding: 16px
        cursor: pointer

        & svg
          margin-right: 8px
          min-width: 24px

      &-content
        font-size: 14px

    &-arrow
      display: flex

      &:before
        content: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1628752130/dog-training/icons/faq-arrow.svg")
        width: 20px
        height: 20px
        transform: rotateZ(360deg)
        transition: $transition

    & .closed
      transition: $transition

      & .payment-page__accordion-item-content
        width: 100%
        opacity: 0
        height: 0
        transition: opacity $transition
        -webkit-transform: scaleY(0)
        -o-transform: scaleY(0)
        -ms-transform: scaleY(0)
        transform: scaleY(0)

    & .open
      transition: $transition

      & .payment-page__accordion-item-content
        width: 100%
        line-height: 150%
        height: 100%
        transition: opacity $transition
        padding: 8px 16px
        -webkit-transform: scaleY(1)
        -o-transform: scaleY(1)
        -ms-transform: scaleY(1)
        transform: scaleY(1)
        -webkit-transform-origin: top
        -o-transform-origin: top
        -ms-transform-origin: top
        transform-origin: top
        opacity: 1

      & .payment-page__accordion-arrow
        &:before
          content: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1628752130/dog-training/icons/faq-arrow-active.svg")
          transform: rotateZ(0deg)
          transition: $transition

      & .payment-page__accordion-item-heading
        padding-bottom: 4px

  &__offer
    padding-bottom: 48px
    margin-bottom: 40px
    margin-top: 40px
    position: relative

    &:before
      content: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1631259214/dog-training/img/bg-three-offers.svg")
      z-index: -1
      position: absolute
      margin: 0 auto
      width: 100%
      height: 100%
      display: flex
      justify-content: center
      align-items: center
    &-mini
      text-align: center
      font-weight: 600
      font-size: 12px
      line-height: 120%
      color: $black
      opacity: 0.7
      padding: 8px 0
    &-secure-payments
      display: flex
      align-items: center
      justify-content: center
      margin-top: 24px

      & img
        max-width: 291px

    &-new
      &-item
        &-price
          font-size: 36px
          font-weight: 800
          line-height: 100%
          display: flex
          align-items: center
          justify-content: center
          position: relative
          padding: 12px 0

    &-label
      background: #44A5FF
      padding: 8px 16px
      font-size: 18px
      line-height: 120%
      font-weight: 700
      color: $white
      text-align: center
      max-width: 300px
      margin: 0 auto 24px
      position: relative

      &:before
        content: ''
        position: absolute
        left: -9px
        width: 9px
        height: 100%
        top: 0
        background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1670499249/dog-training/icons/label.svg")
        background-position-y: -3px

      &:after
        content: ''
        position: absolute
        right: -9px
        width: 9px
        height: 100%
        top: 0
        background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1670499249/dog-training/icons/label.svg")
        background-position-y: -3px
        transform: rotate(180deg)

    &-one
      display: flex
      justify-content: center

      &-trial
        padding: 2px 8px
        font-weight: 700
        line-height: 100%
        background: $gradient-green
        border-radius: 12px
        color: $white

        &-black
          padding: 2px 8px
          font-weight: 700
          line-height: 100%
          background: $black
          border: 0.5px solid #000000
          border-radius: 12px
          color: $white
          margin-left: 4px
          position: relative

          &:before
            content: ""
            position: absolute
            top: 8px
            left: 8px
            background: #FF6050
            width: 60%
            height: 2px

        &-text
          padding-left: 8px

          & span
            font-size: 18px
            text-decoration: line-through

      &-price
        font-size: 24px
        line-height: 700

      &-item
        &-price
          font-size: 36px
          font-weight: 800
          line-height: 100%
          display: flex
          align-items: center
          justify-content: center
          position: relative
          padding: 12px 0

          & span
            font-size: 16px
            line-height: 100%
            color: $black
            font-weight: 400
            padding-left: 8px

      &-text
        font-size: 14px
        color: #8B7867
        text-align: center
        margin: 24px auto
        max-width: 800px

        &.bigBlack
          font-size: 16px
          color: $black

    &.amplitude-three-offers
      display: flex
      flex-direction: column
      align-items: center
      justify-content: center

      &:before
        transform: scale(0.86)

    &.one-offer
      display: block

      &:before
        content: ''

      & .payment-age__offer-btn
        margin: 32px auto

      & .payment-page__get-box-img
        &:before
          content: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1628064914/dog-training/img/based-bg-desk.svg")
          z-index: -1
          position: absolute
          margin: 0 auto
          width: 100%
          height: 100%
          display: flex
          justify-content: center
          align-items: center

      & .spot
        &:before
          transform: scale(1.1)
          padding-bottom: 150px

      & .payment-page__get-box-bg-off
        &:before
          transform: scale(1.1)
          margin-top: -32px

    &-title
      font-size: 26px
      line-height: 120%
      text-align: center
      margin: 40px 0 24px
      font-weight: 800
      position: relative
      z-index: 2

      &-red
        @include textGradient(linear-gradient(159.44deg, #FF765A 5.8%, #FF4040 90.92%))

      &-green
        font-weight: 800
        color: #4BD850

    &-text
      font-size: 14px
      color: #8B7867
      text-align: center
      margin: 24px auto
      max-width: 800px

      &_desk
        display: none

      &_mob
        display: block

      &.smaller
        font-size: 10px
        opacity: 0.35
        line-height: 120%
        &.blackText
          font-size: 14px
          color: rgb(52, 52, 52)
          opacity: 1


      &-link
        text-align: center
        margin: 24px auto
        max-width: 800px
        font-size: 16px
        color: $black
        text-decoration: underline

    &-subtitle
      font-size: 26px
      line-height: 120%
      text-align: center
      margin: 0 0 24px
      font-weight: 700

    &-community
      text-align: center
      font-size: 12px
      line-height: 120%
      padding-bottom: 24px
      font-weight: 600

      &.noDiscount
        padding-bottom: 8px

      &-title
        font-size: 26px
        line-height: 120%
        text-align: center
        margin: 40px 0 8px
        font-weight: 800
        position: relative
        z-index: 2

    & [type="radio"]
      &:checked + label, &:not(:checked) + label
        cursor: pointer
        display: flex
        align-items: center
        font-weight: 600
        color: $newblack
        font-size: 21px
        width: 100%
        height: 100%
        position: absolute
        left: 0
        z-index: 1

      &:checked, &:not(:checked)
        position: absolute
        display: none

      &:checked + label:before, &:not(:checked) + label:before
        content: ''
        position: absolute
        left: 8px
        top: 37%
        width: 24px
        height: 24px
        border: 1px solid #FDE1C9
        border-radius: 100%
        background: $white

      &:checked + label:before, &:checked + label:before
        content: ''
        position: absolute
        left: 8px
        top: 37%
        width: 24px
        height: 24px
        border: 1px solid #63EB61
        border-radius: 100%
        background: $white

      &:checked + label:after
        content: ''
        width: 12px
        height: 12px
        background: #63EB61
        position: absolute
        left: 14px
        top: 44%
        border-radius: 100%
        -webkit-transition: all 0.2s ease
        transition: all 0.2s ease

      &:not(:checked) + label:after
        content: ''
        width: 12px
        height: 12px
        position: absolute
        left: 5%
        top: 42.5%
        border-radius: 100%
        -webkit-transition: all 0.2s ease
        transition: all 0.2s ease
        opacity: 0
        -webkit-transform: scale(0)
        transform: scale(0)
        border: 4px solid #FFFCFA

      &:checked + label:after
        opacity: 1
        -webkit-transform: scale(1)
        transform: scale(1)

    &-item
      margin-bottom: 16px
      position: relative
      width: 100%
      display: flex
      flex-direction: column

      &:nth-child(2)
        &:before
          content: 'Most Popular'
          background: #eeccaf
          z-index: -1
          border-top-left-radius: 16px
          border-top-right-radius: 16px
          width: 100%
          height: 32px
          margin-bottom: -16px
          font-weight: 800
          font-size: 12px
          line-height: 150%
          color: #fef6ee
          text-align: center
          text-transform: uppercase

      &.active
        & .payment-page__offer-item-inner
          background: $white
          box-shadow: 0 8px 8px #F4E4D6
          border: 1px solid $white

        & .payment-page__offer-item-title
          color: #2F3D28

        & .payment-page__offer-bullet
          color: #667D5B

        & .payment-page__offer-item-price-box
          &:before
            content: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1644317733/dog-training/img/price-labe-active.svg")

        &:nth-child(2)
          &:before
            background: linear-gradient(159.44deg, #67EF65 5.8%, #27B935 90.92%)
            color: #359E34

      &-wrapper
        padding-left: 8px

      &-inner
        position: relative
        width: 100%
        background: #FDEEE1
        border: 1px solid #F2DCC9
        border-radius: 16px
        min-height: 84px
        height: 100%
        display: flex
        justify-content: space-between
        overflow: hidden
        padding: 12px

      &-title
        font-size: 20px
        font-weight: 700
        padding-left: 16px

      &-box
        display: flex
        align-items: center
        height: auto
        padding: 8px 8px 8px 32px
        width: 79%

      &-tooltip
        font-size: 13px
        line-height: 110%
        font-weight: 400
        background: #B3DFFF
        border: 1px solid #8AC7F2
        box-shadow: 0 8px 24px #F1E4D8
        border-radius: 16px
        display: flex
        align-items: center
        justify-content: center
        max-width: 245px
        width: 100%
        margin: 0 auto
        position: relative
        transition: 0.2s ease-in-out
        height: 32px
        opacity: 0
        transform: scale(0.6)
        z-index: 2

        &:before
          content: ""
          width: 16px
          height: 16px
          transform: rotate(45deg) skew(10deg, 10deg)
          top: -8.8px
          position: absolute
          display: flex
          background: #b4dfff
          z-index: 0
          border-left: 1px solid #8AC7F2
          border-top: 1px solid #8AC7F2
          border-top-left-radius: 4px

        &.tooltip-show
          transition: 0.2s ease-in-out
          height: 32px
          margin: 16px auto
          opacity: 1
          transform: scale(1)

        & img
          max-width: 16px
          margin: 0 4px 4px

        & span
          position: relative
          z-index: 1

      &-price
        display: flex
        flex-direction: column
        align-items: center
        justify-content: center
        position: relative
        height: 32px
        margin-left: 4px

        &-value
          font-size: 28px
          font-weight: 800
          line-height: 80%

          & span
            line-height: 130%
            font-size: 12px

        &-text
          font-weight: 400
          font-size: 12px
          line-height: 100%

        &-full
          font-size: 20px
          line-height: 70%
          color: #C1B6AD
          display: flex
          flex-direction: column
          justify-content: flex-end

          & span
            position: relative

            &:before
              content: ''
              background: linear-gradient(159.44deg, #FF765A 5.8%, #FF4040 90.92%)
              position: absolute
              top: 5px
              left: 0
              width: 100%
              height: 2px

        &-wrapper
          display: flex

        &-inner
          display: flex
          flex-direction: column
          padding-bottom: 4px

          & p
            &:nth-child(1)
              display: flex
              font-size: 16px
              line-height: 100%
              font-weight: 800

            &:nth-child(2)
              display: flex
              font-size: 10px
              line-height: 100%
              padding-left: 3px
              font-weight: 400

        &-safe
          text-decoration: line-through
          font-size: 12px
          line-height: 100%
          font-weight: 300
          display: flex
          justify-content: flex-end
          opacity: 0.5

        &-pages
          font-size: 10px
          line-height: 100%
          font-weight: 400
          border-radius: 12px
          width: 56px
          height: 16px
          display: flex
          align-items: center
          justify-content: center
          background: #F8EDE3
          color: #CBA077

          &.green
            background: #E1FFD2
            color: #667D5B

        &-box
          position: relative
          display: flex
          align-items: center
          justify-content: center
          width: 85px

          &:before
            content: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1644317733/dog-training/img/price-label.svg")
            width: 85px
            height: 61px
            position: absolute

          &.without-label
            padding-top: 0

    &-bullet
      color: #938989
      font-size: 12px
      line-height: 120%
      margin-bottom: 4px
      display: flex
      align-items: center

      &:before
        content: ''
        width: 4px
        height: 4px
        background: #938989
        display: flex
        border-radius: 100%
        margin: 0 8px 0 6px

      &:last-child
        margin-bottom: 0

    &-services
      &-box
        margin: 48px 0 24px
        display: flex
        align-items: center
        justify-content: center
        width: 100%

    &-img
      width: 100%

    &-people
      background: #E9E7E6
      border-radius: 8px
      padding: 8px
      display: flex
      flex-direction: column
      width: 100%
      margin: 0 auto 16px

      &-wrapp
        display: grid
        grid-template-columns: 1fr 1fr
        grid-column-gap: 8px

      &-box
        padding: 0 4px
        height: 24px
        background: #FFFFFF
        border-radius: 4px
        display: flex
        align-items: center
        justify-content: space-around
        font-weight: 600
        font-size: 12px
        line-height: 120%
        text-align: center
        position: relative
        overflow: hidden

      &-text
        font-weight: 600
        font-size: 12px
        line-height: 120%
        text-align: center
        position: relative

        &.shadow
          &:after
            content: ''
            width: 50px
            background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1678181159/dog-training/icons/linearGradientForText.png") no-repeat left
            background-size: cover
            height: 24px
            position: absolute
            right: -1px
            top: -5px

        &.title
          padding-bottom: 8px

    &-box
      &-img
        margin-bottom: 24px

    &-price
      position: relative

      &-second
        display: flex
        flex-direction: column
        align-items: center
        justify-content: center

      &-percents
        background: linear-gradient(159.44deg, #67EF65 5.8%, #27B935 90.92%)
        border-radius: 16px
        font-size: 12px
        font-weight: 800
        line-height: 120%
        color: white
        padding: 0 6px
        margin-right: 8px

      &-value
        font-size: 22px
        line-height: 100%
        font-weight: 400
        text-decoration: line-through

      &-box
        display: flex
        justify-content: flex-end
        align-items: center
        margin-bottom: -4px

      &-wrapper
        font-size: 54px
        font-weight: 800
        line-height: 100%
        margin: 0 0 16px
        position: relative
        display: flex
        align-items: center
        justify-content: center

      &-main
        font-size: 54px
        line-height: 120%
        position: relative

        &-second
          font-size: 36px
          line-height: 120%
          position: relative

          & span
            font-size: 24px
            font-weight: 400

      &-old-value
        font-size: 22px
        line-height: 120%
        font-weight: 400
        text-decoration: line-through

    &-feed
      display: flex
      justify-content: center
      margin-bottom: 48px

      &-text
        display: inline-block
        text-align: center
        font-weight: 700
        font-size: 18px
        color: $black
        max-width: 284px
        width: 100%
        margin: 0 auto
        position: relative

        &-red
          @include textGradient(linear-gradient(159.44deg, #FF765A 5.8%, #FF4040 90.92%))
          padding-right: 4px
          position: relative

          &:before
            content: ''
            position: absolute
            left: -64px
            top: -8px
            width: 56px
            height: 32px
            background-image: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1625752497/dog-training/icons/clients-icon.png")
            background-repeat: no-repeat
            background-size: contain

    &-btn
      @include btn-red(24px)
      max-width: 414px
      margin: 0 auto
      animation: pulsing 1.25s infinite cubic-bezier(0.66, 0, 0, 1)
      box-shadow: 0 0 0 rgba(255, 68, 66, 0.37)
      &:before
        content: ''
        box-shadow: 0 8px 16px rgba(255, 68, 66, 0.37)
        position: absolute
        top: 0
        left: 0
      @-webkit-keyframes pulsing
        to
          box-shadow: 0 0 0 30px rgba(232, 76, 61, 0)
      @-moz-keyframes pulsing
        to
          box-shadow: 0 0 0 30px rgba(232, 76, 61, 0)
      @-ms-keyframes pulsing
        to
          box-shadow: 0 0 0 30px rgba(232, 76, 61, 0)
      @keyframes pulsing
        to
          box-shadow: 0 0 0 30px rgba(232, 76, 61, 0)

    &-second
      &-text
        font-size: 18px
        color: #8B7867
        margin-left: 16px

      &-billed
        font-size: 14px
        color: #8B7867
        text-align: center
        margin: 8px 0 24px

      &-list
        padding: 12px 28px
        border: 1px solid #8B7867
        border-radius: 16px
        max-width: 300px
        width: 100%
        margin: 0 auto

        &.safe-payment
          margin-top: 24px

      &-item
        display: flex
        align-items: center
        margin-bottom: 6px

        &:last-child
          margin-bottom: 0

    &-off
      overflow: hidden
      background: $white
      border: 1px solid #FAEADC
      box-shadow: 0 4px 16px rgba(220, 205, 192, 0.47)
      border-radius: 16px
      margin-bottom: 16px

      &-title
        background: linear-gradient(122.6deg, #67EF65 -14.2%, #27B935 131.56%)
        color: $white
        font-weight: 800
        font-size: 20px
        padding: 4px 0
        text-align: center
        width: 100%

      &-text
        font-size: 20px
        font-weight: 600

        &.red
          color: #FF6452

        &.blue
          @include textGradient(linear-gradient(159.44deg, #51BAF1 5.8%, #1996D2 90.92%))
          font-weight: 800

        &.timer
          font-size: 30px
          line-height: 100%
          font-weight: 800
          background: #FFE4E1
          padding: 4px 8px
          border-radius: 4px
          color: #FF6452

        &-disc
          text-decoration: line-through

      &-box
        display: flex
        align-items: center
        justify-content: space-between
        padding: 16px 16px 8px

        &:last-child
          padding: 0 16px 8px

    &-health
      &:before
        margin-top: -32px !important

    &-video-wrapp
      width: 64%
      margin: 0 auto

      & .payment-page__video-frame.offer.multioffer
        margin: 0 auto 40px
        width: 100%

  &__memberships
    &-wrapp
      display: flex
      justify-content: center
      padding-bottom: 32px

  &__buy
    margin: 64px 0 80px
    position: inherit
    z-index: 2

    &-inner
      max-width: 375px
      width: 100%
      margin: 0 auto

    &-secure-payments
      display: flex
      align-items: center
      justify-content: center
      margin-top: 24px

      & img
        max-width: 291px

    &-title
      font-size: 26px
      line-height: 120%
      text-align: center
      font-weight: 800
      position: relative
      z-index: 2
      width: 100%
      max-width: 300px
      margin: 40px auto 24px

      & span
        @include textGradient(linear-gradient(159.44deg, #FF765A 5.8%, #FF4040 90.92%))

    &-text
      font-size: 14px
      color: $black
      max-width: 800px
      margin-bottom: 24px

      &:nth-child(2)
        margin-top: 16px
        margin-bottom: 0

      &-date
        font-size: 14px
        color: $black
        max-width: 400px
        margin-top: 16px

    &-subtitle
      font-size: 18px
      line-height: 120%
      text-align: center
      margin-bottom: 16px
      font-weight: 400

      & span
        @include textGradient($gradient-red)
        font-weight: 800

    &-trial
      padding: 2px 8px
      font-weight: 700
      line-height: 100%
      background: $gradient-green
      border-radius: 12px
      color: $white

      &-box
        display: flex
        justify-content: center

      &-text
        font-size: 14px
        color: $black
        text-align: center
        max-width: 800px
        margin-left: 8px

    &-price
      font-size: 36px
      font-weight: 800
      line-height: 100%
      display: flex
      align-items: center
      justify-content: center
      position: relative
      padding: 12px 0

    &-box
      position: relative

      &:before
        content: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1628064914/dog-training/img/based-bg-desk.svg")
        z-index: -1
        position: absolute
        width: 100%
        height: 100%
        display: flex
        justify-content: center
        align-items: center
        margin: 8px auto 0
        transform: scale(0.72)

    &-btn
      @include btn-red(24px)
      max-width: 440px
      margin: 0 auto

    &-img
      width: 100%
      display: none

      &-mob
        display: block
        position: relative
        width: 100%
        max-width: 260px
        margin: 0 auto 8px

    &-multiselect
      display: grid
      grid-template-columns: 1fr 1fr 1fr
      grid-column-gap: 16px
      padding-bottom: 8px

      & [type="radio"]
        &:checked + label, &:not(:checked) + label
          cursor: pointer
          display: flex
          align-items: center
          font-weight: 600
          color: $newblack
          font-size: 21px
          width: 100%
          height: 100%
          position: absolute
          left: 0
          z-index: 1

        &:checked, &:not(:checked)
          position: absolute
          display: none
          content: ''
          left: 8px
          top: 37%
          width: 24px
          height: 24px
          border: 1px solid #63EB61
          border-radius: 100%
          background: $white
          opacity: 1
          -webkit-transform: scale(1)
          transform: scale(1)

      &-btn
        height: 56px
        background: #FFF6EE
        border: 1px solid #FFDDBF
        border-radius: 16px
        position: relative
        display: flex
        align-items: center
        justify-content: center
        font-weight: 700
        line-height: 120%
        font-size: 18px

        &.active
          color: $white
          background: #FCA558
          border: 1px solid #EE9647
          border-radius: 16px

      &-title
        font-weight: 800
        line-height: 110%
        font-size: 20px
        text-align: center
        width: 100%
        margin-bottom: 16px

      &-low
        max-width: 260px
        width: 100%
        font-size: 12px
        margin-bottom: 16px
        position: relative

        &:after
          content: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1655963978/dog-training/img/amazing-arrow.svg")
          position: absolute
          bottom: 0
          right: -32px

        & span
          display: none

        &.amazing
          & span
            position: relative
            font-weight: 700
            color: #EE9647
            display: inline-block

  &__training
    padding: 48px 0 24px

    &-img
      display: flex
      max-width: 175px
      width: 100%
      margin: -32px auto 0

    &-wrapper
      background: $pure
      border-radius: 16px
      padding: 16px
      max-width: 440px

    &-title
      font-size: 22px
      font-weight: 700
      line-height: 120%
      padding: 8px 24px 16px
      text-align: center

    &-text
      font-size: 16px
      line-height: 120%
      padding-bottom: 8px

      & span
        font-weight: 700

  &__condition
    background: #FFF3EE
    border: 1px solid #F2C9C7
    border-radius: 16px
    padding: 8px 24px
    display: flex
    flex-direction: column
    justify-content: center
    align-items: center
    max-width: 504px
    width: 100%
    margin: 16px auto 0

    &-title
      text-align: center
      font-weight: 800
      font-size: 20px
      line-height: 110%
      max-width: 222px
      width: 100%
      margin: 4px auto

    &-subtitle
      font-weight: 400
      font-size: 16px
      line-height: 120%
      text-align: center
      margin: 4px auto 0

  &__video
    max-width: 800px
    margin: 40px auto 0

    & iframe
      border-radius: 8px

    &.minus
      margin: 40px auto -80px

    &-overlay-top
      width: 100%
      height: 60px
      position: absolute
      z-index: 0

    &-overlay-bottom
      width: 100px
      height: 40px
      position: absolute
      bottom: 0
      right: 0
      z-index: 0

    &-title
      font-weight: 800
      font-size: 28px
      line-height: 100%
      padding-bottom: 32px
      text-align: center

    &-thumb
      width: 100%
      position: relative
      z-index: 100
      cursor: pointer
      object-fit: cover
      height: 100%

    &-container
      height: 100%
      position: relative
      border-radius: 8px
      overflow: hidden

    &-unmute
      position: absolute
      top: 16px
      right: 16px
      cursor: pointer
      z-index: 100
      display: none
      filter: drop-shadow(0px 10px 13px rgba(0, 0, 0, 0.25))
      backdrop-filter: blur(2px)
      overflow: hidden
      border-radius: 16px

      &.active
        display: block

    &-mute
      position: absolute
      top: 16px
      right: 16px
      cursor: pointer
      z-index: 100
      display: none
      filter: drop-shadow(0px 10px 13px rgba(0, 0, 0, 0.25))
      backdrop-filter: blur(2px)
      overflow: hidden
      border-radius: 16px

      &.active
        display: block

    &-subtitle
      font-weight: 600
      font-size: 18px
      line-height: 120%
      padding-bottom: 8px
      text-align: center

    &-frame
      position: relative
      width: 100%
      height: 437px
      display: flex
      align-items: center
      justify-content: center

      & .back-0
        @include videoFrameBorderStyle
        width: 100%
        height: 100%
        position: relative
        z-index: 4
        background: whitesmoke

      & .back-1
        @include videoFrameBorderStyle
        width: 90%
        height: 100%
        position: absolute
        bottom: -6px
        z-index: 3
        backdrop-filter: blur(2px)

      & .back-2
        @include videoFrameBorderStyle
        width: 80%
        height: 100%
        position: absolute
        bottom: -12px
        z-index: 2
        backdrop-filter: blur(2px)

      & .back-3
        @include videoFrameBorderStyle
        width: 70%
        height: 100%
        position: absolute
        bottom: -18px
        z-index: 1
        backdrop-filter: blur(2px)

      &.offer
        height: 207px
        margin: 16px 0 40px

      &.multioffer
        max-width: 525px
        width: 100%

      &-inner
        position: relative

      &-discount
        text-align: center
        background: #4BD850
        width: 88%
        margin: 0 auto -16px
        border-radius: 8px 8px 0 0
        color: white
        font-weight: 700
        line-height: 160%

    &-features
      display: grid
      grid-template-columns: 1fr 1fr 1fr
      grid-column-gap: 16px
      margin: 40px 0 64px

      &-item
        background: rgba(238, 238, 238, 0.9)
        border-radius: 7px
        display: flex
        flex-direction: column
        align-items: center
        justify-content: center
        height: 56px

        &-title
          font-size: 28px
          font-weight: 800
          line-height: 100%

        &-text
          font-size: 12px
          line-height: 100%

  &__enjoy
    background: $black url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1675000117/dog-training/img/enjoy-bg.png") center no-repeat
    background-size: cover
    padding: 48px 0 0

    &-title
      font-size: 26px
      font-weight: 800
      line-height: 120%
      text-align: center
      padding-bottom: 16px
      color: $white
      position: relative
      z-index: 1

      & span
        @include textGradient($gradient-red)

    &-inner
      position: relative
      padding-bottom: 128px

    &-classes
      padding-bottom: 24px

      &-wrapp
        padding-bottom: 32px

        &:before
          content: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1675006690/dog-training/img/red-figure.svg")
          z-index: 0
          position: absolute
          margin: 0 auto
          width: 100%
          height: 100%
          display: flex
          justify-content: center
          align-items: center

      & .swiper-wrapper
        transition-timing-function: linear

      &-title
        text-align: center
        padding-bottom: 8px
        font-size: 19px
        font-weight: 600
        color: $white
        position: relative
        z-index: 1

      &-item
        & img
          width: 100%

    &-list
      list-style: disc
      max-width: 285px
      width: 100%
      margin: 0 auto
      position: relative
      z-index: 1

      &-title
        font-size: 26px
        font-weight: 800
        line-height: 120%
        text-align: center
        padding: 16px 0
        color: $white
        position: relative

    &-item
      padding-bottom: 8px
      font-size: 19px
      font-weight: 600
      color: $white

      &:last-of-type
        padding-bottom: 0

    &-btn
      @include btn-green(24px)
      max-width: 414px
      margin: 0 auto 48px

      & .js-scroll
        width: 100%
        height: 100%
        position: absolute
        z-index: 1

.dog-name
  &-classic
    background: none !important
    -webkit-text-fill-color: $black !important

  &-apostrophe
    background: none !important
    -webkit-text-fill-color: $black !important

.black-text
  font-size: 16px
  color: rgb(52, 52, 52)

  & a
    color: rgb(52, 52, 52)
    text-decoration: underline

#solid-payment-form-container
  width: 100%
  height: 100%


.multi-changes
  & .payment-page__summary
    &-hr
      max-width: 180px
      width: 100%
      height: 2px
      background: linear-gradient(159.44deg, #FF765A 5.8%, #FF4040 90.92%)
      border-radius: 23px
      margin: 0 auto 16px
    &-pre
      font-weight: 500
      font-size: 16px
      line-height: 120%
      text-align: center
    &-title
      padding: 8px 0
      & .red
        @include textGradient($gradient-red)
    &-subtitle
      font-weight: 500
      font-size: 18px
      line-height: 120%
      text-align: center
      padding-bottom: 16px
      & .red
        @include textGradient($gradient-red)
        font-weight: 800

  & .payment-page__profit
    &-list
      max-width: 375px
      width: 100%
      margin: 0 auto

    &-item
      background: #F2F5F6
      border-radius: 100px 24px 24px 100px
      border: none
      padding: 0
      box-shadow: none

      & p
        font-size: 16px
        font-weight: 500
        line-height: 110%
        padding: 8px 0
        max-width: 253px

  & .payment-page__profit-number
    min-width: 48px
    height: 48px
    background: #00B67A
    border: 1px solid #009765
    box-shadow: 2px 4px 4px rgba(0, 182, 122, 0.3)

  & .payment-page__behavior
    padding: 80px 0
    max-width: $maxContainer
    width: 100%
    margin: 0 auto
    position: relative

    &:before
      content: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1654527514/dog-training/img/behavior-bg.svg")
      z-index: -1
      position: absolute
      width: 100%
      height: 100%
      display: flex
      justify-content: center
      align-items: center
      margin: -80px auto
      transform: scale(1.07)

    &-title
      font-size: 26px
      line-height: 120%
      text-align: center
      font-weight: 800
      position: relative
      z-index: 2
      width: 100%

      &-wrapp
        padding-bottom: 16px

    &-pretitle
      width: 100%
      text-align: center

    &-box
      position: relative
      display: flex
      justify-content: center

    &-img
      font-size: 0
      max-width: 264px
      width: 100%
      margin: 0 auto

    &-list
      background: $black
      border-radius: 16px
      padding: 16px

    &-item
      display: flex
      align-items: center
      margin-bottom: 16px
      color: $white

      & span
        font-weight: 800
        padding: 0 4px

      &:last-of-type
        margin-bottom: 0

      &-number
        width: 24px
        height: 24px
        min-width: 24px
        background: $white
        color: $black
        font-size: 14px
        font-weight: 800
        display: flex
        justify-content: center
        align-items: center
        border-radius: 100%
        margin-right: 8px

    &-brands
      &-box
        display: flex
        align-items: center
        justify-content: center
        padding: 16px 0

  & .payment-page__archived
    max-width: 567px
    width: 100%
    margin: 0 auto 16px
    &-wrapp
      background: #FFFFFF
      border: 1px solid #E7E7E7
      box-shadow: 0 4px 24px rgba(0, 0, 0, 0.08)
      border-radius: 16px
      padding: 16px

    &-title
      font-size: 26px
      line-height: 120%
      text-align: center
      font-weight: 800
      position: relative
      z-index: 2
      width: 100%
      padding-bottom: 16px
      & span
        @include textGradient($gradient-red)

    &-img
      width: 100%
      margin: 8px auto 0

      &-wrapp
        background: #FFF6EE
        border: 1px solid #FFEFE1
        border-radius: 8px
        padding: 8px
        display: flex
        align-items: center
        justify-content: center
        margin: 0 auto
    &-text
      font-weight: 400
      font-size: 16px
      &-wrapp
        display: flex
        justify-content: space-between
        align-items: center
        margin: 16px 0
    &-name
      font-weight: 800
      font-size: 20px
      line-height: 120%
    &-problem
      font-weight: 800
      font-size: 14px
      line-height: 120%
      & span
        @include textGradient($gradient-red)
    &-disclamer
      font-weight: 400
      font-size: 12px
      line-height: 120%
      text-align: center
      letter-spacing: -0.02em
      margin-top: 16px
      & b
        font-weight: 900
  & .payment-page__users
    background: #00B67A
    padding: 24px 0
    &-title
      color: $white
    &-wrapper
      flex-direction: column
      padding-left: 0
      & .payment-page__users-stars
        margin-bottom: 16px
    &-box
      justify-content: space-between
      & h5
        font-weight: 800
        font-size: 14px
        line-height: 10%
      & p
        font-size: 12px
    &-item
      padding: 16px
  & .payment-page__accordion
    & .open
      background: $white
      border: 1px solid #C9EADF
    &-item
      background: rgba(0, 182, 122, 0.15)
      border: 1px solid #C9EADF
      margin-bottom: 8px
    &-arrow
      &:before
        content: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1681821716/dog-training/icons/arrow-accordion.svg")
  & .payment-page__offer
    &-title
      margin: 40px 0 8px
      & span
        @include textGradient(linear-gradient(159.44deg, #16191E 5.8%, #16191E 90.92%))
    &-mini
      text-align: center
      font-weight: 600
      font-size: 12px
      line-height: 120%
      color: $black
      opacity: 0.7
      padding: 0 0 8px
    &-item
      &.active
        &:nth-child(2):before
          background: #00B67A
          color: $white
          letter-spacing: 0.03em
        & .payment-page__offer-item-price-box:before
          content: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1681822347/dog-training/icons/price-bg.svg")
    & [type=radio]:checked + label:before, .payment-page__offer [type=radio]:checked + label:before
      border: 1px solid #00B67A
    & [type=radio]:checked + label:after
      background: #00B67A

  & .payment-page__moneyback
    &-wrapper
      max-width: 414px
    &-third
      &:before
        content: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1681823064/dog-training/img/moneyback-green-2.svg")
    &-box
      border: 3px solid #4DCCA2
    &-img
      filter: drop-shadow(0 4px 4px #3c725a45)
    &-text
      & a
        @include textGradient(linear-gradient(159.44deg, #30B578 5.8%, #30B578 90.92%))
        &:before
          background: linear-gradient(159.44deg, #30B578 5.8%, #30B578 90.92%)
  & .payment-page__experts
    max-width: 567px
    width: 100%
    margin: 0 auto 48px
    &-title
      font-size: 26px
      line-height: 120%
      text-align: center
      font-weight: 800
      position: relative
      z-index: 2
      width: 100%
      padding-bottom: 16px
      &-small
        font-size: 18px
        line-height: 120%
        text-align: center
        font-weight: 800
        margin-bottom: 8px
    &-inner
      background: #E2EDFF
      border: 1px solid #BBD6FF
      border-radius: 16px
      padding: 16px
    &-img
      width: 100%
      margin-bottom: 8px
    &-text
      margin-bottom: 16px
      font-weight: 500
      font-size: 16px
      line-height: 120%
      text-align: center
    &-list
      background: #FFFFFF
      border: 1px solid #DBE9FF
      border-radius: 8px
      list-style: disc
      padding: 8px
    &-item
      margin-left: 24px
      font-weight: 500
      line-height: 120%
  & .payment-page__obedience
    padding: 0
    & img
      width: 100%
  & .payment-popup__checkout-select .select-active
    background: rgba(0, 182, 122, 0.17)
    border: 1px solid #009F6B
    box-shadow: inset 0 0 1px rgba(88, 171, 74, 0.27)
  & .payment-page__inside
    margin: 24px 0 40px
    position: relative
    & .no-width
      padding: 0
      max-width: 375px
    &-inner
      position: relative
      display: flex
      flex-direction: column
      align-items: center
      justify-content: center
      max-width: 375px
      width: 100%
      margin: 0 auto
      &:before
        content: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1681890436/dog-training/img/back-red.svg")
        position: absolute
        z-index: 0
    &-title
      color: $white
      font-size: 26px
      line-height: 120%
      text-align: center
      font-weight: 800
      position: relative
      z-index: 2
      width: 100%
      padding-bottom: 16px
    &-box
      display: flex
      justify-content: center
      margin-bottom: 8px
      position: relative
    &-img
      &-plan
        width: 100%
        position: relative
        bottom: -2px
      &-box
        border: 2px solid #F48C89
        border-radius: 8px
        overflow: hidden
        margin-right: 8px
        position: relative
      &-courses
        width: 100%
        margin-bottom: 24px
        position: relative
    &-access
      background: linear-gradient(90.47deg, #FFE76B 0%, #FCE156 101.19%)
      box-shadow: 0 3px 0 rgba(0, 0, 0, 0.25), 0 4px 6px rgba(253, 226, 88, 0.29)
      text-shadow: 1px 1px 0 rgba(31, 31, 31, 0.1)
      font-size: 20px
      line-height: 120%
      width: 100%
      text-align: center
      padding: 8px
      position: relative
      margin-bottom: 24px
      & b
        font-weight: 800
    &-includes
      background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1681892115/dog-training/img/black-pattern.jpg") top center repeat
      background-size: cover
      margin-top: -80px
      &-title
        color: $white
        font-size: 26px
        line-height: 120%
        text-align: center
        font-weight: 800
        position: relative
        z-index: 2
        width: 100%
        padding-bottom: 16px
        padding-top: 120px
      &-wrapp
        display: flex
        flex-direction: column
        justify-content: center
        align-items: center
        padding-bottom: 40px
        & p
          color: $white
          text-align: center
          & b
            font-weight: 800
        & svg
          margin: 16px 0


@media (min-width: 415px)
  .payment-page
    &__plan
      max-width: none
      padding-top: 40px

    &__try
      padding: 0 0 64px

    &__care
      &-title
        font-size: 36px

    &__get
      background: none

      &-third
        overflow-x: initial

      &-title
        font-size: 36px

      &-btn
        max-width: 343px
        margin: 0 auto

    &__discount
      margin-bottom: -40px
      &-title
        bottom: 96px

    &__features
      margin-bottom: 0

      &-list
        max-width: 270px
        margin-top: 24px

    &__feed
      &-title
        font-size: 36px

        & span
          &:before
            right: -48px
            width: 44px
            height: 44px

      &-box
        max-width: 557px
        width: 100%
        margin: 0 auto

    &__users
      &-title
        font-size: 36px
        margin-bottom: 32px

        & span
          position: relative
          width: 32px
          height: 32px
          top: 8px

      &-list
        display: flex
        flex-direction: column
        align-items: flex-start

      &-item
        max-width: 557px
        width: 100%

        &:last-child
          margin-right: 0

    &__moneyback
      width: 100%

      &-img
        left: 46%

    &__what
      &-wrapper
        flex-direction: column
        margin-bottom: 40px

    &__get
      overflow: initial

      &-third
        padding-bottom: 48px
        margin-bottom: 88px

@media (min-width: 567px)
  .payment-page
    &__summary
      &-title
        font-size: 48px

    &__profit
      padding: 48px 0

      &-title
        font-size: 48px

      &-calendar
        &-wrapp
          width: 100%
          left: auto
          border-radius: 16px

        &-text
          font-size: 18px
          line-height: 120%
          color: $black
          font-weight: 800
          padding-left: 16px

    &__care
      &-title
        font-size: 48px

    &__offer
      & .one-offer
        & .payment-page__get-box-bg
          &:before
            transform: translateY(0)

      &.amplitude-three-offers
        &:before
          width: auto
          height: auto
          padding-top: 72px
          transform: scale(1)

      &-title
        font-size: 48px

      &-elements
        max-width: 343px
        width: 100%
        margin: 0 auto

      &-box
        &-img
          display: flex
          justify-content: center

          & img
            margin: 0 auto 24px

      &-services
        &-box
          margin: 24px 0

      &-health
        width: 100%

        &:before
          transform: scale(1.1)
          margin-top: -40px

      &-people
        width: 64%

    &__plan
      background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1627553412/dog-training/img/selling-plan-bg-desk.jpg") no-repeat bottom center
      background-size: cover
      padding-bottom: 56px

      &:before
        background: linear-gradient(180deg, rgba(255, 255, 255, 0) 25%, #FFFFFF 40%)
        height: 400px

      &-easier-bg
        background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1647516411/dog-training/img/easier-landing-bg.jpg") no-repeat bottom center
        background-size: 100%

      &-top
        margin-top: -48px

      &-title
        font-size: 36px
        margin: 16px 0 24px

      &-subtitle
        margin-bottom: 24px

      &-icon
        max-width: 56px

        &:nth-child(1)
          left: -112px
          bottom: auto
          top: 110px

        &:nth-child(2)
          left: -64px
          bottom: auto
          top: 56px

      &-img
        border-width: 14px
        border-radius: 24px

      &-box
        max-width: 390px

      &-calendar
        width: 156px
        height: 146px
        left: -80px
        bottom: 40px

        &-top
          height: 32px
          font-size: 20px
          line-height: 120%

        &-days
          font-size: 94px
          height: auto
          display: flex
          align-items: center
          justify-content: center

      &-smile
        bottom: 80px
        left: -64px
        max-width: 120px

    &__easier
      background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1647516411/dog-training/img/easier-landing-bg.jpg") no-repeat bottom center
      background-size: cover
      padding-bottom: 56px
      max-width: none

      &:before
        background: linear-gradient(180deg, rgba(255, 255, 255, 0) 25%, #FFFFFF 40%)
        height: 400px

      &.labradorBg
        background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1668085934/dog-training/img/breedImg/labrador/trainingBgDesk.jpg") top center no-repeat
        background-size: 100%

      &.labradorBgAlexRecs
        background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1676558056/dog-training/img/breedImg/labrador/puppy/land/trainingBgDesk.jpg") top center no-repeat
        background-size: 100%

      &.pitBullBg
        background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1668085920/dog-training/img/breedImg/american-pit-bull-terrier/trainingBgDesk.jpg") no-repeat bottom center
        background-size: 100%

      &.borderCollie
        background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1668085922/dog-training/img/breedImg/border-collie/trainingBgDesk.jpg") no-repeat bottom center
        background-size: 100%

      &.chihuahua
        background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1668085923/dog-training/img/breedImg/chihuahua/trainingBgDesk.jpg") no-repeat bottom center
        background-size: 100%

      &.cockerSpaniel
        background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1668085924/dog-training/img/breedImg/cocker-spaniel/trainingBgDesk.jpg") no-repeat bottom center
        background-size: 100%

      &.cockapoo
        background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1676539675/dog-training/img/breedImg/cockapoo/trainingBgDesk.jpg") top center no-repeat
        background-size: 100%

      &.frenchBulldog
        background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1668085928/dog-training/img/breedImg/french-bulldog/trainingBgDesk.jpg") top center no-repeat
        background-size: 100%

      &.germanShepherdDog
        background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1676282816/dog-training/img/breedImg/german-shepherd/trainingBgDesk.jpg") top center no-repeat
        background-size: 100%

      &.germanShepherdDogAlexRecs
        background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1676557262/dog-training/img/breedImg/german-shepherd/puppy/land/trainingBgDesk.jpg") top center no-repeat
        background-size: 100%

      &.goldenRetriever
        background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1668085930/dog-training/img/breedImg/golden-retriever/trainingBgDesk.jpg") top center no-repeat
        background-size: 100%

      &.jackRussellTerrier
        background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1668085932/dog-training/img/breedImg/jack-russel/trainingBgDesk.jpg") top center no-repeat
        background-size: 100%

      &.labrador
        background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1668085934/dog-training/img/breedImg/labrador/trainingBgDesk.jpg") top center no-repeat
        background-size: 100%

      &.shihTzu
        background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1668085935/dog-training/img/breedImg/shi-tsu/trainingBgDesk.jpg") top center no-repeat
        background-size: 100%

      &.staffordshireBullTerrier
        background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1668085936/dog-training/img/breedImg/staffordshire-bull-terrier/trainingBgDesk.jpg") top center no-repeat
        background-size: 100%

      &-title
        font-size: 48px
        margin: 16px 0 24px

      &-subtitle
        margin-bottom: 24px

      &-icon
        max-width: 56px

        &:nth-child(1)
          left: -112px
          bottom: auto
          top: 110px

        &:nth-child(2)
          left: -64px
          bottom: auto
          top: 56px

      &-img
        border-width: 14px
        border-radius: 24px

      &-box
        max-width: 390px

      &-calendar
        width: 156px
        height: 146px
        left: -80px
        bottom: 40px

        &-top
          height: 32px
          font-size: 20px
          line-height: 120%

        &-days
          font-size: 94px
          height: auto
          display: flex
          align-items: center
          justify-content: center

      &-smile
        bottom: 80px
        left: -64px
        max-width: 120px

    &__what
      margin-bottom: 88px

      &-title
        font-size: 48px
        margin-bottom: 16px

      &-wrapper
        display: flex
        align-items: center

      &-text
        display: none

      &-list
        max-width: 330px

    &__get
      margin-bottom: 40px

      &:before
        transform: scale(1.25)

      &-third
        &:before
          transform: scale(1.1)

      &-img
        display: block
        padding: 16px 0 32px

        &-mob
          display: none

    &__discount

      &-desk
        display: block

        &-sale
          max-width: none
          width: 100%
          left: auto
          bottom: 34%
          margin: 0 auto

          & img
            width: 300px
            height: 300px

          &-box
            display: flex
            max-width: 880px
            width: 100%
            margin: 0 auto

      &-mob
        display: none

    &__moneyback
      &.gradient
        background: none
        padding: 128px 0
        display: flex
        align-items: center
        justify-content: center

        &:before
          transform: scale(1)
          content: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1629106593/dog-training/img/full-green-bg.svg")
          position: absolute
          z-index: -1

        &:after
          content: none

      &-third
        &:before
          transform: scale(1)
          content: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1629106593/dog-training/img/full-green-bg.svg")

    &__features
      &-list
        max-width: 330px
        margin-left: 88px

    &__users
      &-item
        max-width: 557px

      &-list
        align-items: center

    &__accordion
      &-title
        font-size: 48px

    &__based
      position: relative
      display: flex
      align-items: center
      justify-content: center
      width: 100%
      margin-top: 0

      &:before
        margin: -16px 0
        transform: scale(1)

      &-new
        &:before
          transform: scale(1.5)

      &-inner
        max-width: 504px
        width: 100%
        margin: 0 auto

      &-title
        font-size: 48px
        margin: 56px auto 24px
        max-width: none

      &-list
        display: flex

      &-box
        display: flex
        align-items: center
        justify-content: space-between

      &-graph
        max-width: 504px
        width: 100%
        height: 504px
        margin: 0 auto 24px

        &-text
          font-size: 20px
          font-weight: 700
          margin-left: 16px
          display: flex

        & svg
          margin-top: -40px

    &__footer
      &-contact
        width: auto

    &__video
      &-title
        font-size: 48px

      &-frame
        &.offer
          margin: 16px auto 40px
          width: 64%

        &-discount
          width: 57%
          margin: 0 auto -24px

    &__trusted
      &-img
        width: 100%

    &__report
      &-title
        font-size: 38px
        justify-content: flex-start

    &__behavior
      &-title
        font-size: 48px

    &__bonuses
      &-title
        font-size: 48px
        padding-bottom: 24px

    &__trained
      &-title
        font-size: 48px

      &-img
        max-width: 400px

    &__enjoy
      &-title
        font-size: 48px

      &-classes
        &-wrapp
          &:before
            transform: scale(1.5)

@media (max-width: 768px)
  .payment-page
    &__behavior
      &-slider
        width: calc(100% + 32px)
        position: relative
        left: -16px
        padding-left: 16px

  .multi-changes
    & .payment-page
      &__offer
        &.second-offer:before
          transform: scale(0.66)

@media (min-width: 769px)
  .payment-page
    &__discount
      &-desk
        &-sale
          bottom: 25%

          & img
            width: 400px
            height: 400px

    &__easier
      &-box
        margin: 0 auto
        right: -24px

      &-icon
        &:nth-child(1)
          left: -148px
          bottom: auto
          top: 150px
          max-width: 48px

        &:nth-child(2)
          left: -84px

    &__plan
      &-title
        font-size: 48px

    &__users
      &-list
        flex-direction: row
        max-width: none
        align-items: flex-start

      &-item
        margin-right: 32px

      &-title
        font-size: 48px

        & span
          width: 40px
          height: 40px

    &__get
      margin: 0 0 88px

      &:before
        content: none

      &-title
        font-size: 48px

      &-inner
        display: flex
        align-items: center

      &-box
        margin: 0 auto

    &__feed
      &-title
        font-size: 48px

        & span
          &:before
            right: -64px
            width: 56px
            height: 56px

    &__what
      margin-bottom: 64px

    &__summary
      &-inner
        display: flex

    &__obedience
      display: flex
      flex-direction: column
      justify-content: center

    &__based
      &-title
        &-small
          font-size: 32px

    &__profit
      &-list
        &-new
          grid-template-columns: repeat(auto-fill, minmax(350px, 1fr))

      &-item
        &-wrapp
          &:nth-child(3)
            grid-column: 3/1
            max-width: 50%
            margin: 0 auto
            width: 100%
            display: flex
            align-items: center
            justify-content: center

    &__video
      &-frame
        &.offer
          height: 280px
          margin: 24px auto 40px

    &__footer
      background: #F5E2D2

      &-mob
        display: none

      &-desk
        display: flex
        justify-content: space-between
        align-items: center
        background: #F6F6F6
        height: 40px

        &-list
          display: flex
          align-items: center

        &-item
          line-height: 100%
          margin-right: 32px

      &-copyright
        width: auto

      &.black
        & .payment-page__footer-desk
          background: #000000

    &__problems
      &-text
        max-width: 300px

@media (min-width: 1080px)
  .payment-page
    &__offer
      &:before
        content: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1632994504/dog-training/img/three-offers-bg.svg")
        padding-top: 88px

      &-title
        font-size: 48px
        line-height: 120%
        text-align: center
        margin: 40px 0 0
        font-weight: 800
        display: block

      &-subtitle
        display: none

      &-inner
        display: flex
        align-items: center
        width: 100%

      &-elements
        min-width: 343px
        margin-top: 48px

      &-services
        &-box
          display: none

      &-btn
        margin-bottom: 24px

      &-img
        width: 120%
        position: relative
        left: -10%

      &-text
        &_desk
          display: block

        &_mob
          display: none

      &-label
        margin: 24px auto 0

    &__get
      &-img
        padding: 40px 0 16px

      &-box
        width: 80%

    &__profit
      &-list
        &-new
          grid-template-columns: repeat(auto-fill, minmax(300px, 1fr))

      &-item
        &-wrapp
          &:nth-child(3)
            grid-column: auto
            max-width: inherit
            width: 100%

    &__video
      &-frame
        &.offer
          height: 300px

      &-overlay-bottom
        width: 130px
        height: 60px

@media (max-width: $mobile)
  .payment-page
    &__based
      &-graph
        &-health
          &-box
            padding: 0 8px
            margin-top: -24px

    &__get
      &-third
        overflow: initial

    &__agressive
      & .payment-page__based
        &:before
          transform: scale(1.25)

    &__video
      max-width: $mobile

      &-frame
        height: 207px

    &__report
      &-obedience
        &-numbers
          margin-top: -32px

@media (max-width: $mobile320)
  .payment-page
    &__discount
      &-mob
        &-sale
          max-width: 156px

        &-title
          font-size: 22px
          left: 36px
          bottom: 64px

        &-previously
          left: 36px
          bottom: 32px

    &__plan
      &-icon
        &:nth-child(1)
          left: -56px

    &__feed
      &-title
        max-width: 260px
        width: 100%
        margin: 0 auto 16px

    &__what
      width: 100%

      &-inner
        &-img
          max-width: 160px
          margin-right: 16px

      &-subtitle
        max-width: 260px
        width: 100%
        margin: 0 auto 16px
        line-height: 130%

      &-list
        padding-left: 24px

    &__get
      overflow: initial

      &:before
        transform: scale(0.85)

    &__offer
      &-off
        &-text
          font-size: 18px

      &-people
        &-text
          font-size: 11px

          &.title
            font-size: 12px

@media (min-width: 320px)
  .payment-page
    &__based
      &-feature
        display: none

    &__offer
      &-video-wrapp
        width: 100%
