@import "abstracts/variables"
@import "abstracts/mixins"
@import "base/reset"
@import "base/base"
@import "utilities"

@import "components/header-quiz"
@import "components/email-autocomplete"
@import "components/breed-autocomplete"
@import "components/pay-attention"
@import "components/scratch-card"
@import "components/interactive-course-list"

body
  display: flex
  flex-direction: column
  justify-content: space-between
  min-height: 100dvh

.quiz
  &_common
    &_container
      display: flex
      position: relative
    &_gragh
      display: flex
      flex-direction: column
      z-index: 2
      background: linear-gradient(270deg, rgba(255, 255, 255, 0.00) 0%, #FFF 100%)
      padding-right: 70px
      padding-left: 16px
      margin-left: -16px
      &_box
        display: flex
        gap: 8px
        justify-content: center
      &_line
        position: relative
        width: 8px
        flex-shrink: 0
        align-self: stretch
        border-radius: 8px
        background: linear-gradient(180deg, #FF4040 0%, #FFD326 52.6%, #30DF4C 100%)
        z-index: 1002
        &_dot
          position: absolute
          top: 15%
          width: 16px
          height: 16px
          border-radius: 100%
          box-shadow: 0 0 0 4px white
          z-index: 3
          background-color: #FF6B38
          animation: moveThumb 3.2s infinite ease-in-out
          @keyframes moveThumb
            0%
              top: 15%
            25%
              background-color: #FFC828
            50%
              top: 85%
              background-color: #69D741
            75%
              background-color: #FFC828
            100%
              top: 15%
              background-color: #FF6B38
      &_text
        color: $black
        align-self: stretch
        text-align: center
        font-family: "Nunito Sans"
        font-size: 10px
        font-style: normal
        font-weight: 800
        line-height: 120%
        text-transform: uppercase
        margin-bottom: 8px
    &_picture
      margin: 0 auto
      position: absolute
      width: 343px
      height: 385px
      flex-shrink: 0
      &-position-center
        left: 50%
        transform: translateX(-50%)
      &-angel-demon
        width: 385px
        right: -16px
      &-attribute
        position: absolute
        left: 0
        z-index: 3
        opacity: 0
        transition: opacity 0.1s linear
        &.angel, &.demon
          animation-name: fadeIn
          animation-duration: 1.6s
          animation-iteration-count: infinite
        &.angel
          animation-direction: alternate
        &.demon
          animation-direction: alternate-reverse
          @keyframes fadeIn
            0%
              opacity: 0
            60%
              opacity: 0
            100%
              opacity: 1
      &-content
        width: 100%
        height: 100%
      &-dog
        position: absolute
        object-fit: cover
        right: 45px
        top: -35px
        width: 131px
        height: 132px
      &-bicycle
        position: absolute
        object-fit: cover
        right: 26px
        bottom: 140px
        width: 76px
        height: 55px
        transform: rotate(-12deg)
      &-squirrel
        object-fit: cover
        position: absolute
        left: 46px
        width: 92px
        height: 68px
        top: 70px
        transform: rotate(19deg)

  &__satisfy
    &-wrapp
      position: fixed
      bottom: 16px
      width: calc(100% - 32px)
      max-width: 414px
      &.shifted-up
        bottom: 88px !important
    &-list
      display: grid
      grid-template-columns: repeat(5, 1fr)
      height: 80px
      &-item
        border: 1px solid #CACED5
        display: flex
        align-items: center
        justify-content: center
        background: #F3F4F5
        cursor: pointer
        &:first-of-type
          border-radius: 8px 0 0 8px
        &:last-of-type
          border-radius: 0 8px 8px 0
        & img
          max-width: 24px
          width: 100%
          &.bigger
            max-width: 32px
    &-box
      margin-top: 12px
      display: flex
      justify-content: space-between
      align-items: center
    &-text
        font-size: 14px
        font-weight: 500
        line-height: 140%
  & .icons-bigger
    & .quiz__multiple-item-icon
      width: 48px
      height: 48px
    & .quiz__answer-item img
      width: 48px
      height: 48px
  & .button-list
    grid-template-columns: 1fr
    &.left-align
      & .quiz__answer-item
        justify-content: left
    &.quiz
      &__dog
        grid-row-gap: 8px
      &__multiple
        grid-row-gap: 8px

    & .quiz
      &__multiple
        &-item
          height: 80px
      &__answer
        &-item
          height: 80px
          &.full
            grid-column: auto
          &-grid
            height: 80px
            & img
              width: 32px
              margin-right: 16px
  &__body
    &.betterFunnel
      font-family: $nunitoSans!important
      & .quiz__title, .quiz__mid-title
        font-size: 22px
        font-weight: 800
      & .quiz__answer-item .quiz__multiple-item
        font-size: 20px
      & .quiz__reviews-loading-title
        padding-bottom: 16px
      & .quiz__result-input
        &::placeholder
          color: rgba(52, 52, 52, 0.5)
          font-weight: 400
          font-size: 16px
    &.zero-question
      background: $white url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1675000117/dog-training/img/enjoy-bg.png") center center no-repeat
      background-size: cover
      & .header-quiz
        display: none
      & .quiz-footer.first-step
        position: fixed
        z-index: 300
    &-newdes // delete &-newdes when pc_newdes split is closed
      background-repeat: no-repeat
      background-position: top center
      background-size: auto
      background-attachment: fixed
      .header-quiz__logo-newdes
        backface-visibility: hidden
        -webkit-font-smoothing: subpixel-antialiased
        width: 111px
        height: auto
        display: none
      .quiz__index--newdes
        .quiz__title,
        .quiz__subtitle
          text-align: center
        .quiz__title
          font-weight: 700
        .quiz__subtitle
          margin-top: 16px
          margin-bottom: 12px
          font-size: 14px
      .quiz__image-newdes
        width: 100%
      .quiz__question-newdes
        margin-top: 16px
        margin-bottom: 12px
        font-size: 16px
        font-weight: 600
        line-height: 120%
        text-align: center
      .quiz__dog
        &-newdes
          display: grid
          grid-template-columns: 1fr 1fr
          gap: 16px
          .quiz__dog-item
            padding: 12px
            height: 100%
            width: 100%
            justify-content: space-between
            color: #fff
            &-box
              gap: 4px
            &-text
              font-size: 16px
              font-weight: 600
              line-height: 110%
              letter-spacing: 0%
              color: #fff
            &-age
              font-size: 14px
              font-weight: 300
              line-height: 130%
              letter-spacing: 0%
              color: #fff
            &-wrapp
              background: #18191A
              border: none
              margin: 0
              height: 64px
              min-height: 64px
              position: relative
              &.quiz__active
                background: #000
                box-shadow: none
                .quiz__dog-item-icon
                  background: #18191A
                  color: #313131
            &-icon
              width: 32px
              height: 32px
              background: #313131
              border-radius: 100%
              display: flex
              align-items: center
              justify-content: center
              svg
                width: 19px
                height: 19px
                margin: 0
      .quiz__gender-newdes
        .quiz
          &__title
            font-size: 24px
            font-weight: 600
            line-height: 100%
        .quiz__dog
          &.text-center .quiz__dog-item
            justify-content: space-between
          &.button-list
              grid-template-columns: 1fr 1fr
          &-item
            background: #18191A
            border: none
            padding: 12px
            p
              color: #fff
              font-size: 20px
              font-weight: 400
              line-height: 100%
            &-icon--gender
              display: flex
              width: 56px
              height: 56px
              justify-content: center
              align-items: center
              border-radius: 100px
              background: #313131
              svg
                width: 35px
                height: 35px
                margin: 0
                max-width: auto
            &.quiz__active
              background: #000
              box-shadow: none
              .quiz__dog-item-icon--gender
                background: #18191A
                color: #313131
      .quiz__breed-newdes
        .quiz
          &__title
            font-size: 24px
            font-weight: 600
            line-height: 120%
          &__subtitle
            font-size: 16px
            font-weight: 300
            line-height: 130%
            margin: 8px 0 16px
            text-align: left
          &__breeds
            justify-content: flex-start
            gap: 8px
            padding: 0
            margin-bottom: 16px
            &-item
              background: #18191A
              border: none
              padding: 11px 12px
              font-size: 16px
              font-weight: 300
              line-height: 100%
              margin: 0
              &.quiz__active
                background: #000
                box-shadow: none
          &__datalist
            &-label
              color: #fff
              font-size: 14px
              font-weight: 300
              line-height: 130%
              margin-bottom: 2px
            &-items
              border-radius: 12px
              border: 1px solid #404751
              background: #18191A
              height: 48px
              padding: 0 16px
              color: #fff
              font-size: 14px
              font-weight: 300
              line-height: 130%
              &.active
                border-color: #1998CD
              &::placeholder
                color: #C5C6CB
                font-size: 14px
                font-weight: 300
                opacity: 1
            &-arrow
              position: static
              &-box
                position: absolute
                right: 0
                top: 0
                height: 100%
                width: 44px
                display: flex
                align-items: center
                justify-content: center
                &::before
                  content: none
              path
                fill: #C5C6CB
            .breed-autocomplete
              background: #18191A
              border-color: #1998CD
              &__item
                font-size: 16px
                font-weight: 300
                line-height: 130%
                border-color: #404751
                &:hover
                  background: #404751
      .quiz__owners-newdes
        .quiz__quote
          &-title
            font-weight: 600
            margin: 24px 0 8px
            color: #32b8f0
            text-align: left
          &-subtitle
            font-size: 16px
            font-weight: 300
            line-height: 130%
            padding: 0
            margin: 0
            text-align: left
            b
              font-weight: 600
          &-apost
            path
              fill: #32b8f0
          &-text
            font-size: 20px
            font-weight: 500
            line-height: 141%
          &-hr
            border-color: #3D3D3D
            margin: 12px 0
          &-logo
            height: 21px
          &-team-text
            font-size: 14px
            font-weight: 300
            margin: 2px 0 0 4px
          &-wrapp
            background: #18191A
            border: none
            padding: 16px
            border-radius: 4px
            max-width: 100%
            margin-top: 48px
        .quiz__owners-academic
          &-wrapp
            max-width: 100%
          &-title
            font-size: 14px
            font-weight: 300
            line-height: 130%
            b
              font-weight: 600
      .quiz__motivation-newdes
        .quiz
          &__title
            font-size: 24px
            font-weight: 600
            line-height: 120%
            margin: 16px 0 0
          &__subtitle
            font-size: 16px
            font-weight: 300
            line-height: 130%
            margin: 8px 0 16px
            text-align: left
          &__multiple
            grid-row-gap: 12px
            &-item
              background: #18191A
              border-color: #18191A
              color: #fff
              font-size: 20px
              font-style: normal
              font-weight: 400
              line-height: 100%
            &-dot
              border: 1px solid #4A4C4F
              background: #313131
            &-checked
              box-shadow: none
              border-color: #1998CD
            &-checked
              .quiz__multiple-dot
                border-color: #1998CD
                &::before
                  background: #1998CD
                  border-radius: 100%
                  width: 12px
                  height: 12px
      .quiz__goal-newdes
        .quiz
          &__title
            color: #16191E
            margin: 16px 0 0
            font-size: 24px
            font-style: normal
            font-weight: 600
            line-height: 120%
          &__subtitle
            color: #16191E
            margin: 8px 0 16px
            font-size: 16px
            font-style: normal
            font-weight: 300
            line-height: 130%
      .quiz__personal-goal--newdes
        .quiz
          &__title
            font-size: 24px
            font-weight: 600
            line-height: 120%
          &__subtitle
            font-size: 16px
            font-weight: 300
            line-height: 130%
            b
              font-weight: 600

      &--1, &--2, &--3, &--4, &--5, &--6, &--7
        .legal__wrapp
          line-height: 120%
          font-weight: 400
          a
            color: #479AF3
        .header-quiz
          &__inner-bg
            background: none
          &__logo
            display: block
        .quiz-footer__text
          color: #fff
          line-height: 120%
          font-weight: 400
        .quiz__subtitle
          font-weight: 300
          line-height: 120%
          text-align: left
        .quiz__title
          font-weight: 700
          margin: 16px 0 16px
          text-align: left
          letter-spacing: 0
        &.quiz__body
          font-family: "Outfit", sans-serif !important
          letter-spacing: 0

      &--1
        background-color: #25272B
        background-image: url("https://images.paw-champ.com/pc/images/backgrounds/newdes-background-1.png")
      &--2
        background-color: #25272B
        background-image: url("https://images.paw-champ.com/pc/images/backgrounds/newdes-background-2.png")
      &--3
        background-color: #25272B
        background-image: url("https://images.paw-champ.com/pc/images/backgrounds/newdes-background-3.png")
      &--4
        background-color: #4f5154
        background-image: url("https://images.paw-champ.com/pc/images/backgrounds/newdes-background-4.png")
      &--5
        background-color: #8a8c8e
        background-image: url("https://images.paw-champ.com/pc/images/backgrounds/newdes-background-5.png")
      &--6
        background-color: #dcdcdd
        background-image: url("https://images.paw-champ.com/pc/images/backgrounds/newdes-background-6.png")
      &--7
        background-color: #fdffff
        background-image: url("https://images.paw-champ.com/pc/images/backgrounds/newdes-background-7.png")

      &--1, &--2, &--3, &--4, &--5
        color: #fff
        .quiz__title
          color: #fff
        .header-quiz
          &__logo
            display: none
            &-newdes
              display: block
              height: 32px
        .header-quiz__back
          color: #fff
        .progressbar__line
          svg
            path
              &:first-child
                stroke: #404751
              &:last-child
                stroke: #549CCA
        .quiz__btn
          border-radius: 35px
          border: 1px solid #1998CD
          background: #078DC5
          box-shadow: none
          font-family: "Outfit", sans-serif
          font-size: 20px
          font-style: normal
          font-weight: 400
          line-height: 100%
          &:before,
          span:after
            content: none
          span,
          span:first-child
            display: none
          &:disabled,
          &:disabled:active,
          &.quiz__btn-active:disabled,
          &.quiz__btn-active:disabled:active
            background: #078DC5
            border: 1px solid #1998CD
            border-radius: 35px
          &:hover,
          &:disabled:hover
            background: #078DC5
          &:active,
          &.quiz__btn-active:active
            background: #0579A9
            border: 1px solid #0579A9
          &:disabled
            opacity: 0.3
            span
              display: none
          span
            display: none
      &--6
        .progressbar__line
          svg
            path
              &:first-child
                stroke: #F3F4F5

  &-footer
    display: flex
    justify-content: center
    flex-direction: column
    align-items: center
    width: 100%
    position: fixed
    bottom: 0
    z-index: 0
    &.spanish
      position: relative
    &__wrapper
      display: flex
      flex-direction: column
      justify-content: center
      width: 100%
      align-items: center
      padding-top: 6px

    &__text
      font-size: 12px
      line-height: 150%
      text-align: center
      color: $black
      margin-bottom: 8px

    &__list
      display: flex
      padding-bottom: 6px
      flex-wrap: wrap
      justify-content: center

    &__item
      &-separator
        margin: 0 8px
        color: $black

      &-link
        font-size: 12px
        font-weight: 700
        line-height: 140%
        text-align: center
        color: $black
        text-decoration: underline

    &__main
      font-size: 12px
      font-weight: 400
      line-height: 140%
      text-align: center
      color: $black
      background: $quizGrey
      width: 100%
      padding: 9px 0

  &__breeds
    display: flex
    justify-content: center
    flex-wrap: wrap
    padding-bottom: 8px
    &-item
      background: $quizGrey
      border: 1px solid $quizGrey
      border-radius: 32px
      font-weight: 600
      padding: 8px 16px
      margin-right: 8px
      margin-bottom: 8px
      cursor: pointer
      &.active
        transition: $transition
        background: $sand-contrast
        border: 1px solid #F3C195
      &.br-8
        border-radius: 8px
  &__know-these
    &-box
      display: flex
      align-items: center
      border: 3px solid #99DEF4
      box-sizing: border-box
      border-radius: 8px
      padding: 16px
      margin-top: 32px

    &-icon
      max-width: 52px
      min-width: 52px
      padding-right: 16px

    &-text
      font-size: 17px
      font-weight: 400

    &-item
      height: 72px
      font-size: 21px
      font-weight: 600
      border: 1px solid $quizGrey
      background: $quizGrey
      box-sizing: border-box
      border-radius: 8px
      display: flex
      align-items: center
      justify-content: center
      cursor: pointer
      transition: $transition
      text-align: center

      &-img
        max-width: 32px
        width: 100%
        margin-left: 8px

  &__intro
    width: 100%
    max-width: 414px
    margin: 0 auto

    &-short
      height: 100%
      display: flex
      flex-direction: column
      justify-content: space-between

      &-subtitle
        font-size: 18px
        font-weight: 700
        line-height: 140%
        margin: 16px 0 8px

      &-box
        display: flex
        align-items: center
        justify-content: space-between

      &-text
        font-size: 14px
        font-weight: 400
        line-height: 110%
        margin-right: 24px

      &-footer
        margin-bottom: 16px
        color: #16191E
        opacity: 0.5
        text-align: center
        width: 100%
        font-size: 12px

    &-wrapper
      display: flex
      flex-direction: column
      align-items: center
      background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1629988639/dog-training/img/intro-quiz-bg-mob.svg") no-repeat top center
      background-size: contain
      margin-bottom: 16px

    &-title
      @include textGradient(linear-gradient(159.44deg, #FF765A 5.8%, #FF4040 90.92%))
      font-size: 24px
      line-height: 110%
      text-align: center
      font-weight: 800
      max-width: 300px
      width: 100%
      margin: 0 auto 8px

    &-health
      background: #FCDBBE
      padding: 8px 16px
      border-radius: 8px
      display: flex
      flex-direction: column
      align-items: center
      justify-content: center
      border: 1px solid #FCDBBE
      margin-top: 16px

      &-title
        font-weight: 700
        font-size: 20px
        line-height: 120%
        max-width: 317px
        width: 100%
        margin: 0 auto
        text-align: center

      &-subtitle
        font-size: 16px
        line-height: 120%
        text-align: center
        max-width: 300px
        width: 100%
        margin: 4px auto

    &-applica
      height: 80vh
      display: flex
      flex-direction: column
      justify-content: space-between

      &-wrapp
        &:before
          content: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1655903471/dog-training/img/applica-intro-bg.svg")
          z-index: -1
          position: absolute
          width: 100%
          height: 100%
          display: flex
          justify-content: center
          align-items: center
          overflow: hidden

      &-box
        display: flex
        flex-direction: column
        justify-content: center
        align-items: center
        position: relative

      &-title
        font-weight: 800
        font-size: 24px
        line-height: 120%
        text-align: center
        margin-bottom: 8px

      &-subtitle
        font-size: 16px
        font-weight: 600
        line-height: 120%
        text-align: center
        margin-bottom: 8px

      &-estimate
        font-size: 16px
        font-weight: 800
        color: #58C660
        margin-bottom: 24px
        text-align: center

      &-img
        height: 35vh
        margin-bottom: 24px

      &-btn
        @include btn-red(24px)
        margin-bottom: 40px

      &-arrow
        -moz-animation: bounce 3s infinite
        -webkit-animation: bounce 3s infinite
        animation: bounce 3s infinite

        &-box
          display: flex
          justify-content: center
          align-items: center
          height: 40px

        @keyframes bounce
          0%
            -moz-transform: translateY(0px)
            -ms-transform: translateY(0px)
            -webkit-transform: translateY(0px)
            transform: translateY(0px)
          50%
            -moz-transform: translateY(-15px)
            -ms-transform: translateY(-15px)
            -webkit-transform: translateY(-15px)
            transform: translateY(-15px)
          100%
            -moz-transform: translateY(0px)
            -ms-transform: translateY(0px)
            -webkit-transform: translateY(0px)
            transform: translateY(0px)

    &-search
      padding: 8px 16px
      border-radius: 8px
      display: flex
      flex-direction: column
      align-items: center
      justify-content: center
      background: #FFECDB
      border: 1px solid #FCDBBE
      margin-top: 16px

      &-title
        font-weight: 700
        font-size: 20px
        line-height: 120%
        max-width: 317px
        width: 100%
        margin: 0 auto
        text-align: center

      &-subtitle
        font-size: 16px
        font-weight: 600
        line-height: 120%
        text-align: center
        margin: 4px auto 6px

    &-icon
      margin-bottom: 16px

    &-dog
      height: 300px

    &-btn
      @include btn-red(24px)
      max-width: 414px
      width: 100%
      margin: 0 auto 12px

    &-copyright
      text-align: center
      width: 100%
      font-size: 12px
      font-weight: 600
      color: #16191E
      opacity: 0.5

  &__preview
    &-wrapper
      display: flex
      flex-direction: column
      align-items: center

      &:before
        content: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1628064914/dog-training/img/based-bg-desk.svg")
        z-index: -1
        position: absolute
        width: 100%
        height: 120%
        display: flex
        justify-content: center
        align-items: flex-end
        overflow: hidden

    &-title
      font-weight: 700
      font-size: 22px
      line-height: 120%
      text-align: center
      padding-bottom: 8px

    &-subtitle
      font-weight: 600
      font-size: 15px
      line-height: 120%
      text-align: center
      padding-bottom: 16px

    &-img
      &-desk
        display: none

      &-mob
        max-width: 300px
        width: 100%
        margin: 0 auto 16px

    &-btn
      @include btn-red(24px)

  &__index
    display: none
    height: 100%
    flex-direction: column
    width: 100%
    max-width: 446px
    justify-content: flex-start
    &-active
      display: flex
    &.profile
      & .nameInsertSelector
        text-transform: capitalize
    &.graphic
      &-bars
        .quiz__graph-svg
          overflow: visible
      & .nameInsertSelector
        text-transform: capitalize
    &.quiz__result
      & .nameInsertSelector
        text-transform: capitalize
    & .bottom-space
        padding-bottom: 180px
    //for split pc_trust
    &.trust
      & .quiz__reviews-slider
        padding-bottom: 16px
        &-item-name
          color: $gray-700
          font-weight: 700
          &-wrapp
            padding-top: 0

  &__box
    height: auto
    padding-bottom: 128px
    &--magic
      display: flex
      flex-direction: column
    &-narrowed-container
      margin: 0 8px
    &.more-space
      padding-bottom: 164px
  &__content-container
    max-width: 305px
    margin: 0 auto
    &--small
      max-width: 270px
  &__dog-overreact-item
    display: grid
    grid-template-columns: 84px 170px
    grid-column-gap: 16px
    margin-bottom: 12px
  &__dog-overreact-image
    width: 78px
    height: 72px
    margin-left: auto
  &__dog-overreact-item-content
    margin-right: auto
    &-title
      font-size: 20px
      font-weight: 800
    &-list
      list-style: disc
      padding-left: 25px
      margin-top: 3px
  &__hint
    background: #E7FEE1
    border: 1px solid #AEED9C
    border-radius: 8px
    visibility: hidden
    opacity: 0
    height: 0
    min-height: 0
    transform: translateY(16px)
    padding: 8px
    &.active
      margin-bottom: 16px
      opacity: 1
      visibility: visible
      transition: all 0.3s ease-in-out
      transform: translateY(0)
      height: 100%
      & .quiz__hint-title
        display: block
      & .quiz__hint-content
        display: block
    &-title
      font-size: 14px
      font-weight: 800
      margin-bottom: 4px
      color: #385D45
      display: none
    &-content
      font-size: 12px
      font-weight: 400
      color: #334A3C
      line-height: 140%
      display: none
  &__mention
    background: #FCFEE1
    border: 1px solid #EDE09C
    border-radius: 8px
    padding: 8px
    margin-bottom: 16px
    &-title
      font-size: 14px
      font-weight: 800
      color: #312C25
    &-content
      font-size: 12px
      font-weight: 400
      color: #312C25
      line-height: 140%
  &__radio
    display: flex
    flex-direction: column
    margin-bottom: 16px
    &-hint
      &-item
        display: flex
        align-items: center
        position: relative
        height: 80px
        padding: 0 16px
        margin: 0 0 8px
        &:last-of-type
          margin-bottom: 0
        &-input
          cursor: pointer
          border-radius: 8px
          border: 1px solid #F3F4F5
          position: absolute
          width: 100%
          height: 100%
          appearance: none
          z-index: 0
          left: 0
          top: 0
          background: #F3F4F5
          margin: 0
          transition: $transition
          &:checked
            background: #E7FEE1
            border: 1px solid #AEED9C
            box-shadow: 0 4px 4px 0 #DEF5D9
            transition: $transition
            & ~ .quiz__radio-hint-item-img-box
              &:before
                background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1720770250/paw-champ/img/quiz/feeding-bg-active.svg")
        &-icon
          max-width: 40px
          width: 100%
          margin-right: 16px
          position: relative
          pointer-events: none
          z-index: 1
        &-img
          max-width: 70px
          width: 100%
          position: relative
          &-box
            position: relative
            width: 82px
            height: 70px
            display: flex
            align-items: center
            justify-content: center
            margin: 0 8px 0 12px
            &:before
              content: ''
              position: absolute
              background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1720770250/paw-champ/img/quiz/feeding-bg-default.svg") center center no-repeat
              top: 0
              width: 82px
              height: 70px
              background-size: contain
              z-index: 0

        &-text
          position: relative
          z-index: 1
          font-size: 20px
          font-weight: 600
          pointer-events: none
          width: 100%
          &.text-center
            text-align: center
          &.left-margin
            margin-left: 16px
    &-box
      display: flex
      margin-bottom: 12px

      &:last-child
        margin-bottom: 0

    & [type="radio"]
      &:checked, &:not(:checked)
        position: absolute

      &:checked + label, &:not(:checked) + label
        position: relative
        padding-left: 32px
        cursor: pointer
        height: 24px
        display: flex
        align-items: center
        font-weight: 600
        color: $newblack
        font-size: 21px

      &:checked + label:before, &:not(:checked) + label:before
        content: ''
        position: absolute
        left: 0
        top: 0
        width: 24px
        height: 24px
        border: 1px solid #FDE1C9
        border-radius: 100%
        background: $white

      &:checked + label:after
        content: ''
        width: 16px
        height: 16px
        background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1630071366/dog-training/icons/radio.svg")
        position: absolute
        top: 4px
        left: 4px
        border-radius: 100%
        -webkit-transition: all 0.2s ease
        transition: all 0.2s ease
        -webkit-box-shadow: 0 0 0 4px rgba(253, 225, 201, 1)
        -moz-box-shadow: 0 0 0 4px rgba(253, 225, 201, 1)
        box-shadow: 0 0 0 4px rgba(253, 225, 201, 1)

      &:not(:checked) + label:after
        content: ''
        width: 16px
        height: 16px
        background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1630071366/dog-training/icons/radio.svg")
        position: absolute
        top: 4px
        left: 4px
        border-radius: 100%
        -webkit-transition: all 0.2s ease
        transition: all 0.2s ease
        opacity: 0
        -webkit-transform: scale(0)
        transform: scale(0)
        border: 4px solid #FFFCFA

      &:checked + label:after
        opacity: 1
        -webkit-transform: scale(1)
        transform: scale(1)

  &__wrapper
    display: flex
    flex-direction: column
    justify-content: space-between
    height: 85%
  &__header
    text-align: center
    color: $black
    font-size: 26px
    font-style: normal
    font-weight: 800
    line-height: 100%
    margin: 16px 0
    &.mt-40
      margin-top: 40px

  &__title
    font-size: 24px
    line-height: 120%
    color: $black
    font-weight: 700
    text-align: center
    margin: 16px 0
    letter-spacing: -0.02em
    &--is-red, b
      @include textGradient($gradient-red)
      display: inline
    &.left
      text-align: left
    &.red
      color: #F54A4B
    &.red-gradient
      @include textGradient($gradient-red)
      width: 100%
    &-s
      font-size: 22px
      line-height: 120%
      color: $black
      font-weight: 800
      text-align: center
      margin: 16px 0 0
      letter-spacing: -0.02em
    & .default
      background: none
      -webkit-background-clip: text
      -webkit-text-fill-color: $black
    & .capitalize
      text-transform: capitalize
    &-under
      text-align: center
      max-width: 320px
      margin: 24px auto 0
      font-weight: 600
      &.big
        font-size: 22px
        font-weight: 400
        line-height: 120%
      &.fat
        font-size: 22px
        font-weight: 800
        background: #FFED4D
        padding: 8px
      &-a
        text-align: center
        max-width: 275px
        margin: 16px auto
        font-weight: 600
  &__subtitle
    font-weight: 400
    text-align: center
    margin-top: -8px
    margin-bottom: 16px
    &:empty
      display: none
    & b
      font-weight: 700
    &--is-s
      font-size: 14px
    & .bold
      font-weight: 700
  &__dog
    display: grid
    grid-row-gap: 16px
    grid-column-gap: 16px
    grid-template-columns: 1fr 1fr
    &.deutch
      & .quiz__dog-item-text
        font-size: 18px!important
    &.spanish
      & .quiz__dog-item-text
        font-size: 18px!important
    &.text-center
      & .quiz__dog-item
        justify-content: center
        text-align: center
    &.more-age
      & .quiz__dog-item
        flex-direction: column
        background: none
        padding: 0
      & .quiz__dog-item-text
        font-size: 20px
        font-weight: 700

    &-img
      overflow: hidden
      border-radius: 8px
      border: 1px solid #F6DBC2
      width: 100%
      margin-bottom: 32px
      &-big
        width: 100%
        &-wrapp
          max-width: 222px
          width: 100%
          margin: 0 auto 32px
      &-box
        padding-bottom: 16px
        display: flex
        justify-content: center
        align-items: center

    &-text
      font-size: 16px
      padding: 16px
      line-height: 140%
      text-align: center

      & a
        color: $black
        text-decoration: underline

        &:hover
          opacity: 0.8

    &-one
      grid-template-columns: 1fr
      & a
        width: 100%
        height: 100%
        display: flex
        align-items: center
        justify-content: flex-start
      & img
        width: 76px
        max-height: 70px
        object-fit: contain
        object-position: 25% center
        position: relative
      & .quiz__dog-item
        background: transparent
        border: none
        &-box
          padding-bottom: 0
          align-items: flex-start
        &-wrapp
          min-height: 80px
          height: 80px
          display: flex
          align-items: center
          justify-content: center
          margin-bottom: 16px
        &-age
          font-size: 16px
        &-text
          font-weight: 700
    &-three
      grid-template-columns: 1fr
    &-item
      display: flex
      flex-direction: revert
      align-items: center
      justify-content: flex-start
      padding: 0 16px
      height: 80px
      width: 100%
      border: 1px solid $quizGrey
      background: $quizGrey
      box-sizing: border-box
      border-radius: 8px
      cursor: pointer
      transition: $transition
      @at-root .newFun .more-age &
        height: auto
        border: none
      &-wrapp
        @at-root .newFun &
          position: relative
          overflow: hidden
        &-img
          max-width: 56px
          min-width: 56px
          height: 50px
          width: 100%
          object-fit: contain
          &-problems
            margin-right: 16px
      &.crate
        & img
          max-width: 40px
          margin-right: 16px

      &-wrapp
        min-height: 188px
        border: 1px solid $quizGrey
        background: $quizGrey
        box-sizing: border-box
        border-radius: 8px
        display: flex
        flex-direction: column
        justify-content: space-between
        align-items: center
        cursor: pointer
        transition: $transition
        &.quiz__active
          & .quiz__dog-item
            background: transparent

        &.defaultBtn
          min-height: auto
          &.quiz__active
            & .yes
              background: #E7FFE0
            & .no
              background: #E7FFE0
        &.puppyDog
          &.quiz__active
            transition: none

      &-age
        font-size: 12px
        line-height: 150%
        font-weight: 500
        color: $black

      &-img
        max-width: 40px
        &.love
          max-width: 48px
          margin-right: 4px
      & svg
        max-width: 40px
        margin-right: 16px
      &-text
        font-size: 21px
        line-height: 110%
        font-weight: 400
        text-align: center
        color: $black
        @at-root .newFun .more-age &
          color: $white
          font-size: 17px
          font-weight: 700
        &.is-small
          font-size: 18px !important
        &.spanish
          text-align: left
        &.smaller
          font-size: 19px!important
        &.left
          text-align: left
        &.center
          text-align: center
          width: 100%
      &-full
        margin-top: 16px
      &-age
        @at-root .newFun &
          color: $white
          font-weight: 400
        &-img
          width: 100%
          max-width: 128px
          margin: 0 auto
          @at-root .newFun &
            position: absolute
            left: 50%
            bottom: 56px
            transform: translateX(-50%)
            z-index: 5
            width: auto
          &-background
            max-width: 100% !important
            position: absolute
            left: 50%
            bottom: 55px
            transform: translateX(-50%) scaleX(-1)
            width: 117px
            height: 102px
            @at-root .newFun & path
              transition: fill .3s ease
            @at-root .newFun .quiz__active & path
              fill: #CAEEFB
          &-box
            display: flex
            align-items: center
            justify-content: center
      &-box
        display: flex
        flex-direction: column
        justify-content: center
        align-items: center
        padding-bottom: 8px
        @at-root .newFun &
          position: absolute
          left: 0
          bottom: 0
          width: 100%
          align-items: flex-start
          color: $white
          background-color: $blue
          padding: 6px 12px
          svg
            position: absolute
            right: 17px
            top: 50%
            transform: translateY(-50%)
            margin-right: 0
            @media screen and (max-width: 400px)
              right: 10px
      &-pic
        width: 100%
        background: #F9F9F9
        padding-top: 8px
        & img
          height: 145px
          width: auto
      &-inn
        display: flex
        align-items: center
        justify-content: center
        height: 56px
        width: 100%
        background: #F4F4F4
    &-two
      grid-template-columns: repeat(2, 1fr)
      & .quiz__dog-item
        display: flex
        flex-direction: column
        justify-content: center
        text-align: center
        overflow: hidden
        height: 112px
        & svg
          margin-bottom: 12px
          margin-right: 0
          max-width: 48px
        &-text
          font-size: 20px

  &__select
    display: flex
    margin-bottom: 16px

    &-wrapper
      position: relative

    &-label
      font-size: 14px
      color: $input-title

    &-items
      font-size: 21px
      font-weight: 400
      border: 1px solid #FDE2C9
      background: $white
      height: 54px
      border-radius: 8px
      padding: 12px 16px
      color: $black
      width: 100%
      outline: none
      line-height: 24px
      -webkit-appearance: none
      position: relative

      &::-webkit-calendar-picker-indicator
        display: none !important

      &::placeholder
        color: $input-title

    &-arrow
      position: absolute
      right: 16px
      top: 24px
      pointer-events: none

    &-box
      display: flex
      flex-direction: column
      margin-right: 16px
      width: 100%

      &:last-child
        margin-right: 0

  &__datalist
    display: flex
    margin-bottom: 16px
    &-wrapper
      position: relative

    &-label
      font-size: 14px
      color: $black

    &-items
      font-size: 21px
      font-weight: 400
      background: #FCFCFC
      border: 1px solid #EFEFEF
      height: 54px
      border-radius: 8px
      padding: 12px 16px
      color: $black
      width: 100%
      outline: none
      line-height: 24px
      -webkit-appearance: none
      position: relative
      background-image: url("data:image/svg+xmlutf8,<svg fill='black' height='24' viewBox='0 0 24 24' width='24' xmlns='http://www.w3.org/2000/svg'><path d='M7 10l5 5 5-5z'/><path d='M0 0h24v24H0z' fill='none'/></svg>")
      background-repeat: no-repeat
      background-position-x: 97%
      background-position-y: 50%
      &::placeholder
        color: #4E4E4E
        opacity: 0.3
      &.active
        border: 1px solid #16191E
    &-arrow
      pointer-events: none
      position: absolute
      right: 16px
      top: 24px
      &-box
        &:before
          content: ""
          position: absolute
          right: 1px
          top: 1px
          width: 32px
          background: #FCFCFC
          height: calc(100% - 2px)
          border-radius: 14px
    &-box
      display: flex
      flex-direction: column
      margin-right: 16px
      width: 100%

      &:last-child
        margin-right: 0

  &__inputs
    display: flex

    &-label
      color: $black
      font-size: 14px

    &-text
      font-size: 21px
      font-weight: 400
      color: #16191E30
      border: 1px solid $quizGrey
      background: #FCFCFC
      height: 54px
      border-radius: 8px
      padding: 12px 16px
      width: 100%
      outline: none
      line-height: 24px
      margin-bottom: 16px
      -webkit-appearance: none

      &::placeholder
        color: #16191E30
        font-weight: 400
        font-size: 21px

      &-error
        border: 1px solid $red
        background: #FFE0E0

        &::placeholder
          color: $red
      &.active
        border: 1px solid $black
        color: $black
    &-box
      width: 100%

  &__multiple
    display: grid
    grid-template-columns: 1fr 1fr
    grid-row-gap: 16px
    grid-column-gap: 16px
    margin-bottom: 16px
    &.oneItem
      grid-template-columns: 1fr
    &.bottomSpace
      padding-bottom: 112px
    &.deutch
      & .quiz__multiple-item
        font-size: 20px!important
    &-bullet
      max-width: 40px
      margin-right: 16px

    &-img
      width: 100%

      &-wrapp
        max-width: 180px
        width: 100%
        margin: 0 auto 24px

    &-line
      grid-template-columns: 1fr
    &-dot
      background: $white
      border: 1px solid $quizGrey
      min-width: 24px
      height: 24px
      margin-left: 16px
      border-radius: 100%
      position: relative
      display: flex
      align-items: center
      justify-content: center
    &-box
      display: flex
      position: relative
      align-items: center


    &-item
      position: relative
      display: flex
      align-items: center
      justify-content: space-between
      font-size: 21px
      font-weight: 600
      color: $input-black
      border: 1px solid $quizGrey
      background: $quizGrey
      height: 80px
      border-radius: 8px
      padding: 0 16px
      width: 100%
      outline: none
      line-height: 24px
      -webkit-appearance: none
      cursor: pointer
      &,
      & span,
      &-box
        @media screen and (max-width: 410px)
          font-size: 17px !important
          line-height: 1.1em
      &.text-center
        justify-content: center
        text-align: center

      &.disabled
        opacity: 0.7

      &.hidden
        display: none
      &-hr
        background: #F3F4F5
        height: 1px
        width: 100%
        margin: 8px auto
      &-box
        color: $black
        display: flex
        align-items: center
        justify-content: center
        height: 100%
        font-size: 20px
      &-icon
        width: 40px
        height: 40px
        margin-right: 16px

      &-img
        height: 128px
        padding: 0
        &.fullImg
          position: relative
          left: -16px
          min-width: 80px
          height: 100%
          object-fit: cover

        & img
          height: 100%
          border-radius: 10px
          margin: 0 auto

        & p
          text-align: center
          font-size: 24px
          font-weight: 600
          line-height: 140%
          padding: 8px

        &.quiz__multiple-checked
          border: 1px solid #f3c194
          background: $white
          box-shadow: 0 0 16px #F1E0D0

    &-text
      display: flex
      flex-direction: column
      font-family: "Nunito Sans"
      font-size: 20px
      font-style: normal
      font-weight: 400
      line-height: 100%
      &.small
        font-size: 20px
      &.thin
        font-weight: 400
        display: initial
      &.fix-width
        display: block
      &-line
        padding-left: 16px
      &-small
        font-size: 14px
        line-height: 50%
      &-img
        height: 128px
        padding: 0

        & img
          height: 100%
          border-radius: 10px
          margin: 0 auto

        & p
          text-align: center
          font-size: 24px
          font-weight: 600
          line-height: 140%
          padding: 8px

        &.quiz__multiple-checked
          border: 1px solid #f3c194
          background: $white
          box-shadow: 0 0 16px #F1E0D0

    &-subtitle
      text-align: center
      padding-bottom: 24px

    &-checkbox
      position: absolute
      opacity: 0
      width: 100%
      height: 100%
      margin: 0
      left: 0
      z-index: 100
      pointer-events: none

    &-checked
      transition: $transition
      background: $quizActiveGreen
      border: 1px solid $quizActiveBorderGreen
      box-shadow: 0 4px 4px $quizActiveShadowGreen
      & .quiz__multiple-dot
        border: 1px solid #6EE44F
        &:before
          content: ''
          background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1639499753/dog-training/icons/progress-done.svg") no-repeat center center
          background-size: cover
          position: absolute
          width: 20px
          height: 20px

  &__radio
    grid-template-columns: 1fr
    &-img
      max-height: 72px
      height: 100%
      margin-right: 16px

    &-item
      height: 88px
      border-radius: 16px

      &.quiz__multiple-checked
        border: 1px solid #f3c194
        background: $white
        box-shadow: 0 0 16px #F1E0D0
        &:before
          content: ''
        & .quiz__radio-input-label
          &:before
            content: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1624963407/dog-training/icons/done-small.svg")
            position: absolute
            width: 20px
            height: 20px
            transition: $transition

    &-text
      &-box
        display: flex
        flex-direction: column
        line-height: 100%
      & span.capital
        text-transform: capitalize
      &-small
        font-weight: 600
        font-size: 12px
        opacity: 0.3

    &-subtitle
      text-align: center
      padding-bottom: 24px

    &-input
      position: absolute
      opacity: 0
      width: 100%
      height: 100%
      margin: 0
      left: 0
      z-index: 100
      &-label
        min-width: 24px
        height: 24px
        border-radius: 100%
        background: #FFFFFF
        border: 1px solid #F2DCC9
        position: relative
        display: flex
        align-items: center
        justify-content: center
        margin-right: 16px
        cursor: pointer

    &-checked
      transition: $transition
      background: $sand-contrast
      border: 1px solid #F3C195
  &__answer
    display: flex
    flex-direction: column
    margin-bottom: 16px
    &.custdevquizfixes_split
      & .quiz__answer-item
        flex-direction: column
        align-items: flex-start
        height: auto!important
        padding: 16px 8px
        &-subtext
          font-size: 14px
          line-height: 100%
          color: #9B9B9B
          margin-top: 4px
    &-grid
      display: grid
      grid-auto-rows: 1fr 1fr
      grid-template-columns: 1fr 1fr
      grid-row-gap: 8px
      grid-column-gap: 16px
      margin-bottom: 16px
      &.one
        grid-template-columns: 1fr
      &.oneChoice
        & .quiz__answer-item
          margin-bottom: 0
          width: 100%
      &.love
        & .quiz__answer-item
          font-weight: 400

    &-item
      cursor: pointer
      position: relative
      display: flex
      align-items: center
      justify-content: flex-start
      font-size: 21px
      font-weight: 600
      color: $input-black
      border: 1px solid $quizGrey
      background: $quizGrey
      height: 80px
      border-radius: 8px
      padding: 0 16px
      width: 100%
      outline: none
      line-height: 24px
      -webkit-appearance: none
      margin-bottom: 12px
      &:last-child
        margin-bottom: 0
      &-inn
        display: flex
        flex-direction: column
      &-icon
        display: flex
        align-items: center
        justify-content: center
        max-width: 48px
        width: 48px
        height: 48px
        margin-right: 16px
        & svg
          margin: 0!important
      &-txt
        color: $black
        font-size: 20px
        line-height: 100%
        &-small
          color: #8F8F8F
          font-size: 16px
          line-height: 130%
      &-inner
        display: flex
        align-items: center
        font-size: 20px
        font-style: normal
        font-weight: 400
        line-height: 100%
        color: $input-black

      &-sizing
        font-size: 36px
        margin-right: 8px
      &.text-center
        justify-content: center
        text-align: center
        padding: 0 4px

      &.full
        grid-column: 1 / 3
        width: 100%

      & .hisHer, .puppyInsertSelector
        display: contents

      & img
        margin-right: 16px
        width: 40px
        height: 40px

      & svg
        margin-right: 16px

      & .dog-name
        padding-left: 4px

      &-grid
        cursor: pointer
        position: relative
        display: flex
        align-items: center
        font-size: 21px
        font-weight: 600
        color: $newblack
        border: 1px solid $quizGrey
        background: $quizGrey
        height: 72px
        border-radius: 8px
        padding: 0 16px
        width: 100%
        outline: none
        line-height: 24px

        & svg
          margin-right: 8px

        &.text-center
          justify-content: center

      &-long
        font-size: 18px
        height: 64px
        padding: 8px 16px
        line-height: 140%
        text-transform: inherit

      &-text
        font-size: 21px
        height: 72px
        padding: 8px 16px
        line-height: 120%
        text-transform: inherit
        text-align: center

        &-img
          max-width: 32px
          max-height: 32px
          width: 100%
          margin-left: 8px
      &-box
        display: flex
        align-items: center
        justify-content: center
      &.active
        transition: $transition
        background: $quizActiveGreen
        border: 1px solid $quizActiveBorderGreen
        box-shadow: 0 4px 4px $quizActiveShadowGreen
    &-active
      transition: $transition
      background: $quizActiveGreen
      border: 1px solid $quizActiveBorderGreen
      box-shadow: 0 4px 4px $quizActiveShadowGreen

  &__btn
    @include btn-red(22px)
    position: fixed
    bottom: 16px
    width: calc(100% - 32px)
    margin: 0
    max-width: 414px
    z-index: 200
    font-weight: 700
    text-transform: capitalize
    font-family: $nunitoSans
    &.bottom
      bottom: 16px!important
    &.hidden
      opacity: 0
    &.first-step
      bottom: 88px
    &.vagus-reactive
      bottom: 24px !important
    &.love-funnel
      z-index: 1001
    &.quiz__active
      background: #EF3E3B
      border: 1px solid #ED2C29
      box-shadow: none
    &:disabled
      background: #FFE4E2
      border: 1px solid #FFE4E2
      box-shadow: none
      & a
        display: none
      & span
        background: #FFE4E2
        border: 1px solid #FFE4E2
        box-shadow: none
      &:active
        background: #FFE4E2
        border: 1px solid #FFE4E2
        box-shadow: none
      &:hover span
        background: #FFE4E2
        border: 1px solid #FFE4E2
        box-shadow: none

    &-link
      position: absolute
      top: 0
      left: 0
      right: 0
      bottom: 0
      display: block

  &__range
    display: flex
    flex-direction: column

    &.seven
      & .quiz__range
        &-numbers
          margin: 0 auto 12px

        &-wrapper
          &-svg
            display: flex
            justify-content: center
            align-items: center
            padding: 4px 0 24px

        &-slider
          position: relative
          height: 12px
          --value-b: var(--value, 0)
          --completed-a: calc((var(--value-a) - var(--min)) / (var(--max) - var(--min)) * 100)
          --completed-b: calc((var(--value-b) - var(--min)) / (var(--max) - var(--min)) * 100)
          --ca: min(var(--completed-a), var(--completed-b))
          --cb: max(var(--completed-a), var(--completed-b))
          --min: 1
          --max: 7
          --value: 4
          --step: 1

          &-progress
            --clip-start: calc(var(--ca) * 1%)
            --clip: inset(-20px calc(100% - (var(--cb)) * 1%) -20px var(--clip-start))
            position: absolute
            left: 0
            right: 0
            height: 12px
            background: $white
            pointer-events: none
            z-index: 0
            border-radius: 8px

            &:before
              content: ""
              position: absolute
              left: 0
              right: 0
              clip-path: var(--clip)
              top: 0
              bottom: 0
              background: linear-gradient(270deg, #FF4040 0%, #FFD326 52.6%, #FDEDB6 100%)
              z-index: 1
              border-radius: inherit

            &:after
              content: ""
              position: absolute
              top: 0
              right: 0
              bottom: 0
              left: 0
              box-shadow: var(--progress-shadow)
              pointer-events: none
              border-radius: inherit

          & > input
            -webkit-appearance: none
            width: 100%
            height: 12px
            margin: 0
            position: absolute
            left: 0
            cursor: grab
            outline: none
            background: none
            transition: $transition
            z-index: 1

            &:only-of-type
              & ~ .quiz__range-slider-progress
                --clip-start: 0
                border: 1px solid $quizGrey

              & ~ .quiz__range-slider-progress-red
                --clip-start: 0
                border: 1px solid $quizGrey

            &::-webkit-slider-thumb
              -webkit-appearance: none
              border: none
              cursor: pointer
              transition: .3s ease-in-out
              height: 40px
              width: 40px
              border-radius: 100%
              background: black
              display: flex
              align-items: center
              justify-content: center
              background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1624963407/dog-training/icons/range-btn.svg") no-repeat
              position: relative
              box-shadow: 0 8px 16px rgba(61, 22, 22, 0.14)
              -webkit-animation: mover 1s infinite alternate

    &-wrapper
      display: block

    &-box
      display: flex
      justify-content: space-between
      align-items: center
      width: 96%
      margin: 4px auto 0

      &-text
        color: $input-title
        font-size: 14px
        line-height: 100%
        display: flex
        justify-content: center
        align-items: center
        &:nth-child(2)
          text-align: right

        &-icon
          max-width: 24px
          width: 100%
          margin: 0 4px

    &-value
      font-size: 126px
      line-height: 100%
      font-weight: bold
      width: 157px
      height: 157px
      margin-top: 4px
      background: #F9F9F9
      border-radius: 100%
      display: flex
      justify-content: center
      align-items: center

      &-box
        display: flex
        justify-content: center
        flex-direction: column
        align-items: center
        margin-bottom: 24px
        &.spanish
          margin-bottom: 48px
          margin-top: 24px

    &-numbers
      display: flex
      justify-content: space-between
      width: 96%
      margin: 0 auto

      &-item
        color: $input-title
        font-size: 14px
        line-height: 135%
        display: flex
        justify-content: center

    &-disabled
      &-tooltip
        opacity: 1

        &:before
          content: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1624963409/dog-training/icons/slide-tooltip.svg")
          position: absolute
          top: 28px
          left: 129px
          box-shadow: 0 4px 20px rgba(61, 22, 22, 0.04)
          border-radius: 20px 20px 20px 20px
          height: 40px
          animation: mover 1s infinite alternate
          @-webkit-keyframes mover
            0%
              transform: translateY(0)
            100%
              transform: translateY(4px)
          @keyframes mover
            0%
              transform: translateY(0)
            100%
              transform: translateY(4px)
        &.spanish
          &:before
            content: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1688124854/dog-training/icons/spanish-slide.svg")
            top: -62px
            left: 127px
            height: 30px
            box-shadow: 0 16px 16px rgba(61, 22, 22, 0.04)
        &.portugal
          &:before
            content: 'Deslizar'
            top: 28px
            height: max-content
            color: #A49F9A
            left: 39%
            padding: 8px 18px
        &.deutch
          &:before
            content: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1705580187/dog-training/icons/slide-tooltip-de.svg")
        &.french
          &:before
            content: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1715252792/dog-training/img/glisser.svg")
        &.italian
          &:before
            content: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1727284462/paw-champ/icons/slide-it.svg")

      &-value
        color: #EBE8E5

      &-text
        font-weight: bold
        font-size: 48px
        color: $input-title

    &-slider
      position: relative
      height: 12px
      --value-b: var(--value, 0)
      --completed-a: calc((var(--value-a) - var(--min)) / (var(--max) - var(--min)) * 100)
      --completed-b: calc((var(--value-b) - var(--min)) / (var(--max) - var(--min)) * 100)
      --ca: min(var(--completed-a), var(--completed-b))
      --cb: max(var(--completed-a), var(--completed-b))
      --min: 0
      --max: 10
      --value: 5
      --step: 1

      &-progress
        --clip-start: calc(var(--ca) * 1%)
        --clip: inset(-20px calc(100% - (var(--cb)) * 1%) -20px var(--clip-start))
        position: absolute
        left: 0
        right: 0
        height: 12px
        background: $white
        pointer-events: none
        z-index: 0
        border-radius: 8px

        &:before
          content: ""
          position: absolute
          left: 0
          right: 0
          clip-path: var(--clip)
          top: 0
          bottom: 0
          background: linear-gradient(270deg, #30DF4C 0%, #FFD326 52.6%, #FF4040 100%)
          z-index: 1
          border-radius: inherit

        &:after
          content: ""
          position: absolute
          top: 0
          right: 0
          bottom: 0
          left: 0
          box-shadow: var(--progress-shadow)
          pointer-events: none
          border-radius: inherit

      &-progress-red
        --clip-start: calc(var(--ca) * 1%)
        --clip: inset(-20px calc(100% - (var(--cb)) * 1%) -20px var(--clip-start))
        position: absolute
        left: 0
        right: 0
        height: 12px
        background: $white
        pointer-events: none
        z-index: 0
        border-radius: 8px

        &:before
          content: ""
          position: absolute
          left: 0
          right: 0
          clip-path: var(--clip)
          top: 0
          bottom: 0
          background: linear-gradient(270deg, #FF4040 0%, #FFD326 52.6%, #30DF4C 100%)
          z-index: 1
          border-radius: inherit

        &:after
          content: ""
          position: absolute
          top: 0
          right: 0
          bottom: 0
          left: 0
          box-shadow: var(--progress-shadow)
          pointer-events: none
          border-radius: inherit

      & > input
        -webkit-appearance: none
        width: 100%
        height: 12px
        margin: 0
        position: absolute
        left: 0
        cursor: grab
        outline: none
        background: none
        transition: $transition
        z-index: 1

        &:only-of-type
          & ~ .quiz__range-slider-progress
            --clip-start: 0
            border: 1px solid $quizGrey

          & ~ .quiz__range-slider-progress-red
            --clip-start: 0
            border: 1px solid $quizGrey

        &::-webkit-slider-thumb
          -webkit-appearance: none
          border: none
          cursor: pointer
          transition: .3s ease-in-out
          height: 40px
          width: 40px
          border-radius: 100%
          background: black
          display: flex
          align-items: center
          justify-content: center
          background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1624963407/dog-training/icons/range-btn.svg") no-repeat
          position: relative
          box-shadow: 0 8px 16px rgba(61, 22, 22, 0.14)
          -webkit-animation: mover 1s infinite alternate

    &-slider
      --value-a: clamp(var(--min), var(--value, 0), var(--max))

    &-btn
      margin-bottom: 0
      margin-top: 56px

  &__loading
    &-title
      color: $black
      font-weight: 700
      font-size: 20px
      line-height: 140%
      text-align: center
      margin: 16px auto 24px

      &-health
        font-size: 24px
        line-height: 140%
        font-weight: 800
        text-align: center

      &-main
        display: block

      &-name
        @include textGradient(linear-gradient(159.44deg, #FF765A 5.8%, #FF4040 90.92%))

    &-list
      background: $white
      border: 1px solid #FDE2C9
      padding: 24px
      width: 100%
      margin: 0 auto
      border-radius: 16px
      max-width: 240px
      display: none

    &-item
      display: flex
      align-items: center
      margin-bottom: 8px

      &:last-child
        margin-bottom: 0

      &-img-box
        position: relative
        display: flex
        align-items: center
        justify-content: center

        & .load
          position: absolute
          width: 14px
          height: 14px
          border: 2px solid #38DF4B
          border-radius: 50%
          border-right-color: transparent
          border-bottom-color: transparent
          animation-name: rotate
          animation-duration: 1.0s
          animation-iteration-count: infinite
          animation-timing-function: linear

          &:nth-child(1)
            animation-fill-mode: forwards
            animation-name: rotate

          &:nth-child(2)
            animation-fill-mode: forwards
            animation-name: rotate
            animation-delay: 3.1s

          &:nth-child(3)
            animation-delay: 5s
            animation-fill-mode: forwards
            animation-name: rotate

          &:nth-child(4)
            animation-delay: 7.7s
            animation-fill-mode: forwards
            animation-name: rotate

          &:nth-child(5)
            animation-delay: 10s
            animation-fill-mode: forwards
            animation-name: rotate
            @keyframes rotate
              from
                -webkit-transform: rotate(0deg)
                transform: rotate(0deg)
              to
                -webkit-transform: rotate(360deg)
                transform: rotate(360deg)

      &-img
        width: 16px
        height: 16px
        visibility: hidden

        &.done
          visibility: visible

      &-text
        color: $black
        line-height: 140%
        font-size: 14px
        opacity: 0.4
        margin-left: 8px

        &.done
          opacity: 1

  &__result
    &-box
      position: relative
    &-wrapp
      display: flex
      align-items: center
      &-img
        max-width: 92px
      &-text
        font-size: 14px
        font-weight: 800
        padding-left: 8px
        & span
          @include textGradient($gradient-1)
    &-step
      &-one
        display: block

      &-two
        display: none

    &-message
      &-box
        display: flex
        flex-direction: column
        padding: 0
        gap: 8px
        margin: 0 auto 20px

      &-title
        font-weight: 700
        font-size: 24px
        text-align: center
        letter-spacing: -0.03em
        color: $black
        line-height: 130%

        &-main
          display: block

        &-name
          background: linear-gradient(159.44deg, #FF765A 5.8%, #FF4040 90.92%)
          -webkit-background-clip: text
          -webkit-text-fill-color: transparent
          display: inline-block

      &-subtitle
        font-weight: 500
        font-size: 20px
        line-height: 135%
        text-align: center
        letter-spacing: -0.03em
        color: $black

      &-free
        font-size: 20px
        font-weight: 800
        line-height: 100%
        background: #7CD523
        border: 1px solid #6BBE18
        border-radius: 6px
        padding: 4px 8px
        margin: 16px auto 0
        color: $white

      &-upsale
        max-width: 150px
        margin: 0

        &-wrapp
          width: 100%
          display: flex
          justify-content: center
          align-items: center
          position: relative
          &:before
            content: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1684913989/dog-training/img/bg-book.svg")
            z-index: -1
            position: absolute
            width: 100%
            height: 100%
            display: flex
            justify-content: center
            align-items: center
            bottom: -16px

    &-text
      color: $black
      opacity: 0.45
      font-size: 12px
      font-weight: 600
      line-height: 140%
      &.black
        font-size: 14px
        opacity: 1
        font-weight: 400

      &-box
        display: flex
        margin-top: 16px
        padding-bottom: 20vh

      &-icon
        margin-right: 16px
        min-width: 26px

    &-label
      color: $black
      font-family: $main-font
      font-style: normal
      font-weight: 400
      line-height: 130%
      font-size: 14px
      position: relative

      &-error
        color: $red !important
        text-align: center

    &-input
      font-size: 21px
      font-family: $main-font
      font-weight: 400
      letter-spacing: inherit
      color: $black
      border: 1px solid #bfbfbf
      background: #f6f6f6
      height: 54px
      border-radius: 8px
      padding: 12px 16px
      width: 100%
      outline: none
      line-height: 24px
      margin-bottom: 16px
      -webkit-appearance: none

      &::placeholder
        color: #bfbfbf
        font-weight: 400
        font-size: 21px
      &.active
        background: $white
        border: 1px solid $black
      &-error
        border: 1px solid $red
        background: #FFE0E0

        &::placeholder
          color: $red

    &-loader
      display: none
      justify-content: center
      align-items: center

      &-box
        margin-bottom: 16px
        display: flex
        align-items: center
        justify-content: center
      & .preload
        position: absolute
        width: 30px
        height: 30px
        border: 2px solid $red
        border-radius: 50%
        border-right-color: transparent
        border-bottom-color: transparent
        animation-name: rotate
        animation-duration: 1.0s
        animation-iteration-count: infinite
        animation-timing-function: linear
        @keyframes rotate
          from
            -webkit-transform: rotate(0deg)
            transform: rotate(0deg)
          to
            -webkit-transform: rotate(360deg)
            transform: rotate(360deg)

    &-active
      opacity: 1
      visibility: visible

    &-applica
      &-title
        color: $black
        font-weight: 700
        font-size: 20px
        line-height: 140%
        text-align: center
        margin: 0 auto 24px
        padding: 0 24px

        &-name
          background: linear-gradient(159.44deg, #FF765A 5.8%, #FF4040 90.92%)
          -webkit-background-clip: text
          -webkit-text-fill-color: transparent
          display: inline-block

      &-logo
        margin: 0 auto 16px
        width: 100%
        height: 50px

      &-item
        margin-bottom: 16px

        &:last-child
          margin-bottom: 0

      &-jobs
        display: flex
        justify-content: center
        padding-top: 24px
        max-width: 64px
        margin: 0 auto

      &-text
        font-size: 14px
        text-align: center
        margin: 8px 0

        &-name
          color: #B7B7B7
          font-weight: 400
          font-size: 10px
          line-height: 140%

      &-wrapp
        padding: 16px 8px
        display: flex
        flex-direction: column
        align-items: center
        justify-content: center
        max-width: 290px
        width: 100%
        background: $quizGrey
        box-shadow: 0 10px 10px rgba(218, 185, 156, 0.18)
        border-radius: 8px
        margin: 24px auto 0
    & .headesnew-2
      & .quiz__result-text-box
        padding-bottom: 16px
      & .quiz__result-label
        font-size: 16px
        text-align: center
        margin-bottom: 8px
        font-weight: 600
      & .quiz__result-text
        line-height: 130%
        letter-spacing: -0.36px

        &-icon
          height: 16px
          top: 2px
          margin-right: 8px
          width: 12px
          position: relative
      & .quiz__btn-box
        & .positionBtn
          &.quiz__btn
            &.email-submit
              position: relative
              bottom: 0
              width: 100%
              margin-bottom: 16px
      & .quiz__result-input
        position: relative
        &::placeholder
          background-image: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1704879887/dog-training/icons/mail-sp.svg")
          background-size: contain
          background-repeat: no-repeat
          background-position: 0
          position: absolute
          left: 20px
          top: 16px
          width: 100px
          padding-left: 32px
    & .headesnew-3
      height: max-content
      position: fixed
      bottom: 0
      z-index: 1
      background: $white
      border-radius: 16px 16px 0 0
      max-width: 446px
      &__logo
        height: 64px
        display: flex
        align-items: center
        justify-content: center
        width: 100%
      &__blur
        filter: blur(3px)
        height: 80vh
      & .quiz__result-text-box
        padding-bottom: 16px
      & .quiz__reviews-loading-title
        padding: 24px
      & .quiz__result-input
        border-radius: 8px
        border: 2px solid #EEC1BE
        background: #F6F6F6
        margin-bottom: 0
      & .quiz__result-text-icon
        margin-right: 8px
      & .quiz__btn-box
        margin-bottom: 16px
        & .positionBtn
          &.quiz__btn
            &.email-submit
              position: relative
              width: 100%
              bottom: auto

    & .headesnew-5
      &__block
        padding: 16px
        border-radius: 8px
        background: rgba(113, 179, 147, 0.28)
      &__list
        margin-left: 24px

        &-title
          font-size: 14px
          line-height: 110%
          padding-bottom: 8px
          font-weight: 800
        &-item
          list-style: disc
          font-size: 14px
          font-weight: 400
          line-height: 130%
          & .nameInsertSelector
            text-transform: capitalize
      & .quiz__result-text-box
        padding-bottom: 16px
      & .quiz__result-text-icon
        margin-right: 8px
      & .quiz__result-input
        margin-bottom: 0
  &__active
    transition: $transition
    background: $quizActiveGreen
    border: 1px solid $quizActiveBorderGreen
    box-shadow: 0 4px 4px $quizActiveShadowGreen

  &__email-error
    background: #FFE9E5
    color: #EE6B63
    border: 1px solid #EE6B63

    &::placeholder
      color: #EE6B63

  &__mid
    display: flex
    flex-direction: column
    justify-content: flex-start
    &.changed-content
      display: none
      &.active
        display: block
    &-img
      width: 100%
      border-radius: 8px
      overflow: hidden
      &-inn
        max-width: 280px
        width: 100%
        margin: 16px auto 0
      &-box
        max-width: 220px
        width: 100%
        border-radius: 16px
        border: 7px solid $red
        box-shadow: 0 4px 16px rgba(170, 105, 91, 0.47)
        margin: 0 auto
        position: relative
        &:before
          content: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1698748515/dog-training/img/aggression/object.svg")
          z-index: -1
          position: absolute
          width: 100%
          height: 100%
          display: flex
          justify-content: center
          align-items: center
          bottom: 0
    &-object-bg
      position: relative
      &:before
        content: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1698759695/dog-training/img/aggression/object-calendar.svg")
        z-index: -1
        position: absolute
        width: 100%
        height: 100%
        display: flex
        justify-content: center
        align-items: center
        bottom: 16px
    &-need-ask
      max-width: 220px
      width: 100%
      border-radius: 16px
      border: 7px solid $red
      box-shadow: 0 4px 16px rgba(170, 105, 91, 0.47)
    &-calendar
      max-width: 280px
      width: 100%
      border-radius: 16px
      margin: 0 auto 24px
      position: relative
      &:before
        content: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1698759695/dog-training/img/aggression/object-calendar.svg")
        z-index: -1
        position: absolute
        width: 100%
        height: 100%
        display: flex
        justify-content: center
        align-items: center
        bottom: 16px
      &-img
        width: 100%
      &-title
        font-size: 24px
        line-height: 120%
        font-weight: 800
        text-align: center
        width: 100%
        margin: 40px auto 4px
        @include textGradient($gradient-red)
    &-box
      position: relative
      display: flex
      flex-direction: column
      justify-content: center
      align-items: center
      margin-top: 40px

      &:before
        content: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1684857401/dog-training/img/quiz/aboard-grey.svg")
        z-index: -1
        position: absolute
        width: 100%
        height: 100%
        display: flex
        justify-content: center
        align-items: center
        bottom: -16px

      &-text
        font-weight: 800
        font-size: 24px
        line-height: 120%
        @include textGradient($gradient-red)
        padding-bottom: 4px

    &-puppy
      position: relative
      margin: 24px 0
      &-challenge
        margin: 0
      &-img
        max-width: 270px
        width: 100%
        margin: 0 auto
        display: flex
        justify-content: center
        align-items: center
        &--is-small
          max-width: 245px
        &--is-big
          max-width: none
        &-wrapp
          position: relative
          display: flex
          justify-content: center
          align-items: center
          &:before
            content: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1721902678/paw-champ/img/bg-object.svg")
            z-index: -1
            position: absolute
            width: 100%
            height: 100%
            display: flex
            justify-content: center
            align-items: center
            bottom: 16px

        &.amazing
          max-width: none

      &-title
        font-size: 24px
        line-height: 120%
        font-weight: 800
        text-align: center
        width: 100%
        margin: 16px auto 0
        @include textGradient($gradient-red)
        &.bottom-spacing
          margin-bottom: 16px

    &-inner
      display: flex
      flex-direction: column

      & .adultPuppyContainer
        max-width: 290px
        width: 100%
        margin: 0 auto

        & .quiz__mid-subtitle
          font-size: 16px
          font-weight: 600

        &.wider
          max-width: 343px

          & .quiz__mid-subtitle
            font-weight: 400


    &-content
      width: 100%
      &-title
        font-size: 24px
        line-height: 120%
        font-weight: 800
        width: 100%
        margin: 16px auto
      &-subtitle
        font-size: 16px
        line-height: 130%
        font-weight: 600
        width: 100%
        margin: 0 auto 16px
    &-go-ahead
      max-width: 414px
      width: 100%
      height: 228px
      margin: 24px auto

      &.happy
        width: auto
        height: 300px

    &-first-step
      width: 100%
      margin-bottom: 8px
      filter: drop-shadow(0px 4px 14px #FFEFE0)

    &-title
      font-size: 24px
      line-height: 120%
      font-weight: 800
      text-align: center
      width: 100%
      margin: 16px auto 4px
      &.between
        margin-top: -10px
      &-main
        margin: 16px auto
      &.red
        @include textGradient($gradient-red)
      &.spacing-bottom
        margin-bottom: 16px
    &-header
      @extend .quiz__mid-title
      margin: 24px auto 16px

    &-subtitle
      font-size: 16px
      line-height: 130%
      font-weight: 600
      text-align: center
      width: 100%
      margin: 0 auto 16px
      &-challenge
        margin-top: 16px

    &.doggo
      & .quiz__mid-subtitle
        &:last-of-type
          margin-bottom: 32px

      & .quiz__mid-title
        margin-bottom: 8px

    &-masses
      padding: 16px
      background: #FFDDDD
      border: 1px solid #E84A05
      box-sizing: border-box
      border-radius: 8px
      margin-bottom: 16px

      &-box
        display: flex
        align-items: center

      &-text
        font-size: 12px
        line-height: 140%
        color: $newblack

      &-percent
        font-size: 28px
        line-height: 100%
        font-weight: 800
        color: #E84A05
        padding-top: 2px

      &-icon
        max-width: 24px
        max-height: 24px
        margin-right: 8px

    &-exclusive
      padding: 16px
      background: #DAFFE9
      border: 1px solid #27C968
      box-sizing: border-box
      border-radius: 8px
      margin-bottom: 16px

      &-box
        display: flex
        align-items: center

      &-text
        font-size: 12px
        line-height: 140%
        color: $newblack

        & span
          color: #27C968
          font-weight: 700

      &-percent
        font-size: 28px
        line-height: 100%
        font-weight: 800
        color: #27C968
        padding-top: 2px

      &-icon
        max-width: 24px
        max-height: 24px
        margin-right: 8px

  &__owner
    display: flex
    flex-direction: column
    justify-content: flex-start

    &-text
      font-weight: 400
      font-size: 10px
      line-height: 120%
      max-width: 260px
      width: 100%
      margin: 16px auto
      text-align: center

    &-inner
      flex-direction: column
      display: flex

    &-title
      font-size: 24px
      line-height: 120%
      font-weight: 800
      text-align: center
      width: 100%
      margin: 16px auto

    &-img
      width: 100%

  &__trust
    &-title
      font-weight: 800
      font-size: 20px
      line-height: 110%
      margin: 20px 0 32px

    &-text
      font-size: 18px
      line-height: 120%
      margin: 32px 0

    &-list
      padding-bottom: 32px

    &-item
      display: flex
      margin-bottom: 16px

      &:last-of-type
        margin-bottom: 0

      &-logo
        max-width: 64px
        width: 100%
        margin-right: 10px
        object-fit: contain

  &__believe
    &-img
      overflow: hidden
      border-radius: 8px
      border: 1px solid #F6DBC2
      width: 100%

    &-list
      list-style: disc
      font-weight: 600
      line-height: 140%
      margin-left: 24px
      padding: 16px 0

      &-item
        padding-bottom: 8px

        &:last-child
          padding-bottom: 0

    &-btn
      background: #F7DBC2
      border: 1px solid #FFDCBC
      box-sizing: border-box
      border-radius: 8px
      height: 54px
      width: 100%
      font-family: $main-font
      font-weight: 700
      font-size: 24px
      line-height: 33px
      color: #BD9168
      cursor: pointer

      &:disabled
        opacity: 0.3
        transition: $transition
        cursor: default

      &.active
        background: #EDC7A6
        transition: $transition

      &-box
        display: grid
        grid-column-gap: 16px
        grid-template-columns: 1fr 1fr
        padding-bottom: 24px

    &-myths
      position: relative
      background: $white
      padding: 16px 16px 64px
      max-width: 440px
      width: 100%
      margin: 0 auto
      visibility: hidden
      opacity: 0
      transition: $transition

      &.active
        visibility: visible
        opacity: 1
        transition: $transition

      &-title
        font-size: 24px
        font-weight: 700
        text-align: center
        padding-bottom: 16px

      &-subtitle
        font-weight: 700

      &-text
        font-weight: 600
        padding-bottom: 8px

        &:last-of-type
          padding-bottom: 16px

        & span
          @include textGradient($gradient-red)

      &-logo
        display: flex
        margin: 0 auto
        width: 100%
        max-width: 256px
        padding-bottom: 8px

      &-btn
        @include btn-red(24px)

  &__popup
    background: #FFFFFF
    border: 1px solid #FFFFFF
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08)
    backdrop-filter: blur(11px)
    border-radius: 14px
    overflow: hidden
    max-width: 400px
    min-width: 260px
    visibility: hidden
    opacity: 0
    transform: scale(0)
    transition: 0.1s ease-out
    @at-root .newFun &
      transition: 0.3s ease-in
    & .nameInsertSelector
      text-transform: capitalize
    &.active
      visibility: visible
      opacity: 1
      transform: scale(1)
      transition: 0.2s ease-in
    &-bg
      transition: 0.1s background ease-in-out
      position: absolute
      width: 100%
      height: 100%
      top: 0
      bottom: 0
      z-index: -1
      display: flex
      flex-direction: column
      align-items: center
      background: linear-gradient(0deg, rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.4)), rgba(0, 0, 0, 0.24)
      backdrop-filter: blur(2px)
      &.active
        opacity: 1
        transition: 0.2s background ease-in-out
        background: rgba(0, 0, 0, 0.58)
        z-index: 22
    &-title
      text-align: center
      font-size: 20px
      font-weight: 800
      padding-bottom: 8px
      padding-top: 16px
    &-img
      max-width: 170px
      width: 100%
      filter: drop-shadow(0px 3.17992px 12.7197px rgba(220, 205, 192, 0.47))
      border-radius: 8px
      border: 4px solid $white
      min-height: 159px
      &-box
        display: flex
        align-items: center
        justify-content: center
        padding-bottom: 8px
    &-text
      text-align: center
      font-size: 14px
      line-height: 120%
      padding: 0 16px 24px
      & b
        font-weight: 800
        text-decoration: underline
      & .ageInsertSelector
        font-weight: 800
        text-decoration: underline
        text-transform: capitalize
    &-wrapp
      visibility: hidden
      display: flex
      align-items: center
      justify-content: center
      height: 100%
      width: 100%
      position: fixed
      top: 0
      left: 0
      bottom: 0
      z-index: 150
      padding: 40px
      transition: 0.2s ease-in-out
      opacity: 0
      &.active
        transition: 0.2s ease-in-out
        visibility: visible
        opacity: 1

    &-btn
      background: #FFF6EE
      border: 1px solid #FFDDBF
      border-radius: 8px
      padding: 16px
      font-weight: 700
      font-size: 18px
      cursor: pointer

      &-list
        background: $white
        display: grid
        grid-template-columns: 1fr 1fr
        width: 100%
        border-top: 1px solid lightgray

      &-item
        font-size: 17px
        line-height: 140%
        font-weight: 700
        display: flex
        align-items: center
        justify-content: center
        padding: 12px 0
        cursor: pointer
        &:nth-of-type(1)
          border-right: 1px solid lightgray
        &:active
          background: #E7E7E7

  &__humping
    &-wrapper
      display: flex
      flex-direction: column
      align-items: center

    &-title
      font-weight: 700
      font-size: 24px
      line-height: 120%
      text-align: justify
      margin-top: 16px
      margin-bottom: 12px

    &-subtitle
      font-weight: 700
      font-size: 22px
      line-height: 100%
      text-align: justify
      margin-bottom: 8px

    &-text
      font-weight: 600
      font-size: 15px
      line-height: 120%
      text-align: justify
      padding-bottom: 24px

    &-img
      max-height: 200px
      height: 100%
      margin: 0 0 16px

  &__error
    &-message
      width: 100%
      text-align: center
      margin-bottom: 8px
      line-height: 100%
      display: none

      &.active
        display: block
        color: $red

  &__ahead
    &-img
      max-width: 155px

      &-wrapp
        display: flex
        align-items: center
        justify-content: center
        margin-top: 16px

  &__million
    &-wrapper
      max-width: 414px
      width: 100%
      margin: 0 auto

    &-img
      width: 100%
      margin-top: 16px

    &-text
      text-align: center
      font-weight: 600
      line-height: 110%
      margin-bottom: 8px

      &:first-of-type
        margin-top: 8px

      &:last-of-type
        margin-bottom: 64px

    &-title
      font-weight: 800
      font-size: 42px
      text-align: center
      line-height: 110%
      margin-bottom: 8px

      &:last-of-type
        margin-bottom: 0

  &__dogs-spend
    &-title
      font-size: 24px
      line-height: 120%
      color: $black
      font-weight: 700
      text-align: center
      max-width: 190px
      width: 100%
      margin: 16px auto
      position: relative

      &--is-small
        font-size: 23px
        line-height: 1em
        max-width: 154px

      &:before
        content: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1684858377/dog-training/img/quiz/spend-grey.svg")
        z-index: -1
        position: absolute
        width: 100%
        height: 100%
        display: flex
        justify-content: center
        align-items: center

    &-picture
      width: calc(100% + 32px)
      position: absolute
      left: -16px
      -o-object-fit: contain
      object-fit: cover
      min-height: 290px

    &-img
      width: 100%

    &-wrapp
      height: 320px
      position: relative
      display: flex
      align-items: center
      justify-content: center

  &__dogs-friends
    &-wrapp
      display: flex
      justify-content: center
      align-items: center
      padding-bottom: 24px

    &-img
      max-width: 414px
      width: 100%

  &__based
    position: relative
    width: 100%

    &-new
      position: relative
      padding-bottom: 64px
      width: 100%
      margin-top: 32px

      &:before
        content: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1628064914/dog-training/img/based-bg-desk.svg")
        z-index: -1
        position: absolute
        width: 100%
        height: 100%
        display: flex
        justify-content: center
        align-items: center
        transform: scale(1.4)
        margin: -16px auto 0

    &-improve
      background: #FAE7D6
      border: 1px solid #F3DCC7
      border-radius: 16px
      padding: 16px
      margin-bottom: 40px

      &-wrapper
        max-width: 504px
        width: 100%
        margin: 0 auto

      &-percents
        color: #CE9C70
        font-size: 82px
        font-weight: 800
        line-height: 120%
        text-align: center

      &-box
        background: #FFF6EE
        border: 1px solid #F2DDCB
        border-radius: 16px
        padding: 16px
        text-align: center
        font-size: 14px
        color: #523A25
        line-height: 140%
        font-weight: 600

      &-subtitle
        font-size: 16px
        line-height: 140%
        font-weight: 600
        color: #523A25
        text-align: center

    &-inner
      max-width: 414px
      width: 100%
      margin: 0 auto

    &-title
      font-size: 26px
      font-weight: 800
      line-height: 120%
      text-align: center
      max-width: 202px
      width: 100%
      margin: 8px auto 16px

      &-gradient
        @include textGradient($gradient-red)
        font-size: 24px
        text-align: center
        width: 100%
        margin-bottom: 8px

      &-small
        font-size: 20px
        font-weight: 800
        line-height: 120%
        text-align: center
        max-width: 202px
        width: 100%
        margin: 20px auto 16px

    &-subtitle
      font-size: 16px
      line-height: 140%
      font-weight: 600
      color: #16191E
      text-align: center
      margin-bottom: 16px

    &-graph
      background: $white
      border-radius: 16px
      padding: 24px 8px 16px
      border: 1px solid #FAEADC
      box-shadow: 0 4px 16px rgba(220, 205, 192, 0.47)
      margin-bottom: 40px

      &-health
        width: calc(100% + 32px)
        position: relative
        left: -16px
        padding-bottom: 32px

        &-box
          font-size: 12px
          font-weight: 700
          color: #A49F9A
          display: flex
          justify-content: space-between
          align-items: center
          padding: 0 16px
          margin-top: -40px

      &-text
        font-size: 16px
        font-weight: 600
        line-height: 130%
        margin-left: 8px
        display: flex

      &-date
        display: flex
        justify-content: space-between
        align-items: center
        padding: 0 16px 8px
        margin-top: -24px

        &-item
          font-size: 12px
          color: #A49F9A
          line-height: 100%
          font-weight: 700

      & svg
        margin-top: -16px

        & path
          width: 100%

      &-line
        stroke-dasharray: 1000
        animation: dash 12s
        animation-play-state: unset
        &.training-split
          animation: dash 14s

      @keyframes dash
        from
          stroke-dashoffset: 1000
        to
          stroke-dashoffset: 0

    &-list
      display: flex

      &-item
        display: flex
        flex-direction: column
        background: $white
        font-weight: 600
        border-radius: 16px
        padding: 16px
        border: 1px solid #FAEADC
        box-shadow: 0 4px 16px rgba(220, 205, 192, 0.47)
        width: 50%

        & span
          color: $red
          font-weight: 800

        &:first-child
          margin-right: 16px

    &-btn
      @include btn-red(20px)

  &__reviews
    &-loading
      &-title
        font-weight: 800
        font-size: 22px
        line-height: 100%
        padding: 16px 0 8px
        text-align: center
        &-green
          color: #00B67A
          font-weight: 800
          font-size: 26px
          text-align: center
          margin-top: 16px
          margin-bottom: 8px
          line-height: 150%
        &-red, b
          @include textGradient($gradient-red)
          display: inline
          font-weight: 800
          font-size: 26px
          text-align: center
          line-height: 100%
          &-box
            display: flex
            align-items: center
            justify-content: center
            margin-top: 16px
            margin-bottom: 8px
        & .titleThin
          font-weight: 400!important
        & .red
          @include textGradient($gradient-red)
        &.love
          b
            font-size: 22px

      &-subtitle
        font-weight: 400
        font-size: 12px
        line-height: 140%
        text-align: center
        color: $black
        margin-bottom: 16px
      &-text
        font-weight: 600
        font-size: 16px
        line-height: 100%
        color: $black
        text-align: center
        max-width: 247px
        padding: 0 8px
        margin: 0 auto 16px
        &.bold
          font-weight: 800
      &-svg
        width: 100%
        height: auto
        &-container
          position: relative
        &-pointer
          display: flex
          padding: 6px 8px
          border-radius: 4px
          position: absolute
          color: $white
          text-align: center
          justify-content: center
          font-family: "Nunito Sans"
          font-size: 12px
          font-style: normal
          font-weight: 800
          line-height: 100%
          &-img
            position: absolute
            &.blue
              right: -7px
              bottom: 14px
            &.black
              right: 16px
              bottom: -6px
          &.blue
            background-color: $blue-600
            &.nutrition
              top: 4px
              right: 12%
          &.black
            background-color: $black-800
            &.nutrition
              bottom: 39%
              left: 4%
        &--is-reactivity
          margin-bottom: 5px
        &-inner
          width: 100%
          padding-bottom: 16px
          position: relative
          .iteration-show-el
            animation-name: FadeInDown
            animation-duration: .8s
            animation-fill-mode: both
            animation-timing-function: ease-out
            &-p10
              animation-delay: .5s
            &-p30
              animation-delay: 1s
            &-p60
              animation-delay: 1.5s
            &-p90
              animation-delay: 2s

            @keyframes FadeInDown
              from
                opacity: 0
                transform: translateY(-30px)
              to
                opacity: 1
                transform: translateY(0)
        &-date
          font-weight: 800
          font-size: 10px
          line-height: 100%
          text-align: right
          color: #D7D7D7
          text-transform: uppercase
          width: 28px
          visibility: hidden
          opacity: 0
          transition: 0.3s ease-in-out
          &.reactive
            width: auto
          @at-root .newFun &
            width: auto
          &.active
            visibility: visible
            opacity: 1
            transition: 0.3s ease-in-out
          &-wrapp
            width: 100%
            display: flex
            justify-content: space-between
            padding: 0 16px
            margin-top: -32px
            margin-bottom: 16px
            @at-root .newFun &
              padding: 0 32px
            &.aggressive-month
              margin-top: -16px
            &.reactive
              padding: 0 8%
            &.nutrition
              margin-top: 9px

          &-text
            font-weight: 400
            font-size: 10px
            line-height: 100%
            text-align: center
            color: #656565
        & .result-graph
          &-line
            opacity: 0
            &.active
              opacity: 1
              animation-name: AnimatedLines
              animation-iteration-count: 1
              animation-duration: 0.8s
              animation-timing-function: ease-out
              animation-fill-mode: forwards
              transform-origin: bottom
              transform: scaleY(0)

              @keyframes AnimatedLines
                to
                  transform: scaleY(1)
          &-goal
            visibility: hidden
            opacity: 0
            transition: 0.3s ease-in-out
            &.active
              visibility: visible
              opacity: 1
              transition: 0.3s ease-in-out
          &-dot
            visibility: hidden
            opacity: 0
            transition: 0.3s ease-in-out
            &.active
              visibility: visible
              opacity: 1
              transition: 0.3s ease-in-out
          &-bg
            visibility: hidden
            opacity: 0
            transition: 0.3s ease-in-out
            &.active
              visibility: visible
              opacity: 0.3
              transition: 0.3s ease-in-out
          &-dot
            visibility: hidden
            opacity: 0
            transition: 0.3s ease-in-out
            &.active
              visibility: visible
              opacity: 1
              transition: 0.3s ease-in-out
        &-text
          position: absolute
          padding: 6px 8px
          border-radius: 4px
          box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.16)
          color: #FFF
          text-align: center
          font-family: "Nunito Sans"
          font-size: 13px
          font-weight: 800
          line-height: 100%
          z-index: 1
          &:before 
            content: ''
            width: 12px
            height: 12px
            transform: rotate(45deg)
            position: absolute
            z-index: -1
          &--left
            background: #343434
            bottom: 46%
            left: 5%
            @media (min-width: 400px)
              bottom: 44%
            &:before
              left: 17px
              bottom: -3px
              background: #343434
              @media (min-width: 400px)
                left: 21px
          &--right
            background: #1998CD
            top: 15%
            right: 10%
            visibility: hidden
            opacity: 0
            transition: 0.3s ease-in-out
            &:before
              right: -4px
              top: 5px
              background: #1998CD
              @media (min-width: 400px)
                top: 7px
            &.active
              visibility: visible
              opacity: 1
              transition: 0.3s ease-in-out

      &-subtext
        font-weight: 800
        font-size: 26px
        line-height: 100%
        text-align: center
        @at-root .newFun &
          font-size: 22px
        & span, b
          @include textGradient($gradient-red)
          display: inline
          &.textPuppySelector,
          &.puppyInsertSelector,
          &.no-highlight
            background: none
            -webkit-text-fill-color: $black
        & .puppyInsertSelector, .nameInsertSelector
          @include textGradient(linear-gradient(90deg, $black 0%, $black 100%))
        &.portugal
          & span
            &.textPuppySelector
              @include textGradient($gradient-red)
    &-slider
      overflow: hidden
      padding-bottom: 40px
      &-item
        padding: 16px
        background: #F3F4F5
        border: 1px solid #D8DDE2
        border-radius: 16px
        color: $black
        &-stars
          display: flex
          max-width: 96px
          width: 100%
          margin-top: 8px

        &-name
          color: #9FA5AC
          font-size: 12px
          font-weight: 500
          width: 35%
          display: flex
          justify-content: flex-end
          &-wrapp
            display: flex
            align-items: flex-start
            justify-content: space-between
            padding: 8px 0
            & h5
              width: 65%
              font-size: 12px
              line-height: 120%
              font-weight: 800

          &-icon
            width: 20px
            margin-right: 8px
        &-text
          font-size: 14px
          line-height: 120%
        &-title
          font-weight: 800
          font-size: 12px
          line-height: 120%

  &__program
    background: #FDEFE3
    border: 1px solid #FCEADA
    border-radius: 16px
    padding: 0 16px
    margin-bottom: 16px

    &:last-of-type
      margin-bottom: 32px

    &-item
      display: flex
      justify-content: space-between
      align-items: center
      padding: 8px 0

      &-box
        display: flex
        align-items: center
        position: relative
        &.show
          opacity: 0
          transition: all 0.6s ease-in-out
          transform: translateX(-50px)
          &.active
            opacity: 1
            transform: translateX(0)
            left: 0
      &-title
        font-weight: 600
        line-height: 110%

        &-icon
          width: 20px
          margin-right: 8px

      &-answered
        line-height: 110%
        font-size: 14px
        font-weight: 600
        color: #00C108

        &-icon
          width: 16px
          height: 16px
          margin-left: 8px

      &-percents
        color: #FF6050
        font-weight: 600

      &-hr
        width: 100%
        border: 1px solid #EEDCCC

    &-text
      font-size: 14px
      line-height: 120%
      text-align: center
      padding-bottom: 16px
  &__yourself
    display: grid
    grid-row-gap: 16px
    grid-column-gap: 16px
    grid-template-columns: 1fr 1fr
    &-item
      height: 72px
      border: 1px solid $quizGrey
      background: $white
      box-sizing: border-box
      border-radius: 8px
      display: flex
      align-items: center
      justify-content: center
      cursor: pointer
      transition: $transition
      &-text
        font-size: 21px
        line-height: 120%
        font-weight: 600
        text-align: center
        color: $black
      &-wrapp
        position: relative
        display: flex
        flex-direction: column
        justify-content: flex-end
        border-radius: 8px
        &.oneItem
          grid-column: 1/3
          justify-content: center
        &.quiz__active
          & .backgroundChange
            background: #FFDCBD
            border: none
        &-img
          width: 100%
  &__hr
    background: #EFEFEF
    height: 1px
    width: 100%
    max-width: 326px
    margin: 0 auto
  &__profile
    padding: 16px
    background: #FFFFFF
    box-shadow: 0 8px 24px 8px rgba(0, 0, 0, 0.06)
    border-radius: 16px
    overflow: hidden
    margin-bottom: 128px
    visibility: hidden
    opacity: 0
    transition: 1s ease-in-out
    transform: translateY(80px)
    &.shifted-up
      margin-bottom: 150px
    &.love
      & .zero-spacing
        margin: 0
      & .quiz__profile-alert
        background: #4CC0FF30
    &.active
      transition: 1s ease-in-out
      visibility: visible
      opacity: 1
      transform: translateY(0)
    &.heightFix
      & .quiz__profile-girl-img-wrapp
        height: 10vh
      & .quiz__profile-list-text-wrapp
        max-width: 200px
      & .quiz__profile-list-wrapp
        align-items: flex-end
    &-title
      font-weight: 800
      font-size: 14px
      line-height: 120%
      &-center
        display: flex
        align-items: center
        justify-content: center
      & .progressText
        color: #EF3A36
      &-wrapp
        display: flex
        align-items: center
        justify-content: space-between
        margin-bottom: 16px
    &-level
      color: #CCCCCC
      padding: 6px
      border: 1px solid #CCCCCC
      border-radius: 6px
      font-size: 10px
      font-weight: 800
      line-height: 100%
      &--is-aggression
        padding: 4px 6px
    &-graph
      margin-bottom: 16px
      width: 100%
      height: auto
      &.zero-spacing
        margin-bottom: 0
      &-text
        font-size: 8px
        line-height: 120%
        font-weight: 800
        color: #C5C6CB
        width: 100%
        white-space: nowrap
        text-align: center
        text-transform: uppercase
        letter-spacing: -0.02em
        &:first-child
          text-align: left
        &:last-child
          text-align: right
        &.active
          color: $black
        &-wrapp
          display: flex
          justify-content: space-between
          align-items: center
          position: relative
          top: -20px
        &-item
          font-size: 8px
          font-style: normal
          font-weight: 800
          line-height: 120%
          color: #C5C6CB
          text-transform: uppercase
          &.active
            color: $black
      & .tail
        opacity: 0
        transition: 0.3s ease-in-out
        &.active
          transition: 0.3s ease-in-out
          opacity: 1
      & .range-dot
        transform: translateX(-25%)
        opacity: 0
        transition: 0.6s ease-in-out
        &.active
          transition: 0.6s ease-in-out
          transform: translateX(0)
          opacity: 1
        &.aggressive-dot
          &.active
            transform: translateX(23%)!important
      &--text
        color: #CCC
        font-family: "Nunito Sans"
        font-size: 8px
        font-weight: 800
        line-height: 120%
        text-transform: uppercase
        letter-spacing: 0
        &-wrapp
          display: flex
          justify-content: space-between
          align-items: center
          position: absolute
          top: 62px
          left: 0
          right: 0
        &-hightlighted
          color: #16191E

    &-score
      display: flex
      justify-content: space-between
      align-items: center
      padding: 16px
      margin-bottom: 8px
      &-text
        color: $black-900
        text-align: center
        font-family: "Nunito Sans"
        font-size: 14px
        font-style: normal
        font-weight: 800
        line-height: 100%
        & b
          color: $blue-600
      &-line
        position: relative
        width: 86px
        height: 8px
        background-color: $blue-100
        border-radius: 5px
        &-progress-fill
          position: absolute
          top: 0
          left: 0
          height: 100%
          background: linear-gradient(to right, $blue-100, $blue-600)
          border-radius: 5px
          animation: progress-fill-grow 1s linear forwards
          @keyframes progress-fill-grow
            from
              width: 0
            to
              width: 50px
        &-icon
          position: absolute
          top: 50%
          transform: translateY(-50%)
          width: 27px
          height: 27px
          animation: icon-slide-in 1s linear forwards
        @keyframes icon-slide-in
          from
            right: 80%
          to
            right: 25%

    &-alert
      background: rgba(255, 87, 76, 0.19)
      border-radius: 8px
      padding: 8px
      display: flex
      margin-bottom: 16px
      &.nutrition
        background: $blue-100
      &-star
        display: flex
        width: 28px
        height: 28px
        padding: 2px
        align-items: center
        justify-content: center
        border-radius: 6px
        background: $blue-200
        margin-right: 8px
      &.master
        background: #E3F3F9
      &-title
        font-weight: 800
        font-size: 10px
        line-height: 14px
        padding-bottom: 2px
        &[data-i18n="quiz.love_profile.alert.impressive_score_title"]:before
          display: inline-block
          content: ""
          width: 12px
          height: 12px
          margin-right: 2px
          vertical-align: text-top
          background-size: contain
          background-repeat: no-repeat
          background-image: url("https://images.paw-champ.com/pc/emoji/glowing-star.png")
      &-text
        font-weight: 400
        font-size: 10px
        line-height: 120%
        &-wrapp
          display: flex
          flex-direction: column
      &-icon
        max-width: 28px
        width: 100%
        min-width: 28px
        margin-right: 8px
    &-list
      display: flex
      flex-direction: column
      margin-bottom: 16px
      transition: 0.3s ease-in-out
      width: 40%
      &.nutrition
        width: 70%
      &-item
        visibility: hidden
        opacity: 0
        transition: 0.3s ease-in-out
        margin-bottom: 16px
        display: flex
        &:last-of-type
          margin-bottom: 0
        &.active
          visibility: visible
          opacity: 1
          transition: 0.3s ease-in-out
      &-icon
        width: 100%
        &-wrapp
          max-width: 28px
          min-width: 28px
          margin-right: 8px
      &-title
        font-weight: 400
        font-size: 10px
        line-height: 14px
        padding-bottom: 2px
      &-text
        font-weight: 800
        font-size: 12px
        line-height: 120%
        &:first-letter
          text-transform: capitalize
        &-wrapp
          display: flex
          flex-direction: column
          & .problemsInsertSelector,
          & .healthTendInsertSelector,
          & .anxietySignalsInsertSelector
            display: flex
            flex-wrap: wrap
            & .quiz__profile-list-text
              margin-right: 4px
              &:after
                content: ','
              &:last-of-type
                &:after
                  content: none
          & .short-container
            width: 55%
      &-wrapp
        display: flex
        justify-content: space-between
        width: 100%
    &-man
      &-img
        width: 100%
        max-width: 200px
        position: relative
        bottom: -24px
        right: 0
        &.dog
          bottom: 0
        &-wrapp
          visibility: hidden
          opacity: 0
          transform: translate(100%)
          width: 60%
          display: flex
          justify-content: flex-end
          align-items: flex-start
          position: relative
          z-index: 1
          &.active
            transition: 0.4s ease-in-out
            visibility: visible
            opacity: 1
            transform: translate(0)
    &-girl
      &-img
        width: 100%
        max-width: 160px
        &.doggy
          position: relative
          bottom: -10px
          right: -20px
        &.nutrition
          position: absolute
          bottom: -16px
          right: -20px
          height: 215px
          width: 185px
          max-width: 215px

        &-wrapp
          visibility: hidden
          opacity: 0
          width: 50%
          display: flex
          flex-direction: column
          justify-content: flex-end
          align-items: flex-start
          position: relative
          transition: 0.4s ease-in-out
          transform: translate(100%)
          &.nutrition
            height: 200px
            max-width: 215px
          &--is-separation
            min-height: 226px
            img
              position: absolute
              right: -16px
              bottom: -16px
          &.active
            transition: 0.4s ease-in-out
            visibility: visible
            opacity: 1
            transform: translate(0)
          &.summary_var_pics_g3
            width: 125%
            max-width: 225px
            bottom: -21px
            right: -20px
          &.summary_var_pics_g2
            width: 125%
            max-width: 230px
            bottom: -18px
            right: 0
            z-index: 1
            & .quiz__profile-man-img
              max-width: 230px
              width: 150%
              right: -48px
    &-svg
      &-box
        position: relative
        margin-top: 40px
        &--is-separation
          margin-top: 10px
        &-reactive
          margin-bottom: 16px
        &--is-aggression
          margin-top: 4px
        & .tail
          opacity: 0
          position: absolute
          top: -8px
          left: 30%
          transition: opacity 0.3s ease-in-out
          &--is-reactivity
            left: 67%
            &.spanish
              left: 75%
          &.nutrition
            left: auto
            right: 10.5%
            & path
              fill: $black-700
          &.reactive
            left: 80%
          &.master
            left: 22%
          &.active
            opacity: 1
            transition: opacity 0.3s ease-in-out
            &.aggressive-tail
              left: 53%
          &.marker
            padding: 4px 6px
            border-radius: 6px
            background: #404751
            color: #FFF
            font-family: "Nunito Sans"
            font-size: 10px
            font-weight: 800
            line-height: 120%
            top: inherit
            left: inherit
            bottom: 72%
            right: 14%
            transform: translateX(50%)
            &:before 
              content: ''
              background: #404751
              width: 10px
              height: 10px
              transform: rotate(45deg)
              position: absolute
              z-index: -1
              bottom: -4px
              left: calc(50% - 5px)
      &-text
        font-weight: 800
        font-size: 12px
        line-height: 100%
        color: $white
        position: relative
        text-transform: capitalize
        &-box
          position: absolute
          padding: 6px 8px
          width: max-content
          background: #16191E
          border-radius: 8px
          top: -30px
          left: 22%
          opacity: 0
          transition: opacity 0.3s ease-in-out
          &.master
            left: 15%
          &.reactive
            left: 70%
          &.nutrition
            left: auto
            right: 6.5%
            background: $black-700
          &--is-reactivity
            left: auto
            right: 32%
            transform: translateX(50%)
            &.spanish
              right: 24%
          &.active
            transition: opacity 0.3s ease-in-out
            opacity: 1
          &.aggressive-position
            left: 44%
    &-arrow
      transition: 0.3s ease-in
      opacity: 0
      animation-name: fade
      animation-timing-function: ease-in-out
      animation-iteration-count: infinite
      animation-duration: 1s
      @keyframes fade
        0%
          opacity: 0
        100%
          opacity: 1
      &:nth-child(1)
        animation-delay: 0s
      &:nth-child(2)
        animation-delay: 0.2s
      &:nth-child(3)
        animation-delay: 0.4s
      &-box
        display: flex
        align-items: center
        justify-content: center
        position: absolute
        right: -40px
        bottom: 40px
        opacity: 0
        transform: translate(100%)
        transition: 1s ease-in-out
        &.active
          opacity: 1
          transform: translate(0)
          transition: 1s ease-in
        & .pink
          & path
            stroke: #FFF0F6
            stroke-opacity: 1
    &-love
      &-wrapp
        display: flex
        justify-content: space-between
        align-items: center
        padding: 16px
        background: #FFFFFF
        box-shadow: 0 8px 24px 8px rgba(0, 0, 0, 0.06)
        border-radius: 16px
        margin-bottom: 8px
      &-progressbar
        max-width: 86px
        height: 12px
        margin: 0!important
        & .progressBar
          height: 12px
          &:before
            width: 24px
            height: 24px
            right: -2px
  &__graph
    &.french
      & .quiz__graph-text
        text-align: center
      & .quiz__graph-svg-text
        max-width: 180px
    &-svg
      width: 100%
      height: auto
      &-goal
        position: absolute
        bottom: 85%
        right: 24.4%
        background: #1798cd
        color: $white
        font-weight: 700
        font-size: 14px
        line-height: 12px
        width: max-content
        padding: 8px 10px
        border-radius: 4px
        &:after
          content: ''
          width: 12px
          height: 12px
          background: #1798cd
          transform: translateX(50%) rotate(45deg)
          position: absolute
          z-index: -1
          bottom: -3px
          right: 50%
      & .graphBg
        transition: 0.6s ease-in-out
        opacity: 0
        &.active
          transition: 0.6s ease-in-out
          opacity: 1
      & .bgOpacity
        &.active
          opacity: 0.2!important
      & .graphLineVertical
        transition: 0.6s ease-in-out
        opacity: 0
        &.active
          transition: 0.6s ease-in-out
          opacity: 1
      & .tooltip-dot-selector
        transition: opacity 0.9s ease-in-out
        opacity: 0
        &.active
          transition: opacity 0.9s ease-in-out
          opacity: 1
      & .goalSelector
        opacity: 0
        transition: opacity 0.3s ease-in-out
        &.active
          opacity: 1
          transition: opacity 0.3s ease-in-out
      & .goal-dot-selector
        opacity: 0
        transition: opacity 0.3s ease-in-out
        &.active
          opacity: 1
          transition: opacity 0.3s ease-in-out
      & .iteration-show-el
        animation-name: FadeInDown
        animation-duration: .8s
        animation-fill-mode: both
        animation-timing-function: ease-out
        &-p10
          animation-delay: .5s
        &-p30
          animation-delay: 1s
        &-p60
          animation-delay: 1.5s
        &-p90
          animation-delay: 2s

        @keyframes FadeInDown
          from
            opacity: 0
            transform: translateY(-30px)
          to
            opacity: 1
            transform: translateY(0)
      &-inner
        width: 100%
        & .tail-tooltip
          transition: opacity 0.3s ease-in-out
          position: absolute
          top: 47%
          left: 36%
          opacity: 0
          visibility: hidden
          &--is-reactivity
            left: 36.7%
            top: 39%
          &.active
            transition: opacity 0.3s ease-in-out
            opacity: 1
      &-date
        transition: opacity 0.3s ease-in-out
        font-weight: 800
        font-size: 10px
        line-height: 100%
        text-align: right
        color: #D7D7D7
        text-transform: uppercase
        width: 28px
        opacity: 0
        &.active
          transition: opacity 0.3s ease-in-out
          opacity: 1
        &-wrapp
          width: 100%
          display: flex
          justify-content: space-between
          padding: 0 16px
          margin-top: -12px
      &-wrapp
        display: flex
        justify-content: center
        position: relative
        & .tail
          transition: opacity 0.3s ease-in-out
          opacity: 0
          visibility: hidden
          position: absolute
          top: 47%
          left: 36%
          &.active
            transition: opacity 0.3s ease-in-out
            opacity: 1
            visibility: visible
      &-text
        font-weight: 800
        font-size: 12px
        line-height: 100%
        color: $white
        position: relative
        &--is-restricted
          max-width: 170px
          text-align: center
        &-name
          font-size: 10px
          font-weight: 800
          line-height: 100%
          text-align: center
          color: #ffffff
          background: #1998CD
          margin-top: 8px
          width: max-content
          padding: 4px
          border-radius: 4px
          position: absolute
          bottom: 20%
          right: 30%
          z-index: 1
          &:before
            content: ''
            width: 12px
            height: 12px
            background: #1998CD
            transform: rotate(45deg)
            position: absolute
            z-index: -1
            top: -2px
            right: 43%
          &.iteration-show-el
            animation-name: FadeInDown
            animation-duration: .8s
            animation-fill-mode: both
            animation-timing-function: ease-out
            &-p10
              animation-delay: .5s
            &-p30
              animation-delay: 1s
            &-p60
              animation-delay: 1.5s
            &-p90
              animation-delay: 2s
          &--start
            left: calc(12% - 22px)
            margin: 0
            bottom: calc(60% + 4px)
            right: inherit
            background: #fff
            color: #D80027
            box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.16)
            &:before
              background: #fff
              top: calc(100% - 8px)
              right: inherit
              left: 20px
              transform: rotate(45deg)
          &--end
            right: 30%
            top: 64%
            bottom: inherit
            &:before
              right: 21px
              @media (min-width: 400px)
                right: 27px

            @keyframes FadeInDown
              from
                opacity: 0
                transform: translateY(-30px)
              to
                opacity: 1
                transform: translateY(0)
        &-box
          transition: opacity 0.3s ease-in-out
          position: absolute
          padding: 6px 8px
          width: max-content
          background: #16191E
          border-radius: 8px
          visibility: hidden
          opacity: 0
          bottom: 53%
          left: 28%
          &--is-reactivity
            left: 38% !important
            bottom: 60.5%
            transform: translateX(-50%)
            padding: 8px 12px

            .quiz__graph-svg-text
              font-size: 14px
          &.visible
            transition: opacity 0.3s ease-in-out
            opacity: 1
            visibility: visible
        & .tail
          transition: opacity 0.3s ease-in-out
          opacity: 0
          position: absolute
          bottom: -12px
          left: 30%
          &.active
            transition: opacity 0.3s ease-in-out
            opacity: 1
      &-animated-bars
        animation-name: AnimatedBars
        animation-iteration-count: 1
        animation-duration: 1.4s
        animation-timing-function: ease-out
        animation-fill-mode: forwards
        transform-origin: bottom
        transform: scaleY(0)

        @keyframes AnimatedBars
          to
            transform: scaleY(1)
      &-animated-tooltip-part
        animation-name: AnimatedTooltipPart
        animation-iteration-count: 1
        animation-delay: .8s
        animation-duration: 1s
        animation-timing-function: ease-out
        animation-fill-mode: forwards
        opacity: 0
        transform: translateY(-30px)

        @keyframes AnimatedTooltipPart
          to
            opacity: 1
            transform: translateY(0)
      &-animated-tooltip
        animation-name: AnimatedTooltip
        animation-iteration-count: 1
        animation-delay: .8s
        animation-duration: 1s
        animation-timing-function: ease-out
        animation-fill-mode: forwards
        opacity: 0
        transform: translateY(-30px) translateX(50%)

        @keyframes AnimatedTooltip
          to
            opacity: 1
            transform: translateY(0) translateX(50%)
      &-timing
        display: flex
        align-items: center
        justify-content: space-between
        padding: 0 16px
        &-text
          color: #D7D7D7
          font-size: 10px
          font-weight: 800
          line-height: 100%
          text-transform: uppercase
    &-date
      text-align: center
      margin-bottom: 8px
      font-weight: 800
      font-size: 20px
      line-height: 100%
      color: $blue
      text-transform: capitalize
      @at-root .newFun &
        font-weight: 600
        font-size: 16px
        color: $black
    &-text
      visibility: hidden
      margin: 0 auto 16px
      padding: 6px 16px
      font-size: 14px
      line-height: 100%
      max-width: max-content
      background: #F5F5F7
      border-radius: 4px
      &.active
        visibility: visible
    &-subtext
      font-weight: 400
      font-size: 10px
      line-height: 100%
      text-align: center
      color: #656565
      margin-top: 16px
    &.love
      & .quiz__graph-date
        color: $black
        font-size: 16px
        font-weight: 600
    &.training
      & .quiz__graph
        &-svg-text
          font-size: 15px
          &-box
            bottom: 79%
            left: auto
            right: 15%
            background: #1998CD
            padding: 9px
            &:before
              content: ""
              width: 12px
              height: 12px
              background: #1998CD
              display: block
              position: absolute
              right: -4px
              transform: rotate(45deg)
              top: calc(50% - 6px)
              border-radius: 2px
        &-monthes
          display: flex
          justify-content: space-between
          align-items: center
          padding-left: 24px
          padding-right: 8px
          margin-top: -12px
          &-item
            font-size: 10px
            font-weight: 800
            line-height: 100%
            text-transform: uppercase
            &.red
              color: #FF574C
            &.blue
              color: #1998CD

  &__redirect-loader
    display: none
    position: fixed
    z-index: 1000
    background: $white
    align-items: center
    justify-content: center
    width: 100%
    height: 100vh
    top: 0
    left: 0
    & .preload
      position: relative
      width: 40px
      height: 40px
      border: 5px solid $blue
      border-radius: 50%
      border-bottom-color: transparent
      animation-name: rotate
      animation-duration: 1.0s
      animation-iteration-count: infinite
      animation-timing-function: linear
      display: flex
      align-items: center
      justify-content: center
      &:before
        content: ''
        position: absolute
        width: 40px
        height: 40px
        background: rgba(25, 152, 205, 0.28)
        border-radius: 100%
      & span
        background: $white
        width: 30px
        height: 30px
        position: absolute
        border-radius: 100%

      @keyframes rotate
        from
          -webkit-transform: rotate(0deg)
          transform: rotate(0deg)
        to
          -webkit-transform: rotate(360deg)
          transform: rotate(360deg)
  &__based-graph-line
    stroke-dasharray: 1000
    animation: dash 12s
    animation-play-state: unset
    @keyframes dash
      from
        stroke-dashoffset: 1000
      to
        stroke-dashoffset: 0
  &__zero
    height: 100vh
    position: relative
    z-index: 300
    & .quiz__title
      margin-bottom: 24px
    & .quiz__subtitle
      font-size: 18px
      font-weight: 400
      margin-top: -10px
    &-logo
      width: 100%
      &-box
        max-width: 130px
        width: 100%
        margin: 32px auto 48px

    &-img
      width: 100%
      &-box
        max-width: 308px
        width: 100%
        margin: 0 auto
        &.wide
          max-width: none
    &-btn
      &-box
        display: grid
        grid-template-columns: repeat(2, 1fr)
        grid-column-gap: 16px
        & .quiz__btn
          position: relative
          bottom: auto
          width: 100%
          background: linear-gradient(180deg, #FF765A 5.8%, #FF4040 90.92%)
          text-transform: inherit!important
      &-wrapp
        display: grid
        grid-template-columns: 1fr
        & .quiz__btn
          position: relative
          bottom: auto
          width: 100%
          & svg
            margin-right: 8px
            margin-bottom: 1px
  &__progress-line
    margin: 0 auto
    width: 100%
    position: relative
    max-width: 288px
    &-wrapp
      padding: 24px 16px
      max-width: 504px
      width: 100%
      margin: 0 auto
      border-radius: 16px
      background: #EBFFE8
      border: 1px solid #BEECB7

    &-title
      color: #DBDBDB
      font-weight: 700
      font-size: 14px
      line-height: 140%

      &-box
        display: flex
        justify-content: space-between
        align-items: center
        margin-bottom: -12px

      &-main
        color: $black
        font-weight: 800
        font-size: 25px
        line-height: 140%
        text-align: center
        margin-top: 48px
        margin-bottom: 24px

    &-subtitle
      font-size: 14px
      text-align: center
      line-height: 120%
      margin: 8px 0
      font-weight: 700
      color: #DBDBDB

    & .blue
      color: #2A6AF8
    & .red
      color: #F55D3B
    & .green
      color: #00B67A
    &-text
      font-size: 12px
      margin-top: -4px
      color: #646464
    &-item
      margin-bottom: 16px
      &:last-of-type
        margin-bottom: 0
      & svg
        border: 0.3px solid #dddddd
    &-done
      &-wrapp
        display: flex
        align-items: center
        justify-content: center
        margin: 24px 0
      &-item
        width: 18px
        height: 18px
        display: flex
        align-items: center
        justify-content: center
        margin-right: 8px
        &:last-of-type
          margin-right: 0
  & .animation-slides
    background: $white
    top: 0
    left: 0
    right: 0
    width: 100%
    height: 100vh
    z-index: 1111
    opacity: 0
    visibility: hidden
    transition: 0.3s ease
    position: fixed
    &.active
      visibility: visible
      opacity: 1
      transition: 0.3s ease
    & .welcome
      display: flex
      flex-direction: column
      align-items: center
      justify-content: center
      background: #1F3E4A
      &__title
        color: #879B9F
        font-size: 21px
        font-weight: 700
        line-height: 120%
        opacity: 0
        visibility: hidden
        transition: 0.5s ease
        &.active
          transition-delay: 0.25s
          transition: 0.5s ease
          visibility: visible
          opacity: 1
      &__logo
        &-box
          margin-top: 8px
          opacity: 0
          visibility: hidden
          transition: 0.5s ease
          transform: translateY(32px)
          &.active
            transition-delay: 0.25s
            transition: 0.5s ease
            visibility: visible
            opacity: 1
            transform: translateY(0)
    & .problems
      flex-direction: column
      &__items
        display: flex
        flex-direction: column
        padding-top: 8px
        & .quiz__profile-list-text
          font-size: 18px
          font-weight: 700
          @include textGradient($gradient-red)
          list-style: disc
          &:before
            content: '•'
            margin-right: 8px
    & .goal
      flex-direction: column
      max-width: 440px
      & .goal__text-box
        display: flex
        align-items: center
        width: 100%
      & .goal__text-pre
        color: $black
      & .goal__text
        @include textGradient($gradient-red)
      & .goalsInsertSelector
        width: 100%
      & .addictionalText
        visibility: hidden
        @include textGradient($black)
        margin-left: 6px
        &.active
          visibility: visible

    & .plan
      &__text
        & .nameInsertSelector
          text-transform: capitalize
    &__item
      position: absolute
      top: 0
      left: 0
      right: 0
      width: 100%
      height: 100vh
      display: flex
      align-items: center
      justify-content: center
      font-size: 26px
      font-weight: 700
      line-height: 130%
      visibility: hidden
      opacity: 0
      transition: 0.5s ease
      padding: 0 40px 10vh
      max-width: 390px
      margin: 0 auto
      & span
        @include textGradient($gradient-red)
      &.active
        visibility: visible
        opacity: 1
        transition: 0.5s ease
  &__challenge
    background: #EBF9FF
    &-wrapper
      width: 100%
      height: 100%
      margin: 0 auto
      display: flex
      flex-direction: column
      justify-content: space-between
      align-items: center
    &-img
      width: calc(100% + 64px)
      max-width: 500px
      margin: 0 auto
      position: relative
      left: -32px
      &-box
        position: relative
        &:before
          content: ""
          position: absolute
          height: 80px
          left: 0
          right: 0
          bottom: -1px
          background: linear-gradient(to bottom, transparent, #ebf9ff)
          z-index: 1
    &-title
      padding: 32px 0 8px
      font-weight: 800
      font-size: 32px
      line-height: 100%
      text-align: center
      color: $blue
    &-subtitle
      font-weight: 800
      font-size: 20px
      line-height: 100%
      text-align: center
      color: $black
      padding: 0 24px 24px
    &-btn-box
      height: 40vh
      background: #ffffff
      width: 100%
      padding: 0 32px 24px
      position: fixed
      bottom: 0
      border-radius: 4px 4px 0 0
      display: -ms-flexbox
      display: flex
      -ms-flex-pack: center
      justify-content: center
      -ms-flex-align: end
      align-items: flex-end
      &:before
        content: ''
        background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1684933433/dog-training/img/better/thousand-top.svg") top no-repeat
        background-size: cover
        position: absolute
        width: 100%
        height: 64px
        top: -28px
        left: 0
        right: 0
        background: white
        border-radius: 40% 40% 0 0
  &__product
    margin-top: 10vh
    &-title
      text-align: left
      padding-left: 9%
    &-list
      max-width: 250px
      width: 100%
      margin: 0 auto
      position: relative
      left: 16px
      list-style: none
    &-item
      color: $black
      font-size: 14px
      font-weight: 400
      line-height: 110%
      margin-bottom: 16px
      &:last-child
        margin-bottom: 0
      &.splash
        position: relative
        &:before
          content: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1702483491/dog-training/icons/splash.svg")
          position: absolute
          left: -32px
          top: 3px
      &.okay
        position: relative
        &:before
          content: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1702483491/dog-training/icons/okay.svg")
          position: absolute
          left: -32px
          top: 0
      &-goal
        display: inherit
        text-transform: lowercase
  &__better-age
    display: grid
    grid-row-gap: 24px
    grid-column-gap: 16px
    grid-template-columns: 1fr 1fr
    padding-bottom: 48px
    &-item
      cursor: pointer
      &.quiz__active
        border: none
        background: none
      &-box
        display: flex
        flex-direction: column
        justify-content: center
        align-items: center
        padding: 4px 8px
        min-height: 48px
        background: $blue
        box-shadow: 0 4px 0 #1387B7
        border-radius: 0 0 8px 8px
        width: 100%
        color: #ffffff
      &-img
        position: relative
        &-box
          background: #FFFFFF
          border: 2px solid $blue
          border-radius: 8px 8px 0 0
          border-bottom: none
          display: flex
          flex-direction: column
          justify-content: space-between
          align-items: center
          min-height: 128px
          max-height: 128px
          position: relative
          &.puppy
            &:before
              content: ""
              position: absolute
              bottom: 0
              width: 140px
              height: 80px
              background: url('https://res.cloudinary.com/dr0cx27xo/image/upload/v1684913989/dog-training/img/bg-book.svg') no-repeat top
              background-size: cover
            & .quiz__better-age-item-img
              max-width: 80px
              bottom: -38px
          &.adolescent
            &:before
              content: ""
              position: absolute
              bottom: 0
              width: 140px
              height: 80px
              background: url('https://res.cloudinary.com/dr0cx27xo/image/upload/v1684913989/dog-training/img/bg-book.svg') no-repeat bottom
              background-size: cover
              transform: rotate(180deg)
            & .quiz__better-age-item-img
              max-width: 106px
              bottom: -14px
              left: 10px
          &.adult
            &:before
              content: ""
              position: absolute
              bottom: 0
              width: 140px
              height: 80px
              background: url('https://res.cloudinary.com/dr0cx27xo/image/upload/v1684913989/dog-training/img/bg-book.svg') no-repeat top
              background-size: cover
            & .quiz__better-age-item-img
              max-width: 68px
              bottom: 10px
          &.senior
            &:before
              content: ""
              position: absolute
              bottom: 0
              width: 140px
              height: 80px
              background: url('https://res.cloudinary.com/dr0cx27xo/image/upload/v1684913989/dog-training/img/bg-book.svg') no-repeat bottom
              background-size: cover
              transform: rotate(180deg)
            & .quiz__better-age-item-img
              max-width: 88px
              bottom: 16px
      &-text
        font-weight: 700
        font-size: 16px
        line-height: 22px
        display: flex
        align-items: center
        color: #FFFFFF
        text-shadow: 0 1px 0 rgba(0, 0, 0, 0.09)
      &-sub
        font-family: $main-font
        font-style: normal
        font-weight: 500
        font-size: 10px
        line-height: 100%
        text-align: center
        letter-spacing: -0.03em
        color: #FFFFFF
    &-title
      font-size: 24px
      line-height: 120%
      color: $black
      font-weight: 800
      text-align: center
      padding: 4px 0 16px
  &__text
    text-align: center
    width: 100%
    margin: -8px auto 24px
    display: block
    &-red
      font-size: 22px
      line-height: 120%
      font-weight: 800
      text-align: center
      margin: -8px 0 16px
      letter-spacing: -0.02em
      color: #F54A4B
  &__expert
    &-title
      font-size: 22px
      line-height: 120%
      font-weight: 800
      text-align: center
      margin-bottom: 24px
      & b, span
        @include textGradient($gradient-red)
        display: inline
    &-text
      font-size: 14px
      line-height: 120%
      text-align: center
      font-weight: 400
      &.italic
        font-style: italic
        margin-bottom: 24px
    &-name
      font-size: 14px
      font-weight: 700
      line-height: 100%
      margin-bottom: 4px
    &-position
      font-size: 14px
      line-height: 100%
      font-weight: 400
    &-img
      width: 100%
      max-width: 440px
      margin: 0 auto 24px
      &.width-100
        width: calc(100% + 32px)
        left: -16px
        position: relative
      &.nutrition
        height: 211px
        object-fit: contain
    &-wrapp
      max-width: 295px
      width: 100%
      margin: 0 auto
      background: #E9F0F3
      border-radius: 8px
      overflow: hidden
    &-box
      display: flex
      align-items: center
      padding: 12px 16px
      &-title
        background: #1998CD
        padding: 8px
        text-align: center
        font-size: 12px
        font-weight: 600
        letter-spacing: 0.6px
        color: $white
    &-icon
      margin-right: 8px
  &__universities
    padding-top: 32px
    position: relative
    display: flex
    flex-direction: column
    justify-content: center
    align-items: center
    &:before
      content: url('https://images.paw-champ.com/pc/images/figure-frames/lightgrey-1.svg')
      position: absolute
      top: 0
      z-index: -1
    &-item
      display: flex
      flex-direction: column
      justify-content: center
      align-items: center
      margin-bottom: 28px
      &:last-of-type
        margin-bottom: 0
  &__email-subscription
    &-title
      font-size: 22px
      line-height: 120%
      font-weight: 800
      text-align: center
      margin-bottom: 16px
      margin-top: 48px
      & span, b
        @include textGradient($gradient-red)
    &-btn
      &-box
        position: absolute
        bottom: 16px
        left: 0
        width: 100%
        & .quiz__btn
          position: relative
          margin: 0 auto
    &-input
      visibility: hidden
      height: 0
      width: 0
      margin: 0
    &-label
      font-size: 18px
      font-weight: 500
      color: #8A9198
      width: max-content
      margin: 0 auto
      text-align: center
      display: block
      cursor: pointer
      background-color: transparent
      border: none
      font-family: inherit
  &__quote
    &-title
      font-size: 26px
      line-height: 100%
      color: #1998CD
      font-weight: 800
      text-align: center
      margin: 32px 0 16px
      @at-root .newFun &
        width: 100%
      &.red
        @include textGradient($gradient-red)
      &.black
        color: $black
      & .red
        @include textGradient($gradient-red)

    &-subtitle
      font-size: 16px
      font-weight: 400
      text-align: center
      margin: 8px auto 48px
      max-width: 343px
      padding: 0 20px
      @at-root .newFun &
        margin-bottom: 24px
    &-apost
      position: absolute
      top: -16px
    &-wrapp
      padding: 24px 16px 16px
      border-radius: 12px
      background: #D3F2FF
      position: relative
      max-width: 300px
      width: 100%
      margin: 0 auto 24px
    &-text
      font-weight: 700
      line-height: 120%
      font-size: 20px
    &-hr
      border: none
      border-bottom: 1px solid #BCE6F8
    &-team
      &-logo
        max-width: 91px
        width: 100%
        margin-right: 4px
      &-wrapp
        display: flex
        align-items: center
        margin-top: 16px
      &-text
        font-size: 14px
    &_note
      width: calc(100% - 32px)
      max-width: 414px
      padding: 12px
      justify-content: center
      align-items: center
      border-radius: 8px
      border: 1px solid #FFF0D9
      background: #FFF6E6
      position: fixed
      margin: 0 auto 0
      bottom: 150px
      &.relative
        position: relative
        bottom: 0
      &-text
        color: $black
        text-align: center
        font-size: 16px
        font-style: normal
        font-weight: 400
        line-height: 130%
        & b
          font-weight: 700
    &-blue
      color: #1998CD
      background: #E1F2F9
      padding: 8px
      border-radius: 8px
      text-align: center
      max-width: 310px
      width: 100%
      margin: 0 auto
      & b
        font-weight: 800
      &.left-align
        text-align: left
    &-box
      width: 100%
      padding: 12px
      justify-content: center
      align-items: center
      border-radius: 8px
      border: 1px solid #FFF0D9
      background: #FFF6E6
      margin-top: 16px
      margin-bottom: 128px

  &__owners
    &-academic
      &-svg
        max-width: 300px
      &-wrapp
        display: flex
        align-items: center
        max-width: 300px
        margin: 0 auto
        & svg
          height: 100%
        &.golden // for pc_joinover split
          border: 1px solid #FFEBCC
          border-radius: 16px
          background: #FFF6E6
          max-width: 343px
          padding: 8px
      &-title
        font-size: 14px
        font-weight: 500
        margin-left: 16px
        & b
          font-weight: 700
    &-university
      &-wrapp
        display: grid
        grid-template-columns: repeat(3, 1fr)
        grid-column-gap: 16px
        max-width: 300px
        width: 100%
        margin: 0 auto
      &-item
        height: 96px
        display: flex
        align-items: center
        justify-content: center
        background: #F4F4F4
        border-radius: 16px
      &-title
        max-width: 255px
        width: 100%
        margin: 0 auto 16px
        text-align: center
        font-weight: 600
  &__plan
    &-title
      font-size: 22px
      line-height: 120%
      font-weight: 800
      text-align: center
      margin: 12px 0 16px
      text-transform: uppercase
      & .red
        @include textGradient($gradient-1)
        display: initial
    &-subtitle
      font-size: 16px
      line-height: 100%
      text-align: center
      margin: 16px 0 8px
      width: 100%
      &.smaller
        font-size: 14px
    &-text
      font-size: 14px
      line-height: 100%
      font-weight: 700
      text-align: center
      display: block
      margin: 16px auto 32px
      text-transform: uppercase
  &__owners
    &-subtitle
      font-size: 16px
      font-weight: 700
    &-img
      width: 100%
      &.radius
        border-radius: 16px
        overflow: hidden
        margin-bottom: 16px
    &-box
      max-width: 343px
      width: 100%
      margin: 0 auto
      &.wide
        max-width: none
    &-text
      text-align: center
      font-size: 16px
      font-style: normal
      font-weight: 400
      line-height: 130%
      &.left-align
        text-align: left
        margin-bottom: 16px
        &:last-of-type
          margin-bottom: 0
      & b
        font-weight: 700
  &__love
    &-svg
      width: 100%
      height: auto
    &-wrapp
      padding: 16px
      box-shadow: 0 1px 12.6px 8px rgba(0, 0, 0, 0.10)
      background: $white
      border-radius: 16px
      margin-top: 24px
    &-progress
      &-container
        width: 100%
        background: #FFE0E0
        border-radius: 100px
        position: relative
        margin: 16px 0
        border: 0.5px solid #FA9999
      &-bar
        width: 0
        height: 18px
        background: linear-gradient(90deg, #FFE0E0 0%, #EF3A36 100%)
        border-radius: 100px
        transition: 0.5s cubic-bezier(0.165, 0.84, 0.44, 1)
        position: relative
        &:before
          content: ''
          background: url("https://images.paw-champ.com/pc/icons/heart-progressbar.png") center center no-repeat
          background-size: contain
          width: 32px
          height: 32px
          position: absolute
          right: -14px
          top: -7px
      &-text
        position: absolute
        right: 12px
        top: 2px
        transition: 0.5s cubic-bezier(0.165, 0.84, 0.44, 1)
        color: #FA9999
        text-align: center
        font-size: 12px
        line-height: 120%
    &-title
      font-size: 22px
      line-height: 120%
      font-weight: 800
      text-align: center
    &-text
      font-size: 14px
      line-height: normal
      font-weight: 600
      display: block
      &.bottom-margin
        margin-bottom: 16px
      &.small
        color: #838383
        font-size: 12px
        font-style: normal
        font-weight: 400
        line-height: 120%
      &.thin
        font-weight: 400
      &-box
        &.inline
          display: flex
        & svg
          margin-right: 12px
      &.cut-width
        max-width: 250px
      &[data-i18n="quiz.love_level_disobedience_barrier.barrier_message"]:after
        display: inline-block
        content: ""
        width: 16px
        height: 16px
        vertical-align: inherit
        background-size: contain
        background-repeat: no-repeat
        background-image: url("https://images.paw-champ.com/pc/emoji/pensive-face-emoji.png")
    &-line
      border: none
      border-bottom: 1px solid #ECF1F6
      margin: 12px 0
    &-name
      &-label
        font-size: 12px
        color: #343434
        line-height: 100%
        margin-bottom: 4px
      &-input
        &::placeholder
          font-size: 16px
  &__dedicated
    &-wrapp
      display: flex
      align-items: center
      justify-content: center
      border-radius: 16px
      border: 1px solid #EAEDF0
      background: #FCFCFC
      padding: 16px
      margin-bottom: 24px
    &-box
      display: flex
      flex-direction: column
      margin-right: 16px
      &:last-of-type
        margin-right: 0
    &-text
      color: #1998CD
      text-align: center
      font-size: 12px
      font-weight: 800
      line-height: 100%
      margin-top: 8px
      text-transform: capitalize
    &-quote-text
      font-size: 14px
      font-weight: 400
      line-height: 130%
  &__cta
    &-title
      font-size: 14px
      font-weight: 800
      line-height: 120%
      padding-bottom: 2px
    &-text
      font-size: 14px
      line-height: 130%
    &-icon
      margin-right: 8px
      min-width: 28px
    &-wrapp
      display: flex
      background: #FFF6E6
      border-radius: 8px
      transition: height 0.5s ease, opacity 0.3s ease
      height: 0
      opacity: 0
      visibility: hidden
      padding: 0 12px
      overflow: hidden
      &.active
        height: 100%
        transition: height 0.5s ease, opacity 0.3s ease
        opacity: 1
        visibility: visible
        padding: 12px
        margin-bottom: 16px
  &__question-hint
    background: #FFF6E6
    border-radius: 8px
    border: 1px solid #FFF0D9
    padding: 12px
    margin-bottom: 128px
    margin-top: 48px
    text-align: center
    & b
      font-weight: 700
  &__alone
    &-wrapp
      border-radius: 16px
      border: 1px solid $gray-250
      background: $white
      box-shadow: 0px -9px 11.8px 0px rgba(0, 0, 0, 0.07), 0px 8px 24px 8px rgba(0, 0, 0, 0.06)
      padding: 16px
      position: relative
      z-index: 2
      margin-top: -36px
    &-box
      display: flex
      justify-content: space-between
      align-items: center
      margin-bottom: 8px
    &-img
      max-width: 302px
      width: 100%
      margin: 0 auto
      display: block
      min-height: 228px
    &-text
      font-size: 14px
      &-bottom
        font-weight: 700
        line-height: 110%
        margin-top: 24px
        text-align: center
        color: $gray-700
    &-name
      font-size: 20px
      font-style: normal
      font-weight: 800
      line-height: 100%
  &__trustpilot
    width: 100%
    height: auto
.legal
  &.bottom-position
    position: fixed
    bottom: 40px
    left: 0
    right: 0
    z-index: 1000
    max-width: 440px
    width: 100%
    margin: 0 auto
    &.bgc-white
      background: white
      padding: 58px 0 40px
      bottom: 0
      box-shadow: 0 0 20px white
  &.top-position
    margin-top: -124px
  &__wrapp
    font-size: 12px
    font-weight: 300
    line-height: 140%
    text-align: center
    margin-top: 12px
    & a
      font-weight: 400
      color: #3C567D
      text-decoration: underline

.quiz__magic-question
  &-title
    font-weight: 800
    &-wrapp
      display: flex
      align-items: center
      margin-bottom: 16px
    &-icon
      margin-right: 8px
    &-goal
      font-weight: 400
  &-progressbar
    max-width: 140px
    width: 100%
    &.question
      height: auto
    &.hidden
      display: none
  &-done
    display: none
    margin: 0 auto
    &.active
      display: flex
  &-text
    font-size: 12px
    line-height: 120%
    color: $black
    text-align: center
  &-list
    &-title
      font-weight: 800
      margin-bottom: 4px
    &-item
      margin-bottom: 12px
      display: flex
      font-size: 14px
      &.disk
        display: flex
        align-items: center
        margin-left: 8px
        &:before
          content: ''
          position: relative
          margin-right: 12px
          background: #1998CD
          width: 7px
          height: 7px
          display: block
          border-radius: 100%
      &.default
        margin-bottom: 0

        &:before
          content: ''
          background: $black
          width: 4px
          height: 4px
          margin-right: 8px
      &:before
        content: ''
        position: relative
        margin-right: 12px
        background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1726672479/paw-champ/icons/blue-done-icon.svg") center center no-repeat
        background-size: contain
        width: 18px
        height: 18px
        display: block
      & .status
        padding: 4px
        line-height: 100%
        border-radius: 6px
        margin-left: 8px
        background: #FFEA79
        color: #584F1F
        &:before
          content: "Not required"

        &--included
          background: #AAEF80
          color: #2C5123
          &:before
            content: "Included"


  &-box
    display: flex
    margin-bottom: 16px
    &:last-of-type
      margin-bottom: 0
  &-result
    &-box
      padding: 0 24px
    &-text
      font-size: 14px
      line-height: 120%
      padding: 16px 0
    &-title
      font-weight: 800
      font-size: 16px
      margin-bottom: 16px
    &-list
      display: flex
      flex-wrap: wrap
      &-item
        background: #D1F1FF
        padding: 4px 8px
        margin-right: 8px
        margin-bottom: 8px
        border-radius: 6px
        font-size: 14px

  &-img
    max-width: 230px
    width: 100%
    margin: 0 auto
    &-wrapp
      width: 100%
      display: flex
      align-items: center
      justify-content: center
      margin-bottom: 16px

.sizingDot
  width: 100px
  height: 100px
  border-radius: 100%
  background: $white
  transform: scale(0)
  z-index: 9999
  margin: 0 auto
  position: absolute
  &.active
    transition: 2s ease
    transform: scale(15)

.noom
  &__title
    &-top
      font-size: 14px
      font-weight: 400
      text-transform: uppercase
      padding-bottom: 4px
      & .nameInsertSelector
        text-transform: uppercase!important

    &-goal
      font-size: 22px
      font-weight: 800
      line-height: 140%
      padding-bottom: 8px
    &-date
      color: #1998CD
      font-size: 22px
      font-weight: 800
      margin-top: 4px
      & .thin
        font-weight: 400
        color: $black
  &__list
    padding-bottom: 16px
    margin-left: 48px

    &-title
      font-size: 16px
      line-height: 110%
      padding-bottom: 8px
      font-weight: 400
    &-item
      list-style: disc
      font-size: 14px
      font-weight: 400
      line-height: 130%
      & .nameInsertSelector
        text-transform: capitalize
  &__info
    padding: 16px
    border-radius: 8px
    background: rgba(113, 179, 147, 0.28)
    margin-bottom: 80px
    &-title
      font-size: 16px
      font-weight: 800
      line-height: 100%
      padding-bottom: 8px
      text-align: center
    &-text
      font-size: 14px
      font-weight: 400
      line-height: 130%

.magicprogressbar
  & .quiz__reviews-loading-text
      font-weight: 800

.progressbar
  width: 240px
  height: 240px
  margin: 0 auto 8px
  display: flex
  align-items: center
  justify-content: center
  position: relative

  &.reviews
    width: 128px
    height: 128px


  &__text
    font-size: 64px
    font-weight: 700
    color: $black
    position: absolute

    &-reviews
      font-size: 38px
      font-weight: 800
      color: $black
      position: absolute
      line-height: 100%
      display: flex
      align-items: flex-end
      & span
        font-size: 20px
        line-height: 150%


  &__line
    margin-bottom: 16px
    width: 100%
    height: 8px

    & svg
      border-radius: 40px

    &-text
      font-weight: 700
      font-size: 14px
      line-height: 100%

      &_1
        width: 100%
        color: #49D64E

      &_2
        width: 100%
        color: #FF7A5C

      &_3
        width: 100%
        color: #2995F8

  & svg
    border-radius: 100%
    filter: drop-shadow(0 8px 16px #b0c1b266)

    path:nth-child(1)
      stroke: #EFEFEF

.hisHerName
  text-transform: lowercase
.hideAggressiveButtons
  & .quiz__answer-item
    display: none

.pb-intro-loading
  background: $black
  border-radius: 16px
  display: flex
  align-items: center
  justify-content: center
  padding: 2px
  min-height: 24px
  &.g-six
    width: calc(100% - 32px)
  &.g-seven
    background: #eeeeee
  &__wrapp
    width: 100%
    position: fixed
    left: 0
    display: flex
    flex-direction: column
    justify-content: center
    align-items: center
    bottom: 56px
    &:before
      content: ""
      position: absolute
      height: 40px
      left: 0
      right: 0
      top: -21px
      background: linear-gradient(to bottom, transparent, #ffffff)
      z-index: 1
    &:after
      content: ""
      position: absolute
      height: 80px
      left: 0
      right: 0
      bottom: -80px
      background: #ffffff
  &__age
    display: none
  &__text
    color: $white
    position: absolute
  & svg
    border-radius: 16px
    width: 100%
    min-height: 23px
  &__img
    width: auto
    max-width: 230px
    &.creative
      max-width: 343px
      width: 100%
      height: 343px
      margin-bottom: 16px
      object-fit: contain
      background: #eeeeee
    &-box
      display: flex
      align-items: center
      justify-content: center
      position: relative
      &:before
        content: ""
        position: absolute
        height: 40px
        left: 0
        right: 0
        bottom: 0
        background: linear-gradient(to bottom, transparent, #ffffff)
        z-index: 0
  &__loader
    display: flex
    align-items: center
    justify-content: center
    padding-bottom: 8px
    margin-top: -8px
    z-index: 1
    position: relative
    &-img
      animation: rotate 1s linear infinite
      margin-left: 4px
      @keyframes rotate
        from
          transform: rotate(0deg)
        to
          transform: rotate(360deg)
  &__under-text
    & .quiz__title-under
      margin-bottom: -8px
  &__gender
    & .pink
      background: #FFE4E4
      height: 64px
      border: none
      &.quiz__active
        border: none!important
        box-shadow: none!important
    & .blue
      background: #E3F0FF
      height: 64px
      border: none
      &.quiz__active
        border: none!important
        box-shadow: none!important
    &-img
      & .pb-intro-loading__loader
        margin-top: 0
      & .pink
        background: #FFE4E4
        height: 64px
        border: none
        &.quiz__active
          border: none!important
          box-shadow: none!important
      & .blue
        background: #E3F0FF
        height: 64px
        border: none
        &.quiz__active
          border: none!important
          box-shadow: none!important
      & .quiz__title-under
        margin-bottom: -8px

.master-funnel
  & .quiz__profile
    &-girl-img
      max-width: 148px
      &-wrapp
        position: absolute
        bottom: 16px
        right: 0
    &-svg-text-box
      background: #404751
  & .tail
    & path
      fill: #404751
.vagus
  & .gender
    & .quiz__plan-subtitle
      margin: 32px 0 8px
    & .quiz__plan-title
      margin: 0 0 16px
    & .quiz__plan-text
      margin: 0 0 16px

.newFun
  &.breed
    & .quiz__subtitle
      font-size: 16px
      font-weight: 400
    & .quiz__datalist-items
      &::placeholder
        font-size: 16px

    & .quiz__breeds-item
      border-radius: 8px
  & :not(.quiz__btn).quiz__active,
  & .quiz__multiple-checked
    .quiz__multiple-dot
      &:before
        width: 12px
        height: 12px
        border-radius: 50%
        animation-name: FadeIn
        animation-duration: .3s

        @keyframes FadeIn
          from
            opacity: 0
          to
            opacity: 1
  & .quiz__multiple-dot
    min-width: 20px
    width: 20px
    height: 20px
  & .quiz__popup
    max-width: 270px
    width: 100%
    &-text
      padding-bottom: 0
    &-btn
      &-list
        padding: 16px
        border: none
        grid-column-gap: 16px
      &-item
        border: 1px solid #E8E8E8
        background: #F4F4F4
        padding: 0 8px
        height: 40px
        border-radius: 6px

.group3-pink
  background: #FEBAE1
  & .header-quiz__inner-bg
    background: transparent
  & .quiz__dog
    &-item
      border: none
      &-age-img
        position: relative
        z-index: 1
        &-box
          position: relative
          display: flex
          align-items: center
          justify-content: center
          &:before
            content: ''
            position: absolute
            width: 100%
            height: 100%
            z-index: 0
            bottom: -24px
          &.puppy,
          &.adult
            &:before
              content: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1722420066/paw-champ/img/quiz/age-dogs/bg/default-pink-left.svg")
          &.adolescent,
          &.senior
            &:before
              content: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1722420066/paw-champ/img/quiz/age-dogs/bg/default-pink-right.svg")
      &-wrapp
        border: none
        border-radius: 14px
        box-shadow: none
        background: #F0A2CF
        &.quiz__active
          background: #DD78B2
          border: none
          & .quiz__dog-item
            border: none
            height: 100%
            &-age-img-box
              &.puppy,
              &.adult
                &:before
                  content: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1722420066/paw-champ/img/quiz/age-dogs/bg/active-pink-left.svg")
              &.adolescent,
              &.senior
                &:before
                  content: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1722420066/paw-champ/img/quiz/age-dogs/bg/active-pink-right.svg")

.group2-toxic
  background: #B6FF00
  & .header-quiz__inner-bg
    background: transparent
  & .quiz__dog
    &-item
      border: none
      &-age-img
        position: relative
        z-index: 1
        &-box
          position: relative
          display: flex
          align-items: center
          justify-content: center
          &:before
            content: ''
            position: absolute
            width: 100%
            height: 100%
            z-index: 0
            bottom: -24px
          &.puppy,
          &.adult
            &:before
              content: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1722940198/paw-champ/img/quiz/age-dogs/bg/toxic-default-left.svg")
          &.adolescent,
          &.senior
            &:before
              content: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1722940198/paw-champ/img/quiz/age-dogs/bg/toxic-default-right.svg")
      &-wrapp
        border: none
        border-radius: 14px
        box-shadow: none
        background: #A6DF16
        &.quiz__active
          background: #8BB81D
          border: none
          & .quiz__dog-item
            border: none
            height: 100%
            &-age-img-box
              &.puppy,
              &.adult
                &:before
                  content: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1722940198/paw-champ/img/quiz/age-dogs/bg/toxic-active-left.svg")
              &.adolescent,
              &.senior
                &:before
                  content: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1722940198/paw-champ/img/quiz/age-dogs/bg/toxic-active-right.svg")

//for split pc_1stnewlog
.firstnewlog
  & .quiz
    &__btn
      bottom: auto
      position: relative
      width: 100%
    &__subtitle
      margin-top: 16px
    &__title
      &-box
        display: flex
        align-items: center
        margin-bottom: 16px
      &-img
        max-width: 104px
        width: 100%
        margin-left: 16px
    &__subtitle
      font-weight: 700
    &__dog
      &-item
        display: flex
        align-items: center
        justify-content: center
        border: none
        &-box
          padding-bottom: 0
        &-wrapp
          min-height: auto
          height: 60px
          display: flex
          align-items: center
          justify-content: center
    &__calm
      &-title
        font-size: 20px
        font-weight: 800
        line-height: 110%
      &-top
        display: flex
        align-items: flex-end
        justify-content: space-between
        margin-top: 24px
        &-pup
          &-img
            width: 100%
            max-width: 112px
            position: relative
            top: 8px
            z-index: -1
          &-box
            width: 40%
            display: flex
            align-items: center
            justify-content: center
        &-advanced
          &-img
            width: 100%
            margin-bottom: 8px
          &-box
            width: 60%
            padding-left: 8px
          &-subtext
            padding: 2px 6px
            color: #FEFEFE
            background: #333333
            border-radius: 8px
            font-size: 14px
            font-weight: 800
            line-height: 100%
            width: max-content
            margin: 6px 0 8px
        &-users
          &-img
            width: 100%
          &-box
            background: #333333
            border-radius: 8px
            padding: 12px 16px
            display: flex
            align-items: center
            width: 70%
          &-text
            font-size: 12px
            font-weight: 400
            line-height: 100%
            color: $white
            text-wrap: nowrap
            &-big
              font-size: 16px
              font-weight: 800
              line-height: 130%
              color: $white
            &-box
              margin-left: 8px
            &.small
              font-size: 10px
      &-price
        &-inn
          display: flex
          align-items: center
          justify-content: space-between
          margin-bottom: 16px
          margin-top: 4px
        &-rate
          position: relative
          &-box
            display: flex
            flex-direction: column
            justify-content: center
            align-items: center
            font-size: 22px
            font-weight: 800
            line-height: 130%
            width: 64px
          &-inn
            display: flex
            align-items: center
            justify-content: flex-end
            width: 40%
          &-img
            &-box
              display: flex
              flex-direction: column
              justify-content: center
              align-items: center
        &-box
          border-radius: 0 8px 8px 0
          background: $blue
          padding: 8px 16px
          margin-left: -16px
        &-off
          font-size: 14px
          font-weight: 800
          line-height: 130%
          color: $blue
          padding: 4px 8px
          margin-bottom: 12px
          background: $white
          width: max-content
          border-radius: 4px
        &-total
          color: $white
          font-size: 26px
          font-weight: 800
          margin-right: 8px
        &-old
          color: $white
          font-size: 16px
          line-height: 100%
          text-decoration: line-through
        &-wrapp
          display: flex
          align-items: flex-end
          margin-bottom: 8px
        &-text
          color: $white
          font-weight: 600
      &-list
        &-title
          font-size: 26px
          font-weight: 800
          line-height: 110%
          margin-bottom: 16px
        &-item
          margin-bottom: 16px
          position: relative
          display: flex
          &:before
            content: ''
            position: relative
            margin-right: 12px
            background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1722590534/paw-champ/icons/done.svg") center center no-repeat
            background-size: contain
            width: 18px
            height: 18px
            display: block
          &:last-of-type
            margin-bottom: 0
        &-inn
          margin-bottom: 32px


.challenge
  & .quiz__owners-academic-wrapp
    padding-bottom: 128px

//for split pc_trust
.trust
  & .quiz__quote-subtitle
    &.bottom-spacing
      margin-bottom: 16px
  & .graphic-bars, .graphic
    & .quiz__owners-academic-wrapp
      max-width: none
    & .quiz__graph-subtext
      color: $gray-700
  & .quiz__reviews-loading-text
    font-size: 14px
    font-weight: 800
    line-height: 100%
  & .quiz__result-input
    border: 1px solid $gray-250
    background: $white
    height: 48px
    padding: 0 16px
    &::placeholder
      color: $gray-500
  & .alone
    & .quiz__quote-title
      margin: 24px 0 8px

.me-challenge
  &__subtitle
    font-size: 12px
    font-weight: 700

@media (min-height: 668px)
  .quiz
    &__intro
      &-dog
        min-height: 400px
    &__challenge
      &-btn-box
        height: 47vh

  .progressbar
    &__line
      & svg
        width: 100%
        height: 4px

@media (min-width: 440px)
  .quiz
    &-footer
      &.first-step
        position: fixed
    &__intro
      &-applica
        &-wrapp
          &:before
            overflow: inherit
    &__graph
      &-svg
        &-text
          &-box
            left: 30%
    & .animation-slides
      &.container
        max-width: none
      &__item
        max-width: none
        width: 100%
    &__email-subscription
      &-btn
        &-box
          left: 0


@media (min-width: 768px)
  .quiz
    &__bg-white
      background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1629990465/dog-training/img/bottom-figure.svg") bottom center no-repeat
      background-size: 240%

    &__preview
      &-wrapper
        &:before
          overflow: inherit
          height: 110%

      &-img
        &-desk
          display: block
          width: calc(100% + 20rem)
          left: -10rem
          position: relative
          margin: 32px auto 80px

        &-mob
          display: none

    &__intro
      &-applica
        overflow: inherit

        &-img
          width: 100%
          max-width: 340px


  .progressbar
    &.reviews
      width: 160px
      height: 160px

  .pb-intro-loading
    &__wrapp
      &.g6
        position: relative
        bottom: 0

@media (min-width: 992px)
  .quiz
    &__bg-white
      background-size: 90%

    &__index
      & .quiz__intro-box
        max-width: 1024px

    &__intro
      &-wrapper
        background: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1630062223/dog-training/img/intro-quiz-bg-desk.svg") no-repeat top center
        background-size: 150%


@media (max-width: 440px)
  .quiz
    &__range
      &-disabled
        &-tooltip
          &.spanish
            &:before
              left: 110px
          &:before
            left: 114px
    &__challenge
      &-btn-box
        height: 20vh
    &-footer
      &.first-step
        position: relative

@media (max-width: 393px)
  .quiz
    &__range
      &-disabled
        &-tooltip
          &.spanish
            &:before
              left: 100px
          &:before
            left: 104px
    &__challenge
      &-btn-box
        height: 20vh


@media (max-width: 375px)
  .quiz
    &__mid
      &-need-ask
        max-width: 150px
        border: 3px solid #FF6C6C

      &-box
        margin-top: 24px

        &:before
          content: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1651654060/dog-training/img/need-to-ask-small.svg")
          bottom: -8px

        &-text
          font-size: 22px

      &-go-ahead
        max-width: none
        width: auto
        height: 138px
        margin: 24px auto 0

      &-title
        font-size: 22px

    &__range-disabled-tooltip
      &.spanish
        &:before
          left: 91px
      &:before
        left: 94px
      &.portugal
        &:before
          left: 37%

    &__preview
      &-wrapper
        &:before
          align-items: flex-start
          margin-top: 150px
          overflow: hidden

      &-img
        &-mob
          max-width: 250px
    &__body
      &.zero-question
        & .quiz-footer.first-step
          position: relative
    &__profile
      &-man
        &-img
          &-wrapp
            &.summary_var_pics_g2
              & img
                bottom: -34px
    &__challenge
      &-btn-box
        height: 20vh


@media (max-width: $mobile320)
  .quiz
    height: 120%

    &__dog
      &-item
        &-img
          margin: 0

    &__range
      &-disabled
        &-tooltip
          &:before
            left: 67px

    &__answer
      &-item
        height: 64px
        font-size: 18px

        &-grid
          height: 64px
          line-height: 110%
          font-size: 18px

        &-text
          height: 64px

    &__dog-item
      height: 64px

      &-text
        font-size: 18px

    &__mid-go-ahead
      height: 88px

    &__know-these
      &-box
        padding: 8px 16px
        margin-top: 16px

      &-item
        height: 64px
        font-size: 18px

      &-text
        font-size: 15px

    &__multiple
      &-checkbox
        height: 64px

      &-item
        font-size: 18px
        height: 64px
      &.deutch
        & .quiz__multiple-item
          font-size: 18px!important

    &__range-value
      font-size: 88px
      width: 110px
      height: 110px

    & [type="radio"]
      &:checked + label, &:not(:checked) + label
        font-size: 18px

    &__intro
      &-applica
        &-img
          width: 150px
    &__profile
      &-man
        &-img
          &-wrapp
            &.summary_var_pics_g2
              & img
                bottom: -80px
    &__dog-two
      & .quiz__dog-item svg
        margin: 0 0 12px
        width: auto
        height: auto
  .pb-intro-loading
    &__img
      &.creative
        height: auto
