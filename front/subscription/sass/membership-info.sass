@import "abstracts/variables"
@import "abstracts/mixins"
@import "base/reset"
@import "base/base"
@import "utilities"

.membership-info
  display: flex
  align-items: center
  padding: 32px 0
  background: $white

  &_inner
    max-width: 440px

  &_wrapp
    align-items: center
    justify-content: center
    height: 100%
    width: 100%
    position: fixed
    top: 0
    left: 0
    bottom: 0
    transition: 0.2s ease-in-out
    display: none
    z-index: 1
    &-container
      display: flex
      justify-content: center
    &.active
      display: flex
    &-popup
      display: flex
      width: 100%
      max-width: 408px
      padding: 16px
      flex-direction: column
      justify-content: center
      align-items: center
      text-align: center
      gap: 16px
      border-radius: 16px
      background: $white
      box-shadow: 0px 2px 12.5px 8px rgba(0, 0, 0, 0.06)
      position: relative
      &-bg
        position: absolute
        width: 100%
        height: 100%
        top: 0
        right: 0
        bottom: 0
        background: rgba(30, 31, 33, 0.26)
      &-content
        display: flex
        flex-direction: column
        gap: 8px
      &-header
        font-family: $nunitoSans
        font-size: 22px
        font-style: normal
        font-weight: 800
        line-height: 100%
        margin-bottom: 8px
        &.restore
          text-align: left
      &-description
        color: $gray-700
        text-align: center
        font-family: $nunitoSans
        font-size: 16px
        font-style: normal
        font-weight: 400
        line-height: 100%
        &.restore
          text-align: left
          line-height: 125%
          a
            text-decoration: underline
            color: $gray-700
      &-actions
        display: flex
        gap: 16px
        width: 100%
      &-btn
        width: 100%
        display: flex
        height: 44px
        padding: 12px 8px
        justify-content: center
        align-items: center
        cursor: pointer
        border-radius: 8px
        border: 1px solid $black
        font-family: $nunitoSans
        font-size: 16px
        font-style: normal
        line-height: normal
        letter-spacing: -0.48px
        &.reset
          background: $white
          color: $black
          font-weight: 600
        &.success
          background: $black
          color: $white
          font-weight: 700
  &_header
    color: $page-black
    text-align: center
    font-size: 24px
    font-style: normal
    font-weight: 800
    line-height: 100%
    letter-spacing: -0.72px

  &_avatar
    margin: 24px 0
    display: flex
    flex-direction: column
    align-items: center
    gap: 8px

    &_img
      width: 120px
      height: 120px
      background: $quick-silver
      border-radius: 34px
      padding: 4px

    &_email
      color: $page-black
      text-align: center
      font-size: 16px
      font-style: normal
      font-weight: 600
      line-height: 100%
      letter-spacing: -0.48px

  &_card
    display: flex
    padding: 16px
    flex-direction: column
    justify-content: center
    background: $white
    gap: 12px
    align-self: stretch
    border-radius: 16px
    box-shadow: 0 2px 12.5px 8px rgba(0, 0, 0, 0.06)
    margin-bottom: 24px
    &_line
      width: 100%
      border-top: 1px solid #EAEDF0
    &_wrapp
      display: flex
      justify-content: space-between
      align-items: center

    &_title
      color: $page-black
      font-size: 16px
      font-style: normal
      font-weight: 800
      line-height: 100%
      letter-spacing: -0.48px

    &_list
      display: flex
      flex-direction: column
      gap: 8px

      &_status
        display: flex
        gap: 6px
        align-items: center

      &_text
        color: $page-black
        font-size: 14px
        font-style: normal
        font-weight: 400
        line-height: 100%
        letter-spacing: -0.42px

        & b
          font-weight: 600
        &-problems
          line-height: 120%

        &-status
          border-radius: 6px
          padding: 4px 6px
          font-weight: 600
          text-transform: capitalize

          &.active
            background: $tea-green
            color: $verse-green

          &.cancelled
            background: $misty-rose
            color: $bittersweet-shimmer

          &.cancel_in_progress
            background: $lightBlue
            color: $cyan-cornflower-blue
    &_link
      color: $page-black
      font-size: 13px
      font-style: normal
      font-weight: 700
      line-height: 100%
      letter-spacing: -0.39px
      text-decoration-line: underline
      margin-top: 16px
      cursor: pointer
      border: none
      background: none
      width: max-content
      padding: 0

    &_note
      display: flex
      padding: 8px
      align-self: stretch
      color: $page-black
      font-size: 14px
      font-style: italic
      font-weight: 400
      line-height: 100%
      letter-spacing: -0.42px
      border-radius: 8px
      background: $cornsilk
      &-gray
        color: #727578
        font-size: 14px
        font-style: italic
        font-weight: 400
        line-height: 100%
        letter-spacing: -0.42px
        align-self: stretch
    &_restore-btn
      padding: 8px
      border: none
      text-align: center
      font-family: "Nunito Sans"
      font-size: 16px
      font-style: normal
      font-weight: 400
      line-height: 130%
      text-decoration: underline
      background-color: transparent
      cursor: pointer
      color: $black
      display: none
      &.active
        display: block
    &_box
      display: flex
      flex-direction: column
    &_btn
      @include btn-white-black(16px)
      height: 32px
      padding: 0 8px
      font-weight: 400
      transition: $transition
      & svg
        margin-right: 4px
      &:active
        background: $black
        border-color: $black
        color: $white
        transition: $transition
      &:disabled
        opacity: 0.3
        transition: $transition
        pointer-events: none
      &.refresh-btn
        z-index: 0

  &_support
    display: flex
    flex-direction: column
    gap: 8px

    &_btn
      display: flex
      width: 100%
      max-width: 440px
      margin: 0 auto
      height: 44px
      padding: 12px 8px
      justify-content: center
      align-items: center
      border-radius: 8px
      text-align: center
      font-size: 16px
      font-style: normal
      font-weight: 700
      line-height: normal
      letter-spacing: -0.48px
      cursor: pointer

      &_wrapper
        display: flex
        align-items: flex-start
        gap: 16px
        align-self: stretch
        .membership-discount__btn
          display: none

      &.btn-white
        border: 1px solid $page-black
        color: $page-black

      &.btn-black
        background: $page-black
        color: $white
        text-shadow: 0 4px 8px rgba(0, 0, 0, 0.08)
        margin-bottom: 16px

      &.btn-trans
        background: $white
        color: $black
        border: 1px solid $black

    &_title
      color: $page-black
      text-align: center
      font-weight: 400
      line-height: 100%
      letter-spacing: -0.42px

      &-bold
        font-weight: 700
        line-height: 150%

.membership-subscription
  position: fixed
  top: 0
  left: 0
  background: $white
  z-index: 120
  width: 100%
  display: none
  overflow: auto
  height: 100vh
  padding: 24px 0 18px

  &.active
    display: block

  &_inner
    max-width: 440px
    width: 100%
    margin: 0 auto

  &_back
    cursor: pointer
    display: flex
    height: 38px
    align-items: center
    gap: 8px
    &.hide
      visibility: hidden
    &_text
      color: $page-black
      text-overflow: ellipsis
      font-size: 18px
      font-style: normal
      font-weight: 600
      line-height: 130%
      letter-spacing: -0.54px
      pointer-events: none

  &-header
    color: $page-black
    text-align: center
    font-size: 24px
    font-style: normal
    font-weight: 800
    line-height: 100%
    letter-spacing: -0.72px
    margin-top: 16px

  &-title
    color: $page-black
    text-align: center
    font-size: 16px
    font-style: normal
    font-weight: 500
    line-height: 100%
    letter-spacing: -0.48px
    margin-top: 8px

  &-img
    height: 343px
    max-width: 343px
    width: 100%
    display: flex
    margin: 8px auto

  &-note
    color: $page-black
    text-align: center
    font-size: 14px
    font-style: normal
    font-weight: 500
    line-height: 120%
    letter-spacing: -0.42px
    margin-bottom: 8px

  &-btn
    position: relative
    width: 100%
    bottom: 0
    &-container
      display: flex
      justify-content: space-between
      align-items: center
      position: relative
    &-close
      cursor: pointer
      display: flex
      align-items: center
      justify-content: center
      z-index: 10
      position: relative
      width: 24px
      height: 24px

  &_reasons
    display: flex
    width: 100%
    flex-direction: column
    align-items: flex-start
    margin: 24px 0
    gap: 16px
    align-self: stretch

    &_item
      display: flex
      height: 64px
      padding: 16px
      align-items: center
      gap: 8px
      align-self: stretch
      border-radius: 8px
      background: $anti-flash-white
      cursor: pointer
      &:has(input:checked)
        border: 1px solid $page-black

      &_text
        color: $page-black
        font-size: 16px
        font-style: normal
        font-weight: 600
        line-height: 100%
        letter-spacing: -0.48px

      &_container
        position: relative
        height: 20px
        width: 20px
        display: block

        & .membership-subscription_reasons_item_checkbox:checked ~ .membership-subscription_reasons_item_checkmark
          background: $page-black

          &:after
            display: block

      &_checkbox
        position: absolute
        opacity: 0
        cursor: pointer
        height: 0
        width: 0

      &_checkmark
        position: absolute
        top: 0
        left: 0
        height: 20px
        width: 20px
        background-color: $bright-gray
        border-radius: 4px
        background: $anti-flash-white
        border: 1px solid $page-black

        &:after
          content: ""
          position: absolute
          display: none
          width: 100%
          height: 100%
          background-image: url("https://images.paw-champ.com/pc/icons/check-mark-icon-white.svg")
          background-size: contain

  &_box
    height: 90dvh
    display: flex
    flex-direction: column
    justify-content: space-between

  &_feedback
    width: 100%
    display: flex
    height: 148px
    padding: 16px
    align-items: flex-start
    gap: 12px
    align-self: stretch
    border-radius: 16px
    border: 0.5px solid $page-black
    background: $white
    box-shadow: 0px 2px 2px 0px rgba(0, 0, 0, 0.04) inset
    margin-top: 24px
    font-family: $main-font
    outline: none
    @extend .scrollbar-hidden
    resize: none

  &_container
    display: flex
    flex-direction: column
    gap: 24px
    margin-top: 16px

  &_card
    display: flex
    padding: 16px
    flex-direction: column
    align-items: flex-start
    gap: 24px
    align-self: stretch
    border-radius: 16px
    background: $white
    box-shadow: 0px 2px 7.2px 0px rgba(0, 0, 0, 0.07)

    &_item
      display: flex
      align-items: flex-start
      gap: 8px
      align-self: stretch

      &_img
        width: 40px
        height: 40px

      &_header
        color: $page-black
        font-size: 16px
        font-style: normal
        font-weight: 700
        line-height: normal
        letter-spacing: -0.48px

      &_description
        color: $page-black
        font-size: 14px
        font-style: normal
        font-weight: 400
        line-height: normal
        letter-spacing: -0.42px

  &_link
    color: $page-black
    text-align: center
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.08)
    font-size: 16px
    font-style: normal
    line-height: normal
    letter-spacing: -0.48px
    text-decoration-line: underline
    cursor: pointer
  &_btn
    &-close
      cursor: pointer
  &-list
    display: flex
    flex-direction: column
    gap: 8px
    align-self: stretch

    &-item
      display: flex
      height: 64px
      padding: 16px
      align-items: center
      gap: 8px
      align-self: stretch
      border-radius: 8px
      font-weight: 400
  &--discount
    .membership-subscription
      &_container
        gap: 34px
      &-header
        text-align: left
      &-title
        text-align: left
        font-weight: 400
        line-height: 130%
        margin-top: 16px
        letter-spacing: initial
      &_card
        box-shadow: none
        padding: 0 8px
        gap: 0
        &_item
          position: relative
          padding: 8px 0
          align-items: center
          &:not(:last-of-type):after
            content: ""
            position: absolute
            bottom: 0
            left: 0
            right: 0
            height: 1px
            background: #F3F4F5
          &_img
            width: 32px
            height: 32px
          &_description
            font-size: 16px
        &--discount
          display: block
          padding: 17px 16px
          border-radius: 16px
          border: 1px solid #BFDDEA
          background: #E1F2F9
          padding-left: 131px
          position: relative
          @media screen and (max-width: 430px)
               padding-left: 141px
          &_img
            width: 120px
            margin-bottom: -75px
            aspect-ratio: 149 / 111
            position: absolute
            left: 0
            bottom: 50%
            @media screen and (max-width: 430px)
              width: 140px
              margin-bottom: -95px
              left: -10px
            img
              width: 100%
          &_title
            color: #078DC5
            text-shadow: 0px 0px 0.609px rgba(33, 31, 31, 0.11)
            font-size: 20px
            font-weight: 800
            line-height: 100%
          &_subtitle
            margin-top: 4px
            color: #16191E
            font-size: 14px
            font-weight: 400
            line-height: 130%
            b
              font-weight: 800
    .membership-discount__btn
      display: flex
      font-size: 16px
      font-weight: 800
      line-height: 100%
      min-height: 44px
    .membership-default__btn
      display: none
    .description-wrapper-discount
      display: block
    .description-wrapper
      display: none
.membership-subscription_card--discount
  display: none
.description-wrapper-discount
  display: none

.membership-discount
  position: fixed
  top: 0
  left: 0
  background: $white
  z-index: 120
  width: 100%
  display: none
  overflow: auto
  height: 100vh
  padding: 24px 0 96px
  &.active
    display: block
  & .membership-subscription_back
    padding-left: 0
  &__title
    color: $page-black
    font-size: 32px
    font-weight: 800
    line-height: normal
    letter-spacing: -0.96px
    margin-bottom: 4px
    margin-top: 24px
    & span
      @include textGradient($gradient-1)
  &__subtitle
    margin-bottom: 16px
    line-height: normal
    & b
      font-weight: 700
  &__list
    margin-left: 16px
    list-style: disc
    margin-bottom: 16px
    line-height: normal
  &__offer
    &-item
      padding: 16px
      background: $white
      border: 1px solid $blue
      position: relative
      width: 100%
      display: flex
      flex-direction: column
      border-radius: 16px
      &-plan
        font-size: 16px
        font-weight: 800
        line-height: 110%
        text-transform: capitalize
        &-special
          font-size: 20px
          font-weight: 800
          line-height: 100%
          text-transform: capitalize
      &-prices
        display: flex
        align-items: center
        justify-content: space-between
        width: 100%
        &-cancelled
          &-per
            &-day
              font-size: 16px
              font-style: normal
              font-weight: 400
              line-height: 130%
            &-month
              font-size: 12px
              font-style: normal
              font-weight: 400
              line-height: 120%
        &-special
          &-hot
            border-radius: 6px
            border: 1px solid $red-500
            background: $red-100
            padding: 6px
            font-weight: 800
            color: $red-500
            display: flex
            align-items: center
            & b
              margin-top: 2px
              line-height: 80%
              font-weight: 800
          &-per
            &-day
              color: $gray-500
              font-size: 14px
              font-weight: 400
              line-height: 130%
              width: max-content
              text-decoration: line-through
            &-month
              font-size: 14px
              font-style: normal
              font-weight: 400
              line-height: 130%
              margin-bottom: 4px
        &-box
          display: flex
          flex-direction: column
          &.end
            align-items: flex-end
      &-inner
        background: $blue-600
        border-radius: 16px
        margin-bottom: 16px
        &:last-of-type
          margin-bottom: 0
        &.disabled
          opacity: 0.4
          & .membership-discount__offer-item
            border-color: $gray-200
      &-title
        font-size: 12px
        font-weight: 800
        line-height: 100%
        text-transform: uppercase
        padding: 8px
        text-align: center
        color: $white
        display: flex
        align-items: center
        justify-content: center
        & svg
          margin-right: 4px
      &-text
        border-radius: 12px
        border: 1px solid $gray-250
        background: $gray-200
        padding: 12px
        font-size: 14px
        line-height: 120%
        color: $gray-500
        margin-top: 8px
        & a
          color: $gray-500
          text-decoration: underline

    &-list
      position: relative
      margin-bottom: 16px
  &__btn
    @include btn-red(20px)
    font-weight: 800
    box-shadow: none
  &__banner
    padding: 8px 16px 8px 0
    border-radius: 12px
    border: 1px solid $blue-200
    background: $blue-100
    margin-bottom: 24px
    display: flex
    align-items: center
    &-img
      max-width: 112px
      width: 100%
      transform: scale(1.4)
      position: relative
      left: 5px
      bottom: -4px
    &-title
      font-size: 20px
      font-weight: 600
      line-height: 100%
      margin-bottom: 8px
      & b
        font-weight: 800
    &-box
      display: flex
      flex-direction: column
      margin-left: 12px
    &-btn
      @include btn-red(16px)
      min-height: 40px
      font-weight: 800
      border: none
      box-shadow: none

.membership
  &__loader
    display: none
    justify-content: center
    align-items: center
    position: fixed
    top: 0
    left: 0
    z-index: 120
    width: 100%
    height: 100vh
    overflow: hidden
    &.active
      display: flex
      backdrop-filter: blur(2px)
      background: rgba(255, 255, 255, 0.8)
    &.card
      & > .preload
        border-color: rgba(255, 255, 255, 0.41)
        border-top-color: $cyan-cornflower-blue
      &.active
        display: flex
        backdrop-filter: blur(0px)
        background: rgba(30, 31, 33, 0.26)
    & .preload
      display: flex
      width: 40px
      height: 40px
      border: 5px solid $cyan-cornflower-blue
      border-radius: 50%
      border-right-color: #BEE5F0
      border-bottom-color: #BEE5F0
      border-left-color: #BEE5F0
      animation-name: rotate
      animation-duration: 1.0s
      animation-iteration-count: infinite
      animation-timing-function: linear
      margin-bottom: 20px
      @keyframes rotate
        from
          -webkit-transform: rotate(0deg)
          transform: rotate(0deg)
        to
          -webkit-transform: rotate(360deg)
          transform: rotate(360deg)
  &__footer
    display: flex
    align-items: center
    justify-content: center
    padding-top: 32px
    &-item
      display: flex
      align-items: center
      justify-content: center
      margin-right: 8px
      &:last-of-type
        margin-right: 0

      &-link
        font-size: 12px
        font-weight: 700
        line-height: 140%
        text-align: center
        color: $black
        text-decoration: underline
