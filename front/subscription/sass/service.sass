@import "abstracts/variables"
@import "abstracts/mixins"
@import "base/reset"
@import "base/base"

@import "components/header"
@import "../../common/sass/components/footer"

$smallContainer: 566px

.service
  color: $black
  font-family: $nunitoSans
  &__contacts
    min-height: 90vh
    &-box
      display: flex
      align-items: center
      padding: 0 16px
    &-btns
      &-switch
        background: #EBEBEB
        border: 1px solid #CBCBCB
        border-radius: 12px
        padding: 4px
        display: grid
        grid-template-columns: repeat(2, 1fr)
        grid-column-gap: 4px
        box-shadow: 0 0 3.5px 2px #0000000A inset
        max-width: 768px
        width: 100%
        margin: 0 auto 16px
        &-item
          font-size: 14px
          font-weight: 700
          line-height: 100%
          text-align: center
          height: 40px
          display: flex
          align-items: center
          justify-content: center
          background: transparent
          //transition: 0.2s ease
          border-radius: 12px
          &.active
            background: linear-gradient(180deg, #FFFFFF 0%, #F6F6F6 100%)
            border: 1px solid #CACACA
            box-shadow: 0 2px 4px 0 #0000001F
            animation-name: sizing
            animation-duration: 0.3s
            animation-timing-function: ease-in-out
            //transition: 0.2s ease
            &-text
              color: white
            @keyframes sizing
              0%
                transform: scale(1)
              50%
                transform: scale(1.01)
              100%
                transform: scale(1)
    &-content
      width: 100%
      border-radius: 12px
      padding: 8px
      display: none
      &.turquoise
        background: #3FC6BE
        box-shadow: 0 4px 8px 0 #19A49C52
      &.violet
        background: #849FE5
        box-shadow: 0 4px 8px 0 #849FE552
      &.active
        display: block
      &-box
        display: flex
        align-items: center
        justify-content: space-between
      &-img
        min-height: 104px
        width: 100%
        max-width: 104px
        object-fit: cover
    &-data
      &-img
        width: 100%
        max-width: 104px
        object-fit: cover
        &-box
          max-width: 104px
          min-width: 104px
          height: 104px
          width: 104px
      &-text
        color: $white
        font-size: 14px
        margin-left: 16px
    &-title
      font-size: 24px
      text-align: center
      line-height: 130%
      padding: 32px 0 8px
      font-weight: 800
    &-subtitle
      font-size: 14px
      margin-bottom: 32px
      text-align: center
    &-wrapp
      display: grid
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr))
      grid-column-gap: 16px
      max-width: 768px
      width: 100%
      margin: 0 auto
    &-text
      font-size: 20px
      line-height: 160%
      padding-bottom: 24px
      font-weight: 600
      text-align: center
      max-width: 340px
      width: 100%
      margin: 0 auto
    &-email
      color: $white
      font-size: 14px
      &-title
        font-weight: 700
        color: $white
      &-wrapp
        display: flex
        align-items: center
        justify-content: space-between
        grid-column-gap: .5em
        padding: 16px
      &-btn
        max-width: 150px
        padding: 5px 20px
        height: 40px
        border-radius: 8px
        color: $white
        font-weight: 700
        display: flex
        align-items: center
        justify-content: center
        border: 1px solid $white
        white-space: nowrap
  &__title
    padding: 30px 0 8px
    font-size: 26px
    line-height: 130%
    font-weight: 800
    text-align: center
    & span
      @include textGradient(linear-gradient(159.44deg, #FF765A 5.8%, #FF4040 90.92%))
      line-height: 100%
  &__404
    font-size: 126px
    font-weight: 700
    line-height: 120%
    @include textGradient(linear-gradient(159.44deg, #FF765A 5.8%, #FF4040 90.92%))
    text-align: center
    width: 100%
    position: relative
    top: -32px
    &-wrapper
      height: 90vh
      display: flex
      flex-direction: column
      justify-content: space-between
    &-text
      text-align: center
      font-size: 20px
      line-height: 140%
      font-weight: 600
    &-btn
      @include btn-red(24px)
      bottom: 40px
      max-width: 375px
      width: 100%
      margin: 0 auto
      &-link
        position: absolute
        top: 0
        left: 0
        bottom: 0
        right: 0
  &__subtitle
    padding-bottom: 4px
    font-size: 20px
    line-height: 160%
    font-weight: 800
    text-transform: uppercase
    &-small
      padding-bottom: 4px
      line-height: 160%
      font-weight: 500
      font-size: 18px
  &__text
    font-size: 14px
    line-height: 160%
    padding-bottom: 24px
    font-weight: 600
    &:empty
      display: none
    &_link
      display: inline-block
  &__list
    line-height: 160%
    margin-left: 16px
    padding-bottom: 24px
    font-size: 14px
    &-item
      list-style: disc
      padding-bottom: 16px
      &--is-alphabetic
        list-style: lower-alpha
  &__link
    color: $black
    font-weight: 700
    text-decoration: underline
  &__number-list
    line-height: 160%
    margin-left: 16px
    padding-bottom: 24px
    font-size: 14px
    &-item
      list-style: decimal
  &__accordion
    max-width: $mobile320
    width: 100%
    margin: 0 auto 64px
    &-title
      font-size: 26px
      line-height: 120%
      font-weight: 800
      text-align: center
      margin-bottom: 16px
    &-item
      border-bottom: 1px solid #343434
      &-box
        display: flex
        width: 100%
        max-width: 88%
      &-heading
        display: flex
        align-items: center
        justify-content: space-between
        line-height: 150%
        font-size: 15px
        font-weight: 700
        padding: 16px 0
        cursor: pointer
      &-content
        font-size: 14px
        & p
          padding-bottom: 16px
          &:last-of-type
            padding-bottom: 0
    &-arrow
      display: flex

      &:before
        content: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1719397878/dog-training/icons/arrow-down.svg")
        width: 20px
        height: 20px
        transform: rotateZ(360deg)
        transition: $transition

    & .closed
      transition: 0.3s ease-out

      & .service__accordion-item-content
        width: 100%
        opacity: 0
        height: 0
        transition: opacity $transition
        transform: scaleY(0)

    & .open
      transition: 0.3s ease-in-out

      & .service__accordion-item-content
        width: 100%
        line-height: 150%
        height: 100%
        transition: opacity 0.2s ease-in-out
        padding: 16px 0
        transform: scaleX(1)
        transform-origin: center
        opacity: 1

      & .service__accordion-arrow
        &:before
          content: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1719397878/dog-training/icons/arrow-up.svg")
          transform: rotateZ(0deg)
          transition: 0.2s ease-in-out

      & .service__accordion-item-heading
        padding-bottom: 0
  &__footer
    background: #F6F7F8
    &-box
      display: flex
      align-items: center
      justify-content: space-between
      width: 100%
      padding: 8px 0
    &-mob
      display: none
    &-desk
      display: flex
      justify-content: space-between
      align-items: center
      height: 40px
      &-list
        display: flex
        align-items: center
      &-item
        line-height: 100%
        margin-right: 32px
    &-copyright
      width: auto
      color: $black
      font-size: 14px
      font-weight: 400
      line-height: 130%
    &-contact
      color: $black
      font-size: 14px
      font-weight: 700
      line-height: 150%
      text-decoration: underline

@media (min-width: 768px)
  .service
    &__contacts
      &-wrapp
        grid-template-columns: repeat(2, 1fr)

@media (min-width: $smallContainer)
  .service
    &__contacts
      &-btns
        &-switch
          display: none
      &-content
        &.turquoise
          display: block
        &.violet
          display: block
      &-wrapp
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr))

@media (max-width: 767px)
  .service
    &__footer
      width: 100%
      &-mob
        display: flex
        flex-direction: column
        justify-content: space-between
        align-items: center
        &-list
          background: #e0e0e0
          width: 100%
          line-height: 100%
      &-desk
        display: none
      &-copyright
        width: 100%
        text-align: center

@media (max-width: 700px)
  .service
    &-content
      &.turquoise
        display: none
      &.violet
        display: none

@media (max-width: 567px)
  .service
    &__footer-box
      flex-direction: column
      grid-gap: 8px

@media (max-width: 375px)
  .service
    &__contacts
      &-email
        font-size: 12px
