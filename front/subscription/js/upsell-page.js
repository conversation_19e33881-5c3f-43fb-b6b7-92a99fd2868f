import 'whatwg-fetch';
import 'nodelist-foreach-polyfill';

import amplitude from "../../common/components/amplitude";

import { convertPriceToUSD } from '../../common/currencyExchange';
import { formatMoney } from '../../common/common';

document.addEventListener('DOMContentLoaded', () => {
    const addUpsellButton = document.querySelector('#add-upsell-button');
    const noThanksButton = document.querySelector('#no-thanks-button');
    const upsellLoader = document.querySelector('#upsaleLoader');

    const resultPrice = formatMoney(convertPriceToUSD(window.paymentInfo.price, window.paymentInfo.currency));
    const revenue = new amplitude.Revenue().setProductId(window.paymentInfo.id).setPrice(resultPrice).setQuantity(1);

    addUpsellButton.addEventListener('click', async () => {
        addUpsellButton.remove();
        noThanksButton.remove();

        upsellLoader.style.setProperty('display', 'block');

        amplitude.logEvent(window.recurringPaymentConfig.addUpsellEventName);
        window.sessionStorage.setItem('upsellCharged', '1');

        const response = await fetch(window.recurringPaymentConfig.requestUrl);
        const result = await response.json();

        if (response.status === 200 && result.status === 'ok') {
            amplitude.logRevenueV2(revenue);

            setTimeout(() => {
                document.location.replace(window.recurringPaymentConfig.successUrl);
            }, 300);

            return;
        }

        setTimeout(() => {
            document.location.replace(window.recurringPaymentConfig.failUrl);
        }, 300);
    });

    noThanksButton.addEventListener('click', () => {
        amplitude.logEvent(window.recurringPaymentConfig.notThanksEventName);

        setTimeout(() => {
            document.location.replace(window.recurringPaymentConfig.noThanksUrl);
        }, 300);
    });
});
