import 'babel-polyfill';
import 'nodelist-foreach-polyfill';
import './components/email-datalist';

import ProgressBar from 'progressbar.js';
import confetti from 'canvas-confetti';
import Swiper, { Autoplay } from "swiper";
import amplitude from '../../common/components/amplitude';
import handleJumpToQuestion from "../../common/components/quiz/quizJumpToQuestion";
import textResponsesPush from "../../common/components/textResponsesPush";
import QuizStore from './store';
import { saveQuizResponses, saveQuizResponsesOnBackend } from '../../common/quiz-responses';
import { trackFbEvent } from './components/fb-event';
import { replaceTextForMixedBreed } from "../../common/common";
import { translate } from "../../common/components/translator";
import { graphAnimation } from "../../common/components/svg-graphic-animation";
import { fillImportantDatePlaceholder } from "../../common/components/quiz/fillImportantDatePlaceholder";
import { fillTargetDate } from "../../common/components/quiz/fillTargetDateInGraph";
import { checkVisitIdInQuery } from "../../common/user-visit-warden";
import { getAnswersForMultichoice } from "../../common/components/quiz/getAnswersFromMultichoice";
import { getAnswersForRadio } from "../../common/components/quiz/getAnswersFromRadio";
import { initBreedDatalist } from "./components/breed-datalist";
import { insertPriorityProblemOnQuiz, insertPrioritySign, showSelectedSigns } from "../../common/components/quiz/aggressionFunnelLogic";
import { insertPuppyText } from "../../common/components/insertPuppyText";
import { executeSafely } from "../../common/components/executeSafety";
import { addSpecificPrefixForSomeWords } from "../../common/components/prefixText";
import { logImgLoadError } from "../../common/components/ImglogError";
import { WhereAmI } from "../../common/components/whereAmI";
import { initBehaviourTriggersSummaryStepContent } from "./components/behaviour-triggers-summary";
import { initQuestionImagesPreLoading } from "./components/questionsImagesPreLoading";
import capitalizeString from "../../common/components/capitalizeString";
import * as Sentry from "@sentry/browser";
import { fillMonthsOnGraph } from "./components/fillMonthsOnGraph";
import I18nService from "../../common/components/i18nService";
import { initScratchCard } from "./components/scratch-card";
import { createCustomBackground } from "../../common/components/quiz/createCustomBackground";
import { InteractiveCourseController } from "./components/interactive-course";

import("swiper");
// eslint-disable-next-line import/no-unresolved
import("swiper/css");
// eslint-disable-next-line import/no-unresolved
import("swiper/css/autoplay");

document.addEventListener('DOMContentLoaded', () => {
    let forwardTransitionTimeout;
    let backwardTransitionTimeout;

    const backBtn = document.getElementById('backBtn');

    const firstStepPositionQueryParam = new URLSearchParams(window.location.search).get('firstStepPosition')
    const firstStepPosition = firstStepPositionQueryParam ? Number(firstStepPositionQueryParam) + 1 : 0;
    const quizStepCount = document.querySelectorAll('.quiz__index').length - 1;

    const line = new ProgressBar.Line('#progBar', {
        strokeWidth: 1,
        easing: 'easeOut',
        duration: 1000,
        color: '#FF5E4F',
        trailColor: '#E7DEDE',
        trailWidth: 1,
        svgStyle: {
            strokeLinecap: 'round',
        },
    });

    line.set(Math.max(0, firstStepPosition - 1) / quizStepCount);

    if (firstStepPosition > 0) {
        line.animate(firstStepPosition / quizStepCount);
    }

    let responses = {
        age: '',
        breed: '',
        dogName: '',
        obedience: [],
        tricks: [],
        anxietyLevel: [],
        activityLevel: '',
        stressLevel: [],
        problems: [],
        healthTend: [],
        motivation: [],
        spendTime: '',
        anxietySignals: [],
        behavior: [],
        aggressiveSigns: [],
        primaryAggressiveSign: '',
        periodTime: [],
        textProblems: [],
        goals: [],
        nutritionGoal: '',
        nutritionApproach: '',
        timeAimToLeaveAlone: '',
        gender: [],
        together: '',
        basics: [],
        importantEvent: '',
        topics: [],
        health: {
            bodyType: '', dogActivity: '',
        },
        dogCrateTrained: '',
        comeHomeReactions: [],
        knownCues: [],
        excitementTriggers: [],
        aggressionTriggers: [],
        stressTriggers: [],
        behavioursToDiscipline: [],
        disallowMarketingEmails: 0,
        userInteractiveCoureseList: [],
    };
    const progressCircleElement = document.getElementById('circleBar');
    let progressCircle = null;
    if (progressCircleElement) {
        progressCircle = new ProgressBar.Circle('#circleBar', {
            color: '#16191E',
            strokeWidth: 16,
            trailWidth: 6,
            easing: 'linear',
            duration: 10000,
            text: {
                autoStyleContainer: true, className: 'progressbar__text-reviews',
            },
            from: { color: '#FF765A' },
            to: { color: '#FF4040' },
            step(state, circle) {
                circle.path.setAttribute('stroke', state.color);
                circle.path.setAttribute('stroke-width', '6px');
                circle.path.setAttribute('stroke-linecap', 'round');
                const value = `${Math.round(circle.value() * 100)}<span>%</span>`;
                if (value === 0) {
                    circle.setText('');
                } else {
                    circle.setText(value);
                }
            },
        });
    }
    const magicProgressLinesOptions = {
        strokeWidth: 6,
        easing: "easeInOut",
        duration: 3000,
        color: "#1998CD",
        trailColor: "#eee",
        svgStyle: {
            width: "100%",
            height: "6px",
            borderRadius: "8px",
            margin: "12px 0",
        },
        text: {
            style: {
                color: "#9C9C9C",
                position: "absolute",
                right: "0",
                bottom: "32px",
                padding: 0,
                margin: 0,
                transform: null,
            },
            autoStyleContainer: false,
        },
        step: (_, bar) => {
            if (bar.value() < 1) {
                bar.setText(Math.round(bar.value() * 100) + "%");
                bar.text.style.color = bar.value() > 0 && "#343434";
            }
        },
    };
    const quizQuestionAnswers = new amplitude.Identify();
    let quizStore;

    const { language: locale, translations } = window;
    const i18n = new I18nService({
        locale,
        translations,
        onError: Sentry.captureException,
    });

    init();

    function addSplitsToAmplitude(identifyData) {
        if (!window.allSplitValues) {
            return;
        }

        const isPawChampFeatureNameRE = /(?:^pc[._]|[._]pc$)/i;

        for (const [key, value] of Object.entries(window.allSplitValues)) {
            if (isPawChampFeatureNameRE.test(key)) {
                identifyData.set(key, value);
            }
        }
    }

    function initAmplitude() {
        const queryString = window.location.search;

        function getPropertyFromURL(property) {
            let URL = window.location.search;
            URL = URL.match(new RegExp(`${property}=([^&=]+)`));
            return URL ? URL[1] : 'none';
        }

        const indentifyData = new amplitude.Identify()
            .set('quiz_url', window.location.pathname)
            .set('source', queryString.indexOf('fbclid=') !== -1 ? 'fb' : 'none')
            .set('test', queryString.indexOf('test=creative') !== -1 ? 'creative' : 'none')
            .set('visit_params', queryString.replace('?', ''))
            .set('campaign', queryString.indexOf('utm_campaign=') !== -1 ? getPropertyFromURL('utm_campaign') : 'none')
            .set('adset', queryString.indexOf('utm_content=') !== -1 ? getPropertyFromURL('utm_content') : 'none')
            .set('placement', queryString.indexOf('utm_medium=') !== -1 ? getPropertyFromURL('utm_medium') : 'none')
            .set('ad', queryString.indexOf('ad.id') !== -1 ? getPropertyFromURL('ad.id') : 'none')
            .set('campaign_name', queryString.indexOf('campaign_name') !== -1 ? getPropertyFromURL('campaign_name') : 'none')
            .set('adset_name', queryString.indexOf('adset_name') !== -1 ? getPropertyFromURL('adset_name') : 'none')
            .set('ad_name', queryString.indexOf('ad_name') !== -1 ? getPropertyFromURL('ad_name') : 'none')
            .set('domain', window.location.host)
            .set('Width', window.innerWidth)
            .set('Height', window.innerHeight)
            .set('Resolution', (window.innerWidth + 'x' + window.innerHeight));

        addSplitsToAmplitude(indentifyData);

        if (!WhereAmI.isPuppyQuiz()) {
            amplitude.identify(indentifyData);

            if (queryString.indexOf('flow=rem') === -1) {
                amplitude.logEvent('Quiz view', {
                    URL: window.location.href,
                });
            } else {
                amplitude.logEvent('Email view');
            }
        }

        sendEventsToAmplitudePupAdultFunnel();
    }

    function handleMultipleCheckbox() {
        const checkboxes = document.querySelectorAll('.quiz__multiple-item');

        checkboxes.forEach((checkboxItem) => {
            // add disable to multichoice question btns
            if (checkboxItem.closest('.quiz__index').querySelector('.quiz__multiple')) {
                checkboxItem.closest('.quiz__index').querySelector('.quiz__btn').disabled = true;
            }

            checkboxItem.addEventListener('click', () => {
                checkboxItem.classList.toggle('quiz__multiple-checked');

                const questionContainer = checkboxItem.closest('.quiz__index');
                const btn = questionContainer.querySelector('.quiz__btn');
                const hasCheckedAnswers = questionContainer.querySelectorAll('.quiz__multiple-checked').length > 0;

                btn.disabled = !hasCheckedAnswers;
            });
        });
    }

    function handleResultStep() {
        saveQuizResponses(responses);

        trackFbEvent('FinishQuiz');
        amplitude.logEvent('Quiz passed');
    }

    // Send amplitude event with answer age value
    function getAnswersForOneItem(question) {
        const questionAnswer = question.querySelectorAll('.quiz__index-active .quiz__active');

        if (!questionAnswer) {
            return;
        }

        return Array.prototype.map.call(questionAnswer, (el) => el.getAttribute('data-question-value'));
    }

    function insertBreedOnQuiz(question) {
        const selectedBreed = question.querySelector('.quiz__active');
        const insertSelectors = document.querySelectorAll('.breedInsertSelector');
        const answerFromBreedList = getAnswersForBreedQuestion(question);

        if (insertSelectors.length === 0 || (!selectedBreed && !answerFromBreedList)) {
            return;
        }

        const breedValue = selectedBreed?.getAttribute('data-question-value') || answerFromBreedList;

        insertSelectors.forEach((target) => {
            const mixedValue = target.dataset?.mixedBreedValue;
            const isMixed = breedValue === 'Mixed';

            if (mixedValue && isMixed) {
                target.innerText = translate(mixedValue, window.language);
            } else {
                target.innerHTML = translate(breedValue, window.language);
                return breedValue;
            }
        });
    }

    // Send amplitude event with breed value
    function getAnswersForBreedQuestion(question) {
        const breedAnswer = question.querySelector('#dataInput');
        if (breedAnswer && breedAnswer.value) {
            return breedAnswer.value;
        }
        const radioAnswer = question.querySelector('[name="dog-breed-radio"]:checked');

        return radioAnswer && radioAnswer.getAttribute('data-question-value');
    }

    // Send amplitude event with dog name value
    function getAnswersForDogName() {
        const dogNameAnswer = document.getElementById('inputText');

        return (dogNameAnswer?.value || '').trim();
    }

    function getAnswersForRangeSlider() {
        const question = document.querySelector('.quiz__index-active .amplitude-analytic');
        const rangeAnswer = question.querySelector('.rangeSlider');

        return rangeAnswer.value;
    }

    function disableUnselectedOneChoiceBtn(question) {
        const selectedBtn = question.querySelector('.quiz__active');
        const allBtns = question.querySelectorAll('.js-btn');

        allBtns.forEach((btn) => {
            if (btn.classList.contains('.quiz__active') !== true) {
                btn.classList.add('disable');
            }
        });

        if (selectedBtn) {
            selectedBtn.classList.remove('disable');
        }
    }

    function sendAnswersToAmplitude() {
        const question = document.querySelector('.quiz__index-active .amplitude-analytic');

        if (!question) {
            return;
        }

        let answers;

        switch (question.getAttribute('data-type')) {
            case 'one-answer':
                answers = getAnswersForOneItem(question);

                if (answers !== null) {
                    disableUnselectedOneChoiceBtn(question);
                }
                break;
            case 'dog-name':
                answers = getAnswersForDogName(question);
                responses.dogName = getAnswersForDogName(question);
                break;
            case 'breed-select':
                answers = getAnswersForBreedQuestion(question);
                responses.breed = getAnswersForBreedQuestion(question);
                break;
            case 'gender':
                answers = getAnswersForBreedQuestion(question);
                responses.breed = getAnswersForBreedQuestion(question);
                break;
            case 'breed-insert':
                const activeAnswer = question.querySelector('.quiz__breeds-item.quiz__active');

                insertBreedOnQuiz(question);

                if (activeAnswer !== null) {
                    responses.breed = activeAnswer.getAttribute('data-question-value');
                    answers = activeAnswer.getAttribute('data-question-value');
                } else {
                    responses.breed = getAnswersForBreedQuestion(question);
                    answers = getAnswersForBreedQuestion(question);
                }

                break;
            case 'breed-hint':
                const activeBreedBtn = question.querySelector('.quiz__breeds-item.quiz__active');
                const breedInput = question.querySelector('#dataInput');

                insertBreedOnQuiz(question);

                if (activeBreedBtn) {
                    responses.breed = activeBreedBtn.getAttribute('data-question-value');
                    answers = activeBreedBtn.getAttribute('data-question-value');

                    if (breedInput.value.length > 0) {
                        responses.breed = breedInput.value;
                        answers = breedInput.value;
                    }
                }

                if (breedInput.value.length > 0) {
                    answers = breedInput.value;
                }

                break;
            case 'multiple-checkbox':
                answers = getAnswersForMultichoice(question, 'data-question-value').join(', ');
                break;
            case 'radio':
                answers = getAnswersForRadio(question, 'data-question-value').join(', ');
                break;
            case 'range-slider':
                answers = getAnswersForRangeSlider(question);
                break;
            case 'statement-relation':
                const answerValue = question
                    .querySelector('.js-btn.quiz__active')
                    ?.getAttribute('data-question-value');

                answers = parseInt(answerValue, 10) || null;

                break;
            default:
                throw new Error('Unknown data type');
        }

        if (!answers) {
            answers = 'None';
        }

        const amplitudeDataKey = question.getAttribute('data-question');
        quizQuestionAnswers.set(amplitudeDataKey, answers.toString());
        amplitude.identify(quizQuestionAnswers);
    }

    function saveQuizResponse() {
        const question = document.querySelectorAll('.quiz__index-active');
        const response = question[0].querySelector('[data-selector]');

        if (!response) {
            return;
        }

        const responseArray = question[0].querySelector('[data-response-type]');
        const responseType = responseArray.getAttribute('data-response-type');

        let answers;

        switch (response.getAttribute('data-selector')) {
            case 'one-choice': {
                answers = getQuizOneChoice(response);

                if (responseType === 'stress') {
                    responses.stressLevel = responses.stressLevel.concat(answers);
                } else if (responseType === 'anxiety') {
                    responses.anxietyLevel = responses.anxietyLevel.concat(answers);
                } else if (responseType === 'gender') {
                    const heShe = response.querySelector('.quiz__active').getAttribute('data-question-value');
                    localStorage.setItem('DogGender', heShe);

                    responses.gender = responses.gender.concat(answers);
                } else if (responseType === 'breed') {
                    responses.breed = getAnswersForBreedQuestion(answers);
                } else if (responseType === 'breed-insert') {
                    responses.breed = getAnswersForBreedQuestion(answers);
                } else if (responseType === 'together') {
                    responses.together = responseArray.querySelector('.quiz__answer-item.quiz__active').getAttribute('data-question-value');
                } else if (responseType === 'obedience') {
                    const dataValue = responseArray.querySelector('.quiz__answer-item.quiz__active').getAttribute('data-one-choice');

                    responses.obedience.push(Number(dataValue));
                } else if (responseType === 'basics') {
                    responses.basics = responses.basics.concat(answers);
                } else if (responseType === 'dogAge') {
                    placeDogAge();
                } else if (responseType === 'goals') {
                    responses.goals = responses.goals.concat(answers);

                    if (responses.goals.length > 0) {
                        responses.goals = answers;
                    }

                    document.querySelectorAll('.goalInsertSelector')
                        ?.forEach((el) => {
                            el.innerText = answers[0];
                        });
                    insertMappedGoalText(answers[0]);
                } else if (responseType === 'feeding-goals') {
                    responses.nutritionGoal = answers;
                } else if (responseType === 'nutrition-approach') {
                    responses.nutritionApproach = answers;
                } else if (responseType === 'activity-level') {
                    responses.activityLevel = answers;
                } else if (responseType === 'timeAimToLeaveAlone') {
                    responses.timeAimToLeaveAlone = answers[0];
                } else if (responseType === 'importantEvent') {
                    responses.importantEvent = answers;
                } else if (responseType === 'periodTime') {
                    responses.periodTime = answers;
                } else if (responseType === 'time-ready-to-spend') {
                    responses.periodTime = answers;
                } else if (responseType === 'dog-activity') {
                    responses.health.dogActivity = answers;

                    const healthActivityEl = document.getElementById('healthActivity');
                    insertProfilePlaceholder(healthActivityEl, responses.health.dogActivity);
                } else if (responseType === 'body-type') {
                    responses.health.bodyType = answers;

                    const healthBodyTypeEl = document.getElementById('healthBodyType');
                    insertProfilePlaceholder(healthBodyTypeEl, responses.health.bodyType);
                } else if (responseType === 'crate-trainer') {
                    responses.dogCrateTrained = answers[0];
                } else if (responseType === 'spend-time') {
                    responses.spendTime = answers[0];
                }

                break;
            }
            case 'range-choice': {
                const answer = getQuizRangeInput(response);

                if (responseType === 'obedience') {
                    responses.obedience.push(Number(answer));
                }
                break;
            }
            case 'multi-choice': {
                answers = getAnswersForMultichoice(response, 'data-question-value');
                if (responseType === 'problems') {
                    // There is no support for adding problems from different questions!
                    responses.problems = answers;

                    if (window.isProblemsOnProfileEnabled) {
                        // If user returned back to the question with problems and changed them.
                        removeSelectedProblemsFromProfile('.problemsInsertSelector'); // TODO replace me.
                        textResponsesPush(responses.problems, document.querySelectorAll('.problemsInsertSelector'), 'quiz__profile-list-text');
                    }

                    const problemsPriorities = ["Separation anxiety", "Excessive energy", "Leash pulling", "Excessive barking", "Destructive behavior", "House soiling", "Aggression"];
                    const priorityProblem = problemsPriorities.find((problem) => answers.includes(problem));

                    document.querySelectorAll('.priorityProblemInsertSelector')
                        ?.forEach((el) => {
                            el.innerHTML = priorityProblem;
                        });
                }
                if (responseType === 'health-tend') {
                    responses.healthTend = answers;

                    if (responses.healthTend.length > 0) {
                        const healthTendInsertText = answers.map((answer) => translate(`healthTend.${answer}`, window.language));

                        textResponsesPush(healthTendInsertText, document.querySelectorAll('.healthTendInsertSelector'), 'quiz__profile-list-text');
                    }
                }
                if (responseType === 'anxietySignals') {
                    responses.anxietySignals = answers;

                    // Filter "Sleeps or relaxes" option, because it isn't an anxiety signal
                    let anxietySignalsToShow = answers.filter((signal) => signal !== "Sleeps or relaxes")

                    // If there is no anxiety signals selected (which means that there is only "Sleeps or relaxes"
                    // option was chosen), show "Unknown"
                    if (!anxietySignalsToShow.length) {
                        anxietySignalsToShow = ["Unknown"];
                    }

                    textResponsesPush(anxietySignalsToShow, document.querySelectorAll('.anxietySignalsInsertSelector'), 'quiz__profile-list-text');
                } else if (responseType === 'tricks') {
                    responses.tricks = responses.tricks.concat(answers);
                } else if (responseType === 'obedience') {
                    responses.obedience = responses.obedience.concat(answers.length);
                    responses.problems = responses.problems.concat(answers);
                } else if (responseType === 'basics') {
                    answers = getAnswersForMultichoice(response, 'data-one-choice');

                    responses.basics = responses.basics.concat(answers);
                } else if (responseType === 'goals') {
                    responses.goals = responses.goals.concat(answers);

                    if (responses.goals.length > 0) {
                        responses.goals = answers;
                    }

                    const healthGoalsEl = document.getElementById('healthGoals');
                    insertProfilePlaceholder(healthGoalsEl, responses.goals.join(', '));
                } else if (responseType === 'topics') {
                    responses.topics = responses.topics.concat(answers);

                    if (responses.topics.length > 0) {
                        responses.topics = answers;
                    }
                } else if (responseType === 'cues') {
                    responses.knownCues = answers;

                    const commandsPriorities = ["Name", "Sit", "Down", "Touch", "Give a Paw", "Leave it", "Stop", "Come", "Go to your place", "Stay", "Heel"];
                    const priorityCommand = commandsPriorities.find((command) => answers.includes(command));

                    if (priorityCommand === "Name") {
                        const magicStepCommandTextContainer = document.querySelector('.magic-step-command-text-container');

                        if (!magicStepCommandTextContainer) {
                            break;
                        }

                        const dogGenderPronounce = localStorage.getItem("DogGender") === "He" ? "his" : "her";
                        const dogGenderPronounceNoun = localStorage.getItem("DogGender") === "He" ? "he" : "she";
                        const dogAgePronounce = responses.age === "puppy" ? "pup" : "dog";

                        magicStepCommandTextContainer.innerHTML = `
                            Does your ${dogAgePronounce} ignore
                            <b>${dogGenderPronounce} name</b> 
                            when ${dogGenderPronounceNoun} is focused on something else?
                        `
                    }

                    document.querySelectorAll('.priorityCommandInsertSelector')
                        ?.forEach((el) => {
                            el.innerHTML = priorityCommand;
                        })
                } else if (responseType === 'come-home-reaction') {
                    responses.comeHomeReactions = answers;
                } else if (responseType === 'excitement-triggers') {
                    responses.excitementTriggers = answers;
                } else if (responseType === 'aggression-triggers') {
                    responses.aggressionTriggers = answers;
                } else if (responseType === 'stress-triggers') {
                    responses.stressTriggers = answers;
                } else if (responseType === 'behaviours-to-discipline') {
                    responses.behavioursToDiscipline = answers;
                } else if (responseType === 'motivation') {
                    responses.motivation = answers;
                }
                break;
            }
            case 'get-dog-name': {
                answers = getQuizOneChoice(response);

                if (responseType === 'getDogName') {
                    responses.dogName = responses.dogName.concat(answers);
                }

                break;
            }

            default:
                throw new Error('Unknown data type');
        }

        replaceTextForMixedBreed(responses.breed);
    }

    function removeSelectedProblemsFromProfile(problemsSelectorClassName) {
        const profileQuestion = document.querySelector('.profile');

        if (!profileQuestion) {
            return;
        }

        const problemsContainer = profileQuestion.querySelector(problemsSelectorClassName);

        if (!problemsContainer) {
            return;
        }

        const problemsEl = problemsContainer.querySelectorAll('.quiz__profile-list-text');

        if (!problemsEl) {
            return;
        }

        problemsEl.forEach((el) => {
            el.remove();
        });
    }

    function getQuizOneChoice(question) {
        const questionAnswer = question.querySelectorAll('.quiz__index-active .quiz__active');

        if (!questionAnswer) {
            return;
        }

        return Array.prototype.map.call(questionAnswer, (el) => el.getAttribute('data-question-value'));
    }

    function getQuizRangeInput() {
        const activeStep = document.getElementsByClassName('quiz__index-active');

        return activeStep[0].querySelector('input[type="range"]').value;
    }

    function changeContentIfRangeSliderIsLowValue(nextStepBlock) {
        const activeQuestionBtn = nextStepBlock.querySelector('.quiz__btn');

        activeQuestionBtn.addEventListener('click', () => {
            const sliderValue = Number(nextStepBlock.querySelector('.rangeSlider').value);

            changeImageForPuppyFunnel();

            if (sliderValue < 9) {
                return;
            }
            const lowContent = document.getElementById('lowContent');
            const highContent = document.getElementById('highContent');
            lowContent.classList.remove('active');
            highContent.classList.add('active');
        });
    }

    function changeImageForPuppyFunnel() {
        if (responses.age !== 'puppy') {
            return;
        }

        const activeQuestionImgEl = document.getElementById('puppyChangeImgInsertSelector');
        activeQuestionImgEl.src = 'https://res.cloudinary.com/dr0cx27xo/image/upload/v1700490238/dog-training/img/health/choice-high-puppy.jpg';
    }

    // skip specified number of questions
    function skipNextQuestion(triggeredBtn, nextStepBlock) {
        triggeredBtn.setAttribute('data-skip-next-question', 'Skip');
        nextStepBlock.setAttribute('data-skipped', 'true');
    }

    /**
     *
     * @param triggeredBtn
     * @param currentStepId
     */
    function stepForward(triggeredBtn, currentStepId) {
        const currentStepBlock = document.querySelector(`#block${currentStepId}`);
        const nextStepBlock = document.querySelector(`#block${currentStepId + 1}`);
        const quizInfoIntro = document.querySelectorAll('.quiz__intro');

        if (nextStepBlock === null) {
            return;
        }


        saveQuizResponse();
        replaceTextGender();
        sendAnswersToAmplitude();

        if (nextStepBlock.getAttribute('data-remove-checkboxes')) {
            disableCheckboxesWhenSelectedLast(nextStepBlock);
        } else if (nextStepBlock.getAttribute('data-angry-signs')) {
            const answers = getAnswersForMultichoice(currentStepBlock, 'data-question-value');

            if (answers.length > 1) {
                showSelectedSigns(responses, answers);
            } else {
                insertPriorityProblemOnQuiz(responses, answers);
                skipNextQuestion(triggeredBtn, nextStepBlock);
            }

            insertPrioritySign(responses);
        } else if (nextStepBlock.hasAttribute('data-text-problems')) {
            let textToInsert;
            const nonEmptyProblems = responses.problems.filter(Boolean);

            if (responses.problems.filter(Boolean).length === 1) {
                textToInsert = nonEmptyProblems[0].toLowerCase() + ", jumping on people";
            } else {
                textToInsert = nonEmptyProblems
                    .slice(0, 2)
                    .join(', ')
                    .toLowerCase();
            }

            document
                .querySelectorAll('.textProblemsInsertSelector')
                .forEach((el) => {
                    el.innerText = textToInsert;
                });
        }

        if (nextStepBlock.hasAttribute('data-love-progressbar-finish')) {
            const progressBarStart = +nextStepBlock.getAttribute('data-love-progressbar-start') || 0;
            const progressBarFinish = +nextStepBlock.getAttribute('data-love-progressbar-finish') || 100;
            const progressBarDuration = +nextStepBlock.getAttribute('data-love-progressbar-duration') || 1000;

            loveProgressbar(
                progressBarFinish,
                progressBarStart,
                nextStepBlock,
                progressBarDuration,
            )
        }

        const dogNamePlaceholder = document.querySelectorAll('.dog-name');
        dogNamePlaceholder.forEach((el) => {
            if (localStorage.getItem('name') !== null) {
                el.classList.add('dog-name-classic');
                el.classList.add('dog-name-apostrophe');
            }
        });

        amplitude.logEvent('Quiz question answered', { question: currentStepId });
        quizStore.saveResponses(responses);

        // progressbar count
        const stepNumber = parseInt(nextStepBlock.getAttribute('data-id'), 10);
        const questionsIndex = (stepNumber + firstStepPosition) / (quizStepCount + firstStepPosition);

        clearTimeout(backwardTransitionTimeout);
        clearTimeout(forwardTransitionTimeout);

        forwardTransitionTimeout = setTimeout(() => {
            const nextStepType = nextStepBlock.getAttribute('data-step-type') || null;
            line.animate(questionsIndex);

            i18n.translateElement(nextStepBlock, i18n.toContext(responses));

            if (nextStepType === "magic") {
                showMagicQuestionPopup(nextStepBlock)
                    .then(() => {
                        stepForward(triggeredBtn, currentStepId + 1);
                    });
                    // Wait until card stats are updated, then sort
                    InteractiveCourseController.userMatchHander();
                    InteractiveCourseController.cardSortion();
            } else if (nextStepType === "plan-summary") {
                Object.entries({
                    'greetings-manners': !(responses.comeHomeReactions.length === 1 && responses.comeHomeReactions[0] === "Calmly greets"),
                    'crate-training': responses.dogCrateTrained !== "Yes",
                    'basic-commands': responses.knownCues.length < 10
                })
                    .filter(([_, isIncluded]) => isIncluded)
                    .forEach(([statusType]) => {
                        nextStepBlock
                            .querySelector(`.status__${statusType}`)
                            ?.classList
                            .add('status--included')
                    })

                if (responses.knownCues.length < 10) {
                    const itemMasteringBasics = nextStepBlock.querySelector('.plan-summary-list-item__mastering-commands');

                    itemMasteringBasics.remove();
                }
            } else if (nextStepType === "behaviour-triggers-summary") {
                initBehaviourTriggersSummaryStepContent(nextStepBlock, responses);
            }

            window.scrollTo({
                top: 0, behavior: 'smooth',
            });

            // Make placeholders interpolation for specific pages.
            // Must be refactored.

            if (nextStepBlock.hasAttribute('data-hint-banner')) {
                const hintBanner = nextStepBlock?.querySelector('.quiz__cta-wrapp');
                const questionAnswers = nextStepBlock?.querySelectorAll('.quiz__answer-item');
                const currentQuestionStepBtn = nextStepBlock?.querySelector('.quiz__btn');
                const removeActiveClassFromAnswer = () => {
                    questionAnswers.forEach(item => {
                        item.classList.remove('quiz__active');
                    });
                }

                if (questionAnswers && currentQuestionStepBtn) {
                    questionAnswers.forEach((answer) => {
                        answer.addEventListener('click', () => {
                            removeActiveClassFromAnswer();

                            answer.classList.add('quiz__active');

                            if (hintBanner) {
                                hintBanner.classList.add('active');
                            }

                            currentQuestionStepBtn.disabled = false;
                        });
                    });
                }

                setupHintBannerAnimation(nextStepBlock, hintBanner, currentQuestionStepBtn);
            }

            if (nextStepBlock.getAttribute('data-header-hide') === 'Graph') {
                translate(fillImportantDatePlaceholder(responses), window.language);

                if (window.allSplitValues['pc_qualityTime'] === 2) {
                    setPeriodTimeOnPlaceholder(responses);
                    displayTooltipsTrainingGraphic(responses);
                    fillMonthsOnGraph(responses);
                } else {
                    displayTooltips(responses);
                }
            }

            if (nextStepBlock.getAttribute('data-range-health')) {
                changeContentIfRangeSliderIsLowValue(nextStepBlock, currentStepBlock);
            }

            if (nextStepBlock.getAttribute('data-result') === 'reviews') {
                placeProblems();
                handleResultStep();
                showResultPopupsWithAge();

                const resultStep2 = document.getElementById('resultStepTwo');

                nextStepBlock.classList.add('quiz__index-active');
                currentStepBlock.classList.remove('quiz__index-active');
                resultStep2.style.setProperty('display', 'none');
                nextStepBlock.classList.add('quiz__result-active');

                backBtn.style.setProperty('opacity', '0');
                document.querySelector('#progBar').style.setProperty('display', 'none');
                document.querySelector('.header-quiz__inner-bg').style.setProperty('background', 'none');
            } else {
                currentStepBlock.classList.remove('quiz__index-active');
                showNextStep(triggeredBtn, nextStepBlock);

                // Show back button but not for first step
                if (stepNumber !== 1) {
                    document.querySelector('.header-quiz__inner-bg').style.setProperty('background', 'none');
                    document.querySelector('.progressbar__line').style.setProperty('display', 'block');
                    backBtn.style.setProperty('opacity', '1');
                }

                // Hide header elements if needed
                // eslint-disable-next-line default-case
                switch (nextStepBlock.getAttribute('data-header-hide')) {
                    case 'Profile':
                    case 'Graph':
                        document.querySelector('.progressbar__line').style.setProperty('display', 'none');
                        backBtn.style.setProperty('opacity', 0);

                        break;
                    case 'Challenge':
                        document.querySelector('.header-quiz__inner-bg').style.setProperty('background', '#ebf9ff');

                        document.querySelector('.progressbar__line').style.setProperty('display', 'none');
                        backBtn.style.setProperty('opacity', 0);
                        document.body.style.setProperty('background', '#ebf9ff');

                        break;
                    default:
                        // remove this after pc_newdes split close
                        if (window.allSplitValues['pc_newdes'] < 2) {
                            document.body.style.setProperty('background', '#ffffff');
                        }
                }

                // Remove quiz info block if not first question
                quizInfoIntro.forEach((el) => {
                    if (el.getAttribute('data-id') !== '1') {
                        setTimeout(() => {
                            el.remove();
                        }, 50);
                    }
                });

                // Remove quiz footer if not first question
                if (document.querySelector('.quiz-footer')) {
                    document.querySelector('.quiz-footer').style.setProperty('display', 'none');
                }
            }

            // quiz animation some questions
            if (nextStepBlock.getAttribute('data-animation') === 'Profile Animation') {
                summaryProfileAnimation();
            }

            if (nextStepBlock.getAttribute('data-animation') === 'Graph Animation') {
                const dateItems = document.querySelectorAll('.quiz__graph-svg-date');
                const goalDot = document.getElementById('goal-dot');
                const goalSelectors = document.querySelectorAll('.goalSelector');
                const graphBg = document.getElementById('graph-bg');

                graphAnimation(dateItems, goalDot, goalSelectors, graphBg);
            }

            reportUIConsistency();
            // remove on pc_newdes split close
            createCustomBackground(stepNumber);
        }, 800);
    }

    function setupHintBannerAnimation(nextStepBlock, hintBanner, currentQuestionStepBtn) {
        const quizAnswersBlock = nextStepBlock.querySelector('.quiz__satisfy-list');

        if (!currentQuestionStepBtn || !quizAnswersBlock || !hintBanner || hintBanner.dataset.animated === 'true') {
            return;
        }

        const hintBannerTitle = hintBanner.querySelector('.quiz__cta-title');
        const hintBannerText = hintBanner.querySelector('.quiz__cta-text');
        const previosHintTitle = hintBannerTitle.textContent.trim();
        const previosHintText = hintBannerText.textContent.trim();

        hintBannerTitle.textContent = '';
        hintBannerText.textContent = '';

        function textDelay(ms) {
            return new Promise((resolve) => setTimeout(resolve, ms));
        }

        async function animateText(element, text, timeDelay) {
            for (let i = 0; i < text.length; i++) {
                element.textContent += text[i];
                // eslint-disable-next-line no-await-in-loop
                await textDelay(timeDelay);
            }
        }

        quizAnswersBlock.addEventListener('click', async () => {
            hintBanner.classList.add('active');
            currentQuestionStepBtn.classList.remove('hidden');
            hintBanner.dataset.animated = true;

            await animateText(hintBannerTitle, previosHintTitle, 25);
            await animateText(hintBannerText, previosHintText, 15);
        }, { once: true });
    }

    function replaceQuestionForFemaleGender() {
        const genderFemaleSubmit = document.querySelector('.femaleSubmit');
        const genderGenderElement = document.querySelectorAll('.gender-text-selector');

        if (!genderFemaleSubmit || !genderGenderElement) {
            return;
        }

        genderFemaleSubmit.addEventListener('click', () => {
            genderGenderElement.forEach((el) => {
                el.innerHTML = translate(el.textContent.trim(), window.language);
            });

            if (window.language === 'pt') {
                addSpecificPrefixForSomeWords(document.querySelectorAll('.genderPrefixInsertSelector'));
            }
        });
    }

    function skipQuestionIfNotMixedBreedSelected(triggeredBtn, stepNumber, nextStepBlock) {
        if (nextStepBlock.getAttribute('data-dog-size') === null) {
            return;
        }

        localStorage.setItem('MixedBreed', true);

        if (triggeredBtn.getAttribute('data-question-value') !== 'Mixed') {
            localStorage.removeItem('MixedBreed');

            skipNextQuestion(triggeredBtn, nextStepBlock);
        }

        const dogSizeBtns = nextStepBlock.querySelectorAll('.quiz__answer-item');
        const mixedBreedInsertSelector = document.getElementById('mixedBreedInsertSelector');
        const updateMixedBreedText = (el) => {
            const dogSize = el.getAttribute('data-question-value');
            const ageVariations = ['Adolescent', 'Adult', 'Senior'];
            const filteredDogAge = ageVariations.includes(responses.age) ? 'dog' : 'puppy';
            mixedBreedInsertSelector.innerHTML = 'Mixed ' + filteredDogAge + ', ' + dogSize.toLowerCase();
        };

        dogSizeBtns.forEach((el) => {
            el.addEventListener('click', () => {
                updateMixedBreedText(el);
            });
        });
    }

    function reportUIConsistency() {
        const quizActiveSteps = document.querySelectorAll('.quiz__index-active');
        if (quizActiveSteps.length < 2) {
            return;
        }

        const visibleQuestionIDs = Array
            .from(quizActiveSteps.values())
            .map((el) => el.getAttribute('id'))
            .join(',');

        Sentry.captureException(new Error('Inconsistent UI. Multiple questions are visible'), {
            extra: {
                visibleQuestionIDs,
            },
        });
    }

    function showEmail() {
        const resultStep1 = document.getElementById('resultStepOne');
        const resultStep2 = document.getElementById('resultStepTwo');

        resultStep1.style.setProperty('display', 'none');
        resultStep2.style.setProperty('display', 'block');

        window.scrollTo({
            top: 0, behavior: 'smooth',
        });

        // confetti
        const count = 600;
        const defaults = {
            origin: { y: 1 },
        };

        function fire(particleRatio, opts) {
            confetti({ ...defaults, ...opts, particleCount: Math.floor(count * particleRatio) });
        }

        fire(1, {
            spread: 115, startVelocity: 60, ticks: 800,
        });

        amplitude.logEvent('Magic view');
    }

    function showPreviousStep(currentStepNumber) {
        if (currentStepNumber < 2 && !window.isPuppyQuiz) {
            return;
        }

        if (currentStepNumber === 2 && !window.isPuppyQuiz) {
            backBtn.style.setProperty('opacity', '0');
        }

        const currentStepBlock = document.querySelector(`#block${currentStepNumber}`);
        const previousStepBlock = document.querySelector(`#block${currentStepNumber - 1}`);

        clearTimeout(backwardTransitionTimeout);
        clearTimeout(forwardTransitionTimeout);
        backwardTransitionTimeout = setTimeout(() => {
            if (currentStepNumber < 2 && window.isPuppyQuiz) {
                window.history.back();
                return;
            }

            const hideCurrentStepBlock = () => currentStepBlock.classList.remove('quiz__index-active');
            const showPreviousStepBlock = () => previousStepBlock.classList.add('quiz__index-active');

            hideCurrentStepBlock();

            i18n.translateElement(previousStepBlock, i18n.toContext(responses));

            const previousVisibleStepBlock = getFirstNonSkippedBlock(currentStepBlock);
            if (previousVisibleStepBlock) {
                previousVisibleStepBlock.classList.add('quiz__index-active');
            } else {
                showPreviousStepBlock();
            }

            // remove this after pc_newdes split close
            if (window.allSplitValues['pc_newdes'] < 2) {
                document.body.style.setProperty('background', '#ffffff');
            }

            // progressbar
            const questionsIndex = (currentStepNumber + firstStepPosition - 1) / (quizStepCount + firstStepPosition);
            line.animate(questionsIndex);
            reportUIConsistency();

            // remove this after pc_newdes split close
            createCustomBackground(currentStepNumber - 1);
        }, 1000);
    }

    function getFirstNonSkippedBlock(blockElement) {
        let current = blockElement.previousElementSibling;
        while (current) {
            if (current.tagName === 'DIV' && !current.hasAttribute('data-skipped')) {
                return current;
            }
            current.removeAttribute('data-skipped');
            current = current.previousElementSibling;
        }
        return null;
    }

    function showFirstBlock() {
        if (window.location.href.indexOf('skip-quiz') > -1 || window.location.href.indexOf('flow=rem') > -1) {
            const stepToShowElement = document.querySelector('.skipQuiz');
            i18n.translateElement(stepToShowElement, i18n.toContext(responses));

            stepToShowElement.classList.add('quiz__index-active');
            document.getElementById('resultStepOne').style.setProperty('display', 'none');
            document.getElementById('resultStepTwo').style.setProperty('display', 'block');

            document.querySelector('#progBar').style.setProperty('display', 'none');
            document.querySelector('.header-quiz__inner-bg').style.setProperty('background', 'none');

            backBtn.style.setProperty('opacity', '0');
        } else {
            const firstStepElement = document.getElementById('block1');
            i18n.translateElement(firstStepElement, i18n.toContext(responses));

            document.getElementById('block1').classList.add('quiz__index-active');

            if (!window.isPuppyQuiz) {
                backBtn.style.setProperty('opacity', '0');
                document.querySelector('#progBar').style.setProperty('display', 'none');
            }

            // remove on pc_newdes split close
            createCustomBackground(1);
        }
    }

    function selectValidation() {
        const activeStep = document.getElementsByClassName('quiz__index-active');
        const selectList = activeStep[0].getElementsByClassName('quiz__select-items');
        const quizBtn = activeStep[0].querySelector('.quiz__btn');

        if (selectList?.length === 0) {
            return;
        }

        for (let i = 0; i < selectList?.length; i += 1) {
            if (selectList[i].selectedIndex === 0) {
                quizBtn.disabled = true;
                return;
            }
        }

        quizBtn.disabled = false;
    }

    function addActiveClass() {
        const activeAnswer = document.querySelectorAll('.js-btn:not(.quiz__btn)');

        activeAnswer.forEach((el) => {
            el.addEventListener('click', () => {
                const container = el.closest('.quiz__index');
                const activeAnswerEl = container.getElementsByClassName('quiz__active');

                activeAnswerEl.length > 0 && activeAnswerEl[0].classList.remove('quiz__active');
                el.classList.add('quiz__active');
            });
        });
    }

    function addErrorMessage() {
        const quizErrorBtn = document.querySelectorAll('.quiz__error-btn');

        quizErrorBtn.forEach((btn) => {
            btn.addEventListener('click', () => {
                const currentActiveStep = document.querySelector('.quiz__index-active');
                const message = currentActiveStep.closest('.quiz__index-active').querySelector('.quiz__error-message');

                message.classList.add('active');
            });
        });
    }

    function rangeInputsDefault() {
        let rangeInputColor = ['#FC2C27', '#FF443F', '#FF623A', '#FF8334', '#FFA62E', '#FFC629', '#E6D52B', '#BDD733', '#92DA3B', '#6ADC42', '#38DF4B'];
        const rangeInputColorReverse = ['#38DF4B', '#6ADC42', '#92DA3B', '#BDD733', '#E6D52B', '#FFC629', '#FFA62E', '#FF8334', '#FF623A', '#FF443F', '#FC2C27'];
        const rangeInputs = document.querySelectorAll('.range-default');

        rangeInputs.forEach((rangeInput) => {
            const initialValue = rangeInput.value;
            const currentQuizWrapper = rangeInput.closest('.quiz__index');
            const currentRangeValueBlock = currentQuizWrapper.querySelector('.quiz__range-value');

            if (currentRangeValueBlock.classList.contains('reverse-color-value')) {
                rangeInputColor = rangeInputColorReverse;
            }

            // Set up initial state
            currentRangeValueBlock.innerText = initialValue;
            currentRangeValueBlock.parentNode.style.setProperty('color', rangeInputColor[initialValue]);

            rangeInput.addEventListener('input', () => {
                const currentValue = rangeInput.value;

                // Update value/color for current range value block
                currentRangeValueBlock.innerHTML = currentValue;
                currentRangeValueBlock.parentNode.style.setProperty('color', rangeInputColor[currentValue]);

                // Update color on current range input
                rangeInput.parentNode.style.setProperty('--value', currentValue);
                rangeInput.parentNode.style.setProperty('--text-value', currentValue);

                // Remove disabled status
                rangeInput.classList.remove('quiz__range-disabled-tooltip');
                currentRangeValueBlock.classList.remove('quiz__range-disabled-value');
            });
        });
    }

    function replaceTextGender() {
        const male = {
            He: 'he',
            His: 'his',
            Him: 'him',
        };

        const female = {
            He: 'she',
            His: 'her',
            Him: 'her',
        };

        document.querySelectorAll('.hisHer').forEach((el) => {
            const dogGender = localStorage.getItem('DogGender');
            const replacementMap = dogGender === "He" ? male : female;

            el.innerText = translate(replacementMap[el.getAttribute('data-gender')], window.language);
        });
    }

    function redirectToLanding(path, userID) {
        const url = new URL(`${document.location.origin}${path}/${userID}${document.location.search}`);
        if (responses.breed?.length > 0) {
            const breed = String(responses.breed.split(' ').join(''));

            url.searchParams.append('breed', breed);
        }
        if (responses.age?.length > 0) {
            const age = String(responses.age);

            url.searchParams.append('age', age);
        }
        document.location.replace(url);
    }

    function placeDogName() {
        const getDogName = document.querySelector('.dog-name-submit');
        const nameInsertSelector = document.querySelectorAll('.nameInsertSelector');

        if (getDogName === null) {
            return;
        }

        getDogName.addEventListener('click', () => {
            const dogName = document.getElementById('inputText').value;

            responses.dogName = dogName;

            nameInsertSelector.forEach((sel) => {
                sel.innerHTML = dogName;
            });
        });
    }

    // if split close to control group use function placeDogNameForSplit for default without skipDogNameBtn

    function placeDogNameForSplit() {
        const submitDogNameBtn = document.querySelector('.dog-name-submit');
        const skipDogNameBtn = document.querySelector('.dog-name-skip');
        const nameInsertSelector = document.querySelectorAll('.nameInsertSelector');

        const insertDogNameInNameInsertSelectors = () => {
            nameInsertSelector.forEach((sel) => {
                sel.innerHTML = responses.dogName;
            });
        }

        if (submitDogNameBtn) {
            submitDogNameBtn.addEventListener('click', () => {
                const dogNameInput = document.getElementById('inputText');

                responses.dogName = dogNameInput.value;
                insertDogNameInNameInsertSelectors();
            });
        }

        if (skipDogNameBtn) {
            skipDogNameBtn.addEventListener('click', () => {
                responses.dogName = responses.age === 'puppy' ? 'your puppy' : 'your dog';
                insertDogNameInNameInsertSelectors();
            });
        }
    }

    function placeDogAge() {
        const ageInsertSelector = document.querySelectorAll('.ageInsertSelector');
        const activeQuestion = document.querySelector('.quiz__index-active .quiz__active');
        const dogAge = activeQuestion.getAttribute('data-question-value');

        if (ageInsertSelector === null) {
            return;
        }

        ageInsertSelector.forEach((sel) => {
            sel.innerHTML = translate(dogAge.toLowerCase(), window.language);
        });
    }

    function placeProblems() {
        const problem = responses.problems[0];
        const problemInsertSelector = document.querySelectorAll('.problemInsertSelector');

        if (!problem || !problemInsertSelector) {
            return;
        }

        problemInsertSelector.forEach((sel) => {
            sel.innerHTML = problem;
        });
    }

    function keypressEnter(currentStepBtn, currentStepSelector) {
        if (currentStepSelector !== null) {
            currentStepSelector.addEventListener('keyup', (event) => {
                if (event.keyCode === 13) {
                    currentStepBtn.click();
                }
            });
        }
    }

    function replaceResultSubtitleTimeout() {
        const name = capitalizeString(responses.dogName);
        const replaceSubtitle = document.getElementById('replaceSubtitle');

        if (!replaceSubtitle || !name) {
            return;
        }

        if (WhereAmI.isQuiz2() || WhereAmI.isPuppyQuiz2()) {
            replaceSubtitle.innerHTML = `Finalising ${name}'s plan...`;
            replaceSubtitle.innerHTML = `Finalising ${name}'s plan...`;
        } else {
            replaceSubtitle.innerHTML = translate('magic.100_text', window.language, { name });
        }
    }

    function submitEmail() {
        const emailField = document.getElementById('email');
        const emailSubmit = document.getElementById('email-submit');
        const redirectToLandingBtn = document.getElementById('redirect-to-landing-btn');
        const resultStepTwo = document.getElementById('resultStepTwo');
        const resultStepThree = document.getElementById('resultStepThree');
        const resultStepEmailSubscription = document.getElementById('resultStepEmailSubscription');
        const countriesForCompliance = ["BE", "BG", "CZ", "DK", "DE", "EE", "IE", "EL", "ES", "FR", "HR", "IT", "CY", "LV", "LT", "LU", "HU", "MT", "NL", "AT", "PL", "PT", "RO", "SI", "SK", "FI", "SE", "GB"];

        const shouldShowReceiveEmailsConfirmationStep = countriesForCompliance.includes(window.userCountry);

        if (resultStepThree) {
            resultStepThree.style.display = 'none';
        }

        if (resultStepEmailSubscription) {
            resultStepEmailSubscription.style.display = 'none';
        }

        const setLoaderVisibility = (loader, state) => {
            if (!loader) {
                console.warn('Loader element not found');
                return;
            }

            loader.style.display = state;
        }

        const sendAmplitudeEvent = (el) => {
            const getEventValue = el.getAttribute('data-event-value');
            amplitude.logEvent(getEventValue);
        }

        const runAnimation = () => {
            const resultGraphGoal = document.querySelectorAll('.result-graph-goal');
            const resultGraphData = document.querySelectorAll('.quiz__reviews-loading-svg-date');
            const resultGraphDot = document.getElementById('resultGraphDot');
            const resultGraphBg = document.getElementById('resultGraphBg');

            graphAnimation(resultGraphData, resultGraphDot, resultGraphGoal, resultGraphBg);
        }

        if (shouldShowReceiveEmailsConfirmationStep) {
            const triggerEmailSubscriptionStepEl = document.querySelectorAll('.triggerStep');

            triggerEmailSubscriptionStepEl.forEach((el) => {
                el.addEventListener('click', async (evt) => {
                    responses.disallowMarketingEmails = +el.getAttribute('data-response-value') || 0;

                    evt.preventDefault()
                    sendAmplitudeEvent(el);
                    const userCreationResult = await createUser();

                    resultStepEmailSubscription.style.display = 'none';

                    if (userCreationResult === 200) {
                        resultStepThree.style.display = 'block';
                        resultStepEmailSubscription.style.display = 'none';
                        runAnimation();
                    }
                });
            });
        }

        emailField.addEventListener('input', () => {
            emailSubmit.disabled = emailField.value.length <= 0;
        });

        emailSubmit.addEventListener('click', async () => {
            resultStepTwo.style.display = 'none';

            logAmplitudeEvent('Email Submit Push');

            if (!shouldShowReceiveEmailsConfirmationStep) {
                const status = await createUser();

                if (status === 200) {
                    resultStepThree.style.display = 'block';
                    runAnimation();
                }
            } else {
                resultStepEmailSubscription.style.display = 'block';
            }
        });

        const buildCreateUserURL = (userResponses) => {
            const resultURL = new URL(window.location.origin + window.userCreateUrl + window.location.search);
            if (userResponses.breed?.length > 0) {
                resultURL.searchParams.append('breed', String(userResponses.breed.split(' ').join('')));
            }

            if (userResponses.age?.length > 0) {
                resultURL.searchParams.append('age', String(userResponses.age));
            }
            return resultURL;
        };

        const showEmailError = (loaderEl, errorMessage) => {
            const emailText = document.getElementById('emailText');

            setTimeout(() => {
                resultStepTwo.style.display = 'block';
                resultStepThree.style.display = 'none';
                emailText.classList.add('quiz__result-label-error');
                emailField.classList.add('quiz__email-error');
                emailSubmit.style.display = 'flex';
                setLoaderVisibility(loaderEl, 'none');
                emailText.innerText = translate(errorMessage, window.language);
            }, 1000);
            emailField.oninput = () => {
                emailField.classList.remove('quiz__email-error');
                !WhereAmI.isChallengeFunnel() && emailField.style.setProperty('border', '1px solid #EE6B63');
            };
        }

        const logAmplitudeEvent = (eventName, eventData = null) => {
            if (eventData) {
                amplitude.logEvent(eventName, eventData);
            } else {
                amplitude.logEvent(eventName);
            }
        }

        const saveUserIDToLocalStorage = (userId) => {
            localStorage.setItem('userId', userId ? userId : null);
        }

        const createUser = async () => {
            try {
                const email = emailField && emailField.value.trim();
                const loader = document.getElementById('resultLoader');

                setLoaderVisibility(loader, 'flex');
                emailSubmit.style.display = 'none';

                const createUserURL = buildCreateUserURL(responses);
                const response = await fetch(createUserURL, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',
                    },
                    body: JSON.stringify({
                        email,
                        problems: responses.problems,
                        disallow_marketing_emails: responses.disallowMarketingEmails,
                    }),
                });
                const result = await response.json();

                if (response.status === 200) {
                    await trackFbEvent('Email', result.uuid);
                    logAmplitudeEvent(result.status === 'created' ? 'Email submit' : 'Email Second Submit');
                    saveUserIDToLocalStorage(result.uuid);
                    saveQuizResponse(responses);
                    setLoaderVisibility(loader, 'none');
                } else if (result.status === 'error' && result.message !== null) {
                    logAmplitudeEvent(
                        result.message === 'User with this email already exists' ?
                            'User exist email error' :
                            'User email error'
                    );

                    const errorMessage = result.message;
                    showEmailError(loader, errorMessage);

                    if (result.amplitudeEvent) {
                        logAmplitudeEvent('EmailVerificationFailed', { email, context: result.context.remarks });
                    }
                }

                return response.status;
            } catch (e) {
                const emailText = document.getElementById('emailText');

                console.error(e);
                emailText.innerText = translate('email.error.something_went_wrong', window.language);
            }
        };

        const processToLanding = () => {
            const loader = document.getElementById('resultLoader');
            const userId = localStorage.getItem('userId');
            const responses = localStorage.getItem('quizResponses') ? JSON.parse(localStorage.getItem('quizResponses')) : {};

            setLoaderVisibility(loader, 'flex');
            saveQuizResponses(responses);
            saveQuizResponsesOnBackend(responses, userId)
                .then(() => {
                    const landingUrl = String(redirectToLandingBtn.getAttribute('data-landing-url'));
                    redirectToLanding(landingUrl, userId);
                });
        };

        redirectToLandingBtn.addEventListener('click', async () => {
            const isDiscSplit = window.allSplitValues['pc_disc_dokrut_love'] > 1 || window.allSplitValues['pc_disc_dokrut'] > 1;
            const isEnglishChallengeFunnel = WhereAmI.isChallengeFunnel() && window.language === 'en';
            const showDiscountScratchCard = isEnglishChallengeFunnel || WhereAmI.isVagusFunnel() || isDiscSplit;

            if (showDiscountScratchCard) {
                document.getElementById('resultStepTwo').style.display = 'none';
                document.getElementById('resultStepThree').style.display = 'none';

                const scratchCardScreenElement = document.getElementById('scratchCardDiscount');
                const scratchCardCanvasElement = scratchCardScreenElement.querySelector('canvas');
                const scratchCardOverlayElement = document.getElementById('scratchCardDiscountOverlay');
                const scratchCardPopupElement = document.getElementById('scratchCardDiscountPopup');
                const scratchCardContinueButton = scratchCardPopupElement.querySelector('button');

                i18n.translateElement(scratchCardScreenElement, i18n.toContext(responses));
                i18n.translateElement(scratchCardPopupElement, i18n.toContext(responses));

                scratchCardCanvasElement.classList.remove('fade-out');

                const showDiscountPopup = () => {
                    scratchCardOverlayElement.classList.add('visible');
                    requestAnimationFrame(() => {
                        scratchCardPopupElement.classList.add('slide-up');
                    });
                    scratchCardContinueButton.addEventListener('click', async () => {
                        scratchCardPopupElement.classList.remove('slide-up');
                        requestAnimationFrame(() => {
                            scratchCardOverlayElement.classList.remove('visible');
                        });
                        scratchCardScreenElement.style.display = 'none';
                        await processToLanding();
                    });
                };

                initScratchCard(scratchCardScreenElement, {
                    imageSrc: 'https://images.paw-champ.com/pc/images/figured-images/scratch-card-foil.png' + `?#${new Date().getTime()}`,
                    onComplete: async () => {
                        scratchCardCanvasElement.classList.add('fade-out');
                        setTimeout(() => showDiscountPopup(), 1000);
                    },
                });

                const scratchGestureHintElement = scratchCardScreenElement.querySelector('.scratch-card-hint');
                scratchGestureHintElement.classList.remove('fade-out');

                const startHintAnimationEvent = new Event('startHintAnimation');

                scratchGestureHintElement.addEventListener('startHintAnimation', () => {
                    scratchGestureHintElement.querySelector('#scratch_path_animation').beginElement();
                    scratchGestureHintElement.querySelector('#scratch_hand_animation').beginElement();
                }, false);

                const SCRATCH_CARD_EVENTS = ['click', 'touchstart', 'mousedown'];

                SCRATCH_CARD_EVENTS.forEach((event) => {
                    scratchCardCanvasElement.addEventListener(event, () => {
                        scratchGestureHintElement.classList.add('fade-out');
                    }, { once: true });
                });

                scratchGestureHintElement.dispatchEvent(startHintAnimationEvent);
            } else {
                await processToLanding();
            }
        });
    }

    function showNextStep(triggeredBtn, nextStepBlock) {
        const dateItems = document.querySelectorAll('.quiz__graph-svg-date');
        const goalDot = document.getElementById('goal-dot');
        const goalSelectors = document.querySelectorAll('.goalSelector');
        const graphBg = document.getElementById('graph-bg');

        if (triggeredBtn?.getAttribute('data-skip-next-question') === 'Skip') {
            nextStepBlock.setAttribute('data-skipped', 'true');
            nextStepBlock.nextElementSibling?.classList.add('quiz__index-active');
            graphAnimation(dateItems, goalDot, goalSelectors, graphBg);
        } else {
            nextStepBlock.classList.add('quiz__index-active');
        }
    }

    function sendEventsToAmplitudePupAdultFunnel() {
        if (WhereAmI.isPuppyQuiz()) {
            responses.age = 'puppy';
        }

        const puppyDog = document.querySelector('.puppyDog');
        const adultDog = document.querySelectorAll('.adultDog');

        if (puppyDog === null || adultDog === null) {
            return;
        }

        if (!WhereAmI.isPuppyQuiz()) {
            puppyDog?.addEventListener('click', () => {
                const identifyData = new amplitude.Identify()
                    .set('Dog age', 'Puppy');

                amplitude.identify(identifyData);
                amplitude.logEvent('Quiz question answered', { question: 0 });
                amplitude.logEvent('Puppy Dog Funnel');
                localStorage.setItem('PuppyDog', 'ok');
            });
        }

        adultDog.forEach((btn) => {
            btn.addEventListener('click', () => {
                const dogAge = btn.getAttribute('data-question-value');

                if (!dogAge) {
                    return;
                }

                amplitude.logEvent('Adult Dog Funnel');

                responses.age = dogAge;
            });
        });
    }

    function disableCheckboxesWhenSelectedLast(nextStepBlock) {
        const checkboxes = nextStepBlock.querySelectorAll('.quiz__multiple-item');
        const lastCheckboxes = nextStepBlock.querySelectorAll('.removeCheckboxesBtn');

        checkboxes.forEach((checkbox) => {
            checkbox.addEventListener('click', () => {
                lastCheckboxes.forEach((lastCheckbox) => {
                    lastCheckbox.classList.remove('quiz__multiple-checked');
                });
            });
        });

        lastCheckboxes.forEach((lastCheckbox) => {
            lastCheckbox.addEventListener('click', () => {
                checkboxes.forEach((checkbox) => {
                    checkbox.classList.remove('quiz__multiple-checked');
                });
                lastCheckboxes.forEach((otherCheckbox) => {
                    otherCheckbox.classList.remove('quiz__multiple-checked');
                });
                lastCheckbox.classList.add('quiz__multiple-checked');
                lastCheckbox.closest('.quiz__index').querySelector('.quiz__btn').disabled = false;
            });
        });
    }

    function initBreds() {
        initBreedDatalist(document.querySelector('.quiz__datalist'));
    }

    function quizButtonsActions() {
        const jsBtns = document.querySelectorAll('.js-btn');
        jsBtns[0].addEventListener('click', () => {
            trackFbEvent('StartQuiz');
        });

        jsBtns.forEach((item) => {
            item.addEventListener('click', (event) => {
                const currentQuestionId = event.currentTarget.getAttribute('id');
                const triggeredBtn = event.currentTarget;
                const hintBannerParentEl = triggeredBtn.closest('[data-hint-banner]');
                const isSatisfyItem = triggeredBtn.classList.contains('quiz__satisfy-list-item');

                if (!isSatisfyItem || !hintBannerParentEl) {
                    stepForward(triggeredBtn, parseInt(currentQuestionId, 10));
                }
            });
        });
    }

    function addListenersToTextInputs() {
        const inputsCheck = document.querySelectorAll('.quiz__inputs-text');

        inputsCheck.forEach((input) => {
            input.addEventListener('input', () => {
                const currentActiveStep = document.querySelector('.quiz__index-active');
                const currentActiveStepBtn = currentActiveStep.querySelector('.quiz__btn');
                const currentActiveStepInput = currentActiveStep.querySelector('.quiz__inputs-text');

                if (!currentActiveStepInput || !currentActiveStepBtn) {
                    reportUIConsistency();
                }

                if (input && input.value.length > 0) {
                    keypressEnter(currentActiveStepBtn, currentActiveStepInput);
                    currentActiveStepInput.classList.add('active');
                    currentActiveStepBtn.disabled = false;
                } else {
                    currentActiveStepInput.classList.remove('active');
                    currentActiveStepBtn.disabled = true;
                }

                currentActiveStepBtn.disabled = input?.value === '';
            });
        });
    }

    function backBtnInit() {
        backBtn.addEventListener('click', () => {
            const currentStepNumber = document.querySelector('.quiz__index-active').getAttribute('data-id');

            showPreviousStep(parseInt(currentStepNumber, 10));
        });
    }

    function addListenersToAllInputs() {
        const quizContainer = document.getElementById('quizContainer');

        quizContainer.addEventListener('input', () => {
            selectValidation();
        });
    }

    function dogBreedInputsActions() {
        const dogBreedInput = document.querySelector('input.quiz__datalist-items[name="breeds"]');
        const dogBreedRatios = document.querySelectorAll('[name="dog-breed-radio"]');
        // Disable radiobuttons on input to text field
        dogBreedInput?.addEventListener('input', () => {
            dogBreedRatios.forEach((dogBreedRadio) => {
                dogBreedRadio.checked = false;
            });
        });

        // Enable submit btn on radiobutton click and clear text input
        dogBreedRatios.forEach((dogBreedRadio) => {
            dogBreedRadio.addEventListener('change', () => {
                const currentStepContainer = document.querySelector('.quiz__index-active');
                currentStepContainer.querySelector('.quiz__btn').disabled = false;
                dogBreedInput.value = '';
            });
        });
    }

    function dataInputStatusCheck() {
        const dataInput = document.getElementById('dataInput');

        if (dataInput !== null) {
            dataInput.addEventListener('input', () => {
                const activeStep = document.getElementsByClassName('quiz__index-active');
                const dataInputBtn = activeStep[0].querySelector('.quiz__btn');

                if (dataInput?.value !== false) {
                    dataInputBtn.disabled = false;
                }
                if (dataInput?.value === '') {
                    dataInputBtn.disabled = true;
                }
            });
        }
    }

    function trackFbEventWhenUserAnswerFirstQuestion() {
        const activeStep = document.querySelector('.quiz__index-active');
        const firstStepBtns = activeStep.querySelectorAll('.js-btn');

        firstStepBtns.forEach((item) => {
            item.addEventListener('click', () => {
                trackFbEvent('StartQuiz');
            });
        });
    }

    function getThreeMontsLater(selector1, selector2, selector3) {
        if (!selector1 || !selector2 || !selector3) {
            return;
        }

        const currentDate = new Date();
        const nextMonthDate = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, currentDate.getDate());
        const thirdMonthDate = new Date(currentDate.getFullYear(), currentDate.getMonth() + 2, currentDate.getDate());

        const { language = 'en' } = window;

        selector1.innerText = new Intl.DateTimeFormat(language, { month: 'short' }).format(currentDate);
        selector2.innerText = new Intl.DateTimeFormat(language, { month: 'short' }).format(nextMonthDate);
        selector3.innerText = new Intl.DateTimeFormat(language, { month: 'short' }).format(thirdMonthDate);
    }

    function displayTooltips() {
        const responsesTimeArr = responses.periodTime;
        const tooltip = document.getElementById('tooltip-selector');
        const tooltipTail = document.getElementById('tail-selector');
        const tooltipDot = document.getElementById('tooltip-dot');
        const tooltipSubtitleSelector = document.getElementById('tooltip-subtitle-selector');
        const graphLine = document.getElementById('graph-line');

        if (!responsesTimeArr) {
            return;
        }

        if (responsesTimeArr.includes('Month') || responsesTimeArr.includes('Months')) {
            tooltipSubtitleSelector.classList.add('active');

            setTimeout(() => {
                graphLine.classList.add('active');
                tooltip.classList.add('visible');
                tooltipDot.classList.add('active');
                tooltipTail.style.setProperty('opacity', '1');
                tooltipTail.style.setProperty('visibility', 'visible');
                tooltipTail.style.setProperty('transition: ', 'opacity 0.3s ease-in-out');
            }, 1800);
        }
    }

    function setPeriodTimeOnPlaceholder() {
        const periodTimePlaceholder = document.querySelectorAll('.periodTimeInsertSelector');
        const timeToTrainInsertSelectror = document.querySelectorAll('.timeToTrainInsertSelectror');
        const periodTime = String(responses.periodTime);

        if (!periodTimePlaceholder.length) {
            return;
        }

        const periodTimeMap = {
            '5 min/day': 10,
            '10 min/day': 20,
            '15 min/day': 30,
            '30 min/day': 60,
            '60 min/day': 120
        };

        timeToTrainInsertSelectror?.forEach((el) => {
            const numericValue = periodTime.replace(/[^0-9]/g, '');
            el.innerText = String(numericValue);
        });

        periodTimePlaceholder?.forEach((el) => {
            const timeToTrain = responses.periodTime; // Assuming you add a data-timeToTrain attribute to specify the timeToTrain
            el.textContent = periodTimeMap[timeToTrain] || 0;
        });
    }

    function displayTooltipsTrainingGraphic() {
        const responsesTimeArr = responses.periodTime;
        const tooltip = document.getElementById('tooltip-selector');
        const tooltipTail = document.getElementById('tail-selector');
        const tooltipDot = document.getElementById('tooltip-dot');
        const graphLine = document.getElementById('graph-line');

        if (responsesTimeArr && responsesTimeArr.length === 0) {
            return;
        }

        setTimeout(() => {
            graphLine?.classList.add('active');
            tooltip?.classList.add('visible');
            tooltipDot?.classList.add('active');
            tooltipTail?.style.setProperty('opacity', '1');
            tooltipTail?.style.setProperty('visibility', 'visible');
            tooltipTail?.style.setProperty('transition: ', 'opacity 0.3s ease-in-out');
        }, 3000);
    }

    function fillPlaceholders() {
        placeDogName();
        insertPuppyText(responses);

        if (window.allSplitValues['pc_qualityTime'] === 2) {
            return;
        }

        const targetDatePlaceholder = document.getElementById('target-date-placeholder');

        if (!targetDatePlaceholder) {
            return;
        }

        const targetDateFormat = targetDatePlaceholder.getAttribute("data-date-format");

        fillTargetDate(targetDatePlaceholder, targetDateFormat || "[DAY] [MONTH] [YEAR]");
    }

    function summaryProfileAnimation() {
        const listItems = document.querySelectorAll('.quiz__profile-list-item');
        const girlImg = document.getElementById('profileImg');
        const quizProfile = document.getElementById('quizProfile');
        const profileArrow = document.getElementById('profileArrow');
        const rangeDot = document.getElementById('rangeDot');
        const obedienceTail = document.getElementById('obedienceTail');
        const obediencePopup = document.getElementById('obediencePopup');
        let i = 0;

        setTimeout(() => {
            quizProfile?.classList.add('active');
        }, 200);
        setTimeout(() => {
            girlImg?.classList.add('active');
        }, 600);
        setTimeout(() => {
            profileArrow?.classList.add('active');
        }, 1400);
        setTimeout(() => {
            rangeDot?.classList.add('active');
        }, 1200);

        setTimeout(() => {
            obedienceTail?.classList.add('active');
            obediencePopup?.classList.add('active');
        }, 1600);

        const interval = setInterval(() => {
            if (i >= listItems.length) {
                clearInterval(interval);
                return;
            }

            listItems[i].classList.add('active');
            i++;
        }, 300);
    }

    function removeHeaderOnFirstQuestion() {
        const quizHeader = document.getElementById('header-quizDefault');
        const clickSelectors = document.querySelectorAll('.clickSelector');

        clickSelectors.forEach((el) => {
            el.addEventListener('click', () => {
                setTimeout(() => {
                    document.body.classList.remove('zero-question');
                    quizHeader.style.setProperty('display', 'flex');
                }, 800);
            });
        });
    }

    function delay(timeout) {
        return new Promise((resolve) => {
            setTimeout(resolve, timeout);
        });
    }

    function updateTextColor(progressBar) {
        const progressBarElement = document.getElementById(progressBar._container.id);
        const textElement = progressBarElement.querySelector(".quiz-magic_progressbar_text");
        if (textElement) {
            textElement.classList.add("active");
        }
    }

    function changeProgress(progressBar) {
        const progressBarElement = document.getElementById(progressBar._container.id);
        const progressbarText = progressBarElement.querySelector(".progressbar-text");
        const { svg, path } = progressBar;
        svg.classList.add("quiz-magic_progressbar_svg-active");
        path.classList.add("quiz-magic_progressbar_stroke-light");

        if (progressbarText) {
            progressbarText.innerHTML = `
            <img src="https://images.paw-champ.com/pc/icons/check-circle-light-blue.svg" 
                alt="Success icon" 
                class="quiz-magic_progressbar_icon" />
        `;
        }
    }

    function oneProblemInsert(isProblemToLowercase) {
        const problemsArray = responses.problems;
        const firstProblemInsertSelector = document.getElementById('firstProblemInsertSelector');

        if (problemsArray.length === 0 || !firstProblemInsertSelector) {
            return;
        }

        let translatedProblem = translate(problemsArray[0], window.language);
        if (isProblemToLowercase) {
            translatedProblem = translatedProblem.charAt(0).toLowerCase() + translatedProblem.slice(1);
        }
        firstProblemInsertSelector.innerHTML = translatedProblem;
    }

    function showResultPopupsWithAge() {
        const reviewSlider = new Swiper('.quiz__reviews-slider', {
            modules: [Autoplay],
            autoplay: {
                delay: 3000,
            },
            slidesPerView: 1,
            loop: true,
            centeredSlides: true,
        });
        reviewSlider.autoplay.start();
        oneProblemInsert(true);
        replaceTextForMixedBreed(responses.breed);

        progressCircle.animate(0.32, {
            duration: 2200,
        });
        delay(2000)
            .then(() => showMagicPopup(
                document.getElementById("quizPopUpProblem"),
                document.querySelectorAll(".quizPopUpProblemBtn"),
                "Magic1",
                document.getElementById("magicProblemPopup"),
                "Loader Question Answer 1",
            ))
            .then(() => delay(2000))
            .then(() => showMagicPopup(
                document.getElementById("quizPopUpObedience"),
                document.querySelectorAll(".quizPopUpObedienceBtn"),
                "Magic2",
                document.getElementById("magicObediencePopup"),
                "Loader Question Answer 2",
            ))
            .then(() => progressCircle.animate(1, { duration: 2000 }))
            .then(() => delay(1500))
            .then(replaceResultSubtitleTimeout)
            .then(() => delay(1000))
            .then(() => {
                if (window.allSplitValues.pc_bundle > 1) {
                    document.getElementById('resultStepOne').style.setProperty('display', 'none');
                    return InteractiveCourseController.init();
                }
            })
            .then(showEmail)
            .catch(console.error);
    }

    function showMagicPopup(quizPopUpContainer, quizPopUpProblemBtns, eventName, quizPopupEl, amplitudeEvent, withProgressCircle = true) {
        return new Promise((resolve) => {
            const showPopUp = () => {
                quizPopUpContainer.classList.add('active');
                setTimeout(() => {
                    quizPopupEl.classList.add('active');
                }, 200);
            };
            const hidePopUp = () => {
                quizPopupEl.classList.remove('active');
                quizPopUpContainer.classList.remove('active');
            };

            if (quizPopUpContainer === null) {
                showEmail();
                resolve();

                return;
            }

            amplitude.logEvent(eventName);
            showPopUp();

            quizPopUpProblemBtns.forEach((item) => {
                item.addEventListener('click', () => {
                    item.classList.add('quiz__popup-btn-item--is-active');
                    const dataAttrValue = item.getAttribute('data-event');
                    const identify = new amplitude.Identify().set(amplitudeEvent, dataAttrValue);

                    amplitude.identify(identify);

                    hidePopUp();
                    if (withProgressCircle) {
                        progressCircle.animate(0.82, {
                            duration: 2000,
                        });
                    }

                    resolve();
                });
            });
        });
    }

    function onMagicVisit() {
        const addStylesToHeaderWhenMagicStart = () => {
            document.getElementById('resultStepTwo').style.setProperty('display', 'none');
            backBtn.style.setProperty('opacity', '0');
            document.querySelector('#progBar').style.setProperty('display', 'none');
            document.querySelector('.header-quiz__inner-bg').style.setProperty('background', 'none');
        };

        saveQuizResponses(responses);
        addStylesToHeaderWhenMagicStart();
        showResultPopupsWithAge();
    }

    function insertProfilePlaceholder(element, value) {
        element.innerHTML = value;
    }

    function removeFooterOnPuppyQuiz() {
        if (WhereAmI.isPuppyQuiz()) {
            document.querySelector('.quiz-footer').style.display = 'none';
        }
    }

    function change1stScreen() {
        if (WhereAmI.isPuppyQuiz()) {
            return;
        }
        const quizBody = document.querySelector('.quiz__body');

        if (!quizBody) {
            return;
        }

        quizBody.style.removeProperty('background');
    }

    function showMagicQuestionPopup(stepContainer) {
        return new Promise((resolve) => {
            const progressCircleContainer = stepContainer.querySelector('.progressbar');
            const progressCircle = new ProgressBar.Circle(progressCircleContainer, {
                color: '#16191E',
                strokeWidth: 16,
                trailWidth: 6,
                easing: 'linear',
                duration: 10000,
                text: {
                    autoStyleContainer: true, className: 'progressbar__text-reviews', style: {},
                },
                from: { color: '#1998CD' },
                to: { color: '#1998CD' },
                step(state, circle) {
                    circle.path.setAttribute('stroke', state.color);
                    circle.path.setAttribute('stroke-width', '6px');
                    circle.path.setAttribute('stroke-linecap', 'round');

                    const value = circle.value();

                    circle.setText(value === 0
                        ? ''
                        : `${Math.round(value * 100)}<span>%</span>`
                    );
                },
            })

            const applyProgress = (progress, duration) => {
                progressCircle.animate(progress, {
                    duration,
                });
            }

            applyProgress(0.64, 2200);

            const replaceProgressbarToIcon = () => {
                progressCircleContainer.classList.add('hidden');
                stepContainer.querySelector('#circleBarQuestion1Done')?.classList.add('active');
                stepContainer.querySelector('#circleBarQuestion1Text')?.remove();
            }

            const replaceTitle = (text) => {
                const questionTitle = stepContainer.querySelector('#circleBarQuestion1title');

                if (questionTitle) {
                    questionTitle.innerText = text;
                }
            }

            delay(2000)
                .then(() => showMagicPopup(
                    stepContainer.querySelector('[data-magic-question-popup-container]'),
                    stepContainer.querySelectorAll('.quizPopUpClarifyBtn'),
                    'Clarify',
                    stepContainer.querySelector('[data-magic-question-popup]'),
                    stepContainer.getAttribute('data-popup-question-amplitude-event-name'),
                    false,
                ))
                .then(() => replaceTitle('analysing your answers...'))
                .then(() => applyProgress(1, 2000))
                .then(() => delay(2000))
                .then(() => replaceTitle('Done'))
                .then(replaceProgressbarToIcon)
                .then(() => delay(1000))
                .then(resolve)
                .catch(console.error);
        })
    }

    function preserveQuizProgressForPuppyRedirect() {
        if (WhereAmI.isPuppyQuiz()) {
            const savedAnswers = JSON.parse(localStorage.getItem('savedAnswers') || null);

            if (!savedAnswers) {
                return;
            }

            responses.goals = savedAnswers.goals;
            responses.gender = savedAnswers.gender;

            return;
        }

        const puppyRedirectButton = document.querySelector('.puppyDog');

        if (!puppyRedirectButton) {
            return;
        }

        puppyRedirectButton.addEventListener('click', () => {
            try {
                localStorage.setItem('savedAnswers', JSON.stringify({
                    goals: responses.goals,
                    gender: responses.gender
                }));
            } catch (error) {
                console.error('Error save responses to localStorage:', error);
            }
        });
    }

    /**
     *
     * @param {number} percentage
     * @param {number} currentPercent
     * @param {HTMLElement} activeQuestion
     * @param {number} duration
     */
    function loveProgressbar(percentage, currentPercent, activeQuestion, duration = 1000) {
        const progressBar = activeQuestion.querySelector('.progressBar');
        const progressText = activeQuestion.querySelector('.progressText');

        if (!progressBar || !progressText) {
            console.warn("Element progressBar or progressText does not exist");
            return;
        }

        const finishPercentage = Math.min(100, Math.max(0, percentage));
        const step = finishPercentage / (duration / 8);

        const animate = () => {
            if (currentPercent < finishPercentage) {
                currentPercent = Math.min(currentPercent + step, finishPercentage);
                progressBar.style.width = currentPercent + '%';
                progressText.textContent = Math.round(currentPercent) + '%';

                requestAnimationFrame(animate);
            }
        };

        setTimeout(() => {
            animate();
        }, 600);
    }

    function insertMappedGoalText(goal) {
        const goalTextEl = document.getElementById('goal-mapped-text');

        if (!goalTextEl) {
            return;
        }

        const age = responses.age === 'puppy' ? 'pup' : 'dog';

        const PUPPY_TRAINING_TOPICS = {
            "Learn puppy basics": `Teach your ${age} essential basics`,
            "Improve obedience": `Develop reliable obedience in your ${age}`,
            "Learn useful commands": `Focus on teaching your ${age} essential commands`,
            "Build a strong bond with doggo": "Improve connection between you",
            "Build a strong bond": "Improve connection between you",
            "Improve emotional state & behavior": "Improve your dog’s emotional state and behavior",
            "Ease social anxiety": "Ease your dog’s social anxiety",
            "Reduce separation anxiety": "Reduce your dog’s separation anxiety",
            "Support nervous system": "Support your dog’s nervous system",
        };

        goalTextEl.textContent = PUPPY_TRAINING_TOPICS[goal] || goal;
    }

    function init() {
        executeSafely(checkVisitIdInQuery);
        executeSafely(initAmplitude);
        executeSafely(showFirstBlock);

        quizStore = new QuizStore({
            isPuppyQuiz: WhereAmI.isPuppyQuiz(),
        });

        responses = executeSafely(
            handleJumpToQuestion,
            {
                ...responses,
                ...quizStore.getResponses(),
            },
            summaryProfileAnimation,
            onMagicVisit,
        );

        executeSafely(addActiveClass);
        executeSafely(quizButtonsActions);
        executeSafely(initBreds);
        executeSafely(rangeInputsDefault);
        executeSafely(addErrorMessage);
        executeSafely(fillPlaceholders);
        executeSafely(backBtnInit);
        executeSafely(submitEmail);
        executeSafely(handleMultipleCheckbox);
        executeSafely(getThreeMontsLater, document.getElementById('monthGraph1'), document.getElementById('monthGraph2'), document.getElementById('monthGraph3'));
        executeSafely(getThreeMontsLater, document.getElementById('monthAfter1'), document.getElementById('monthAfter2'), document.getElementById('monthAfter3'));
        executeSafely(removeHeaderOnFirstQuestion);
        executeSafely(addListenersToTextInputs);
        executeSafely(addListenersToAllInputs);
        executeSafely(dogBreedInputsActions);
        executeSafely(dataInputStatusCheck);
        executeSafely(trackFbEventWhenUserAnswerFirstQuestion);
        executeSafely(insertPuppyText, responses);
        executeSafely(replaceQuestionForFemaleGender);
        executeSafely(removeFooterOnPuppyQuiz);
        executeSafely(logImgLoadError);
        executeSafely(change1stScreen);
        executeSafely(initQuestionImagesPreLoading);

        if (WhereAmI.isPuppyQuiz()) {
            replaceTextGender();
        }

        const shouldPreserveQuizProgress = WhereAmI.isPunishmentFunnel()
            || WhereAmI.isReactivityFunnel()
            || WhereAmI.isObedienceFunnel()
            || WhereAmI.isChallengeFunnel()
            || WhereAmI.isVagusAnxietyFunnel()
            || WhereAmI.isVagusFunnel()
            || WhereAmI.isLoveFunnel();

        if (shouldPreserveQuizProgress) {
            preserveQuizProgressForPuppyRedirect();
        }
    }
});
