@import "./abstracts/_variables.sass"
@import "./abstracts/mixins"
@import "./base/reset"
@import "./base/base"
@import "../../subscription/sass/components/header"
@import "components/footer"

main
  padding-top: 16px

.blog-post-page
  font-family: $main-font
  &_container
    width: 100%
    max-width: 720px
    display: flex
    margin: 0 auto
    justify-content: center

  &_module
    @extend .blog-post-page_container
    flex-direction: column
    justify-content: center
    align-items: center
    margin-bottom: 48px
    &-media
      @extend .blog-post-page__img-wrapp
      &.video
        max-width: 100%
        object-fit: cover
    &-item
      display: flex
      flex-direction: column
      align-items: center
      & > .blog-post-page_module-media
        margin-top: 16px

  &__title
    font-size: 28px
    font-weight: 800
    line-height: 120%

  &__time
    font-size: 10px
    line-height: 130%
    color: #343434
    display: flex
    align-items: center

    & svg
      margin-right: 2px
      margin-top: -1px

    & span
      margin-left: 2px

  &__date
    border-radius: 6px
    background: #F0F0F0
    padding: 2px 8px
    font-size: 10px
    font-style: normal
    font-weight: 400
    line-height: 130%
    margin-right: 16px

  &__info-box
    display: flex
    align-items: center
    margin-bottom: 16px
    margin-top: 24px

  &__btn
    &-back
      display: flex
      align-items: center
      font-size: 18px
      font-style: normal
      font-weight: 600
      line-height: 130%
      margin-bottom: 24px
      color: #343434
      cursor: pointer
      width: max-content

      & svg
        margin-right: 8px

  &__expert
    display: flex
    align-items: center
    margin: 16px 0

    &-img
      width: 100%

      &-wrapp
        min-width: 40px
        width: 40px
        height: 40px
        border: 2px solid #F0F0F0
        border-radius: 100%
        overflow: hidden
        margin-right: 8px

    &-name
      font-weight: 700
      line-height: 140%
      color: #343434

    &-description
      font-size: 14px
      font-weight: 400
      line-height: 140%
      color: #34343499
  &-quiz
    display: flex
    justify-content: center
    align-items: center
    flex-direction: column
    &-btn
      @include btn-red(24px)
      max-width: 248px
      margin-bottom: 16px
    &-text
      font-size: 14px
      font-weight: 400
      line-height: 120%
      text-align: center
      max-width: 184px
      width: 100%
      margin-bottom: 48px
  &__img
    width: 100%
    margin: 0 auto
    &-wrapp
      max-width: 343px
      width: 100%
      max-height: 343px
      height: 100%
      margin: 0 auto
      overflow: hidden
      border-radius: 16px
  &__content
    & > *:not(h1, h2, blockquote)
      margin-top: 16px
    & > h1, h2
      margin-top: 40px
    & blockquote > p
      font-style: italic
      color: #888888
      padding-left: 10px
      border-left: 3px solid #dee2e6
    & blockquote
      margin-top: 20px
      margin-bottom: 20px
    & h1
      font-size: 28px
    & h2
      font-size: 24px
    & h3
      font-size: 20px
      line-height: 130%
    & h1, h2
      line-height: 100%
    & strong
      font-weight: 800
    & hr
      display: none
    & ul
      padding-left: 24px
      list-style-type: disc
  &__recommended
    margin-bottom: 48px
    &-title
      font-size: 32px
      font-weight: 800
      line-height: 120%
      text-align: center
  &__list
    display: grid
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr))
    grid-row-gap: 16px
    grid-column-gap: 16px
    margin-top: 16px

    &-img
      width: 100%
      &-wrapp
        max-width: 440px
        width: 100%
        margin: 0 auto 16px
        overflow: hidden
        border-radius: 16px

    &-item
      border-bottom: 1px solid $blog-light-grey
      padding-bottom: 16px
      &-time
        font-size: 10px
        line-height: 130%
        color: $blog-black
        display: flex
        align-items: center
        &-wrapp
          display: flex
          flex-direction: column
          align-items: center
          justify-content: center

          & svg
            margin-right: 2px
            margin-top: -1px

          & span
            margin-left: 2px
      &-link
        display: flex
        margin-bottom: 32px
        &:last-of-type
          margin-bottom: 0
      &-title
        font-size: 16px
        font-weight: 800
        line-height: 120%
        margin-bottom: 8px
        color: $blog-black
      &-description
        font-size: 12px
        line-height: 130%
        color: $blog-black
      &-img
        width: 100%
        margin-bottom: 8px
        border-radius: 8px
        overflow: hidden
        &-wrapp
          width: 100%
          max-width: 88px
          min-width: 88px
          margin-right: 16px

      &-date
        border-radius: 6px
        background: #F0F0F0
        padding: 2px 8px
        font-size: 10px
        font-style: normal
        font-weight: 400
        line-height: 130%
        margin-bottom: 6px
        display: flex
        text-wrap: nowrap
        color: $blog-black
        width: 100%
        justify-content: center


@media (min-width: 768px)
  .blog-post-page
    &_module
      margin-bottom: 64px
    &__time
      font-size: 12px
    &__date
      font-size: 12px
    &__title
      font-size: 40px
    &__info
      &-box
        margin-top: 0
    &__head
      &-box
        width: 75%
        margin-left: 24px
    &__expert
      &-img
        &-wrapp
          min-width: 48px
          width: 48px
          height: 48px
    &__img
      &-wrapp
        max-height: 400px
        max-width: 400px
    &__list
      grid-template-columns: repeat(auto-fill, minmax(440px, 1fr))
      grid-row-gap: 32px
      grid-column-gap: 32px
      &-item
        &-title
          font-size: 16px
        &-description
          font-size: 12px
    &__recommended
      &-title
        font-size: 32px
