import { sendEventAmplitude } from "../unsubscribe/js/components/sendEventAmplitude";

const pageConfig = {
    default: {
        actionPath: "Rate_pop_up_action",
        viewPath: "Rate_pop_up_view",
        trustpilotPath: "Trustpilot_click",
    },
};

const pagePath = window.location.pathname.replace('/', '');
const config = pageConfig[pagePath] || pageConfig.default;

init();

function addEventListenersToReactionBtns() {
    const reactionBnts = document.querySelectorAll('.rate__button');

    if (!reactionBnts.length) {
        return;
    }

    reactionBnts.forEach((btn) => {
        btn.addEventListener('click', () => {
            removeActiveClassFromReactionBtns();
            btn.classList.add('active');
        });
    });
}

function createEventParams(reaction) {
    let ticketParams = {};
    try {
        ticketParams = window.ticketParams ? JSON.parse(window.ticketParams) : {};
    } catch (e) {
        console.warn('Failed to parse ticketParams:', e);
    }

    return {
        Reaction: reaction,
        group: ticketParams.group || null,
        channel: ticketParams.channel || null,
        expertName: ticketParams.expertName || null,
    };
}

async function addLikeBtnEventListener() {
    const trustpilotRedirectBtn = document.getElementById("redirectTrustpilotBtn");
    const trustpilotLink = trustpilotRedirectBtn.getAttribute(
        "data-trustpilot-link",
    );

    trustpilotRedirectBtn.addEventListener("click", () => {
        const reaction = 'like';
        const eventParams = createEventParams(reaction);

        submitReaction(reaction);

        Promise.allSettled([
            sendEventAmplitude(config.actionPath, { eventParams }),
            sendEventAmplitude(config.trustpilotPath, { eventParams }),
        ]).then(() => {
            window.location.href = trustpilotLink;
        });
    });
}

function showThankYouMessage() {
    const likeContentSel = document.getElementById('likeContent');
    const dislikeContentSel = document.getElementById('dislikeContent');

    if (!likeContentSel || !dislikeContentSel) {
        return;
    }

    likeContentSel.classList.add('hidden');

    setTimeout(() => {
        dislikeContentSel.classList.add('visible');
    }, 1300);
}

function removeActiveClassFromReactionBtns() {
    const reactionBnts = document.querySelectorAll('.rate__button');

    if (!reactionBnts.length) {
        return;
    }

    reactionBnts.forEach((btn) => {
        btn.classList.remove('active');
    });
}

function addFeedBtnEventListener() {
    const feedBtn = document.querySelectorAll('.feedBtn');

    if (!feedBtn) {
        return;
    }

    feedBtn.forEach((btn) => {
        btn.addEventListener('click', () => {
            const reaction = btn.getAttribute("data-event");
            const eventParams = createEventParams(reaction);

            showThankYouMessage();
            sendEventAmplitude(config.actionPath, { eventParams });
            submitReaction(reaction);
            redirectToMainPage();
        });
    });
}

function redirectToMainPage() {
    const queryParams = window.location.search;

    setTimeout(() => {
        window.location.href = `${window.location.origin}${queryParams}`;
    }, 3000);
}

function addNoThanksBtnEventListener() {
    const noThanksBtn = document.getElementById('noThanksBtn');

    if (!noThanksBtn) {
        return;
    }

    noThanksBtn.addEventListener('click', () => {
        showThankYouMessage();
        redirectToMainPage();
    });
}

async function submitReaction(reaction) {
    const searchParams = new URLSearchParams(window.location.search);
    if (!searchParams.has('ticket_id')) {
        return;
    }

    const data = {
        csrf_token: window.submitReactionToken,
        ticket_id: searchParams.get('ticket_id'),
        reaction,
    };

    const response = await fetch(window.location.href, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
        },
        body: JSON.stringify(data),
    });

    if (!response.ok) {
        throw new Error(`Submit reaction error! status: ${response.status}`);
    }
}

function init() {
    sendEventAmplitude(config.viewPath, { Reaction: 'none' });
    addEventListenersToReactionBtns();
    addLikeBtnEventListener();
    addFeedBtnEventListener();
    addNoThanksBtnEventListener();
}
