@import "abstracts/variables"
@import "abstracts/mixins"
@import "base/reset"
@import "base/base"

@import "components/upsale-progressbar"

.home-alone
  & main
    padding-top: 0
  &__header
    padding: 16px 0 32px
    max-width: 440px
    width: 100%
    margin: 0 auto
  &__title
    font-size: 26px
    font-weight: 800
    line-height: 110%
    text-align: left
    max-width: 408px
    width: 100%
    margin: 0 auto 24px
    & span
      @include textGradient(linear-gradient(159.44deg, #FDA3B7 5.8%, #FE7490 90.92%))
  &__list
    margin-left: 32px
    margin-bottom: 64px
    list-style: none
    max-width: 300px
    width: 100%
    &-item
      font-weight: 400
      font-size: 16px
      color: $black
      margin-bottom: 16px
      position: relative
      &:last-child
        margin-bottom: 0
      &:before
        content: url("https://res.cloudinary.com/dr0cx27xo/image/upload/v1638364286/dog-training/icons/done-pink-small.svg")
        position: absolute
        left: -28px
        top: -2px
  &__price
    background: #FFEEF1
    border: 1px solid #FDD7DE
    border-radius: 16px
    min-height: 116px
    display: flex
    justify-content: flex-end
    align-items: center
    position: relative
    margin: 32px 0
    &-title
      font-size: 16px
      line-height: 120%
      font-weight: 400
      color: #655356
      margin-bottom: 12px
    &-img
      max-width: 210px
      position: absolute
      left: -16px
    &-wrapper
      width: 43%
    &-box
      display: flex
    &-value
      background: #F7FCFF
      border: 1px solid #C7E7F7
      border-radius: 8px
      color: #655356
      margin-right: 8px
      display: flex
      width: 74px
      height: 32px
      justify-content: center
      align-items: center
      font-weight: 800
      font-size: 26px
      & span
        font-size: 10px
        line-height: 120%
        margin: -8px 2px 0 0
    &-old
      color: #655356
      text-decoration: line-through
      font-size: 14px
    &-sale
      background: linear-gradient(159.44deg, #67EF65 5.8%, #27B935 90.92%)
      border: 1px solid #BDE3F4
      border-radius: 8px 8px 0 0
      font-weight: 400
      position: absolute
      top: -24px
      right: 32px
      width: 118px
      height: 24px
      color: #FEFEFE
      text-align: center
      display: flex
      align-items: center
      justify-content: center
      & span
        font-weight: 800
        margin-right: 4px
  &__btn
    font-size: 24px
    line-height: 100%
    width: 100%
    max-width: 414px
    margin: 0 auto 32px
    height: 54px
    padding: 0 24px
    display: flex
    align-items: center
    justify-content: center
    color: $white
    font-weight: 700
    position: relative
    z-index: 1
    border-radius: 8px
    text-align: center
    outline: none
    cursor: pointer
    box-sizing: border-box
    box-shadow: 0 8px 16px #F4AEBA
    font-family: $main-font
    background: linear-gradient(159.44deg, #FDA3B7 5.8%, #FE7490 90.92%)
    border: 1px solid #EF6581
    &-text
      color: #B784A3
      font-size: 18px
      line-height: 130%
      height: 54px
      width: 100%
      max-width: 414px
      margin: 0 auto 40px
      cursor: pointer
      text-align: center
  &__loader
    display: none
    width: 32px
    height: 32px
    margin: 0 auto
    &-box
      height: 128px
      width: 100%
      margin-bottom: 16px
      align-items: center
      justify-content: center
    & .preload
      position: absolute
      width: 30px
      height: 30px
      border: 3px solid $blue
      border-radius: 50%
      border-right-color: transparent
      border-bottom-color: transparent
      animation-name: loaderRotate
      animation-duration: 1.0s
      animation-iteration-count: infinite
      animation-timing-function: linear
      @keyframes loaderRotate
        from
          -webkit-transform: rotate(0deg)
          transform: rotate(0deg)
        to
          -webkit-transform: rotate(360deg)
          transform: rotate(360deg)



