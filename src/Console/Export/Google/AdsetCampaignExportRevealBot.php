<?php

declare(strict_types=1);

namespace App\Console\Export\Google;

use App\Console\Common\DateRangeCommandHelper;
use App\Infrastructure\ExternalService\Google\Sheet\GoogleSheetAdapter;
use App\Infrastructure\ExternalService\Slack\Formatters\GoogleSheetErrorFormatter;
use App\Infrastructure\Repository\CostsFbRepository;
use DateTimeImmutable;
use Psr\Log\LoggerInterface;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Throwable;

#[AsCommand('app:google:adset_campaign_reveal')]
class AdsetCampaignExportRevealBot extends Command
{
    public function __construct(
        private readonly string $sheetId,
        private readonly GoogleSheetAdapter $googleSheetAdapter,
        private readonly CostsFbRepository $costsFbRepository,
        private readonly LoggerInterface $logger,
        private readonly GoogleSheetErrorFormatter $errorFormatter,
    ) {
        parent::__construct();
    }

    protected function configure(): void
    {
        $this->setDescription('Update google sheet data')
            ->setDefinition([
                new InputArgument('page_name', InputArgument::REQUIRED),
                ...DateRangeCommandHelper::inputConfig(),
            ]);
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $io->title('Started update of reveal bot sheet');

        $pageName = $input->getArgument('page_name');

        [$dateStart, $dateEnd] = DateRangeCommandHelper::getDateRange(
            input: $input,
            defaultStart: new DateTimeImmutable('midnight'),
            endDateOffsetModifier: '+1 day last second',
        );

        if (($dateStart !== null && $dateEnd !== null) && ($dateEnd < $dateStart)) {
            $io->error(sprintf(
                'End date (%s) must be greater than or equal to the start date (%s)',
                $dateEnd->format('Y-m-d H:i:s'),
                $dateStart->format('Y-m-d H:i:s')
            ));

            return Command::FAILURE;
        }

        $formattedDateStart = $dateStart->format('Y-m-d');
        $formattedDateEnd = $dateEnd->format('Y-m-d');
        $io->info("Getting data form DB for period $formattedDateStart - $formattedDateEnd...");
        $data = $this->costsFbRepository->getAdsetCampaignData($dateStart, $dateEnd);

        $io->info('Clearing rows...');
        try {
            $this->googleSheetAdapter->clearRows($this->sheetId, $pageName);
        } catch (Throwable $throwable) {
            $errorText = $this->errorFormatter->format($throwable);

            $io->error($throwable->getMessage());
            $this->logger->error(
                'Adset campaign reveal bot error occurred while clearing rows',
                [
                    'message' => $throwable->getMessage(),
                    'errorText' => $errorText,
                ],
            );

            return Command::FAILURE;
        }

        $io->info('Writing rows..');

        $headers = ['adset_id', 'campaign_id'];
        $exportData[] = $headers;

        foreach ($data as $datum) {
            $exportData[] = [$datum['adset_id'], $datum['campaign_id']];
        }

        try {
            $this->googleSheetAdapter->appendRows($this->sheetId, $pageName, $exportData);
        } catch (Throwable $throwable) {
            $errorText = $this->errorFormatter->format($throwable);

            $io->error($throwable->getMessage());
            $this->logger->error(
                'Adset campaign reveal bot error occurred while writing rows',
                [
                    'message' => $throwable->getMessage(),
                    'errorText' => $errorText,
                ],
            );

            return Command::FAILURE;
        }

        $io->success('Great success!');

        return Command::SUCCESS;
    }
}
