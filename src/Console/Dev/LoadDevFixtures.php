<?php

declare(strict_types=1);

namespace App\Console\Dev;

use App\Domain\Entity\Product;
use App\Domain\Entity\UnsubscribeReason;
use App\Domain\Entity\User;
use App\Domain\Entity\UserAdmin;
use App\Domain\Entity\UserLMS;
use App\Domain\Enum\FinancialPeriod;
use App\Domain\Enum\MailingChannel;
use App\Domain\Enum\UserLMSStatus;
use Doctrine\ORM\EntityManagerInterface;
use OpenSpout\Reader\CSV\Reader;
use OpenSpout\Reader\CSV\Options;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use DateTimeImmutable;
use Exception;
use Throwable;

#[AsCommand(
    name: 'app:dev:load-fixtures',
    description: 'Loads development fixture data from multiple CSV files into the database',
)]
class LoadDevFixtures extends Command
{
    private const FIXTURES_DIR = '/deployment/dev-fixtures/';

    private array $csvFilesToProcess = [
        'product.csv' => 'processProductCsv',
        'unsubscribe_reason.csv' => 'processUnsubscribeReasonCsv',
        'user.csv' => 'processUserCsv',
        'user_admin.csv' => 'processUserAdminCsv',
        'user_lms.csv' => 'processUserLmsCsv',
    ];

    public function __construct(
        private readonly EntityManagerInterface $entityManager,
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);
        $fixturesDir = getcwd() . self::FIXTURES_DIR;

        $io->title('Loading development fixtures from CSV files');

        foreach ($this->csvFilesToProcess as $csvFile => $processMethod) {
            $filePath = $fixturesDir . $csvFile;

            if (!file_exists($filePath)) {
                $io->warning(sprintf('CSV file not found: %s', $filePath));
                continue;
            }

            $io->section(sprintf('Processing %s', $csvFile));

            try {
                $recordCount = $this->{$processMethod}($filePath, $io);
                $io->success(sprintf('Successfully processed %d records from %s', $recordCount, $csvFile));
            } catch (Throwable $e) {
                $io->error(sprintf('Error processing %s: %s', $csvFile, $e->getMessage()));
            }
        }

        $io->success('Development fixtures loaded successfully');

        return Command::SUCCESS;
    }

    private function processProductCsv(string $filePath, SymfonyStyle $io): int
    {
        $reader = $this->getReader($filePath);
        $recordCount = 0;

        foreach ($reader->getSheetIterator() as $sheet) {
            foreach ($sheet->getRowIterator() as $rowIndex => $row) {
                if ($rowIndex === 1) {
                    continue;
                }

                try {
                    $cells = $row->getCells();
                    $id = $cells[0]->getValue();
                    $description = $cells[1]->getValue();
                    $trialAmount = $this->parseNullableValue($cells[2]->getValue());
                    $trialCurrency = $this->parseNullableValue($cells[3]->getValue());
                    $name = $cells[5]->getValue();
                    $amount = (int) $cells[6]->getValue();
                    $currency = $cells[7]->getValue();
                    $trial = (bool) (int) $cells[8]->getValue();
                    $discount50ProductId = $this->parseNullableValue($cells[9]->getValue());
                    $discount75ProductId = $this->parseNullableValue($cells[10]->getValue());
                    $discountUnlimitedProductId = $this->parseNullableValue($cells[11]->getValue());
                    $trialDuration = $this->parseNullableValue($cells[12]->getValue());
                    $billingPeriod = $this->parseNullableValue($cells[13]->getValue());

                    $product = $this->entityManager->getRepository(Product::class)->find($id);

                    if (!$product) {
                        $product = new Product($id, $name, $description, $amount, $currency, $trial);
                    }

                    $product->setTrialAmount($trialAmount !== null ? (int) $trialAmount : null);
                    $product->setTrialCurrency($trialCurrency);

                    $discount50Product = null;
                    if ($discount50ProductId !== null) {
                        $discount50Product = $this->entityManager->getRepository(Product::class)->find($discount50ProductId);
                    }
                    $product->setDiscount50Product($discount50Product);

                    $discount75Product = null;
                    if ($discount75ProductId !== null) {
                        $discount75Product = $this->entityManager->getRepository(Product::class)->find($discount75ProductId);
                    }
                    $product->setDiscount75Product($discount75Product);

                    $discountUnlimitedProduct = null;
                    if ($discountUnlimitedProductId !== null) {
                        $discountUnlimitedProduct = $this->entityManager->getRepository(Product::class)->find($discountUnlimitedProductId);
                    }
                    $product->setDiscountUnlimitedProduct($discountUnlimitedProduct);

                    if ($trialDuration !== null) {
                        $product->setTrialDuration(FinancialPeriod::from($trialDuration));
                    }

                    if ($billingPeriod !== null) {
                        $product->setBillingPeriod(FinancialPeriod::from($billingPeriod));
                    }

                    $this->entityManager->persist($product);
                    ++$recordCount;
                } catch (Exception $e) {
                    $io->warning(sprintf('Error processing product row %d: %s', $rowIndex, $e->getMessage()));
                }
            }
        }

        $this->entityManager->flush();
        $reader->close();

        return $recordCount;
    }

    private function processUnsubscribeReasonCsv(string $filePath, SymfonyStyle $io): int
    {
        $reader = $this->getReader($filePath);
        $recordCount = 0;

        foreach ($reader->getSheetIterator() as $sheet) {
            foreach ($sheet->getRowIterator() as $rowIndex => $row) {
                if ($rowIndex === 1) {
                    continue;
                }

                try {
                    $cells = $row->getCells();
                    $name = $cells[1]->getValue();
                    $template = $cells[2]->getValue();
                    $unsubscribeReason = new UnsubscribeReason($name, $template);

                    $this->entityManager->persist($unsubscribeReason);
                    ++$recordCount;
                } catch (Exception $e) {
                    $io->warning(sprintf('Error processing unsubscribe reason row %d: %s', $rowIndex, $e->getMessage()));
                }
            }
        }

        $this->entityManager->flush();
        $reader->close();

        return $recordCount;
    }

    private function processUserCsv(string $filePath, SymfonyStyle $io): int
    {
        $reader = $this->getReader($filePath);
        $recordCount = 0;

        foreach ($reader->getSheetIterator() as $sheet) {
            foreach ($sheet->getRowIterator() as $rowIndex => $row) {
                if ($rowIndex === 1) {
                    continue;
                }

                try {
                    $cells = $row->getCells();
                    $externalId = $cells[1]->getValue();
                    $email = $cells[2]->getValue();
                    $password = $this->parseNullableValue($cells[4]->getValue());
                    $mailingChannel = $this->parseNullableValue($cells[5]->getValue());

                    $existingUserByExternalId = $this->entityManager->getRepository(User::class)->findOneBy(['externalId' => $externalId]);

                    if ($existingUserByExternalId) {
                        $io->info(sprintf('Skipping user with external ID %s (already exists)', $externalId));
                        continue;
                    }

                    $existingUserByEmail = $this->entityManager->getRepository(User::class)->findOneBy(['email' => $email]);

                    if ($existingUserByEmail) {
                        $io->info(sprintf('Skipping user with email %s (already exists)', $email));
                        continue;
                    }

                    $user = new User();
                    $user->setExternalId($externalId);
                    $user->setEmail($email);

                    if ($password === null || $password === '<null>') {
                        $user->setPassword(null);
                    } else {
                        $user->setPassword($password);
                    }

                    if ($mailingChannel !== null) {
                        $user->setMailingChannel(MailingChannel::tryFrom($mailingChannel));
                    }

                    $this->entityManager->persist($user);
                    ++$recordCount;
                } catch (Exception $e) {
                    $io->warning(sprintf('Error processing user row %d: %s', $rowIndex, $e->getMessage()));
                }
            }
        }

        $this->entityManager->flush();
        $reader->close();

        return $recordCount;
    }

    private function processUserAdminCsv(string $filePath, SymfonyStyle $io): int
    {
        $reader = $this->getReader($filePath);
        $recordCount = 0;

        foreach ($reader->getSheetIterator() as $sheet) {
            foreach ($sheet->getRowIterator() as $rowIndex => $row) {
                if ($rowIndex === 1) {
                    continue;
                }

                try {
                    $cells = $row->getCells();
                    $email = $cells[1]->getValue();
                    $password = $cells[2]->getValue();
                    $roles = $this->parseArray($cells[3]->getValue());

                    $existingAdminUser = $this->entityManager->getRepository(UserAdmin::class)->findOneBy(['email' => $email]);

                    if ($existingAdminUser) {
                        $io->info(sprintf('Skipping admin user with email %s (already exists)', $email));
                        continue;
                    }

                    $adminUser = new UserAdmin();
                    $adminUser->setEmail($email);
                    $adminUser->setRoles($roles);
                    $adminUser->setPassword($password);

                    $this->entityManager->persist($adminUser);
                    ++$recordCount;
                } catch (Exception $e) {
                    $io->warning(sprintf('Error processing admin user row %d: %s', $rowIndex, $e->getMessage()));
                }
            }
        }

        $this->entityManager->flush();
        $reader->close();

        return $recordCount;
    }

    private function processUserLmsCsv(string $filePath, SymfonyStyle $io): int
    {
        $reader = $this->getReader($filePath);
        $recordCount = 0;

        foreach ($reader->getSheetIterator() as $sheet) {
            foreach ($sheet->getRowIterator() as $rowIndex => $row) {
                if ($rowIndex === 1) {
                    continue;
                }

                try {
                    $cells = $row->getCells();
                    $userId = (int) $cells[1]->getValue();
                    $externalId = $cells[2]->getValue();
                    $status = $cells[3]->getValue();
                    $createdAt = $this->parseDateTime($cells[4]->getValue());

                    $user = $this->entityManager->getRepository(User::class)->find($userId);
                    if (!$user) {
                        $io->warning(sprintf('User with ID %s not found for UserLMS record', $userId));
                        continue;
                    }

                    $existingUserLms = $this->entityManager->getRepository(UserLMS::class)->findOneBy([
                        'user' => $user,
                    ]);

                    if ($existingUserLms) {
                        $io->info(sprintf('Skipping UserLMS for User ID %s (already exists)', $userId));
                        continue;
                    }

                    $userLms = new UserLMS(
                        $user,
                        UserLMSStatus::from($status)
                    );

                    $user->setExternalId($externalId);

                    if ($createdAt !== null && method_exists($userLms, 'setCreatedAt')) {
                        $userLms->setCreatedAt($createdAt);
                    }

                    $this->entityManager->persist($userLms);
                    ++$recordCount;
                } catch (Exception $e) {
                    $io->warning(sprintf('Error processing user LMS row %d: %s', $rowIndex, $e->getMessage()));
                }
            }
        }

        $this->entityManager->flush();
        $reader->close();

        return $recordCount;
    }

    private function getReader(string $filePath): Reader
    {
        $options = new Options();
        $options->FIELD_DELIMITER = ',';
        $options->FIELD_ENCLOSURE = '"';

        $reader = new Reader($options);
        $reader->open($filePath);

        return $reader;
    }

    private function parseNullableValue(?string $value): ?string
    {
        if ($value === null || $value === '' || $value === '<null>') {
            return null;
        }

        return $value;
    }

    private function parseDateTime(?string $dateTimeString): ?DateTimeImmutable
    {
        if ($dateTimeString === null || $dateTimeString === '' || $dateTimeString === '<null>') {
            return null;
        }

        return new DateTimeImmutable($dateTimeString);
    }

    private function parseArray(string $arrayString): array
    {
        if (str_starts_with($arrayString, '[') && str_ends_with($arrayString, ']')) {
            return json_decode($arrayString, true) ?: [];
        }

        return array_map('trim', explode(',', $arrayString));
    }
}
