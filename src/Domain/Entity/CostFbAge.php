<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Infrastructure\Repository\CostsFbAgeRepository;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: CostsFbAgeRepository::class)]
#[ORM\Table(name: 'costs_fb_age')]
#[ORM\Index(columns: ['date_created'], name: 'date_created')]
#[ORM\Index(columns: ['campaign_name'], name: 'campaign_name')]
#[ORM\Index(columns: ['campaign_id'], name: 'campaign_id')]
class CostFbAge extends AbstractCost
{
    #[ORM\Column(type: 'string', length: 255, nullable: true)]
    private ?string $age = null;

    public function getAge(): ?string
    {
        return $this->age;
    }

    public function setAge(?string $age): self
    {
        $this->age = $age;

        return $this;
    }
}
