<?php

declare(strict_types=1);

namespace App\Domain\Entity;

use App\Infrastructure\Repository\UserNonLoginRepository;
use DateTimeImmutable;
use Doctrine\ORM\Mapping as ORM;
use Symfony\Bridge\Doctrine\Validator\Constraints\UniqueEntity;

#[ORM\Entity(repositoryClass: UserNonLoginRepository::class)]
#[ORM\HasLifecycleCallbacks]
#[UniqueEntity('user')]
class UserNonLogin
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column(type: 'integer')]
    private int $id;

    #[ORM\Column(type: 'datetime_immutable', nullable: true)]
    private ?DateTimeImmutable $notificationDate = null;

    public function __construct(
        #[ORM\OneToOne(targetEntity: User::class)]
        #[ORM\JoinColumn(unique: true, nullable: false, onDelete: 'CASCADE')]
        private User $user,
        #[ORM\Column(type: 'datetime_immutable')]
        private DateTimeImmutable $paymentDate,
    ) {
    }

    public function getId(): int
    {
        return $this->id;
    }

    public function setId(int $id): UserNonLogin
    {
        $this->id = $id;

        return $this;
    }

    public function getNotificationDate(): ?DateTimeImmutable
    {
        return $this->notificationDate;
    }

    public function setNotificationDate(?DateTimeImmutable $notificationDate): UserNonLogin
    {
        $this->notificationDate = $notificationDate;

        return $this;
    }

    public function getUser(): User
    {
        return $this->user;
    }

    public function setUser(User $user): UserNonLogin
    {
        $this->user = $user;

        return $this;
    }

    public function getPaymentDate(): DateTimeImmutable
    {
        return $this->paymentDate;
    }

    public function setPaymentDate(DateTimeImmutable $paymentDate): UserNonLogin
    {
        $this->paymentDate = $paymentDate;

        return $this;
    }
}
