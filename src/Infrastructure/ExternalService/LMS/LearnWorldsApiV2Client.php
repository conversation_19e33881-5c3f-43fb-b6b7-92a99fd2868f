<?php

declare(strict_types=1);

namespace App\Infrastructure\ExternalService\LMS;

use App\Infrastructure\ExternalService\LMS\DTO\CreateUserDTO;
use App\Infrastructure\ExternalService\LMS\DTO\EnrollUserToProductDTO;
use App\Infrastructure\ExternalService\LMS\DTO\GetUserCoursesResponseDTO;
use App\Infrastructure\ExternalService\LMS\DTO\SSOLinkDTO;
use App\Infrastructure\ExternalService\LMS\DTO\TagUserDTO;
use App\Infrastructure\ExternalService\LMS\DTO\UnenrollUserFromProductDTO;
use App\Infrastructure\ExternalService\LMS\DTO\UpdateUserDTO;
use Symfony\Component\DependencyInjection\Attribute\AsAlias;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Serializer\Normalizer\AbstractObjectNormalizer;
use Symfony\Component\Serializer\SerializerInterface;
use App\Infrastructure\ExternalService\LMS\DTO\GetAllUserResponseDTO;
use Symfony\Contracts\HttpClient\HttpClientInterface;
use Symfony\Contracts\HttpClient\ResponseInterface;

#[AsAlias]
class LearnWorldsApiV2Client implements LearnWorldsApiV2ClientInterface
{
    public function __construct(
        private readonly HttpClientInterface $lmsV2Client,
        private readonly SerializerInterface $serializer,
        private readonly LearnWorldsTokenCacheProvider $tokenProvider,
    ) {
    }

    /**
     * @throws \Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface
     */
    public function getUser(string $email, array $params = []): ResponseInterface
    {
        return $this->lmsV2Client->request('GET', "/admin/api/v2/users/$email", [
            'query' => $params,
            'auth_bearer' => $this->tokenProvider->getToken(),
        ]);
    }

    /**
     * @throws \Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface
     */
    public function createUser(CreateUserDTO $createUserDTO): ResponseInterface
    {
        $body = $this->serializer->serialize($createUserDTO, 'json');

        return $this->lmsV2Client->request('POST', '/admin/api/v2/users', [
            'body' => $body,
            'auth_bearer' => $this->tokenProvider->getToken(),
        ]);
    }

    /**
     * @throws \Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface
     */
    public function updateUser(string $email, UpdateUserDTO $updateUserDTO): ResponseInterface
    {
        $body = $this->serializer->serialize(
            $updateUserDTO,
            'json',
            [AbstractObjectNormalizer::SKIP_NULL_VALUES => true]
        );

        return $this->lmsV2Client->request('PUT', "/admin/api/v2/users/$email", [
            'body' => $body,
            'auth_bearer' => $this->tokenProvider->getToken(),
        ]);
    }

    /**
     * @throws \Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface
     */
    public function enrollUserToProduct(
        string $email,
        EnrollUserToProductDTO $enrollUserToProductDTO,
    ): ResponseInterface {
        $body = $this->serializer->serialize($enrollUserToProductDTO, 'json');

        return $this->lmsV2Client->request('POST', "/admin/api/v2/users/$email/enrollment", [
            'body' => $body,
            'auth_bearer' => $this->tokenProvider->getToken(),
        ]);
    }

    /**
     * @throws \Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface
     */
    public function unenrollUserFromProduct(
        string $email,
        UnenrollUserFromProductDTO $unenrollUserFromProductDTO,
    ): ResponseInterface {
        $body = $this->serializer->serialize($unenrollUserFromProductDTO, 'json');

        return $this->lmsV2Client->request('DELETE', "/admin/api/v2/users/$email/enrollment", [
            'body' => $body,
            'auth_bearer' => $this->tokenProvider->getToken(),
        ]);
    }

    /**
     * @throws \Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface
     */
    public function getSSOLink(SSOLinkDTO $SSOLinkDTO): ResponseInterface
    {
        return $this->lmsV2Client->request('POST', '/admin/api/sso', [
            'json' => (array) $SSOLinkDTO,
            'auth_bearer' => $this->tokenProvider->getToken(),
        ]);
    }

    /**
     * @throws \Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface
     * @throws \Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface
     * @throws \Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface
     * @throws \Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface
     */
    public function getAllUsers(int $page, int $itemsPerPage): GetAllUserResponseDTO
    {
        $builtQuery = http_build_query(['page' => $page, 'items_per_page' => $itemsPerPage]);

        $response = $this->lmsV2Client->request('GET', "/admin/api/v2/users?$builtQuery", [
            'auth_bearer' => $this->tokenProvider->getToken(),
        ]);

        return $this->serializer->deserialize($response->getContent(), GetAllUserResponseDTO::class, 'json');
    }

    /**
     * @throws \Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface
     */
    public function tagUser(string $email, TagUserDTO $tagUserDTO): ResponseInterface
    {
        $body = $this->serializer->serialize($tagUserDTO, 'json');

        return $this->lmsV2Client->request('PUT', "/admin/api/v2/users/$email/tags", [
            'body' => $body,
            'auth_bearer' => $this->tokenProvider->getToken(),
        ]);
    }

    /**
     * @throws \Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface
     * @throws \Symfony\Contracts\HttpClient\Exception\ServerExceptionInterface
     * @throws \Symfony\Contracts\HttpClient\Exception\RedirectionExceptionInterface
     * @throws \Symfony\Contracts\HttpClient\Exception\ClientExceptionInterface
     */
    public function getUserCourses(string $email, int $page = 1, int $itemsPerPage = 50): ?GetUserCoursesResponseDTO
    {
        $builtQuery = http_build_query(['page' => $page, 'items_per_page' => $itemsPerPage]);

        $response = $this->lmsV2Client->request('GET', "/admin/api/v2/users/$email/courses?$builtQuery", [
            'auth_bearer' => $this->tokenProvider->getToken(),
        ]);

        // LMS returns 404 response if user not enrolled to any course
        if ($response->getStatusCode() === Response::HTTP_NOT_FOUND) {
            return null;
        }

        return $this->serializer->deserialize($response->getContent(), GetUserCoursesResponseDTO::class, 'json');
    }

    /**
     * @throws \Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface
     */
    public function suspendUser(string $email): ResponseInterface
    {
        return $this->lmsV2Client->request('PUT', "/admin/api/v2/users/$email/suspend", [
            'auth_bearer' => $this->tokenProvider->getToken(),
        ]);
    }

    /**
     * @throws \Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface
     */
    public function unsuspendUser(string $email): ResponseInterface
    {
        return $this->lmsV2Client->request('PUT', "/admin/api/v2/users/$email/unsuspend", [
            'auth_bearer' => $this->tokenProvider->getToken(),
        ]);
    }
}
