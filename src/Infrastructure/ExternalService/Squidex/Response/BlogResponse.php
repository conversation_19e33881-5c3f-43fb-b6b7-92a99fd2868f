<?php

declare(strict_types=1);

namespace App\Infrastructure\ExternalService\Squidex\Response;

use DateTimeImmutable;

class BlogResponse extends ResponseItemData
{
    public function __construct(
        public string $alias,
        public string $headline,
        public string $description,
        public string $body,
        public Asset $cover,
        public string $coverAlt,
        public string $coverTitle,
        public string $keywords,
        public float $timeToRead,
        public DateTimeImmutable $publishedAt,
        public string $author,
        public ?string $publisher = null,
        /** @var PostModule[] */
        public array $modules = [],
    ) {
    }
}
