<?php

declare(strict_types=1);

namespace App\Infrastructure\ExternalService\Squidex\Request;

use Symfony\Component\Serializer\Annotation\SerializedName;

class Query
{
    #[SerializedName('$top')]
    public int $top;
    #[SerializedName('$skip')]
    public int $skip;
    #[SerializedName('$search')]
    public string $search;
    #[SerializedName('$filter')]
    public string $filter;
    #[SerializedName('$orderby')]
    public string $orderBy;
}
