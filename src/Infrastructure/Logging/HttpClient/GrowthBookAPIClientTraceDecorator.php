<?php

declare(strict_types=1);

namespace App\Infrastructure\Logging\HttpClient;

use Psr\Log\LoggerInterface;
use Symfony\Component\DependencyInjection\Attribute\AsDecorator;
use Symfony\Component\DependencyInjection\Attribute\AutowireDecorated;
use Symfony\Contracts\HttpClient\HttpClientInterface;

#[AsDecorator(decorates: 'growthBookAPI.client.retryable', priority: 10)]
class GrowthBookAPIClientTraceDecorator extends AbstractHttpClientLoggingDecorator
{
    public function __construct(
        #[AutowireDecorated]
        HttpClientInterface $httpClient,
        LoggerInterface $logger,
    ) {
        parent::__construct($httpClient, $logger);
    }
}
