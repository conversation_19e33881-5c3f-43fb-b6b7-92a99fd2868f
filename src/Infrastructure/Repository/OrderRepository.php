<?php

namespace App\Infrastructure\Repository;

use App\Domain\Entity\Order;
use App\Domain\Entity\Product;
use App\Domain\Entity\User;
use App\Domain\Enum\OrderStatus;
use DateTime;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\DBAL\Connection;
use Doctrine\DBAL\LockMode;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Query\Expr\Join;
use Doctrine\ORM\Query\ResultSetMapping;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method Order|null find($id, $lockMode = null, $lockVersion = null)
 * @method Order|null findOneBy(array $criteria, array $orderBy = null)
 * @method Order[]    findAll()
 * @method Order[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class OrderRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry, private readonly Connection $connection)
    {
        parent::__construct($registry, Order::class);
    }

    public function getByExternalIdWithLock(string $externalId): ?Order
    {
        $qb = $this->createQueryBuilder('o');

        return $qb->andWhere('o.externalId = :externalId')
            ->setParameter('externalId', $externalId)
            ->getQuery()
            ->setLockMode(LockMode::PESSIMISTIC_WRITE)
            ->getOneOrNullResult();
    }

    public function getSuccessOrdersWithFbParams(DateTime $dateStart, DateTime $dateEnd): array
    {
        return $this->getEntityManager()
            ->createQueryBuilder()
            ->select('ufbp.fbc, ufbp.fbp, o.createdAt AS event_time')
            ->from(Order::class, 'o')
            ->innerJoin('o.user', 'u', Join::WITH)
            ->innerJoin('u.userFbParams', 'ufbp', Join::WITH)
            ->where('o.status = \'success\'')
            ->andWhere('o.createdAt >= :date_start')
            ->andWhere('o.createdAt <= :date_end')
            ->setParameters([
                'date_start' => $dateStart->format('Y-m-d H:i:s'),
                'date_end' => $dateEnd->format('Y-m-d H:i:s'),
            ])
            ->getQuery()
            ->getScalarResult();
    }

    public function getLastSuccessfulOrderWithRecurringToken(User $user): ?Order
    {
        $qb = $this->createQueryBuilder('o');

        return $qb
            ->andWhere('o.user = :user')
            ->andWhere('o.status in (:status)')
            ->andWhere($qb->expr()->isNotNull('o.recurringToken'))
            ->addOrderBy('o.createdAt', 'DESC')
            ->setParameters([
                'user' => $user,
                'status' => $this->getAllSuccessfulStatuses(),
            ])
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();
    }

    public function getUserLastSuccessfulOrder(User $user): ?Order
    {
        $qb = $this->createQueryBuilder('o');

        return $qb
            ->andWhere('o.user = :user')
            ->andWhere('o.status in (:status)')
            ->addOrderBy('o.createdAt', 'DESC')
            ->setParameters([
                'user' => $user,
                'status' => $this->getAllSuccessfulStatuses(),
            ])
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * @return Order[]
     */
    public function getUserSuccessfulOrders(User $user): array
    {
        $qb = $this->createQueryBuilder('o');

        return $qb
            ->andWhere('o.user = :user')
            ->andWhere('o.status in (:status)')
            ->addOrderBy('o.createdAt', 'DESC')
            ->setParameters([
                'user' => $user,
                'status' => $this->getAllSuccessfulStatuses(),
            ])
            ->getQuery()
            ->getResult();
    }

    /**
     * @return Order[]
     */
    public function getUserRefundedOrders(User $user): array
    {
        $qb = $this->createQueryBuilder('o');

        return $qb
            ->andWhere('o.user = :user')
            ->andWhere('o.status = :status')
            ->addOrderBy('o.createdAt', 'DESC')
            ->setParameters([
                'user' => $user,
                'status' => OrderStatus::REFUNDED->value,
            ])
            ->getQuery()
            ->getResult();
    }

    public function getLastCreatedOrderForUser(User $user, Product $product, string $paymentType = null): ?Order
    {
        $params = [
            'user' => $user,
            'status' => Order::STATUS_NEW,
            'product' => $product,
            'amount' => $product->getAmount(),
        ];

        if (isset($paymentType)) {
            $params['paymentType'] = $paymentType;
        }

        return $this->findOneBy($params, ['createdAt' => 'DESC']);
    }

    public function deleteDanglingOrders(DateTime $tillDate): void
    {
        $qb = $this->connection->createQueryBuilder();
        $qb->delete('`order`')
            ->andWhere('status = :status')
            ->andWhere('created_at <= :tillDate')
            ->setParameter('tillDate', $tillDate, Types::DATE_MUTABLE)
            ->setParameter('status', OrderStatus::NEW->value, Types::STRING);

        $qb->executeStatement();
    }

    /**
     * @return Order[]
     */
    public function getByPayPalEmail(string $email): array
    {
        $qb = $this->createQueryBuilder('o');

        return $qb
            ->andWhere("JSON_CONTAINS(lower(o.paypalPayerDetails), :email, '$.payer_email') = 1")
            ->setParameter('email', '"' . strtolower($email) . '"')
            ->getQuery()
            ->getResult();
    }

    /**
     * @return Order[]
     */
    public function getByUserEmail(string $email): array
    {
        $qb = $this->createQueryBuilder('o');
        $qb->join('o.user', 'u');

        return $qb
            ->andWhere('u.email = :email')
            ->setParameter('email', $email)
            ->getQuery()
            ->getResult();
    }

    /**
     * @return Order[]
     */
    public function getAuthorizedOrders(): array
    {
        $rsm = new ResultSetMapping();
        // Map the main entity (Order)
        $rsm->addEntityResult(Order::class, 'o');
        $rsm->addFieldResult('o', 'id', 'id');
        $rsm->addFieldResult('o', 'order_external_id', 'externalId');
        $rsm->addFieldResult('o', 'status', 'status');
        $rsm->addFieldResult('o', 'amount', 'amount');
        $rsm->addFieldResult('o', 'user_id', 'user');

        // Map the joined entity (User)
        $rsm->addJoinedEntityResult(User::class, 'u', 'o', 'user');
        $rsm->addFieldResult('u', 'user_id', 'id');
        $rsm->addFieldResult('u', 'user_external_id', 'externalId');
        $rsm->addFieldResult('u', 'email', 'email');

        $sql = 'SELECT
                        o.id, 
                        o.external_id as order_external_id, 
                        o.status, 
                        o.amount, 
                        o.user_id, 
                        u.id AS user_id, 
                        u.external_id as user_external_id, 
                        u.email 
                    FROM `order` o
                    INNER JOIN user u ON o.user_id = u.id
                    WHERE o.status = ? AND o.type = ?';
        $query = $this->getEntityManager()->createNativeQuery($sql, $rsm);
        $query->setParameter(1, OrderStatus::AUTH_OK->value);
        $query->setParameter(2, Order::TYPE);

        return $query->getResult();
    }

    public function getMIDAlertReport(): array
    {
        $sql = "SELECT l.mid_descriptor,
                       t.num_ord
                FROM (SELECT DISTINCT(mid_descriptor)
                      FROM `order`
                      WHERE mid_descriptor IN (
                                               'pawchamp',
                                               'paw-champ.com^Dover',
                                               'paw-champ.com^Limassol',
                                               'SA_paw-champ.com',
                                               'PAW-CHAMP.COM',
                                               'paw-champ.com_HK',
                                               'supportpawchamp',
                                               'paw-champ.com #Esquire',
                                               'paw-champ'
                          )) l
                         LEFT JOIN (SELECT CASE
                                               WHEN mid_descriptor IN ('pawchamp', 'vpawchamp') THEN 'pawchamp'
                                               ELSE mid_descriptor
                                               END   AS normalized_mid,
                                           COUNT(id) AS num_ord
                                    FROM `order`
                                    WHERE (
                                              (created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)
                                                  AND status in ('approved', 'auth_ok', 'settle_ok', 'refunded', 'settled')
                                                  AND mid_descriptor IN ('pawchamp', 'vpawchamp')
                                                  )
                                                  OR (created_at > DATE_SUB(NOW(), INTERVAL 6 HOUR)
                                                  AND status in ('approved', 'auth_ok', 'settle_ok', 'refunded', 'settled')
                                                  AND mid_descriptor IN ('supportpawchamp')
                                                  )
                                                  OR (created_at > DATE_SUB(NOW(), INTERVAL 3 HOUR)
                                                  AND status in ('approved', 'auth_ok', 'settle_ok', 'refunded', 'settled')
                                                  AND mid_descriptor IN ('SA_paw-champ.com', 'paw-champ.com^Dover')
                                                  )
                                                  OR (created_at > DATE_SUB(NOW(), INTERVAL 2 HOUR)
                                                  AND status in ('approved', 'auth_ok', 'settle_ok', 'refunded', 'settled')
                                                  AND mid_descriptor = 'paw-champ.com^Limassol'
                                                  )
                                                  OR (created_at > DATE_SUB(NOW(), INTERVAL 4 HOUR)
                                                  AND status in ('approved', 'auth_ok', 'settle_ok', 'refunded', 'settled')
                                                  AND mid_descriptor = 'PAW-CHAMP.COM'
                                                  )
                                                  OR (created_at > DATE_SUB(NOW(), INTERVAL 12 HOUR)
                                                  AND status in ('approved', 'auth_ok', 'settle_ok', 'refunded', 'settled')
                                                  AND mid_descriptor IN ('paw-champ.com #Esquire', 'paw-champ.com_HK', 'paw-champ')
                                                  )
                                              )
                                    GROUP BY normalized_mid) t
                                   ON l.mid_descriptor = t.normalized_mid
                WHERE t.num_ord IS NULL";

        $stmt = $this->getEntityManager()->getConnection()->prepare($sql);

        return $stmt->executeQuery()->fetchAllAssociative();
    }

    public function getPaymentSystemAlertReport(): array
    {
        $sql = "SELECT *
                         FROM (SELECT 'first'                as order_type,
                                      'paw_champ'            as domain,
                                      l.payment_type,
                                      l.card_brand,
                                      COALESCE(t.counter, 0) AS counter
                               FROM (
                                        -- Отримання унікальних комбінацій payment_type та card_brand
                                        SELECT payment_type,
                                               IF(payment_type = 'paypal-vault', ' ', card_brand) AS card_brand
                                        FROM `order`
                                        WHERE card_brand IN ('VISA', 'MASTERCARD')
                                           OR payment_type = 'paypal-vault'
                                        GROUP BY payment_type, IF(payment_type = 'paypal-vault', ' ', card_brand)) AS l
                                        LEFT JOIN
                                    (
                                        -- Підрахунок транзакцій для кожної комбінації payment_type та card_brand
                                        SELECT COUNT(*)                                               AS counter,
                                               o.payment_type,
                                               IF(o.payment_type = 'paypal-vault', ' ', o.card_brand) AS card_brand
                                        FROM `order` o
                                                 -- Додавання дати підписки юзера, щоб виявити перший або рекурентний платіж
                                                 LEFT JOIN subscription s
                                                           ON o.user_id = s.user_id
                                        WHERE ((o.created_at BETWEEN DATE_SUB(NOW(), INTERVAL 90 MINUTE) AND NOW()
                                            AND s.started_at BETWEEN DATE_SUB(NOW(), INTERVAL 24 HOUR) AND NOW() -- перший платіж!
                                            AND o.payment_type = 'paypal-vault') -- paypal
                                            OR
                                               (o.created_at BETWEEN DATE_SUB(NOW(), INTERVAL 150 MINUTE) AND NOW()
                                                   AND s.started_at BETWEEN DATE_SUB(NOW(), INTERVAL 24 HOUR) AND NOW() -- перший платіж!
                                                   AND (o.payment_type = 'apple-pay' AND o.card_brand = 'VISA')) -- apple-pay VISA
                                            OR
                                               (o.created_at BETWEEN DATE_SUB(NOW(), INTERVAL 5 HOUR) AND NOW()
                                                   AND s.started_at BETWEEN DATE_SUB(NOW(), INTERVAL 24 HOUR) AND NOW() -- перший платіж!
                                                   AND (o.payment_type = 'card' AND o.card_brand = 'MASTERCARD')) -- card MASTERCARD
                                                   AND o.status IN ('approved', 'auth_ok', 'settle_ok', 'refunded', 'settled')
                                            OR
                                               (o.created_at BETWEEN DATE_SUB(NOW(), INTERVAL 4 HOUR) AND NOW()
                                                   AND s.started_at BETWEEN DATE_SUB(NOW(), INTERVAL 24 HOUR) AND NOW() -- перший платіж!
                                                   AND ((o.payment_type = 'card' AND o.card_brand = 'VISA') OR
                                                        (o.payment_type = 'apple-pay' AND o.card_brand = 'MASTERCARD')))) -- card - VISA, apple-pay - MASTERCARD
                                          AND o.status IN ('approved', 'auth_ok', 'settle_ok', 'refunded', 'settled')
                                        GROUP BY o.payment_type, o.card_brand) AS t
                                    ON
                                        t.payment_type = l.payment_type
                                            AND t.card_brand = l.card_brand
                               UNION
                               SELECT 'first',
                                      'paw_champ',
                                      'any payment type',
                                      ' '      as card_brand,
                                      COUNT(*) as counter
                               FROM `order` o
                                        LEFT JOIN subscription s
                                                  ON o.user_id = s.user_id
                               WHERE o.created_at BETWEEN DATE_SUB(NOW(), INTERVAL 30 MINUTE) AND NOW()
                                 AND s.started_at BETWEEN DATE_SUB(NOW(), INTERVAL 24 HOUR) AND NOW()
                                 AND o.status IN ('approved', 'auth_ok', 'settle_ok', 'refunded', 'settled')
                               union
                               SELECT 'recurring',
                                      'paw_champ',
                                      l.payment_type,
                                      l.card_brand,
                                      COALESCE(t.counter, 0) AS counter
                               FROM (
                                        -- Отримання унікальних комбінацій payment_type та card_brand
                                        SELECT payment_type,
                                               IF(payment_type = 'paypal-vault', ' ', card_brand) AS card_brand
                                        FROM `order`
                                        WHERE card_brand IN ('VISA', 'MASTERCARD')
                                           OR payment_type = 'paypal-vault'
                                        GROUP BY payment_type, IF(payment_type = 'paypal-vault', ' ', card_brand)) AS l
                                        LEFT JOIN
                                    (
                                        -- Підрахунок транзакцій для кожної комбінації payment_type та card_brand
                                        SELECT COUNT(*)                                               AS counter,
                                               o.payment_type,
                                               IF(o.payment_type = 'paypal-vault', ' ', o.card_brand) AS card_brand
                                        FROM `order` o
                                                 -- Додавання дати підписки юзера, щоб виявити перший або рекурентний платіж
                                                 LEFT JOIN subscription s
                                                           ON o.user_id = s.user_id
                                        WHERE ((o.created_at BETWEEN DATE_SUB(NOW(), INTERVAL 45 MINUTE) AND NOW()
                                            AND s.started_at NOT BETWEEN DATE_SUB(NOW(), INTERVAL 24 HOUR) AND NOW() -- рекурентний платіж!
                                            AND o.payment_type = 'paypal-vault') -- paypal
                                            OR
                                               (o.created_at BETWEEN DATE_SUB(NOW(), INTERVAL 90 MINUTE) AND NOW()
                                                   AND
                                                s.started_at NOT BETWEEN DATE_SUB(NOW(), INTERVAL 24 HOUR) AND NOW() -- рекурентний платіж!
                                                   AND (o.payment_type = 'apple-pay' AND o.card_brand = 'VISA')) -- apple-pay - VISA
                                            OR
                                               (o.created_at BETWEEN DATE_SUB(NOW(), INTERVAL 3 HOUR) AND NOW()
                                                   AND
                                                s.started_at NOT BETWEEN DATE_SUB(NOW(), INTERVAL 24 HOUR) AND NOW() -- рекурентний платіж!
                                                   AND (o.payment_type = 'card' AND o.card_brand = 'MASTERCARD')) -- card MASTERCARD
                                            OR
                                               (o.created_at BETWEEN DATE_SUB(NOW(), INTERVAL 2 HOUR) AND NOW()
                                                   AND
                                                s.started_at NOT BETWEEN DATE_SUB(NOW(), INTERVAL 24 HOUR) AND NOW() -- рекурентний платіж!
                                                   AND ((o.payment_type = 'card' AND o.card_brand = 'VISA') OR
                                                        (o.payment_type = 'apple-pay' AND o.card_brand = 'MASTERCARD')))) -- card - VISA, apple-pay - MASTERCARD
                                          AND o.status IN ('approved', 'auth_ok', 'settle_ok', 'refunded', 'settled')
                                        GROUP BY o.payment_type, o.card_brand) AS t
                                    ON
                                        t.payment_type = l.payment_type
                                            AND t.card_brand = l.card_brand) total
                         WHERE counter = 0";

        $stmt = $this->getEntityManager()->getConnection()->prepare($sql);

        return $stmt->executeQuery()->fetchAllAssociative();
    }

    private function getAllSuccessfulStatuses(): array
    {
        return [
            Order::STATUS_SUCCESS,
            OrderStatus::APPROVED->value,
            OrderStatus::REFUNDED->value,
            OrderStatus::AUTH_OK->value,
            OrderStatus::SETTLE_OK->value,
            OrderStatus::VOID_OK->value,
        ];
    }
}
