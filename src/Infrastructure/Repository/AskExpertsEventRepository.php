<?php

declare(strict_types=1);

namespace App\Infrastructure\Repository;

use App\Domain\Entity\AskExpertsEvent;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method AskExpertsEvent|null find($id, $lockMode = null, $lockVersion = null)
 * @method AskExpertsEvent|null findOneBy(array $criteria, array $orderBy = null)
 * @method AskExpertsEvent[]    findAll()
 * @method AskExpertsEvent[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class AskExpertsEventRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, AskExpertsEvent::class);
    }
}
