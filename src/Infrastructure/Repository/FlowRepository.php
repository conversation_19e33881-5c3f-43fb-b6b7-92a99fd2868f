<?php

declare(strict_types=1);

namespace App\Infrastructure\Repository;

use App\Domain\Entity\Flow;
use App\Infrastructure\ExternalService\Solid\DTO\FlowDTO;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\DBAL\Connection;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method Flow|null find($id, $lockMode = null, $lockVersion = null)
 * @method Flow|null findOneBy(array $criteria, array $orderBy = null)
 * @method Flow[]    findAll()
 * @method Flow[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class FlowRepository extends ServiceEntityRepository
{
    public function __construct(ManagerRegistry $registry, private readonly Connection $connection)
    {
        parent::__construct($registry, Flow::class);
    }

    /**
     * @param array|FlowDTO[] $data
     */
    public function batchInsertOrUpdate(array $data): void
    {
        $batch = [];
        foreach ($data as $flow) {
            $date = $flow->date->format('Y-m-d H:i:s');
            $settlementDate = $flow->settlementDate->format('Y-m-d H:i:s');
            $createdAt = $flow->createdAt->format('Y-m-d H:i:s');
            $updatedAt = $flow->updatedAt->format('Y-m-d H:i:s');
            $deadlineDate = $flow->deadlineDate ? "'{$flow->deadlineDate->format('Y-m-d H:i:s')}'" : 'null';

            $arnCode = is_null($flow->arnCode) ? 'null' : "'$flow->arnCode'";
            $feeCurrency = is_null($flow->financeFeeCurrency) ? 'null' : "'$flow->financeFeeCurrency'";
            $feeAmount = $flow->financeFeeAmount ?? 'null';
            $disputeAmount = $flow->disputeAmount ?? 'null';

            $batch[] = "('$flow->id', '$flow->chargebackId', $flow->amount, '$flow->type', '$flow->status',
                        '$date', '$settlementDate', '$createdAt', '$updatedAt', '$flow->currency', $arnCode, 
                        $deadlineDate, $disputeAmount, $feeAmount, $feeCurrency)";

            if (count($batch) === 500) {
                $this->processBatchInsertOrUpdate($batch);
                $batch = [];
            }
        }

        if (count($batch)) {
            $this->processBatchInsertOrUpdate($batch);
        }
    }

    private function processBatchInsertOrUpdate(array $batch): void
    {
        $sql = 'INSERT INTO flow (id, chargeback_id, amount, type, status, date, settlement_date, created_at, 
                                        updated_at, currency, arn_code, deadline_date, dispute_amount, 
                                        finance_fee_amount, finance_fee_currency)
                        VALUES ' . implode(',', $batch) . ' ON DUPLICATE KEY UPDATE 
                        status = VALUES(status), updated_at = VALUES(updated_at)';

        $statement = $this->connection->prepare($sql);
        $statement->executeStatement();
    }
}
