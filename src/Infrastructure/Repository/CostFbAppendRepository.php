<?php

declare(strict_types=1);

namespace App\Infrastructure\Repository;

use App\Domain\Entity\CostFbAppend;
use Doctrine\Persistence\ManagerRegistry;

/**
 * @method CostFbAppend|null find($id, $lockMode = null, $lockVersion = null)
 * @method CostFbAppend|null findOneBy(array $criteria, array $orderBy = null)
 * @method CostFbAppend[]    findAll()
 * @method CostFbAppend[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class CostFbAppendRepository extends AbstractCostRepository
{
    public function __construct(ManagerRegistry $registry)
    {
        parent::__construct($registry, CostFbAppend::class);
    }
}
