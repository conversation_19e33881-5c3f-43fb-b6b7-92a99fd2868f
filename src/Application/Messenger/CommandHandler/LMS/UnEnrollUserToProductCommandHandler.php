<?php

declare(strict_types=1);

namespace App\Application\Messenger\CommandHandler\LMS;

use App\Application\Messenger\Command\LMS\UnEnrollUserToProductCommand;
use App\Application\Messenger\CommandHandler\CommandHandlerInterface;
use App\Domain\Enum\UserLMSStatus;
use App\Infrastructure\Exception\API\EntityNotFoundException;
use App\Infrastructure\ExternalService\LMS\Exception\LMSException;
use App\Infrastructure\ExternalService\LMS\LearnWorldsApiV2Adapter;
use App\Infrastructure\Repository\UserRepository;
use Psr\Log\LoggerInterface;

class UnEnrollUserToProductCommandHandler implements CommandHandlerInterface
{
    public function __construct(
        private readonly LearnWorldsApiV2Adapter $learnWorldsApiV2Adapter,
        private readonly LoggerInterface $logger,
        private readonly UserRepository $userRepository,
    ) {
    }

    public function __invoke(UnEnrollUserToProductCommand $command): void
    {
        $user = $this->userRepository->find($command->getUserId())
            ?? throw new EntityNotFoundException(
                'UnEnrollUserToProductCommand failed. User was not found',
                context: ['userId' => $command->getUserId()]
            );

        try {
            $this->learnWorldsApiV2Adapter->unenrollUserFromProduct(
                $user,
                $command->getProductId(),
                $command->getProductType(),
            );
        } catch (LMSException $exception) {
            $this->logger->error($exception->getMessage(), $exception->getContext());

            throw $exception;
        }

        $user->getLMS()?->setStatus(UserLMSStatus::UNENROLLED);
    }
}
