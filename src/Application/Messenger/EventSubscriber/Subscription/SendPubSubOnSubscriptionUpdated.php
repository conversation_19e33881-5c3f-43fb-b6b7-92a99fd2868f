<?php

declare(strict_types=1);

namespace App\Application\Messenger\EventSubscriber\Subscription;

use App\Application\Messenger\Event\Subscription\SubscriptionUpdatedEvent;
use App\Application\Messenger\EventSubscriber\EventSubscriberInterface;
use App\Application\Messenger\PubSub\Subscription\Send\SubscriptionCreatedEventSendUser;
use App\Application\Messenger\Transport\PubSub\PubSubEventTypeEnum;
use App\Application\Messenger\Transport\PubSub\PubSubEventTypeStamp;
use App\Domain\Entity\Subscription;
use App\Domain\Entity\User;
use App\Infrastructure\Repository\SubscriptionRepository;
use Psr\Log\LoggerInterface;
use Symfony\Component\Messenger\Envelope;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Messenger\Stamp\DispatchAfterCurrentBusStamp;

class SendPubSubOnSubscriptionUpdated implements EventSubscriberInterface
{
    public function __construct(
        private readonly SubscriptionRepository $subscriptionRepository,
        private readonly MessageBusInterface $pubsubSendBus,
        private readonly LoggerInterface $logger,
    ) {
    }

    public function __invoke(SubscriptionUpdatedEvent $createdEvent): void
    {
        $subscription = $this->subscriptionRepository->find($createdEvent->getSubscriptionId());
        if (is_null($subscription)) {
            $this->logger->error(
                'SendPubSubOnSubscriptionUpdated has failed.',
                ['message' => 'Subscription not found', 'subscriptionId' => $createdEvent->getSubscriptionId()]
            );

            return;
        }

        $user = $subscription->getUser();

        $pubSubEvent = new SubscriptionCreatedEventSendUser($this->createPubSubSubscriptionDTO($user, $subscription));
        $this->pubsubSendBus->dispatch(new Envelope($pubSubEvent), [
            new DispatchAfterCurrentBusStamp(),
            new PubSubEventTypeStamp(PubSubEventTypeEnum::SUBSCRIPTION_UPDATED),
        ]);
    }

    private function createPubSubSubscriptionDTO(
        User $user,
        Subscription $subscription
    ): \App\Application\Messenger\PubSub\Subscription\Subscription {
        return new \App\Application\Messenger\PubSub\Subscription\Subscription(
            $subscription->getExternalId(),
            $user->getExternalId(),
            $subscription->getStatus(),
            $subscription->getStartedAt(),
            $subscription->getExpiredAt(),
            $user->getLMS()?->getUnenrollAt(),
        );
    }
}
