<?php

declare(strict_types=1);

namespace App\Application\Content;

use App\Domain\Enum\UserLocaleEnum;
use App\Domain\Enum\FunnelNameEnum;

class QuizContent
{
    private static function getSplitContent(string $quiz, array $splitValues = []): array
    {
        $map = [
            [
                'rule' => new ContentRule(['quizMain']),
                'questions' => [
                    'compliance-age-4th',
                    'compliance-gender',
                    'compliance-breed',
                    'compliance-adult-spayed',
                    'compliance-improve',
                    'compliance-adult-refuse',
                    'compliance-ignored',
                    'compliance-solve-first',
                    'compliance-adult-treats',
                    'compliance-adult-cues',
                    'compliance-adult-range-obedience',
                    'compliance-adult-skills',
                    'compliance-adult-describe-icons',
                    'compliance-adult-range-socialization',
                    'compliance-adult-stress',
                    'compliance-adult-range-friendly',
                    'compliance-adult-perceive',
                    'compliance-adult-relationship',
                    'compliance-adult-range-strengthening',
                    'compliance-adult-ask',
                    'compliance-spend',
                    'compliance-name',
                    'compliance-profile',
                    'compliance-coming',
                    'compliance-when',
                    'compliance-graph',
                ],
            ],
            [
                'rule' => new ContentRule(['quizMainPuppy']),
                'questions' => [
                    'compliance-puppy-gender',
                    'compliance-puppy-breed',
                    'compliance-puppy-spayed',
                    'compliance-improve',
                    'compliance-puppy-refuse',
                    'compliance-puppy-problems',
                    'compliance-puppy-potty',
                    'compliance-puppy-crate',
                    'compliance-solve-first',
                    'compliance-puppy-range-obedience',
                    'compliance-puppy-cues',
                    'compliance-puppy-skills',
                    'compliance-puppy-describe',
                    'compliance-puppy-range-socialization',
                    'compliance-puppy-stress',
                    'compliance-puppy-perceive',
                    'compliance-puppy-amazing',
                    'compliance-puppy-comfortable',
                    'compliance-puppy-petted',
                    'compliance-puppy-play',
                    'compliance-adult-range-strengthening',
                    'compliance-spend',
                    'compliance-puppy-name',
                    'compliance-profile',
                    'compliance-coming',
                    'compliance-when',
                    'compliance-graph',
                ],
            ],
            [
                'rule' => new ContentRule(['quiz1']),
                'questions' => [
                    'age-master',
                    'gender',
                    'react-satisfied',
                    'breed',
                    'adult-spayed',
                    'master-unique',
                    'master-check-up',
                    'health-tendencies',
                    'health-appetite',
                    'master-digestion',
                    'health-body',
                    'health-active',
                    'health-teeth',
                    'health-breath',
                    'health-activities',
                    'master-hygiene',
                    'master-raising',
                    'master-meet',
                    'master-other-pets',
                    'master-experience',
                    'master-emotions',
                    'master-describe-experience',
                    'master-topics',
                    'master-voice',
                    'master-goals',
                    'master-range-disobey',
                    'master-solve-first',
                    'master-triggers',
                    'master-listen',
                    'master-instant-reaction',
                    'master-marker-words',
                    'master-correction',
                    'master-reinforcement',
                    'master-nearly-finished',
                    'adult-cues',
                    'master-release',
                    'master-perform-distance',
                    'discipline',
                    'boundaries',
                    'master-come-home',
                    'master-motivates',
                    'master-specific-toys',
                    'master-parenting',
                    'comfortable',
                    'petted',
                    'play',
                    'master-look',
                    'rude',
                    'master-alone',
                    'master-left-alone',
                    'master-distress',
                    'master-raise',
                    'adult-ask',
                    'spend-main',
                    'master-optimal',
                    'name',
                    'master-profile',
                    'coming',
                    'better-when',
                    'master-graph',
                ],
            ],
            [
                'rule' => new ContentRule(['quiz1-puppy']),
                'questions' => [
                    'gender',
                    'react-satisfied',
                    'breed',
                    'puppy-spayed',
                    'master-unique',
                    'master-check-up',
                    'health-tendencies',
                    'health-appetite',
                    'master-digestion',
                    'health-body',
                    'health-active',
                    'health-teeth',
                    'health-breath',
                    'health-activities',
                    'master-hygiene',
                    'master-raising',
                    'master-meet',
                    'master-other-pets',
                    'master-experience',
                    'master-emotions',
                    'master-describe-experience',
                    'master-topics',
                    'master-voice',
                    'master-goals',
                    'master-range-disobey',
                    'master-solve-first',
                    'master-triggers',
                    'master-listen',
                    'master-instant-reaction',
                    'master-marker-words',
                    'master-correction',
                    'master-reinforcement',
                    'master-nearly-finished',
                    'puppy-cues',
                    'master-release',
                    'master-perform-distance',
                    'discipline',
                    'boundaries',
                    'master-come-home',
                    'master-motivates',
                    'master-specific-toys',
                    'master-parenting',
                    'comfortable',
                    'petted',
                    'play',
                    'master-look',
                    'rude',
                    'master-alone',
                    'master-left-alone',
                    'master-distress',
                    'master-raise',
                    'adult-ask',
                    'spend-main',
                    'master-optimal',
                    'name',
                    'master-profile',
                    'coming',
                    'better-when',
                    'master-graph',
                ],
            ],
            [
                'rule' => new ContentRule([
                    'quiz2',
                    'quiz35',
                    'quiz36',
                    'quiz37',
                    'quiz38',
                    'quiz39',
                ]),
                'questions' => [
                    'localized/gender-shelf',
                    'localized/age-list-split',
                    'localized/snuggle-with-dog',
                    'localized/photos-of-dog',
                    'localized/spoil-dog-with-treats',
                    'localized/dog-sleep-wherever-wants',
                    'localized/jump-into-fire-for-dog',
                    'localized/love-level-progress',
                    'localized/feel-guilty-for-not-spending-time',
                    'localized/feel-bad-when-dog-looks-upset',
                    'localized/satisfied-with-behavior',
                    'localized/misbehavior-reaction',
                    'localized/solve-first',
                    'localized/love-level-disobedience-barrier',
                    'localized/change-dogs-life-for-better',
                    'localized/how-important-training',
                    'localized/training-is-reflection-of-love',
                    'localized/how-long-you-walk-with-dog',
                    'localized/training-during-walk',
                    'localized/structured-plan',
                    'localized/breed-2',
                    'localized/practicing-dog-behaviorist',
                    'localized/how-you-call-your-dog',
                    'localized/name',
                    'localized/love-profile',
                    'localized/ask-expert',
                    'localized/spend',
                    'localized/love-graph',
                ],
            ],
            [
                'rule' => new ContentRule([
                    'quiz2-puppy',
                    'quiz35-puppy',
                    'quiz36-puppy',
                    'quiz37-puppy',
                    'quiz38-puppy',
                    'quiz39-puppy',
                ]),
                'questions' => [
                    'localized/snuggle-with-dog',
                    'localized/photos-of-dog',
                    'localized/spoil-dog-with-treats',
                    'localized/dog-sleep-wherever-wants',
                    'localized/jump-into-fire-for-dog',
                    'localized/love-level-progress',
                    'localized/feel-guilty-for-not-spending-time',
                    'localized/feel-bad-when-dog-looks-upset',
                    'localized/satisfied-with-behavior',
                    'localized/misbehavior-reaction',
                    'localized/solve-first',
                    'localized/love-level-disobedience-barrier',
                    'localized/change-dogs-life-for-better',
                    'localized/how-important-training',
                    'localized/training-is-reflection-of-love',
                    'localized/how-long-you-walk-with-dog',
                    'localized/training-during-walk',
                    'localized/structured-plan',
                    'localized/breed-2',
                    'localized/practicing-dog-behaviorist',
                    'localized/how-you-call-your-dog',
                    'localized/name',
                    'localized/love-profile',
                    'localized/ask-expert',
                    'localized/spend',
                    'localized/love-graph',
                ],
            ],
            [
                'rule' => new ContentRule(['quiz2'], [new ContentSplitRule('pc_love1screens', 2)]),
                'questions' => [
                    'love-training-quiz',
                    'love-gender-vertical',
                    'love-age',
                    'love-snuggle',
                    'love-photos',
                    'love-spoil',
                    'love-sleep',
                    'love-jump',
                    'love-level',
                    'love-guilty',
                    'love-upset',
                    'love-satisfied',
                    'love-misbehaves',
                    'love-tendencies',
                    'love-disobedience',
                    'love-change-better',
                    'love-dog-training-important',
                    'love-reflection',
                    'love-walk',
                    'love-during-walk',
                    'love-structured-plan',
                    'love-breed',
                    'love-practicing',
                    'love-how-do-you-call',
                    'love-name',
                    'love-profile',
                    'love-ask',
                    'love-spend',
                    'love-graph',
                ],
            ],
            [
                'rule' => new ContentRule(['quiz2'], [new ContentSplitRule('pc_lovevalue', 2)]),
                'questions' => [
                    'love-gender',
                    'love-age',
                    'love-breed',
                    'love-snuggle',
                    'love-photos',
                    'love-spoil',
                    'love-sleep',
                    'love-jump',
                    'love-level',
                    'love-guilty',
                    'love-upset',
                    'love-satisfied',
                    'love-misbehaves',
                    'love-tendencies',
                    'love-disobedience',
                    'love-change-better',
                    'love-dog-training-important',
                    'love-reflection',
                    'love-walk',
                    'love-during-walk',
                    'love-structured-plan',
                    'love-practicing',
                    'love-how-do-you-call',
                    'love-name',
                    'love-profile',
                    'love-ask',
                    'love-spend',
                    'love-graph',
                ],
            ],
            [
                'rule' => new ContentRule(['quiz2'], [new ContentSplitRule('pc_lovevalue', 3)]),
                'questions' => [
                    'love-gender',
                    'love-age',
                    'love-belly',
                    'love-spoil',
                    'love-photos',
                    'love-jump',
                    'love-level',
                    'love-missing',
                    'love-guilty',
                    'love-upset',
                    'love-pattern',
                    'love-satisfied',
                    'love-misbehaves',
                    'love-tendencies',
                    'love-disobedience',
                    'love-change-better',
                    'love-dog-training-important',
                    'love-reflection',
                    'love-walk',
                    'love-during-walk',
                    'love-structured-plan',
                    'love-breed',
                    'love-practicing',
                    'love-how-do-you-call',
                    'love-name',
                    'love-profile',
                    'love-ask',
                    'love-spend',
                    'love-graph',
                ],
            ],
            [
                'rule' => new ContentRule(['quiz2-puppy'], [new ContentSplitRule('pc_lovevalue', 2)]),
                'questions' => [
                    'love-breed',
                    'love-snuggle',
                    'love-photos',
                    'love-spoil',
                    'love-sleep',
                    'love-jump',
                    'love-level',
                    'love-guilty',
                    'love-upset',
                    'love-satisfied',
                    'love-misbehaves',
                    'love-tendencies',
                    'love-disobedience',
                    'love-change-better',
                    'love-dog-training-important',
                    'love-reflection',
                    'love-walk',
                    'love-during-walk',
                    'love-structured-plan',
                    'love-practicing',
                    'love-how-do-you-call',
                    'love-name',
                    'love-profile',
                    'love-ask',
                    'love-spend',
                    'love-graph',
                ],
            ],
            [
                'rule' => new ContentRule(['quiz2-puppy'], [new ContentSplitRule('pc_lovevalue', 3)]),
                'questions' => [
                    'love-belly',
                    'love-spoil',
                    'love-photos',
                    'love-jump',
                    'love-level',
                    'love-missing',
                    'love-guilty',
                    'love-upset',
                    'love-pattern',
                    'love-satisfied',
                    'love-misbehaves',
                    'love-tendencies',
                    'love-disobedience',
                    'love-change-better',
                    'love-dog-training-important',
                    'love-reflection',
                    'love-walk',
                    'love-during-walk',
                    'love-structured-plan',
                    'love-breed',
                    'love-practicing',
                    'love-how-do-you-call',
                    'love-name',
                    'love-profile',
                    'love-ask',
                    'love-spend',
                    'love-graph',
                ],
            ],
            [
                'rule' => new ContentRule(['quiz3']),
                'questions' => [
                    'fr-gender-split',
                    'fr-dogs-owners',
                    'fr-goal',
                    'fr-age-list-split',
                    'fr-react-satisfied',
                    'fr-breed',
                    'fr-adult-spayed',
                    'fr-reinforcement-understanding',
                    'fr-reinforcement-range',
                    'fr-reinforcement-info',
                    'fr-adult-refuse',
                    'fr-ignored',
                    'fr-solve-first',
                    'fr-adult-treats',
                    'fr-adult-cues',
                    'fr-adult-range-obedience',
                    'fr-adult-skills',
                    'fr-stress',
                    'fr-adult-range-friendly',
                    'fr-adult-perceive',
                    'fr-developed',
                    'fr-adult-relationship',
                    'fr-reliable',
                    'fr-did-you-hear',
                    'fr-practicing-obedience',
                    'fr-adult-ask',
                    'fr-spend',
                    'fr-name',
                    'fr-profile',
                    'fr-coming',
                    'fr-when',
                    'fr-graph',
                ],
            ],
            [
                'rule' => new ContentRule(['quiz3-puppy-fr']),
                'questions' => [
                    'fr-react-satisfied',
                    'fr-breed',
                    'fr-puppy-spayed',
                    'fr-reinforcement-understanding',
                    'fr-reinforcement-range',
                    'fr-reinforcement-info',
                    'fr-puppy-refuse',
                    'fr-puppy-problems',
                    'fr-puppy-potty',
                    'fr-puppy-crate',
                    'fr-solve-first',
                    'fr-puppy-range-obedience',
                    'fr-puppy-cues',
                    'fr-puppy-skills',
                    'fr-stress',
                    'fr-puppy-perceive',
                    'fr-puppy-amazing',
                    'fr-puppy-comfortable',
                    'fr-puppy-petted',
                    'fr-puppy-play',
                    'fr-developed',
                    'fr-reliable',
                    'fr-did-you-hear',
                    'fr-practicing-obedience',
                    'fr-adult-ask',
                    'fr-spend',
                    'fr-name',
                    'fr-profile',
                    'fr-coming',
                    'fr-when',
                    'fr-graph',
                ],
            ],
            [
                'rule' => new ContentRule(['quiz6']),
                'questions' => [
                    'gender-split',
                    'dogs-owners',
                    'goal',
                    'age-list-split',
                    'breed',
                    'adult-spayed',
                    'ignored',
                    'solve-first',
                    'adult-treats',
                    'adult-cues',
                    'adult-range-obedience',
                    'adult-skills',
                    'adult-refuse',
                    'misbehave',
                    'rude',
                    'listen',
                    'frustrated',
                    'range-places-adult',
                    'tough',
                    'stress',
                    'adult-range-friendly',
                    'adult-perceive',
                    'developed',
                    'adult-relationship',
                    'reliable',
                    'did-you-hear',
                    'practicing-obedience',
                    'adult-ask',
                    'spend',
                    'name',
                    'better-profile',
                    'coming',
                    'better-when',
                    'graph',
                ],
            ],
            [
                'rule' => new ContentRule(['quiz6'], [new ContentSplitRule('pc_pun_sflow')]),
                'questions' => [
                    'age-4th',
                    'gender',
                    'react-satisfied',
                    'breed',
                    'adult-spayed',
                    'reinforcement-understanding',
                    'reinforcement-range',
                    'reinforcement-info',
                    'goal',
                    'ignored',
                    'solve-first',
                    'adult-treats',
                    'adult-cues',
                    'adult-range-obedience',
                    'adult-skills',
                    'adult-refuse',
                    'misbehave',
                    'rude',
                    'listen',
                    'frustrated',
                    'range-places-adult',
                    'tough',
                    'stress',
                    'adult-range-friendly',
                    'adult-perceive',
                    'adult-relationship',
                    'adult-ask',
                    'spend',
                    'name',
                    'better-profile',
                    'coming',
                    'better-when',
                    'graph',
                ],
            ],
            [
                'rule' => new ContentRule(['quiz6-puppy-pain']),
                'questions' => [
                    'breed',
                    'puppy-spayed',
                    'puppy-problems',
                    'puppy-potty',
                    'crate',
                    'solve-first',
                    'puppy-range-obedience',
                    'puppy-cues',
                    'puppy-skills',
                    'puppy-refuse',
                    'misbehave',
                    'rude',
                    'listen',
                    'frustrated',
                    'range-places-puppy',
                    'tough',
                    'stress',
                    'puppy-perceive',
                    'puppy-amazing',
                    'puppy-comfortable',
                    'puppy-petted',
                    'puppy-play',
                    'developed',
                    'reliable',
                    'did-you-hear',
                    'practicing-obedience',
                    'adult-ask',
                    'spend',
                    'puppy-name',
                    'better-profile',
                    'coming',
                    'better-when',
                    'graph',
                ],
            ],
            [
                'rule' => new ContentRule(['quiz7']),
                'questions' => [
                    'de-gender-split',
                    'de-dogs-owners',
                    'de-goal',
                    'de-age-list-split',
                    'de-react-satisfied',
                    'de-breed',
                    'de-adult-spayed',
                    'de-reinforcement-understanding',
                    'de-reinforcement-range',
                    'de-reinforcement-info',
                    'de-adult-refuse',
                    'de-ignored',
                    'de-solve-first',
                    'de-adult-treats',
                    'de-adult-cues',
                    'de-adult-range-obedience',
                    'de-adult-skills',
                    'de-stress',
                    'de-adult-range-friendly',
                    'de-adult-perceive',
                    'de-developed',
                    'de-adult-relationship',
                    'de-reliable',
                    'de-did-you-hear',
                    'de-practicing-obedience',
                    'de-adult-ask',
                    'de-spend',
                    'de-name',
                    'de-better-profile',
                    'de-coming',
                    'de-better-when',
                    'de-graph',
                ],
            ],
            [
                'rule' => new ContentRule(['quiz7-puppy-de']),
                'questions' => [
                    'de-react-satisfied',
                    'de-puppy-breed',
                    'de-puppy-spayed',
                    'de-reinforcement-understanding',
                    'de-reinforcement-range',
                    'de-reinforcement-info',
                    'de-puppy-refuse',
                    'de-puppy-problems',
                    'de-puppy-potty',
                    'de-puppy-crate',
                    'de-solve-first',
                    'de-puppy-range-obedience',
                    'de-puppy-cues',
                    'de-puppy-skills',
                    'de-stress',
                    'de-puppy-perceive',
                    'de-puppy-amazing',
                    'de-puppy-comfortable',
                    'de-puppy-petted',
                    'de-puppy-play',
                    'de-developed',
                    'de-reliable',
                    'de-did-you-hear',
                    'de-practicing-obedience',
                    'de-adult-ask',
                    'de-spend',
                    'de-puppy-name',
                    'de-better-profile',
                    'de-coming',
                    'de-better-when',
                    'de-graph',
                ],
            ],
            [
                'rule' => new ContentRule(['quiz8']),
                'questions' => [
                    'pt-gender-split',
                    'pt-dogs-owners',
                    'pt-goal',
                    'pt-age-list-split',
                    'pt-react-satisfied',
                    'pt-breed',
                    'pt-adult-spayed',
                    'pt-reinforcement-understanding',
                    'pt-reinforcement-range',
                    'pt-reinforcement-info',
                    'pt-refuse',
                    'pt-ignored',
                    'pt-solve-first',
                    'pt-adult-treats',
                    'pt-cues',
                    'pt-range-obedience',
                    'pt-skills',
                    'pt-adult-stress',
                    'pt-range-friendly',
                    'pt-perceive',
                    'pt-developed',
                    'pt-adult-relationship',
                    'pt-reliable',
                    'pt-did-you-hear',
                    'pt-practicing-obedience',
                    'pt-ask',
                    'pt-spend',
                    'pt-name',
                    'pt-profile',
                    'pt-coming',
                    'pt-when',
                    'pt-graph',
                ],
            ],
            [
                'rule' => new ContentRule(['quiz8-puppy-pt']),
                'questions' => [
                    'pt-react-satisfied',
                    'pt-breed',
                    'pt-puppy-spayed',
                    'pt-reinforcement-understanding',
                    'pt-reinforcement-range',
                    'pt-reinforcement-info',
                    'pt-refuse',
                    'pt-puppy-problems',
                    'pt-puppy-potty',
                    'pt-puppy-crate',
                    'pt-solve-first',
                    'pt-range-obedience',
                    'pt-cues',
                    'pt-skills',
                    'pt-puppy-stress',
                    'pt-perceive',
                    'pt-amazing',
                    'pt-puppy-comfortable',
                    'pt-puppy-petted',
                    'pt-puppy-play',
                    'pt-developed',
                    'pt-reliable',
                    'pt-did-you-hear',
                    'pt-practicing-obedience',
                    'pt-ask',
                    'pt-spend',
                    'pt-name',
                    'pt-profile',
                    'pt-coming',
                    'pt-when',
                    'pt-graph',
                ],
            ],
            [
                'rule' => new ContentRule(['quiz9']),
                'questions' => [
                    'gender-split',
                    'dogs-owners',
                    'goal',
                    'age-list-split',
                    'react-satisfied',
                    'breed',
                    'adult-spayed',
                    'reinforcement-understanding',
                    'reinforcement-range',
                    'reinforcement-info',
                    'adult-refuse',
                    'ignored',
                    'solve-first',
                    'adult-treats',
                    'adult-cues',
                    'adult-range-obedience',
                    'adult-skills',
                    'stress',
                    'adult-range-friendly',
                    'adult-perceive',
                    'developed',
                    'adult-relationship',
                    'reliable',
                    'did-you-hear',
                    'practicing-obedience',
                    'adult-ask',
                    'spend-main',
                    'name',
                    'better-profile',
                    'coming',
                    'better-when',
                    'graph',
                ],
            ],
            [
                'rule' => new ContentRule(
                    ['quiz9'],
                    [new ContentSplitRule('pc_qualityTime', 2)]
                ),
                'questions' => [
                    'gender-split',
                    'dogs-owners',
                    'goal',
                    'age-list-split',
                    'react-satisfied',
                    'breed',
                    'adult-spayed',
                    'reinforcement-understanding',
                    'reinforcement-range',
                    'reinforcement-info',
                    'adult-refuse',
                    'ignored',
                    'solve-first',
                    'adult-treats',
                    'adult-cues',
                    'adult-range-obedience',
                    'adult-skills',
                    'stress',
                    'adult-range-friendly',
                    'adult-perceive',
                    'developed',
                    'adult-relationship',
                    'reliable',
                    'did-you-hear',
                    'practicing-obedience',
                    'adult-ask',
                    'name',
                    'better-profile',
                    'spend-list',
                    'graphic-training',
                    'best-time-to-train',
                    'dedicated',
                ],
            ],
            [
                'rule' => new ContentRule(
                    ['quiz9-puppy'],
                    [new ContentSplitRule('pc_qualityTime', 2)]
                ),
                'questions' => [
                    'react-satisfied',
                    'breed',
                    'puppy-spayed',
                    'reinforcement-understanding',
                    'reinforcement-range',
                    'reinforcement-info',
                    'puppy-refuse',
                    'puppy-problems',
                    'puppy-potty',
                    'crate',
                    'solve-first',
                    'puppy-range-obedience',
                    'puppy-cues',
                    'puppy-skills',
                    'stress',
                    'puppy-perceive',
                    'puppy-amazing',
                    'puppy-comfortable',
                    'puppy-petted',
                    'puppy-play',
                    'developed',
                    'reliable',
                    'did-you-hear',
                    'practicing-obedience',
                    'adult-ask',
                    'puppy-name',
                    'better-profile',
                    'spend-list',
                    'graphic-training',
                    'best-time-to-train',
                    'dedicated',
                ],
            ],
            [
                'rule' => new ContentRule(['quiz9-puppy']),
                'questions' => [
                    'react-satisfied',
                    'breed',
                    'puppy-spayed',
                    'reinforcement-understanding',
                    'reinforcement-range',
                    'reinforcement-info',
                    'puppy-refuse',
                    'puppy-problems',
                    'puppy-potty',
                    'crate',
                    'solve-first',
                    'puppy-range-obedience',
                    'puppy-cues',
                    'puppy-skills',
                    'stress',
                    'puppy-perceive',
                    'puppy-amazing',
                    'puppy-comfortable',
                    'puppy-petted',
                    'puppy-play',
                    'developed',
                    'reliable',
                    'did-you-hear',
                    'practicing-obedience',
                    'adult-ask',
                    'spend-main',
                    'puppy-name',
                    'better-profile',
                    'coming',
                    'better-when',
                    'graph',
                ],
            ],
            [
                'rule' => new ContentRule(['quiz10']),
                'questions' => [
                    'es-gender-split',
                    'es-dogs-owners',
                    'es-goal',
                    'es-age-list-split',
                    'es-react-satisfied',
                    'es-breed',
                    'es-adult-spayed',
                    'es-reinforcement-understanding',
                    'es-reinforcement-range',
                    'es-reinforcement-info',
                    'es-adult-refuse',
                    'es-ignored',
                    'es-solve-first',
                    'es-adult-treats',
                    'es-adult-cues',
                    'es-adult-range-obedience',
                    'es-adult-skills',
                    'es-stress',
                    'es-adult-range-friendly',
                    'es-adult-perceive',
                    'es-developed',
                    'es-adult-relationship',
                    'es-reliable',
                    'es-did-you-hear',
                    'es-practicing-obedience',
                    'es-adult-ask',
                    'es-spend',
                    'es-name',
                    'es-profile',
                    'es-coming',
                    'es-when',
                    'es-graph',
                ],
            ],
            [
                'rule' => new ContentRule(['quiz10-puppy-es']),
                'questions' => [
                    'es-react-satisfied',
                    'es-breed',
                    'es-puppy-spayed',
                    'es-reinforcement-understanding',
                    'es-reinforcement-range',
                    'es-reinforcement-info',
                    'es-puppy-refuse',
                    'es-puppy-problems',
                    'es-puppy-potty',
                    'es-puppy-crate',
                    'es-solve-first',
                    'es-puppy-range-obedience',
                    'es-puppy-cues',
                    'es-puppy-skills',
                    'es-stress',
                    'es-puppy-perceive',
                    'es-puppy-amazing',
                    'es-puppy-comfortable',
                    'es-puppy-petted',
                    'es-puppy-play',
                    'es-developed',
                    'es-reliable',
                    'es-did-you-hear',
                    'es-practicing-obedience',
                    'es-adult-ask',
                    'es-spend',
                    'es-name',
                    'es-profile',
                    'es-coming',
                    'es-when',
                    'es-graph',
                ],
            ],
            [
                'rule' => new ContentRule(['quiz11']),
                'questions' => [
                    'age-list-anxiety',
                    'gender-love',
                    'breed-anxiety',
                    'dogs-owners',
                    'anxiety-motivation-to-start',
                    'anxiety-goal',
                    'anxiety-personalized-goal',
                    'hypervigilant',
                    'restless-and-unable-to-switch-off',
                    'upon-seeing-other-dogs',
                    'ignores-commands-completely',
                    'anxiety-signs',
                    'you-are-not-alone',
                    'ignored',
                    'solve-first',
                    'adult-cues',
                    'challenge-discipline',
                    'misbehavior-or-anxiety',
                    'fear-or-anxiety-in-new-environments',
                    'staying-calm',
                    'medical-reason',
                    'anxiety-scared',
                    'anxiety-stress-triggers',
                    'anxiety-aggressive',
                    'anxiety-aggression-triggers',
                    'developed',
                    'nervous-system',
                    'did-you-hear',
                    'practicing-obedience',
                    'motivation-during-challenges',
                    'most-during-training-motivation',
                    'adult-ask',
                    'vagus-name',
                    'anxiety-profile',
                    'time-spend',
                    'anxiety-training-journey',
                ],
            ],
            [
                'rule' => new ContentRule(['quiz11-puppy']),
                'questions' => [
                    'gender-love',
                    'breed-anxiety',
                    'dogs-owners',
                    'anxiety-motivation-to-start',
                    'anxiety-goal',
                    'anxiety-personalized-goal',
                    'hypervigilant',
                    'restless-and-unable-to-switch-off',
                    'upon-seeing-other-dogs',
                    'ignores-commands-completely',
                    'anxiety-signs',
                    'you-are-not-alone',
                    'ignored',
                    'solve-first',
                    'adult-cues',
                    'challenge-discipline',
                    'misbehavior-or-anxiety',
                    'fear-or-anxiety-in-new-environments',
                    'staying-calm',
                    'medical-reason',
                    'anxiety-scared',
                    'anxiety-stress-triggers',
                    'anxiety-aggressive',
                    'anxiety-aggression-triggers',
                    'developed',
                    'nervous-system',
                    'did-you-hear',
                    'practicing-obedience',
                    'motivation-during-challenges',
                    'most-during-training-motivation',
                    'adult-ask',
                    'vagus-name',
                    'anxiety-profile',
                    'time-spend',
                    'anxiety-training-journey',
                ],
            ],
            [
                'rule' => new ContentRule(['quiz13']),
                'questions' => [
                    'es-gender-split',
                    'es-dogs-owners',
                    'es-goal-main',
                    'es-age-list-split',
                    'es-breed',
                    'es-adult-spayed',
                    'es-ignored',
                    'es-solve-first',
                    'es-adult-treats',
                    'es-adult-cues',
                    'es-adult-range-obedience',
                    'es-adult-skills',
                    'es-adult-refuse',
                    'es-misbehave',
                    'es-rude',
                    'es-listen',
                    'es-frustrated',
                    'es-range-places',
                    'es-tough',
                    'es-stress',
                    'es-adult-range-friendly',
                    'es-adult-perceive',
                    'es-developed',
                    'es-adult-relationship',
                    'es-reliable',
                    'es-did-you-hear',
                    'es-practicing-obedience',
                    'es-adult-ask',
                    'es-spend',
                    'es-name',
                    'es-profile',
                    'es-coming',
                    'es-when',
                    'es-graph',
                ],
            ],
            [
                'rule' => new ContentRule(['quiz13-puppy-es']),
                'questions' => [
                    'es-breed',
                    'es-puppy-spayed',
                    'es-puppy-problems',
                    'es-puppy-potty',
                    'es-crate',
                    'es-solve-first',
                    'es-puppy-range-obedience',
                    'es-puppy-cues',
                    'es-puppy-skills',
                    'es-puppy-refuse',
                    'es-misbehave',
                    'es-rude',
                    'es-listen',
                    'es-frustrated',
                    'es-range-places-puppy',
                    'es-tough',
                    'es-stress',
                    'es-puppy-perceive',
                    'es-puppy-amazing',
                    'es-puppy-comfortable',
                    'es-puppy-petted',
                    'es-puppy-play',
                    'es-developed',
                    'es-reliable',
                    'es-did-you-hear',
                    'es-practicing-obedience',
                    'es-adult-ask',
                    'es-spend',
                    'es-name',
                    'es-profile',
                    'es-coming',
                    'es-when',
                    'es-graph',
                ],
            ],
            [
                'rule' => new ContentRule(['quiz14']),
                'questions' => [
                    'de-gender-split',
                    'de-dogs-owners',
                    'de-goal-main',
                    'de-age-list-split',
                    'de-breed',
                    'de-adult-spayed',
                    'de-ignored',
                    'de-solve-first',
                    'de-adult-treats',
                    'de-adult-cues',
                    'de-adult-range-obedience',
                    'de-adult-skills',
                    'de-adult-refuse',
                    'de-misbehave',
                    'de-rude',
                    'de-listen',
                    'de-frustrated',
                    'de-range-places',
                    'de-tough',
                    'de-stress',
                    'de-adult-range-friendly',
                    'de-adult-perceive',
                    'de-developed',
                    'de-adult-relationship',
                    'de-reliable',
                    'de-did-you-hear',
                    'de-practicing-obedience',
                    'de-adult-ask',
                    'de-spend',
                    'de-name',
                    'de-better-profile',
                    'de-coming',
                    'de-better-when',
                    'de-graph',
                ],
            ],
            [
                'rule' => new ContentRule(['quiz14-puppy-de']),
                'questions' => [
                    'de-breed',
                    'de-puppy-spayed',
                    'de-puppy-problems',
                    'de-puppy-potty',
                    'de-crate',
                    'de-solve-first',
                    'de-puppy-range-obedience',
                    'de-puppy-cues',
                    'de-puppy-skills',
                    'de-puppy-refuse',
                    'de-misbehave',
                    'de-rude',
                    'de-listen',
                    'de-frustrated',
                    'de-range-places-puppy',
                    'de-tough',
                    'de-stress',
                    'de-puppy-perceive',
                    'de-puppy-amazing',
                    'de-puppy-comfortable',
                    'de-puppy-petted',
                    'de-puppy-play',
                    'de-developed',
                    'de-reliable',
                    'de-did-you-hear',
                    'de-practicing-obedience',
                    'de-adult-ask',
                    'de-spend',
                    'de-puppy-name',
                    'de-better-profile',
                    'de-coming',
                    'de-better-when',
                    'de-graph',
                ],
            ],
            [
                'rule' => new ContentRule(['quiz15']),
                'questions' => [
                    'fr-gender-split',
                    'fr-dogs-owners',
                    'fr-goal-main',
                    'fr-age-list-split',
                    'fr-breed',
                    'fr-adult-spayed',
                    'fr-ignored',
                    'fr-solve-first',
                    'fr-adult-treats',
                    'fr-adult-cues',
                    'fr-adult-range-obedience',
                    'fr-adult-skills',
                    'fr-adult-refuse',
                    'fr-misbehave',
                    'fr-rude',
                    'fr-listen',
                    'fr-frustrated',
                    'fr-range-places',
                    'fr-tough',
                    'fr-stress',
                    'fr-adult-range-friendly',
                    'fr-adult-perceive',
                    'fr-developed',
                    'fr-adult-relationship',
                    'fr-reliable',
                    'fr-did-you-hear',
                    'fr-practicing-obedience',
                    'fr-adult-ask',
                    'fr-spend',
                    'fr-name',
                    'fr-profile',
                    'fr-coming',
                    'fr-when',
                    'fr-graph',
                ],
            ],
            [
                'rule' => new ContentRule(['quiz15-puppy-fr']),
                'questions' => [
                    'fr-breed',
                    'fr-puppy-spayed',
                    'fr-puppy-problems',
                    'fr-puppy-potty',
                    'fr-crate',
                    'fr-solve-first',
                    'fr-puppy-range-obedience',
                    'fr-puppy-cues',
                    'fr-puppy-skills',
                    'fr-puppy-refuse',
                    'fr-misbehave',
                    'fr-rude',
                    'fr-listen',
                    'fr-frustrated',
                    'fr-range-places-puppy',
                    'fr-tough',
                    'fr-stress',
                    'fr-puppy-perceive',
                    'fr-puppy-amazing',
                    'fr-puppy-comfortable',
                    'fr-puppy-petted',
                    'fr-puppy-play',
                    'fr-developed',
                    'fr-reliable',
                    'fr-did-you-hear',
                    'fr-practicing-obedience',
                    'fr-adult-ask',
                    'fr-spend',
                    'fr-puppy-name',
                    'fr-profile',
                    'fr-coming',
                    'fr-when',
                    'fr-graph',
                ],
            ],
            [
                'rule' => new ContentRule(['quiz16']),
                'questions' => [
                    'pt-gender-split',
                    'pt-dogs-owners',
                    'pt-goal-main',
                    'pt-age-list-split',
                    'pt-breed',
                    'pt-adult-spayed',
                    'pt-ignored',
                    'pt-solve-first',
                    'pt-adult-treats',
                    'pt-cues',
                    'pt-range-obedience',
                    'pt-skills',
                    'pt-refuse',
                    'pt-misbehave',
                    'pt-rude',
                    'pt-listen',
                    'pt-frustrated',
                    'pt-range-places',
                    'pt-tough',
                    'pt-stress',
                    'pt-range-friendly',
                    'pt-perceive',
                    'pt-developed',
                    'pt-adult-relationship',
                    'pt-reliable',
                    'pt-did-you-hear',
                    'pt-practicing-obedience',
                    'pt-ask',
                    'pt-spend',
                    'pt-name',
                    'pt-profile',
                    'pt-coming',
                    'pt-when',
                    'pt-graph',
                ],
            ],
            [
                'rule' => new ContentRule(['quiz16-puppy-pt']),
                'questions' => [
                    'pt-breed',
                    'pt-puppy-spayed',
                    'pt-puppy-problems',
                    'pt-puppy-potty',
                    'pt-crate',
                    'pt-solve-first',
                    'pt-range-obedience',
                    'pt-cues',
                    'pt-skills',
                    'pt-refuse',
                    'pt-misbehave',
                    'pt-rude',
                    'pt-listen',
                    'pt-frustrated',
                    'pt-range-places-puppy',
                    'pt-tough',
                    'pt-stress',
                    'pt-perceive',
                    'pt-amazing',
                    'pt-puppy-comfortable',
                    'pt-puppy-petted',
                    'pt-puppy-play',
                    'pt-developed',
                    'pt-reliable',
                    'pt-did-you-hear',
                    'pt-practicing-obedience',
                    'pt-ask',
                    'pt-spend',
                    'pt-name',
                    'pt-profile',
                    'pt-coming',
                    'pt-when',
                    'pt-graph',
                ],
            ],
            [
                'rule' => new ContentRule(['quiz17']),
                'questions' => [
                    'gender-split',
                    'dogs-owners',
                    'react-goals',
                    'age-list-split',
                    'react-satisfied',
                    'breed',
                    'adult-spayed',
                    'react-understanding',
                    'react-range-reactivity-understanding',
                    'react-is-dog-overreact',
                    'react-ignored',
                    'react-come-home',
                    'crate',
                    'adult-treats',
                    'adult-cues',
                    'react-discipline',
                    'adult-skills',
                    'solve-first',
                    'react-excited',
                    'react-excitement-triggers',
                    'react-aggressive',
                    'react-aggression-triggers',
                    'react-scared',
                    'react-stress-triggers',
                    'developed',
                    'frustrated',
                    'react-range-another-dog-calmly-reaction',
                    'react-identified-dogs-behaviour',
                    'triggers-in-dog-training',
                    'did-you-hear',
                    'practicing',
                    'adult-ask',
                    'spend',
                    'name',
                    'react-profile',
                    'coming',
                    'better-when',
                    'react-graph',
                ],
            ],
            [
                'rule' => new ContentRule(['quiz17'], [new ContentSplitRule('pc_deepexp', 2)]),
                'questions' => [
                    'gender-split',
                    'dogs-owners',
                    'react-goals',
                    'age-list-split',
                    'react-satisfied',
                    'breed',
                    'adult-spayed',
                    'react-understanding',
                    'react-range-reactivity-understanding',
                    'overexitements',
                    'react-ignored',
                    'react-come-home',
                    'crate',
                    'adult-treats',
                    'adult-cues',
                    'react-discipline',
                    'solve-first',
                    'frustrated',
                    'expert-advice',
                    'react-excited',
                    'react-excitement-triggers',
                    'react-aggressive',
                    'react-aggression-triggers',
                    'react-scared',
                    'react-stress-triggers',
                    'react-range-another-dog-calmly-reaction',
                    'react-identified-dogs-behaviour',
                    'triggers-in-dog-training',
                    'traumatic-experience',
                    'developed',
                    'get-worse',
                    'did-you-hear',
                    'practicing',
                    'adult-ask',
                    'dog-owners-questions-matter',
                    'spend',
                    'name',
                    'react-profile',
                    'coming',
                    'better-when',
                    'react-graph',
                ],
            ],
            [
                'rule' => new ContentRule(
                    ['quiz17-puppy'],
                    [new ContentSplitRule('pc_deepexp', 2)]
                ),
                'questions' => [
                    'react-satisfied',
                    'breed',
                    'puppy-spayed',
                    'react-understanding',
                    'react-range-reactivity-understanding',
                    'overexitements',
                    'react-ignored',
                    'react-come-home',
                    'crate',
                    'puppy-potty',
                    'puppy-cues',
                    'react-discipline',
                    'solve-first',
                    'frustrated',
                    'expert-advice',
                    'react-excited',
                    'react-excitement-triggers',
                    'react-aggressive',
                    'react-aggression-triggers',
                    'react-scared',
                    'react-stress-triggers',
                    'react-range-another-dog-calmly-reaction',
                    'react-identified-dogs-behaviour',
                    'triggers-in-dog-training',
                    'traumatic-experience',
                    'developed',
                    'get-worse',
                    'did-you-hear',
                    'practicing',
                    'adult-ask',
                    'dog-owners-questions-matter',
                    'spend',
                    'name',
                    'react-profile',
                    'coming',
                    'better-when',
                    'react-graph',
                ],
            ],
            [
                'rule' => new ContentRule(
                    ['quiz17'],
                    [new ContentSplitRule('pc_qualityTime', 2)]
                ),
                'questions' => [
                    'gender-split',
                    'dogs-owners',
                    'react-goals',
                    'age-list-split',
                    'react-satisfied',
                    'breed',
                    'adult-spayed',
                    'react-understanding',
                    'react-range-reactivity-understanding',
                    'react-is-dog-overreact',
                    'react-ignored',
                    'react-come-home',
                    'crate',
                    'adult-treats',
                    'adult-cues',
                    'react-discipline',
                    'adult-skills',
                    'solve-first',
                    'react-excited',
                    'react-excitement-triggers',
                    'react-aggressive',
                    'react-aggression-triggers',
                    'react-scared',
                    'react-stress-triggers',
                    'developed',
                    'frustrated',
                    'react-range-another-dog-calmly-reaction',
                    'react-identified-dogs-behaviour',
                    'triggers-in-dog-training',
                    'did-you-hear',
                    'practicing',
                    'adult-ask',
                    'name',
                    'react-profile',
                    'spend-list',
                    'graphic-training',
                    'best-time-to-train',
                    'dedicated',
                ],
            ],
            [
                'rule' => new ContentRule(
                    ['quiz17-puppy'],
                    [new ContentSplitRule('pc_qualityTime', 2)]
                ),
                'questions' => [
                    'react-satisfied',
                    'breed',
                    'puppy-spayed',
                    'react-understanding',
                    'react-range-reactivity-understanding',
                    'react-is-dog-overreact',
                    'react-ignored',
                    'react-come-home',
                    'crate',
                    'puppy-potty',
                    'puppy-cues',
                    'react-discipline',
                    'puppy-skills',
                    'solve-first',
                    'react-excited',
                    'react-excitement-triggers',
                    'react-aggressive',
                    'react-aggression-triggers',
                    'react-scared',
                    'react-stress-triggers',
                    'developed',
                    'frustrated',
                    'react-range-another-dog-calmly-reaction',
                    'react-identified-dogs-behaviour',
                    'triggers-in-dog-training',
                    'did-you-hear',
                    'practicing',
                    'adult-ask',
                    'puppy-name',
                    'react-profile',
                    'spend-list',
                    'graphic-training',
                    'best-time-to-train',
                    'dedicated',
                ],
            ],
            [
                'rule' => new ContentRule(
                    ['quiz17'],
                    [new ContentSplitRule('pc_creofun', 2)]
                ),
                'questions' => [
                    'react-age-4th',
                    'gender',
                    'limiting',
                    'breed',
                    'other-dogs',
                    'adult-spayed',
                    'jump-on-people',
                    'react-understanding',
                    'react-is-dog-overreact',
                    'react-goals',
                    'react-ignored',
                    'react-come-home',
                    'crate',
                    'adult-treats',
                    'adult-cues',
                    'react-discipline',
                    'adult-skills',
                    'solve-first',
                    'react-excited',
                    'react-excitement-triggers',
                    'react-aggressive',
                    'react-aggression-triggers',
                    'react-scared',
                    'react-stress-triggers',
                    'developed',
                    'frustrated',
                    'react-range-another-dog-calmly-reaction',
                    'react-identified-dogs-behaviour',
                    'trouble-affording',
                    'triggers-in-dog-training',
                    'did-you-hear',
                    'practicing',
                    'adult-ask',
                    'spend',
                    'name',
                    'react-profile',
                    'coming',
                    'better-when',
                    'react-graph',
                ],
            ],
            [
                'rule' => new ContentRule(
                    ['quiz17'],
                    [
                        new ContentSplitRule('pc_1stnewlog', 2),
                    ]
                ),
                'questions' => [
                    'age-stnewlog-g2',
                    'gender',
                    'react-satisfied',
                    'breed',
                    'adult-spayed',
                    'react-understanding',
                    'react-range-reactivity-understanding',
                    'react-is-dog-overreact',
                    'react-goals',
                    'react-ignored',
                    'react-come-home',
                    'crate',
                    'adult-treats',
                    'adult-cues',
                    'react-discipline',
                    'adult-skills',
                    'solve-first',
                    'react-excited',
                    'react-excitement-triggers',
                    'react-aggressive',
                    'react-aggression-triggers',
                    'react-scared',
                    'react-stress-triggers',
                    'frustrated',
                    'react-range-another-dog-calmly-reaction',
                    'react-identified-dogs-behaviour',
                    'adult-ask',
                    'spend',
                    'name',
                    'react-profile',
                    'coming',
                    'better-when',
                    'react-graph',
                ],
            ],
            [
                'rule' => new ContentRule(
                    ['quiz17'],
                    [
                        new ContentSplitRule('pc_1stnewlog', 3),
                    ]
                ),
                'questions' => [
                    'react-overcome',
                    'age-stnewlog',
                    'gender',
                    'react-satisfied',
                    'breed',
                    'adult-spayed',
                    'react-understanding',
                    'react-range-reactivity-understanding',
                    'react-is-dog-overreact',
                    'react-goals',
                    'react-ignored',
                    'react-come-home',
                    'crate',
                    'adult-treats',
                    'adult-cues',
                    'react-discipline',
                    'adult-skills',
                    'solve-first',
                    'react-excited',
                    'react-excitement-triggers',
                    'react-aggressive',
                    'react-aggression-triggers',
                    'react-scared',
                    'react-stress-triggers',
                    'frustrated',
                    'react-range-another-dog-calmly-reaction',
                    'react-identified-dogs-behaviour',
                    'adult-ask',
                    'spend',
                    'name',
                    'react-profile',
                    'coming',
                    'better-when',
                    'react-graph',
                ],
            ],
            [
                'rule' => new ContentRule(
                    ['quiz17'],
                    [
                        new ContentSplitRule('pc_1stnewlog', 4),
                    ]
                ),
                'questions' => [
                    'calm-and-focused',
                    'age-stnewlog',
                    'gender',
                    'react-satisfied',
                    'breed',
                    'adult-spayed',
                    'react-understanding',
                    'react-range-reactivity-understanding',
                    'react-is-dog-overreact',
                    'react-goals',
                    'react-ignored',
                    'react-come-home',
                    'crate',
                    'adult-treats',
                    'adult-cues',
                    'react-discipline',
                    'adult-skills',
                    'solve-first',
                    'react-excited',
                    'react-excitement-triggers',
                    'react-aggressive',
                    'react-aggression-triggers',
                    'react-scared',
                    'react-stress-triggers',
                    'frustrated',
                    'react-range-another-dog-calmly-reaction',
                    'react-identified-dogs-behaviour',
                    'adult-ask',
                    'spend',
                    'name',
                    'react-profile',
                    'coming',
                    'better-when',
                    'react-graph',
                ],
            ],
            [
                'rule' => new ContentRule(
                    ['quiz17-puppy-magic']
                ),
                'questions' => [
                    'gender',
                    'react-satisfied',
                    'breed',
                    'puppy-spayed',
                    'react-understanding',
                    'react-range-reactivity-understanding',
                    'react-is-dog-overreact',
                    'magic-01',
                    'magic-01-result',
                    'react-goals',
                    'react-ignored',
                    'react-come-home',
                    'crate',
                    'puppy-potty',
                    'puppy-cues',
                    'magic-02',
                    'magic-02-result',
                    'react-discipline',
                    'solve-first',
                    'react-excited',
                    'react-excitement-triggers',
                    'react-aggressive',
                    'react-aggression-triggers',
                    'react-scared',
                    'react-stress-triggers',
                    'magic-03',
                    'magic-03-result',
                    'frustrated',
                    'react-range-another-dog-calmly-reaction',
                    'adult-ask',
                    'spend',
                    'puppy-name',
                    'react-profile',
                    'coming',
                    'better-when',
                    'react-graph',
                ],
            ],
            [
                'rule' => new ContentRule(
                    ['quiz17-puppy-reactpun'],
                ),
                'questions' => [
                    'gender',
                    'react-satisfied',
                    'breed',
                    'puppy-spayed',
                    'react-understanding',
                    'react-range-reactivity-understanding',
                    'react-is-dog-overreact',
                    'react-goals',
                    'react-ignored',
                    'react-come-home',
                    'crate',
                    'puppy-potty',
                    'puppy-cues',
                    'react-discipline',
                    'puppy-skills',
                    'adult-refuse',
                    'react-irritability',
                    'react-provoke-reactive-behaviour',
                    'react-been-trained-methods',
                    'rude',
                    'listen',
                    'range-places-puppy',
                    'react-tough',
                    'solve-first',
                    'react-excited',
                    'react-excitement-triggers',
                    'react-aggressive',
                    'react-aggression-triggers',
                    'react-scared',
                    'react-stress-triggers',
                    'frustrated',
                    'react-range-another-dog-calmly-reaction',
                    'react-identified-dogs-behaviour',
                    'adult-ask',
                    'spend',
                    'puppy-name',
                    'react-profile',
                    'coming',
                    'better-when',
                    'react-graph',
                ],
            ],
            [
                'rule' => new ContentRule(
                    ['quiz17-puppy'],
                ),
                'questions' => [
                    'react-satisfied',
                    'breed',
                    'puppy-spayed',
                    'react-understanding',
                    'react-range-reactivity-understanding',
                    'react-is-dog-overreact',
                    'react-ignored',
                    'react-come-home',
                    'crate',
                    'puppy-potty',
                    'puppy-cues',
                    'react-discipline',
                    'puppy-skills',
                    'solve-first',
                    'react-excited',
                    'react-excitement-triggers',
                    'react-aggressive',
                    'react-aggression-triggers',
                    'react-scared',
                    'react-stress-triggers',
                    'developed',
                    'frustrated',
                    'react-range-another-dog-calmly-reaction',
                    'react-identified-dogs-behaviour',
                    'triggers-in-dog-training',
                    'did-you-hear',
                    'practicing',
                    'adult-ask',
                    'spend',
                    'puppy-name',
                    'react-profile',
                    'coming',
                    'better-when',
                    'react-graph',
                ],
            ],
            [
                'rule' => new ContentRule(
                    ['quiz17-puppy'],
                    [
                        new ContentSplitRule('pc_creofun', 2),
                    ]
                ),
                'questions' => [
                    'gender',
                    'limiting',
                    'breed',
                    'other-dogs',
                    'puppy-spayed',
                    'jump-on-people',
                    'react-understanding',
                    'react-is-dog-overreact',
                    'react-goals',
                    'react-ignored',
                    'react-come-home',
                    'crate',
                    'puppy-potty',
                    'puppy-cues',
                    'react-discipline',
                    'puppy-skills',
                    'solve-first',
                    'react-excited',
                    'react-excitement-triggers',
                    'react-aggressive',
                    'react-aggression-triggers',
                    'react-scared',
                    'react-stress-triggers',
                    'developed',
                    'frustrated',
                    'react-range-another-dog-calmly-reaction',
                    'react-identified-dogs-behaviour',
                    'trouble-affording',
                    'triggers-in-dog-training',
                    'did-you-hear',
                    'practicing',
                    'adult-ask',
                    'spend',
                    'puppy-name',
                    'react-profile',
                    'coming',
                    'better-when',
                    'react-graph',
                ],
            ],
            [
                'rule' => new ContentRule(
                    ['quiz18'],
                ),
                'questions' => [
                    'de-gender-split',
                    'de-dogs-owners',
                    'de-react-goals',
                    'de-age-list-split',
                    'de-react-satisfied',
                    'de-breed',
                    'de-react-adult-spayed',
                    'de-react-understanding',
                    'de-react-range-reactivity-understanding',
                    'de-react-is-dog-overreact',
                    'de-react-ignored',
                    'de-react-come-home',
                    'de-react-crate',
                    'de-react-adult-treats',
                    'de-react-adult-cues',
                    'de-react-discipline',
                    'de-adult-skills',
                    'de-solve-first',
                    'de-react-excited',
                    'de-react-excitement-triggers',
                    'de-react-aggressive',
                    'de-react-aggression-triggers',
                    'de-react-scared',
                    'de-react-stress-triggers',
                    'de-developed',
                    'de-frustrated',
                    'de-react-range-another-dog-calmly-reaction',
                    'de-react-identified-dogs-behaviour',
                    'de-triggers-in-dog-training',
                    'de-did-you-hear',
                    'de-practicing',
                    'de-adult-ask',
                    'de-react-spend',
                    'de-name',
                    'de-react-profile',
                    'de-coming',
                    'de-better-when',
                    'de-react-graph',
                ],
            ],
            [
                'rule' => new ContentRule(
                    ['quiz18-puppy-de'],
                ),
                'questions' => [
                    'de-react-satisfied',
                    'de-breed',
                    'de-react-puppy-spayed',
                    'de-react-understanding',
                    'de-react-range-reactivity-understanding',
                    'de-react-is-dog-overreact',
                    'de-react-ignored',
                    'de-react-come-home',
                    'de-react-puppy-crate',
                    'de-puppy-potty',
                    'de-puppy-cues',
                    'de-react-discipline',
                    'de-puppy-skills',
                    'de-solve-first',
                    'de-react-excited',
                    'de-react-excitement-triggers',
                    'de-react-aggressive',
                    'de-react-aggression-triggers',
                    'de-react-scared',
                    'de-react-stress-triggers',
                    'de-developed',
                    'de-frustrated',
                    'de-react-range-another-dog-calmly-reaction',
                    'de-react-identified-dogs-behaviour',
                    'de-triggers-in-dog-training',
                    'de-did-you-hear',
                    'de-practicing',
                    'de-adult-ask',
                    'de-react-spend',
                    'de-puppy-name',
                    'de-react-profile',
                    'de-coming',
                    'de-better-when',
                    'de-react-graph',
                ],
            ],
            [
                'rule' => new ContentRule(
                    ['quiz19'],
                ),
                'questions' => [
                    'fr-gender-split',
                    'fr-dogs-owners',
                    'fr-react-goals',
                    'fr-age-list-split',
                    'fr-react-satisfied',
                    'fr-breed',
                    'fr-adult-spayed',
                    'fr-react-understanding',
                    'fr-react-range-reactivity-understanding',
                    'fr-react-is-dog-overreact',
                    'fr-react-ignored',
                    'fr-react-come-home',
                    'fr-crate',
                    'fr-adult-treats',
                    'fr-adult-cues',
                    'fr-react-discipline',
                    'fr-adult-skills',
                    'fr-solve-first',
                    'fr-react-excited',
                    'fr-react-excitement-triggers',
                    'fr-react-aggressive',
                    'fr-react-aggression-triggers',
                    'fr-react-scared',
                    'fr-react-stress-triggers',
                    'fr-developed',
                    'fr-frustrated',
                    'fr-react-range-another-dog-calmly-reaction',
                    'fr-react-identified-dogs-behaviour',
                    'fr-triggers-in-dog-training',
                    'fr-did-you-hear',
                    'fr-practicing',
                    'fr-adult-ask',
                    'fr-spend',
                    'fr-name',
                    'fr-react-profile',
                    'fr-coming',
                    'fr-when',
                    'fr-react-graph',
                ],
            ],
            [
                'rule' => new ContentRule(
                    ['quiz19-puppy-fr'],
                ),
                'questions' => [
                    'fr-react-satisfied',
                    'fr-breed',
                    'fr-puppy-spayed',
                    'fr-react-understanding',
                    'fr-react-range-reactivity-understanding',
                    'fr-react-is-dog-overreact',
                    'fr-react-ignored',
                    'fr-react-come-home',
                    'fr-puppy-crate',
                    'fr-puppy-potty',
                    'fr-puppy-cues',
                    'fr-react-discipline',
                    'fr-puppy-skills',
                    'fr-solve-first',
                    'fr-react-excited',
                    'fr-react-excitement-triggers',
                    'fr-react-aggressive',
                    'fr-react-aggression-triggers',
                    'fr-react-scared',
                    'fr-react-stress-triggers',
                    'fr-developed',
                    'fr-frustrated',
                    'fr-react-range-another-dog-calmly-reaction',
                    'fr-react-identified-dogs-behaviour',
                    'fr-triggers-in-dog-training',
                    'fr-did-you-hear',
                    'fr-practicing',
                    'fr-adult-ask',
                    'fr-spend',
                    'fr-puppy-name',
                    'fr-react-profile',
                    'fr-coming',
                    'fr-when',
                    'fr-react-graph',
                ],
            ],
            [
                'rule' => new ContentRule(
                    ['quiz20'],
                ),
                'questions' => [
                    'es-gender-split',
                    'es-dogs-owners',
                    'es-react-goals',
                    'es-age-list-split',
                    'es-react-satisfied',
                    'es-breed',
                    'es-adult-spayed',
                    'es-react-understanding',
                    'es-react-range-reactivity-understanding',
                    'es-react-is-dog-overreact',
                    'es-react-ignored',
                    'es-react-come-home',
                    'es-adult-crate',
                    'es-adult-treats',
                    'es-adult-cues',
                    'es-react-discipline',
                    'es-adult-skills',
                    'es-solve-first',
                    'es-react-excited',
                    'es-react-excitement-triggers',
                    'es-react-aggressive',
                    'es-react-aggression-triggers',
                    'es-react-scared',
                    'es-react-stress-triggers',
                    'es-developed',
                    'es-frustrated',
                    'es-react-range-another-dog-calmly-reaction',
                    'es-react-identified-dogs-behaviour',
                    'es-triggers-in-dog-training',
                    'es-did-you-hear',
                    'es-practicing',
                    'es-adult-ask',
                    'es-react-spend',
                    'es-name',
                    'es-react-profile',
                    'es-coming',
                    'es-when',
                    'es-react-graph',
                ],
            ],
            [
                'rule' => new ContentRule(
                    ['quiz20-puppy-es'],
                ),
                'questions' => [
                    'es-react-satisfied',
                    'es-breed',
                    'es-puppy-spayed',
                    'es-react-understanding',
                    'es-react-range-reactivity-understanding',
                    'es-react-is-dog-overreact',
                    'es-react-ignored',
                    'es-react-come-home',
                    'es-puppy-crate',
                    'es-puppy-potty',
                    'es-puppy-cues',
                    'es-react-discipline',
                    'es-puppy-skills',
                    'es-solve-first',
                    'es-react-excited',
                    'es-react-excitement-triggers',
                    'es-react-aggressive',
                    'es-react-aggression-triggers',
                    'es-react-scared',
                    'es-react-stress-triggers',
                    'es-developed',
                    'es-frustrated',
                    'es-react-range-another-dog-calmly-reaction',
                    'es-react-identified-dogs-behaviour',
                    'es-triggers-in-dog-training',
                    'es-did-you-hear',
                    'es-practicing',
                    'es-adult-ask',
                    'es-react-spend',
                    'es-name',
                    'es-react-profile',
                    'es-coming',
                    'es-when',
                    'es-react-graph',
                ],
            ],
            [
                'rule' => new ContentRule(
                    ['quiz21'],
                ),
                'questions' => [
                    'pt-gender-split',
                    'pt-dogs-owners',
                    'pt-react-goals',
                    'pt-age-list-split',
                    'pt-react-satisfied',
                    'pt-breed',
                    'pt-adult-spayed',
                    'pt-react-understanding',
                    'pt-react-range-reactivity-understanding',
                    'pt-react-is-dog-overreact',
                    'pt-react-ignored',
                    'pt-react-come-home',
                    'pt-crate',
                    'pt-adult-treats',
                    'pt-cues',
                    'pt-react-discipline',
                    'pt-skills',
                    'pt-solve-first',
                    'pt-react-excited',
                    'pt-react-excitement-triggers',
                    'pt-react-aggressive',
                    'pt-react-aggression-triggers',
                    'pt-react-scared',
                    'pt-react-stress-triggers',
                    'pt-developed',
                    'pt-frustrated',
                    'pt-react-range-another-dog-calmly-reaction',
                    'pt-react-identified-dogs-behaviour',
                    'pt-triggers-in-dog-training',
                    'pt-did-you-hear',
                    'pt-practicing',
                    'pt-ask',
                    'pt-spend',
                    'pt-name',
                    'pt-react-profile',
                    'pt-coming',
                    'pt-when',
                    'pt-react-graph',
                ],
            ],
            [
                'rule' => new ContentRule(
                    ['quiz21-puppy-pt'],
                ),
                'questions' => [
                    'pt-react-satisfied',
                    'pt-breed',
                    'pt-puppy-spayed',
                    'pt-react-understanding',
                    'pt-react-range-reactivity-understanding',
                    'pt-react-is-dog-overreact',
                    'pt-react-ignored',
                    'pt-react-come-home',
                    'pt-crate',
                    'pt-puppy-potty',
                    'pt-cues',
                    'pt-react-discipline',
                    'pt-skills',
                    'pt-solve-first',
                    'pt-react-excited',
                    'pt-react-excitement-triggers',
                    'pt-react-aggressive',
                    'pt-react-aggression-triggers',
                    'pt-react-scared',
                    'pt-react-stress-triggers',
                    'pt-developed',
                    'pt-frustrated',
                    'pt-react-range-another-dog-calmly-reaction',
                    'pt-react-identified-dogs-behaviour',
                    'pt-triggers-in-dog-training',
                    'pt-did-you-hear',
                    'pt-practicing',
                    'pt-ask',
                    'pt-spend',
                    'pt-name',
                    'pt-react-profile',
                    'pt-coming',
                    'pt-when',
                    'pt-react-graph',
                ],
            ],
            [
                'rule' => new ContentRule(
                    ['quiz22'],
                ),
                'questions' => [
                    'separation-age-4th',
                    'gender',
                    'separation-concerned',
                    'separation-breed',
                    'adult-spayed',
                    'separation-understanding',
                    'separation-info',
                    'separation-goal',
                    'separation-overwhelmed-range',
                    'separation-aim-to-leave-alone',
                    'separation-when-show-signs-of-distress',
                    'separation-skills',
                    'separation-feel-guilty-range',
                    'separation-how-often-dog-alone',
                    'separation-concerned-finding-babysit',
                    'separation-crate',
                    'separation-manageable',
                    'separation-left-alone-reaction',
                    'separation-alone-in-confined-space',
                    'separation-following-range',
                    'separation-close-when-sleeping',
                    'separation-preparing-to-leave',
                    'separation-got-it',
                    'frustrated',
                    'separation-solve-first',
                    'separation-scared-or-anxious',
                    'separation-anxiety-triggers',
                    'separation-take-with-me-range',
                    'separation-ask',
                    'separation-spend',
                    'name',
                    'separation-profile',
                    'coming',
                    'better-when',
                    'separation-graph',
                ],
            ],
            [
                'rule' => new ContentRule(
                    ['quiz22-puppy'],
                ),
                'questions' => [
                    'gender',
                    'separation-concerned',
                    'separation-breed',
                    'puppy-spayed',
                    'separation-understanding',
                    'separation-info',
                    'separation-goal',
                    'separation-overwhelmed-range',
                    'separation-aim-to-leave-alone',
                    'separation-when-show-signs-of-distress',
                    'separation-skills',
                    'separation-feel-guilty-range',
                    'separation-how-often-dog-alone',
                    'separation-concerned-finding-babysit',
                    'separation-crate',
                    'separation-manageable',
                    'separation-left-alone-reaction',
                    'separation-alone-in-confined-space',
                    'separation-following-range',
                    'separation-close-when-sleeping',
                    'separation-preparing-to-leave',
                    'separation-got-it',
                    'frustrated',
                    'separation-solve-first',
                    'separation-scared-or-anxious',
                    'separation-anxiety-triggers',
                    'separation-take-with-me-range',
                    'separation-ask',
                    'separation-spend',
                    'name',
                    'separation-profile',
                    'coming',
                    'better-when',
                    'separation-graph',
                ],
            ],
            [
                'rule' => new ContentRule(['quiz23']),
                'questions' => [
                    'aggression-age-4th',
                    'gender',
                    'react-satisfied',
                    'breed',
                    'adult-spayed',
                    'aggression-understanding',
                    'aggression-range-reactivity-understanding',
                    'aggression-triggers-info',
                    'aggression-goals',
                    'react-ignored',
                    'aggression-come-home',
                    'crate',
                    'adult-treats',
                    'adult-cues',
                    'aggression-discipline',
                    'adult-skills',
                    'solve-first',
                    'aggression-gotten-into-dog-fight',
                    'aggression-sometimes-feel-dog-out-of-control',
                    'react-aggressive',
                    'react-aggression-triggers',
                    'react-excited',
                    'react-excitement-triggers',
                    'react-scared',
                    'react-stress-triggers',
                    'frustrated',
                    'react-range-another-dog-calmly-reaction',
                    'react-identified-dogs-behaviour',
                    'adult-ask',
                    'spend',
                    'name',
                    'aggression-profile',
                    'coming',
                    'better-when',
                    'aggression-graph',
                ],
            ],
            [
                'rule' => new ContentRule(['quiz23-puppy']),
                'questions' => [
                    'gender',
                    'react-satisfied',
                    'breed',
                    'puppy-spayed',
                    'aggression-understanding',
                    'aggression-range-reactivity-understanding',
                    'aggression-triggers-info',
                    'aggression-goals',
                    'react-ignored',
                    'aggression-come-home',
                    'crate',
                    'puppy-potty',
                    'puppy-cues',
                    'aggression-discipline',
                    'puppy-skills',
                    'solve-first',
                    'aggression-gotten-into-dog-fight',
                    'aggression-sometimes-feel-dog-out-of-control',
                    'react-aggressive',
                    'react-aggression-triggers',
                    'react-excited',
                    'react-excitement-triggers',
                    'react-scared',
                    'react-stress-triggers',
                    'frustrated',
                    'react-range-another-dog-calmly-reaction',
                    'react-identified-dogs-behaviour',
                    'adult-ask',
                    'spend',
                    'name',
                    'aggression-profile',
                    'coming',
                    'better-when',
                    'aggression-graph',
                ],
            ],
            [
                'rule' => new ContentRule(
                    ['quiz24'],
                ),
                'questions' => [
                    'it-gender-split',
                    'it-dogs-owners',
                    'it-goal-main',
                    'it-age-list-split',
                    'it-breed',
                    'it-adult-spayed',
                    'it-ignored',
                    'it-solve-first',
                    'it-adult-treats',
                    'it-adult-cues',
                    'it-adult-range-obedience',
                    'it-adult-skills',
                    'it-adult-refuse',
                    'it-misbehave',
                    'it-rude',
                    'it-listen',
                    'it-frustrated',
                    'it-range-places-adult',
                    'it-tough',
                    'it-stress',
                    'it-adult-range-friendly',
                    'it-adult-perceive',
                    'it-developed',
                    'it-adult-relationship',
                    'it-reliable',
                    'it-did-you-hear',
                    'it-practicing-obedience',
                    'it-adult-ask',
                    'it-spend',
                    'it-name',
                    'it-better-profile',
                    'it-coming',
                    'it-better-when',
                    'it-graph',
                ],
            ],
            [
                'rule' => new ContentRule(
                    ['quiz24-puppy-it'],
                ),
                'questions' => [
                    'it-breed',
                    'it-puppy-spayed',
                    'it-puppy-problems',
                    'it-puppy-potty',
                    'it-crate',
                    'it-solve-first',
                    'it-puppy-range-obedience',
                    'it-puppy-cues',
                    'it-puppy-skills',
                    'it-puppy-refuse',
                    'it-misbehave',
                    'it-rude',
                    'it-listen',
                    'it-frustrated',
                    'it-range-places-puppy',
                    'it-tough',
                    'it-stress',
                    'it-puppy-perceive',
                    'it-puppy-amazing',
                    'it-puppy-comfortable',
                    'it-puppy-petted',
                    'it-puppy-play',
                    'it-developed',
                    'it-reliable',
                    'it-did-you-hear',
                    'it-practicing-obedience',
                    'it-adult-ask',
                    'it-spend',
                    'it-puppy-name',
                    'it-better-profile',
                    'it-coming',
                    'it-better-when',
                    'it-graph',
                ],
            ],
            [
                'rule' => new ContentRule(['quiz25']),
                'questions' => [
                    'it-gender-split',
                    'it-dogs-owners',
                    'it-react-goals',
                    'it-age-list-split',
                    'it-react-satisfied',
                    'it-breed',
                    'it-adult-spayed',
                    'it-react-understanding',
                    'it-react-range-reactivity-understanding',
                    'it-react-is-dog-overreact',
                    'it-react-ignored',
                    'it-react-come-home',
                    'it-crate',
                    'it-adult-treats',
                    'it-adult-cues',
                    'it-react-discipline',
                    'it-adult-skills',
                    'it-solve-first',
                    'it-react-excited',
                    'it-react-excitement-triggers',
                    'it-react-aggressive',
                    'it-react-aggression-triggers',
                    'it-react-scared',
                    'it-react-stress-triggers',
                    'it-developed',
                    'it-frustrated',
                    'it-react-range-another-dog-calmly-reaction',
                    'it-react-identified-dogs-behaviour',
                    'it-triggers-in-dog-training',
                    'it-did-you-hear',
                    'it-practicing',
                    'it-adult-ask',
                    'it-spend',
                    'it-name',
                    'it-react-profile',
                    'it-coming',
                    'it-better-when',
                    'it-react-graph',
                ],
            ],
            [
                'rule' => new ContentRule(
                    ['quiz25-puppy-it'],
                ),
                'questions' => [
                    'it-react-satisfied',
                    'it-breed',
                    'it-puppy-spayed',
                    'it-react-understanding',
                    'it-react-range-reactivity-understanding',
                    'it-react-is-dog-overreact',
                    'it-react-ignored',
                    'it-react-come-home',
                    'it-crate',
                    'it-puppy-potty',
                    'it-puppy-cues',
                    'it-react-discipline',
                    'it-puppy-skills',
                    'it-solve-first',
                    'it-react-excited',
                    'it-react-excitement-triggers',
                    'it-react-aggressive',
                    'it-react-aggression-triggers',
                    'it-react-scared',
                    'it-react-stress-triggers',
                    'it-developed',
                    'it-frustrated',
                    'it-react-range-another-dog-calmly-reaction',
                    'it-react-identified-dogs-behaviour',
                    'it-triggers-in-dog-training',
                    'it-did-you-hear',
                    'it-practicing',
                    'it-adult-ask',
                    'it-spend',
                    'it-puppy-name',
                    'it-react-profile',
                    'it-coming',
                    'it-better-when',
                    'it-react-graph',
                ],
            ],
            [
                'rule' => new ContentRule(['quiz26']),
                'questions' => [
                    'it-gender-split',
                    'it-dogs-owners',
                    'it-goal',
                    'it-age-list-split',
                    'it-react-satisfied',
                    'it-breed',
                    'it-adult-spayed',
                    'it-reinforcement-understanding',
                    'it-reinforcement-range',
                    'it-reinforcement-info',
                    'it-adult-refuse',
                    'it-ignored',
                    'it-solve-first',
                    'it-adult-treats',
                    'it-adult-cues',
                    'it-adult-range-obedience',
                    'it-adult-skills',
                    'it-stress',
                    'it-adult-range-friendly',
                    'it-adult-perceive',
                    'it-developed',
                    'it-adult-relationship',
                    'it-reliable',
                    'it-did-you-hear',
                    'it-practicing-obedience',
                    'it-adult-ask',
                    'it-spend-main',
                    'it-name',
                    'it-better-profile',
                    'it-coming',
                    'it-better-when',
                    'it-graph',
                ],
            ],
            [
                'rule' => new ContentRule(['quiz26-puppy-it']),
                'questions' => [
                    'it-react-satisfied',
                    'it-breed',
                    'it-puppy-spayed',
                    'it-reinforcement-understanding',
                    'it-reinforcement-range',
                    'it-reinforcement-info',
                    'it-puppy-refuse',
                    'it-puppy-problems',
                    'it-puppy-potty',
                    'it-crate',
                    'it-solve-first',
                    'it-puppy-range-obedience',
                    'it-puppy-cues',
                    'it-puppy-skills',
                    'it-stress',
                    'it-puppy-perceive',
                    'it-puppy-amazing',
                    'it-puppy-comfortable',
                    'it-puppy-petted',
                    'it-puppy-play',
                    'it-developed',
                    'it-reliable',
                    'it-did-you-hear',
                    'it-practicing-obedience',
                    'it-adult-ask',
                    'it-spend-main',
                    'it-puppy-name',
                    'it-better-profile',
                    'it-coming',
                    'it-better-when',
                    'it-graph',
                ],
            ],
            [
                'rule' => new ContentRule(['quiz27']),
                'questions' => [
                    'age-list-challenge-split',
                    'gender-challenge',
                    'breed-challenge',
                    'dogs-owners',
                    'challenge-motivation-to-start',
                    'goal',
                    'challenge-personalized-goal',
                    'failed-dog-training-attempts',
                    'training-consistency-challenges',
                    'making-dog-training-fun',
                    'you-are-not-alone',
                    'adult-refuse',
                    'ignored',
                    'adult-treats',
                    'solve-first',
                    'adult-cues',
                    'challenge-discipline',
                    'challenge-skills',
                    'fear-or-anxiety-in-new-environments',
                    'stress',
                    'good-behavior-reward',
                    'most-during-training-motivation',
                    'developed',
                    'reliable',
                    'did-you-hear',
                    'practicing-obedience',
                    'adult-ask',
                    'motivation-during-challenges',
                    'optimal-training-moments',
                    'name',
                    'better-profile',
                    'time-spend',
                    'training-journey',
                ],
            ],
            [
                'rule' => new ContentRule(['quiz27'], [new ContentSplitRule('pc_trust', 2)]),
                'questions' => [
                    'age-list-challenge-split',
                    'gender-challenge',
                    'breed-challenge',
                    'dogs-owners',
                    'challenge-motivation-to-start',
                    'goal',
                    'challenge-personalized-goal',
                    'failed-dog-training-attempts',
                    'training-consistency-challenges',
                    'making-dog-training-fun',
                    'you-are-not-alone-split',
                    'adult-refuse',
                    'ignored',
                    'adult-treats',
                    'solve-first',
                    'adult-cues',
                    'challenge-discipline',
                    'challenge-skills',
                    'fear-or-anxiety-in-new-environments',
                    'stress',
                    'good-behavior-reward',
                    'most-during-training-motivation',
                    'developed',
                    'reliable',
                    'did-you-hear',
                    'practicing-obedience',
                    'adult-ask',
                    'motivation-during-challenges',
                    'optimal-training-moments',
                    'name',
                    'better-profile',
                    'time-spend',
                    'training-journey',
                ],
            ],
            [
                'rule' => new ContentRule(['quiz27-puppy'], [new ContentSplitRule('pc_trust', 2)]),
                'questions' => [
                    'gender-challenge',
                    'breed-challenge',
                    'dogs-owners',
                    'challenge-motivation-to-start',
                    'goal',
                    'challenge-personalized-goal',
                    'failed-dog-training-attempts',
                    'training-consistency-challenges',
                    'making-dog-training-fun',
                    'you-are-not-alone-split',
                    'puppy-refuse',
                    'puppy-problems',
                    'challenge-potty',
                    'crate',
                    'solve-first',
                    'puppy-cues',
                    'challenge-discipline',
                    'challenge-skills',
                    'fear-or-anxiety-in-new-environments',
                    'stress',
                    'good-behavior-reward',
                    'most-during-training-motivation',
                    'developed',
                    'reliable',
                    'did-you-hear',
                    'practicing-obedience',
                    'adult-ask',
                    'motivation-during-challenges',
                    'optimal-training-moments',
                    'name',
                    'better-profile',
                    'time-spend',
                    'training-journey',
                ],
            ],
            [
                'rule' => new ContentRule(['quiz27-puppy']),
                'questions' => [
                    'gender-challenge',
                    'breed-challenge',
                    'dogs-owners',
                    'challenge-motivation-to-start',
                    'goal',
                    'challenge-personalized-goal',
                    'failed-dog-training-attempts',
                    'training-consistency-challenges',
                    'making-dog-training-fun',
                    'you-are-not-alone',
                    'puppy-refuse',
                    'puppy-problems',
                    'challenge-potty',
                    'crate',
                    'solve-first',
                    'puppy-cues',
                    'challenge-discipline',
                    'challenge-skills',
                    'fear-or-anxiety-in-new-environments',
                    'stress',
                    'good-behavior-reward',
                    'most-during-training-motivation',
                    'developed',
                    'reliable',
                    'did-you-hear',
                    'practicing-obedience',
                    'adult-ask',
                    'motivation-during-challenges',
                    'optimal-training-moments',
                    'name',
                    'better-profile',
                    'time-spend',
                    'training-journey',
                ],
            ],
            [
                'rule' => new ContentRule([
                    'quiz28',
                    'quiz29',
                    'quiz30',
                    'quiz31',
                    'quiz32',
                ]),
                'questions' => [
                    // Temporary solution with "localized/".
                    // Because pc_MeChallenge split is active at the funnel and it doesn't have localizations
                    // At this point it is impossible to set different templates_dir's for the same quiz slug
                    'localized/age-list-split',
                    'localized/gender',
                    'localized/breed',
                    'localized/dogs-owners',
                    'localized/motivation-to-start',
                    'localized/goal',
                    'localized/personalized-goal',
                    'localized/failed-dog-training-attempts',
                    'localized/training-consistency',
                    'localized/making-dog-training-fun',
                    'localized/you-are-not-alone',
                    'localized/refuse-pay-attention',
                    'localized/ignored',
                    'localized/treats',
                    'localized/solve-first',
                    'localized/cues',
                    'localized/discipline',
                    'localized/skills',
                    'localized/fear-or-anxiety-in-new-environments',
                    'localized/stress',
                    'localized/good-behavior-reward',
                    'localized/most-during-training-motivation',
                    'localized/developed',
                    'localized/reliable',
                    'localized/did-you-hear',
                    'localized/practicing-dog-behaviorist',
                    'localized/ask-expert',
                    'localized/motivation-during-challenges',
                    'localized/optimal-training-moments',
                    'localized/name',
                    'localized/profile',
                    'localized/spend',
                    'localized/training-journey',
                ],
            ],
            [
                'rule' => new ContentRule([
                    'quiz28-puppy',
                    'quiz29-puppy',
                    'quiz30-puppy',
                    'quiz31-puppy',
                    'quiz32-puppy',
                ]),
                'questions' => [
                    // Temporary solution with "localized/".
                    // Because pc_MeChallenge split is active at the funnel and it doesn't have localizations
                    // At this point it is impossible to set different templates_dir's for the same quiz slug
                    'localized/gender',
                    'localized/breed',
                    'localized/dogs-owners',
                    'localized/motivation-to-start',
                    'localized/goal',
                    'localized/personalized-goal',
                    'localized/failed-dog-training-attempts',
                    'localized/training-consistency',
                    'localized/making-dog-training-fun',
                    'localized/you-are-not-alone',
                    'localized/refuse-pay-attention',
                    'localized/puppy-problems',
                    'localized/potty',
                    'localized/crate',
                    'localized/solve-first',
                    'localized/cues',
                    'localized/discipline',
                    'localized/skills',
                    'localized/fear-or-anxiety-in-new-environments',
                    'localized/stress',
                    'localized/good-behavior-reward',
                    'localized/most-during-training-motivation',
                    'localized/developed',
                    'localized/reliable',
                    'localized/did-you-hear',
                    'localized/practicing-dog-behaviorist',
                    'localized/ask-expert',
                    'localized/motivation-during-challenges',
                    'localized/optimal-training-moments',
                    'localized/name',
                    'localized/profile',
                    'localized/spend',
                    'localized/training-journey',
                ],
            ],
            [
                'rule' => new ContentRule(['quiz27'], [new ContentSplitRule('pc_MeChallenge', 2)]),
                'questions' => [
                    'age-list-challenge-split',
                    'gender-challenge',
                    'breed-challenge',
                    'dogs-owners',
                    'motivation-to-start',
                    'goal',
                    'challenge-personalized-goal',
                    'failed-dog-training-attempts',
                    'training-consistency-challenges',
                    'making-dog-training-fun',
                    'you-are-not-alone',
                    'adult-refuse',
                    'ignored',
                    'adult-treats',
                    'solve-first',
                    'adult-cues',
                    'challenge-discipline',
                    'challenge-skills',
                    'fear-or-anxiety-in-new-environments',
                    'stress',
                    'good-behavior-reward',
                    'most-during-training-motivation',
                    'developed',
                    'reliable',
                    'did-you-hear',
                    'practicing-obedience',
                    'adult-ask',
                    'motivation-during-challenges',
                    'optimal-training-moments',
                    'name',
                    'better-profile',
                    'time-spend',
                    'training-journey',
                ],
            ],
            [
                'rule' => new ContentRule(['quiz34']),
                'questions' => [
                    'localized/vagus-gender-reactive',
                    'localized/vagus-age',
                    'localized/vagus-breed',
                    'localized/dogs-owners',
                    'localized/dragging-me-down-reactive',
                    'localized/startles-at-every-little-noise',
                    'localized/barks-at-everything',
                    'localized/upon-seeing-other-dogs',
                    'localized/ignoring-commands-reactive',
                    'localized/appetite-experience',
                    'localized/digestion-troubles',
                    'localized/sleepy-changes',
                    'localized/random-behavior-changes',
                    'localized/react-come-home',
                    'localized/solve-first',
                    'localized/vagus-scared',
                    'localized/vagus-stress-triggers',
                    'localized/vagus-aggressive',
                    'localized/vagus-aggression-triggers',
                    'localized/gets-exited',
                    'localized/vagus-excitement-triggers',
                    'localized/react-identified-dogs-behaviour',
                    'localized/ever-heard-vagus-nerve',
                    'localized/vagus-in-dogs-anyway',
                    'localized/challenge-motivation-to-start',
                    'localized/vagus-challenge-goal',
                    'localized/did-you-hear',
                    'localized/practicing',
                    'localized/vagus-name',
                    'localized/vagus-profile-react',
                    'localized/love-spend',
                    'localized/vagus-graph-reactive',
                ],
            ],
            [
                'rule' => new ContentRule(
                    ['quiz34-puppy'],
                ),
                'questions' => [
                    'localized/vagus-breed',
                    'localized/dogs-owners',
                    'localized/dragging-me-down-reactive',
                    'localized/startles-at-every-little-noise',
                    'localized/barks-at-everything',
                    'localized/upon-seeing-other-dogs',
                    'localized/ignoring-commands-reactive',
                    'localized/appetite-experience',
                    'localized/digestion-troubles',
                    'localized/sleepy-changes',
                    'localized/random-behavior-changes',
                    'localized/react-come-home',
                    'localized/solve-first',
                    'localized/vagus-scared',
                    'localized/vagus-stress-triggers',
                    'localized/vagus-aggressive',
                    'localized/vagus-aggression-triggers',
                    'localized/gets-exited',
                    'localized/vagus-excitement-triggers',
                    'localized/react-identified-dogs-behaviour',
                    'localized/ever-heard-vagus-nerve',
                    'localized/vagus-in-dogs-anyway',
                    'localized/challenge-motivation-to-start',
                    'localized/vagus-challenge-goal',
                    'localized/did-you-hear',
                    'localized/practicing',
                    'localized/vagus-name',
                    'localized/vagus-profile-react',
                    'localized/love-spend',
                    'localized/vagus-graph-reactive',
                ],
            ],
            [
                'rule' => new ContentRule(
                    ['quiz34-puppy'],
                    [new ContentSplitRule('pc_joinover')]
                ),
                'questions' => [
                    'vagus-breed',
                    'dog-owners-chosen-us',
                    'dragging-me-down-reactive',
                    'startles-at-every-little-noise',
                    'barks-at-everything',
                    'upon-seeing-other-dogs',
                    'ignoring-commands-reactive',
                    'appetite-experience',
                    'digestion-troubles',
                    'sleepy-changes',
                    'random-behavior-changes',
                    'react-come-home',
                    'solve-first',
                    'react-scared',
                    'vagus-stress-triggers',
                    'react-aggressive',
                    'vagus-aggression-triggers',
                    'gets-exited',
                    'vagus-excitement-triggers',
                    'react-identified-dogs-behaviour',
                    'ever-heard-vagus-nerve',
                    'vagus-in-dogs-anyway',
                    'challenge-motivation-to-start',
                    'vagus-challenge-goal',
                    'did-you-hear',
                    'practicing',
                    'join-dog-owners',
                    'vagus-name',
                    'vagus-profile-react',
                    'love-spend',
                    'vagus-graph-reactive',
                ],
            ],
            [
                'rule' => new ContentRule(['quiz34'], [new ContentSplitRule('pc_trust', 2)]),
                'questions' => [
                    'vagus-gender-reactive',
                    'vagus-age',
                    'vagus-breed',
                    'dogs-owners',
                    'dragging-me-down-reactive',
                    'startles-at-every-little-noise',
                    'barks-at-everything',
                    'upon-seeing-other-dogs',
                    'ignoring-commands-reactive',
                    'you-are-not-alone-split',
                    'appetite-experience',
                    'digestion-troubles',
                    'sleepy-changes',
                    'random-behavior-changes',
                    'react-come-home',
                    'solve-first',
                    'react-scared',
                    'vagus-stress-triggers',
                    'react-aggressive',
                    'vagus-aggression-triggers',
                    'gets-exited',
                    'vagus-excitement-triggers',
                    'react-identified-dogs-behaviour',
                    'ever-heard-vagus-nerve',
                    'vagus-in-dogs-anyway',
                    'challenge-motivation-to-start',
                    'vagus-challenge-goal',
                    'did-you-hear',
                    'practicing-obedience',
                    'vagus-name',
                    'vagus-profile-react',
                    'love-spend',
                    'vagus-graph-reactive',
                ],
            ],
            [
                'rule' => new ContentRule(['quiz34-puppy'], [new ContentSplitRule('pc_trust', 2)]),
                'questions' => [
                    'vagus-breed',
                    'dogs-owners',
                    'dragging-me-down-reactive',
                    'startles-at-every-little-noise',
                    'barks-at-everything',
                    'upon-seeing-other-dogs',
                    'ignoring-commands-reactive',
                    'you-are-not-alone-split',
                    'appetite-experience',
                    'digestion-troubles',
                    'sleepy-changes',
                    'random-behavior-changes',
                    'react-come-home',
                    'solve-first',
                    'react-scared',
                    'vagus-stress-triggers',
                    'react-aggressive',
                    'vagus-aggression-triggers',
                    'gets-exited',
                    'vagus-excitement-triggers',
                    'react-identified-dogs-behaviour',
                    'ever-heard-vagus-nerve',
                    'vagus-in-dogs-anyway',
                    'challenge-motivation-to-start',
                    'vagus-challenge-goal',
                    'did-you-hear',
                    'practicing-obedience',
                    'vagus-name',
                    'vagus-profile-react',
                    'love-spend',
                    'vagus-graph-reactive',
                ],
            ],
            [
                'rule' => new ContentRule(['quiz40']),
                'questions' => [
                    'vagus-gender',
                    'vagus-age',
                    'vagus-breed',
                    'upon-seeing-other-dogs',
                    'dragging-me-down-the-street',
                    'startles-at-every-little-noise',
                    'restless-and-unable-to-switch-off',
                    'ignores-commands-completely',
                    'imbalanced-vagus-nerve',
                    'balanced-vagus-nerve',
                    'vagus-satisfied',
                    'react-come-home',
                    'solve-first',
                    'obedience-changed-over-time',
                    'dont-understand-my-dog-misbehavior',
                    'ever-heard-vagus-nerve',
                    'vagus-in-dogs-anyway',
                    'react-scared',
                    'vagus-stress-triggers',
                    'react-aggressive',
                    'vagus-aggression-triggers',
                    'developed',
                    'triggers-vagus',
                    'did-you-hear',
                    'practicing',
                    'adult-ask',
                    'vagus-name',
                    'vagus-profile',
                    'time-spend',
                    'vagus-graph',
                ],
            ],
            [
                'rule' => new ContentRule(
                    ['quiz40-puppy'],
                ),
                'questions' => [
                    'vagus-breed',
                    'upon-seeing-other-dogs',
                    'dragging-me-down-the-street',
                    'startles-at-every-little-noise',
                    'restless-and-unable-to-switch-off',
                    'ignores-commands-completely',
                    'imbalanced-vagus-nerve',
                    'balanced-vagus-nerve',
                    'vagus-satisfied',
                    'react-come-home',
                    'solve-first',
                    'obedience-changed-over-time',
                    'dont-understand-my-dog-misbehavior',
                    'ever-heard-vagus-nerve',
                    'vagus-in-dogs-anyway',
                    'react-scared',
                    'vagus-stress-triggers',
                    'react-aggressive',
                    'vagus-aggression-triggers',
                    'developed',
                    'triggers-vagus',
                    'did-you-hear',
                    'practicing',
                    'adult-ask',
                    'vagus-name',
                    'vagus-profile',
                    'time-spend',
                    'vagus-graph',
                ],
            ],
            [
                'rule' => new ContentRule([
                    'quiz41', // EN
                    'quiz42', // DE
                    'quiz43', // FR
                    'quiz44', // ES_LA
                    'quiz45', // PT_BR
                    'quiz46', // IT
                ]),
                'questions' => [
                    'localized/age-list-split',
                    'localized/gender',
                    'localized/breed',
                    'localized/spaying-neutering-your-dog',
                    'localized/dog-food-choice-priority',
                    'localized/dogs-owners',
                    'localized/motivation-for-dog-feeding-guide',
                    'localized/main-goal-for-dog-feeding-guide',
                    'localized/personalized-goal',
                    'localized/dog-eats-things-off-ground',
                    'localized/concerned-about-dog-weight',
                    'localized/worried-about-long-term-dog-diet',
                    'localized/you-are-not-alone',
                    'localized/choose-dog-body-type',
                    'localized/dog-appetite-level',
                    'localized/dog-main-diet-type',
                    'localized/dog-table-food-habit',
                    'localized/feeding-habits-shape-behavior',
                    'localized/dog-health-issues-check',
                    'localized/dog-tummy-issues-check',
                    'localized/dog-behavior-feeding-issues',
                    'localized/developed',
                    'localized/did-you-hear',
                    'localized/practicing-dog-behaviorist',
                    'localized/ask-expert',
                    'localized/name',
                    'localized/profile-nutrition',
                    'localized/training-journey',
                ],
            ],
            [
                'rule' => new ContentRule([
                    'quiz41-puppy', // EN
                    'quiz42-puppy', // DE
                    'quiz43-puppy', // FR
                    'quiz44-puppy', // ES_LA
                    'quiz45-puppy', // PT_BR
                    'quiz46-puppy', // IT
                ]),
                'questions' => [
                    'localized/gender',
                    'localized/breed',
                    'localized/spaying-neutering-your-dog',
                    'localized/dog-food-choice-priority',
                    'localized/dogs-owners',
                    'localized/motivation-for-dog-feeding-guide',
                    'localized/main-goal-for-dog-feeding-guide',
                    'localized/personalized-goal',
                    'localized/dog-eats-things-off-ground',
                    'localized/concerned-about-dog-weight',
                    'localized/worried-about-long-term-dog-diet',
                    'localized/you-are-not-alone',
                    'localized/choose-dog-body-type',
                    'localized/dog-appetite-level',
                    'localized/dog-main-diet-type',
                    'localized/dog-table-food-habit',
                    'localized/feeding-habits-shape-behavior',
                    'localized/dog-health-issues-check',
                    'localized/dog-tummy-issues-check',
                    'localized/dog-behavior-feeding-issues',
                    'localized/developed',
                    'localized/did-you-hear',
                    'localized/practicing-dog-behaviorist',
                    'localized/ask-expert',
                    'localized/name',
                    'localized/profile-nutrition',
                    'localized/training-journey',
                ],
            ],
        ];

        return QuizContentHelper::resolveSplitContent($map, $quiz, $splitValues);
    }

    private const PARAMS = [
        'quizMain' => [
            'filename' => 'compliance-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-bank-adult',
            'locale' => UserLocaleEnum::EN,
            'isPuppyFunnel' => false,
            'funnelName' => FunnelNameEnum::COMPLIANCE,
        ],
        'quizMainPuppy' => [
            'filename' => 'compliance-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-bank-puppy',
            'locale' => UserLocaleEnum::EN,
            'isPuppyFunnel' => true,
            'funnelName' => FunnelNameEnum::COMPLIANCE,
        ],
        'quiz1' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-master',
            'locale' => UserLocaleEnum::EN,
            'isPuppyFunnel' => false,
            'funnelName' => FunnelNameEnum::MASTER,
        ],
        'quiz2' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-love',
            'locale' => UserLocaleEnum::EN,
            'isPuppyFunnel' => false,
            'funnelName' => FunnelNameEnum::LOVE,
        ],
        'quiz2-puppy' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-love',
            'locale' => UserLocaleEnum::EN,
            'isPuppyFunnel' => true,
            'funnelName' => FunnelNameEnum::LOVE,
        ],
        'quiz1-puppy' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-master',
            'locale' => UserLocaleEnum::EN,
            'isPuppyFunnel' => true,
            'funnelName' => FunnelNameEnum::MASTER,
        ],
        'quiz3' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-fr-obedience',
            'templates_dir' => 'FR',
            'locale' => UserLocaleEnum::FR,
            'isPuppyFunnel' => false,
            'funnelName' => FunnelNameEnum::OBEDIENCE,
        ],
        'quiz3-puppy-fr' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-fr-obedience',
            'templates_dir' => 'FR',
            'locale' => UserLocaleEnum::FR,
            'isPuppyFunnel' => true,
            'funnelName' => FunnelNameEnum::OBEDIENCE,
        ],
        'quiz6' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-punish',
            'locale' => UserLocaleEnum::EN,
            'isPuppyFunnel' => false,
            'funnelName' => FunnelNameEnum::PUNISHMENT,
        ],
        'quiz6-puppy-pain' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-punish',
            'locale' => UserLocaleEnum::EN,
            'isPuppyFunnel' => true,
            'funnelName' => FunnelNameEnum::PUNISHMENT,
        ],
        'quiz7' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-de-obedience',
            'templates_dir' => 'DE',
            'locale' => UserLocaleEnum::DE,
            'isPuppyFunnel' => false,
            'funnelName' => FunnelNameEnum::OBEDIENCE,
        ],
        'quiz7-puppy-de' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-de-obedience',
            'templates_dir' => 'DE',
            'locale' => UserLocaleEnum::DE,
            'isPuppyFunnel' => true,
            'funnelName' => FunnelNameEnum::OBEDIENCE,
        ],
        'quiz8' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-pt-obedience',
            'templates_dir' => 'PT',
            'locale' => UserLocaleEnum::PT,
            'isPuppyFunnel' => false,
            'funnelName' => FunnelNameEnum::OBEDIENCE,
        ],
        'quiz8-puppy-pt' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-pt-obedience',
            'templates_dir' => 'PT',
            'locale' => UserLocaleEnum::PT,
            'isPuppyFunnel' => true,
            'funnelName' => FunnelNameEnum::OBEDIENCE,
        ],
        'quiz9' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-obedience',
            'locale' => UserLocaleEnum::EN,
            'isPuppyFunnel' => false,
            'funnelName' => FunnelNameEnum::OBEDIENCE,
        ],
        'quiz9-puppy' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-obedience',
            'locale' => UserLocaleEnum::EN,
            'isPuppyFunnel' => true,
            'funnelName' => FunnelNameEnum::OBEDIENCE,
        ],
        'quiz10' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-spanish',
            'templates_dir' => 'ES',
            'locale' => UserLocaleEnum::ES,
            'isPuppyFunnel' => false,
            'funnelName' => FunnelNameEnum::OBEDIENCE,
        ],
        'quiz10-puppy-es' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-spanish',
            'templates_dir' => 'ES',
            'locale' => UserLocaleEnum::ES,
            'isPuppyFunnel' => true,
            'funnelName' => FunnelNameEnum::OBEDIENCE,
        ],
        'quiz11' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-anxiety',
            'locale' => UserLocaleEnum::EN,
            'isPuppyFunnel' => false,
            'funnelName' => FunnelNameEnum::ANXIETY,
        ],
        'quiz11-puppy' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-anxiety',
            'locale' => UserLocaleEnum::EN,
            'isPuppyFunnel' => true,
            'funnelName' => FunnelNameEnum::ANXIETY,
        ],
        'quiz13' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-spanish',
            'templates_dir' => 'ES',
            'locale' => UserLocaleEnum::ES,
            'isPuppyFunnel' => false,
            'funnelName' => FunnelNameEnum::PUNISHMENT,
        ],
        'quiz13-puppy-es' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-spanish',
            'templates_dir' => 'ES',
            'locale' => UserLocaleEnum::ES,
            'isPuppyFunnel' => true,
            'funnelName' => FunnelNameEnum::PUNISHMENT,
        ],
        'quiz14' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-de-obedience',
            'templates_dir' => 'DE',
            'locale' => UserLocaleEnum::DE,
            'isPuppyFunnel' => false,
            'funnelName' => FunnelNameEnum::PUNISHMENT,
        ],
        'quiz14-puppy-de' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-de-obedience',
            'templates_dir' => 'DE',
            'locale' => UserLocaleEnum::DE,
            'isPuppyFunnel' => true,
            'funnelName' => FunnelNameEnum::PUNISHMENT,
        ],
        'quiz15' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-fr-obedience',
            'templates_dir' => 'FR',
            'locale' => UserLocaleEnum::FR,
            'isPuppyFunnel' => false,
            'funnelName' => FunnelNameEnum::PUNISHMENT,
        ],
        'quiz15-puppy-fr' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-fr-obedience',
            'templates_dir' => 'FR',
            'locale' => UserLocaleEnum::FR,
            'isPuppyFunnel' => true,
            'funnelName' => FunnelNameEnum::PUNISHMENT,
        ],
        'quiz16' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-pt-obedience',
            'templates_dir' => 'PT',
            'locale' => UserLocaleEnum::PT,
            'isPuppyFunnel' => false,
            'funnelName' => FunnelNameEnum::PUNISHMENT,
        ],
        'quiz16-puppy-pt' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-pt-obedience',
            'templates_dir' => 'PT',
            'locale' => UserLocaleEnum::PT,
            'isPuppyFunnel' => true,
            'funnelName' => FunnelNameEnum::PUNISHMENT,
        ],
        'quiz17' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-reactivity',
            'locale' => UserLocaleEnum::EN,
            'isPuppyFunnel' => false,
            'funnelName' => FunnelNameEnum::REACTIVITY,
        ],
        'quiz17-puppy-reactpun' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-reactivity',
            'locale' => UserLocaleEnum::EN,
            'isPuppyFunnel' => true,
            'funnelName' => FunnelNameEnum::REACTIVITY,
        ],
        'quiz17-puppy' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Reactive',
            'result_page' => 'funnel-reactivity',
            'locale' => UserLocaleEnum::EN,
            'isPuppyFunnel' => true,
            'funnelName' => FunnelNameEnum::REACTIVITY,
        ],
        'quiz17-puppy-magic' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Reactive',
            'result_page' => 'funnel-reactivity',
            'locale' => UserLocaleEnum::EN,
            'isPuppyFunnel' => true,
            'funnelName' => FunnelNameEnum::REACTIVITY,
        ],
        'quiz18' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-de-reactivity',
            'templates_dir' => 'DE',
            'locale' => UserLocaleEnum::DE,
            'isPuppyFunnel' => false,
            'funnelName' => FunnelNameEnum::REACTIVITY,
        ],
        'quiz18-puppy-de' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-de-reactivity',
            'templates_dir' => 'DE',
            'locale' => UserLocaleEnum::DE,
            'isPuppyFunnel' => true,
            'funnelName' => FunnelNameEnum::REACTIVITY,
        ],
        'quiz19' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-fr-reactivity',
            'templates_dir' => 'FR',
            'locale' => UserLocaleEnum::FR,
            'isPuppyFunnel' => false,
            'funnelName' => FunnelNameEnum::REACTIVITY,
        ],
        'quiz19-puppy-fr' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-fr-reactivity',
            'templates_dir' => 'FR',
            'locale' => UserLocaleEnum::FR,
            'isPuppyFunnel' => true,
            'funnelName' => FunnelNameEnum::REACTIVITY,
        ],
        'quiz20' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Reactive',
            'result_page' => 'funnel-es-reactivity',
            'templates_dir' => 'ES',
            'locale' => UserLocaleEnum::ES,
            'isPuppyFunnel' => false,
            'funnelName' => FunnelNameEnum::REACTIVITY,
        ],
        'quiz20-puppy-es' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Reactive',
            'result_page' => 'funnel-es-reactivity',
            'templates_dir' => 'ES',
            'locale' => UserLocaleEnum::ES,
            'isPuppyFunnel' => true,
            'funnelName' => FunnelNameEnum::REACTIVITY,
        ],
        'quiz21' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-pt-reactivity',
            'templates_dir' => 'PT',
            'locale' => UserLocaleEnum::PT,
            'isPuppyFunnel' => false,
            'funnelName' => FunnelNameEnum::REACTIVITY,
        ],
        'quiz21-puppy-pt' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-pt-reactivity',
            'templates_dir' => 'PT',
            'locale' => UserLocaleEnum::PT,
            'isPuppyFunnel' => true,
            'funnelName' => FunnelNameEnum::REACTIVITY,
        ],
        'quiz22' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-separation',
            'locale' => UserLocaleEnum::EN,
            'isPuppyFunnel' => false,
            'funnelName' => FunnelNameEnum::SEPARATION,
        ],
        'quiz22-puppy' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-separation',
            'locale' => UserLocaleEnum::EN,
            'isPuppyFunnel' => true,
            'funnelName' => FunnelNameEnum::SEPARATION,
        ],
        'quiz23' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-aggression',
            'locale' => UserLocaleEnum::EN,
            'isPuppyFunnel' => false,
            'funnelName' => FunnelNameEnum::AGGRESSION,
        ],
        'quiz23-puppy' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-aggression',
            'locale' => UserLocaleEnum::EN,
            'isPuppyFunnel' => true,
            'funnelName' => FunnelNameEnum::AGGRESSION,
        ],
        'quiz24' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-it-punish',
            'templates_dir' => 'IT',
            'locale' => UserLocaleEnum::IT,
            'isPuppyFunnel' => false,
            'funnelName' => FunnelNameEnum::PUNISHMENT,
        ],
        'quiz24-puppy-it' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-it-punish',
            'templates_dir' => 'IT',
            'locale' => UserLocaleEnum::IT,
            'isPuppyFunnel' => true,
            'funnelName' => FunnelNameEnum::PUNISHMENT,
        ],
        'quiz25' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-it-reactivity',
            'templates_dir' => 'IT',
            'locale' => UserLocaleEnum::IT,
            'isPuppyFunnel' => false,
            'funnelName' => FunnelNameEnum::REACTIVITY,
        ],
        'quiz25-puppy-it' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-it-reactivity',
            'templates_dir' => 'IT',
            'locale' => UserLocaleEnum::IT,
            'isPuppyFunnel' => true,
            'funnelName' => FunnelNameEnum::REACTIVITY,
        ],
        'quiz26' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-it-obedience',
            'templates_dir' => 'IT',
            'locale' => UserLocaleEnum::IT,
            'isPuppyFunnel' => false,
            'funnelName' => FunnelNameEnum::OBEDIENCE,
        ],
        'quiz26-puppy-it' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-it-obedience',
            'templates_dir' => 'IT',
            'locale' => UserLocaleEnum::IT,
            'isPuppyFunnel' => true,
            'funnelName' => FunnelNameEnum::OBEDIENCE,
        ],
        'quiz27' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-challenge',
            'locale' => UserLocaleEnum::EN,
            'isPuppyFunnel' => false,
            'funnelName' => FunnelNameEnum::CHALLENGE,
        ],
        'quiz27-puppy' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-challenge',
            'locale' => UserLocaleEnum::EN,
            'isPuppyFunnel' => true,
            'funnelName' => FunnelNameEnum::CHALLENGE,
        ],
        'quiz28' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-challenge',
            'locale' => UserLocaleEnum::DE,
            'isPuppyFunnel' => false,
            'funnelName' => FunnelNameEnum::CHALLENGE,
        ],
        'quiz28-puppy' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-challenge',
            'locale' => UserLocaleEnum::DE,
            'isPuppyFunnel' => true,
            'funnelName' => FunnelNameEnum::CHALLENGE,
        ],
        'quiz29' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-challenge',
            'locale' => UserLocaleEnum::FR,
            'isPuppyFunnel' => false,
            'funnelName' => FunnelNameEnum::CHALLENGE,
        ],
        'quiz29-puppy' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-challenge',
            'locale' => UserLocaleEnum::FR,
            'isPuppyFunnel' => true,
            'funnelName' => FunnelNameEnum::CHALLENGE,
        ],
        'quiz30' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-challenge',
            'locale' => UserLocaleEnum::ES_LA,
            'isPuppyFunnel' => false,
            'funnelName' => FunnelNameEnum::CHALLENGE,
        ],
        'quiz30-puppy' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-challenge',
            'locale' => UserLocaleEnum::ES_LA,
            'isPuppyFunnel' => true,
            'funnelName' => FunnelNameEnum::CHALLENGE,
        ],
        'quiz31' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-challenge',
            'locale' => UserLocaleEnum::PT_BR,
            'isPuppyFunnel' => false,
            'funnelName' => FunnelNameEnum::CHALLENGE,
        ],
        'quiz31-puppy' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-challenge',
            'locale' => UserLocaleEnum::PT_BR,
            'isPuppyFunnel' => true,
            'funnelName' => FunnelNameEnum::CHALLENGE,
        ],
        'quiz32' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-challenge',
            'locale' => UserLocaleEnum::IT,
            'isPuppyFunnel' => false,
            'funnelName' => FunnelNameEnum::CHALLENGE,
        ],
        'quiz32-puppy' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-challenge',
            'locale' => UserLocaleEnum::IT,
            'isPuppyFunnel' => true,
            'funnelName' => FunnelNameEnum::CHALLENGE,
        ],
        'quiz34' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-vagus',
            'locale' => UserLocaleEnum::EN,
            'isPuppyFunnel' => false,
            'funnelName' => FunnelNameEnum::VAGUS,
        ],
        'quiz34-puppy' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-vagus',
            'locale' => UserLocaleEnum::EN,
            'isPuppyFunnel' => true,
            'funnelName' => FunnelNameEnum::VAGUS,
        ],
        'quiz35' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-love',
            'locale' => UserLocaleEnum::DE,
            'isPuppyFunnel' => false,
            'funnelName' => FunnelNameEnum::LOVE,
        ],
        'quiz35-puppy' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-love',
            'locale' => UserLocaleEnum::DE,
            'isPuppyFunnel' => true,
            'funnelName' => FunnelNameEnum::LOVE,
        ],
        'quiz36' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-love',
            'locale' => UserLocaleEnum::FR,
            'isPuppyFunnel' => false,
            'funnelName' => FunnelNameEnum::LOVE,
        ],
        'quiz36-puppy' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-love',
            'locale' => UserLocaleEnum::FR,
            'isPuppyFunnel' => true,
            'funnelName' => FunnelNameEnum::LOVE,
        ],
        'quiz37' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-love',
            'locale' => UserLocaleEnum::ES_LA,
            'isPuppyFunnel' => false,
            'funnelName' => FunnelNameEnum::LOVE,
        ],
        'quiz37-puppy' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-love',
            'locale' => UserLocaleEnum::ES_LA,
            'isPuppyFunnel' => true,
            'funnelName' => FunnelNameEnum::LOVE,
        ],
        'quiz38' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-love',
            'locale' => UserLocaleEnum::PT_BR,
            'isPuppyFunnel' => false,
            'funnelName' => FunnelNameEnum::LOVE,
        ],
        'quiz38-puppy' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-love',
            'locale' => UserLocaleEnum::PT_BR,
            'isPuppyFunnel' => true,
            'funnelName' => FunnelNameEnum::LOVE,
        ],
        'quiz39' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-love',
            'locale' => UserLocaleEnum::IT,
            'isPuppyFunnel' => false,
            'funnelName' => FunnelNameEnum::LOVE,
        ],
        'quiz39-puppy' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-love',
            'locale' => UserLocaleEnum::IT,
            'isPuppyFunnel' => true,
            'funnelName' => FunnelNameEnum::LOVE,
        ],
        'quiz40' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-vagus-anxiety',
            'locale' => UserLocaleEnum::EN,
            'isPuppyFunnel' => false,
            'funnelName' => FunnelNameEnum::VAGUS_ANXIETY,
        ],
        'quiz40-puppy' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-vagus-anxiety',
            'locale' => UserLocaleEnum::EN,
            'isPuppyFunnel' => true,
            'funnelName' => FunnelNameEnum::VAGUS_ANXIETY,
        ],
        'quiz41' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-nutrition',
            'locale' => UserLocaleEnum::EN,
            'isPuppyFunnel' => false,
            'funnelName' => FunnelNameEnum::NUTRITION,
        ],
        'quiz41-puppy' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-nutrition',
            'locale' => UserLocaleEnum::EN,
            'isPuppyFunnel' => true,
            'funnelName' => FunnelNameEnum::NUTRITION,
        ],
        'quiz42' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-nutrition',
            'locale' => UserLocaleEnum::DE,
            'isPuppyFunnel' => false,
            'funnelName' => FunnelNameEnum::NUTRITION,
        ],
        'quiz42-puppy' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-nutrition',
            'locale' => UserLocaleEnum::DE,
            'isPuppyFunnel' => true,
            'funnelName' => FunnelNameEnum::NUTRITION,
        ],
        'quiz43' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-nutrition',
            'locale' => UserLocaleEnum::FR,
            'isPuppyFunnel' => false,
            'funnelName' => FunnelNameEnum::NUTRITION,
        ],
        'quiz43-puppy' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-nutrition',
            'locale' => UserLocaleEnum::FR,
            'isPuppyFunnel' => true,
            'funnelName' => FunnelNameEnum::NUTRITION,
        ],
        'quiz44' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-nutrition',
            'locale' => UserLocaleEnum::ES_LA,
            'isPuppyFunnel' => false,
            'funnelName' => FunnelNameEnum::NUTRITION,
        ],
        'quiz44-puppy' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-nutrition',
            'locale' => UserLocaleEnum::ES_LA,
            'isPuppyFunnel' => true,
            'funnelName' => FunnelNameEnum::NUTRITION,
        ],
        'quiz45' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-nutrition',
            'locale' => UserLocaleEnum::PT_BR,
            'isPuppyFunnel' => false,
            'funnelName' => FunnelNameEnum::NUTRITION,
        ],
        'quiz45-puppy' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-nutrition',
            'locale' => UserLocaleEnum::PT_BR,
            'isPuppyFunnel' => true,
            'funnelName' => FunnelNameEnum::NUTRITION,
        ],
        'quiz46' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-nutrition',
            'locale' => UserLocaleEnum::IT,
            'isPuppyFunnel' => false,
            'funnelName' => FunnelNameEnum::NUTRITION,
        ],
        'quiz46-puppy' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-nutrition',
            'locale' => UserLocaleEnum::IT,
            'isPuppyFunnel' => true,
            'funnelName' => FunnelNameEnum::NUTRITION,
        ],
        'quiz34' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-vagus',
            'locale' => UserLocaleEnum::EN,
            'isPuppyFunnel' => false,
            'funnelName' => FunnelNameEnum::VAGUS,
        ],
        'quiz34-puppy' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-vagus',
            'locale' => UserLocaleEnum::EN,
            'isPuppyFunnel' => true,
            'funnelName' => FunnelNameEnum::VAGUS,
        ],
        'quiz34' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-vagus',
            'locale' => UserLocaleEnum::EN,
            'isPuppyFunnel' => false,
            'funnelName' => FunnelNameEnum::VAGUS,
        ],
        'quiz34-puppy' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-vagus',
            'locale' => UserLocaleEnum::EN,
            'isPuppyFunnel' => true,
            'funnelName' => FunnelNameEnum::VAGUS,
        ],
        'quiz34' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-vagus',
            'locale' => UserLocaleEnum::EN,
            'isPuppyFunnel' => false,
            'funnelName' => FunnelNameEnum::VAGUS,
        ],
        'quiz34-puppy' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-vagus',
            'locale' => UserLocaleEnum::EN,
            'isPuppyFunnel' => true,
            'funnelName' => FunnelNameEnum::VAGUS,
        ],
        'quiz34' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-vagus',
            'locale' => UserLocaleEnum::EN,
            'isPuppyFunnel' => false,
            'funnelName' => FunnelNameEnum::VAGUS,
        ],
        'quiz34-puppy' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-vagus',
            'locale' => UserLocaleEnum::EN,
            'isPuppyFunnel' => true,
            'funnelName' => FunnelNameEnum::VAGUS,
        ],
        'quiz34' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-vagus',
            'locale' => UserLocaleEnum::EN,
            'isPuppyFunnel' => false,
            'funnelName' => FunnelNameEnum::VAGUS,
        ],
        'quiz34-puppy' => [
            'filename' => 'subscription-questions',
            'title' => 'Paw Champ | Quiz',
            'result_page' => 'funnel-vagus',
            'locale' => UserLocaleEnum::EN,
            'isPuppyFunnel' => true,
            'funnelName' => FunnelNameEnum::VAGUS,
        ],
    ];

    /**
     * Return static quiz content.
     */
    public static function getContent(string $quiz, array $splitValues = []): array
    {
        $content = self::PARAMS[$quiz] ?? [];
        $splitContent = self::getSplitContent($quiz, $splitValues);

        return array_merge($content, $splitContent);
    }
}
