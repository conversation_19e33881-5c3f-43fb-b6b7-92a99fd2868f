<?php

declare(strict_types=1);

namespace App\Application\Funnel\Rate;

use App\Infrastructure\Exception\AbstractLoggableException;
use App\Infrastructure\ExternalService\TrustPilot\TrustPilotAdapter;
use App\Infrastructure\ExternalService\Zendesk\ZendeskAdapter;
use App\Infrastructure\Repository\UserRepository;
use App\Infrastructure\Tool\UserLocaleResolver;
use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Serializer\SerializerInterface;
use Throwable;

use function Symfony\Component\String\u;

class RateController extends AbstractController
{
    public function __construct(
        private readonly TrustPilotAdapter $trustPilotAdapter,
        private readonly UserLocaleResolver $userLocaleResolver,
        private readonly UserRepository $userRepository,
        private readonly SerializerInterface $serializer,
        private readonly ZendeskAdapter $zendeskAdapter,
        private readonly LoggerInterface $logger,
    ) {
    }

    #[Route(path: '/rate', name: 'rate')]
    public function rate(Request $request, array $fallbacks = []): Response
    {
        try {
            $ajaxResponse = $this->handleXmlHttpRequest($request);
            if ($ajaxResponse instanceof Response) {
                return $ajaxResponse;
            }

            $params = $this->serializer->denormalize($request->query->all(), RateParamsDTO::class);

            $trustPilotLink = $this->getTrustPilotLink($params);
        } catch (Throwable $e) {
            $context = ['data' => $request->getContent()];

            if ($e instanceof AbstractLoggableException) {
                $context = [...$context, ...$e->getContext()];
            }

            $this->logger->error('Rate action error', ['exception' => $e, 'context' => $context]);

            return $this->json(
                ['status' => 'error', 'message' => 'Something went wrong'],
                Response::HTTP_BAD_REQUEST,
            );
        }

        return $this->render(
            'subscription/pages/product/rate.html.twig',
            [
                'hasTranslations' => true,
                'trustpilotLink' => $trustPilotLink,
                'params' => $params,
                'fallbacks' => $fallbacks,
            ]
        );
    }

    #[Route(path: '/rate-expert', name: 'rate_expert')]
    public function rateExpert(Request $request): Response
    {
        return $this->rate($request, ['group' => 'dog_experts']);
    }

    #[Route(path: '/rate-chat-expert', name: 'rate_chat_expert')]
    public function rateChatExpert(Request $request): Response
    {
        return $this->rate($request, ['group' => 'support']);
    }

    private function getTrustPilotLink(RateParamsDTO $params): string
    {
        $user = null;
        if (!is_null($params->email)) {
            $user = $this->userRepository->getSupportUserByEmail($params->email);
        }
        if (is_null($user) && !is_null($params->productEmail)) {
            $user = $this->userRepository->getSupportUserByEmail($params->productEmail);
        }

        $defaultLink = 'https://www.trustpilot.com/evaluate/paw-champ.com';
        if (is_null($user)) {
            return $defaultLink;
        }

        $tags = [];
        if (!is_null($params->channel)) {
            $tags[] = 'rate_channel_' . u($params->channel)->snake()->toString();
        }
        if (!is_null($params->group)) {
            $tags[] = 'expert_name_' . u($params->group)->snake()->toString();
        }
        if (!is_null($params->expertName)) {
            $tags[] = 'rate_' . u($params->expertName)->snake()->toString();
        }

        $locale = $this->userLocaleResolver->getPreferredLanguage();
        try {
            return $this->trustPilotAdapter->getInvitationLink($user, $locale->IETF(), $tags);
        } catch (Throwable) {
            return $defaultLink;
        }
    }

    private function handleXmlHttpRequest(Request $request): bool | Response
    {
        if (!$request->isXmlHttpRequest()) {
            return false;
        }

        $token = $request->getPayload()->get('csrf_token');
        if (!$this->isCsrfTokenValid('submit_reaction', $token)) {
            return $this->json('error', Response::HTTP_BAD_REQUEST);
        }

        /** @var RateReactionDTO $data */
        $data = $this->serializer->deserialize($request->getContent(), RateReactionDTO::class, 'json');
        if (is_null($data->ticketID)) {
            return $this->json('ok');
        }

        $zdTagPrefix = 'rate_us_';
        $zdTag = $zdTagPrefix . $data->reaction->value;
        $this->zendeskAdapter->addTagsToTicket((int) $data->ticketID, [$zdTag]);

        return $this->json('ok');
    }
}
