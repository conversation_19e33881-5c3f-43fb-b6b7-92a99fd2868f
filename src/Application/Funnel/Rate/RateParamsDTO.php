<?php

declare(strict_types=1);

namespace App\Application\Funnel\Rate;

use Symfony\Component\Serializer\Annotation\SerializedName;

class RateParamsDTO
{
    public ?string $email = null;
    #[SerializedName('product_email')]
    public ?string $productEmail = null;
    #[SerializedName('communication_channel')]
    public ?string $channel = null;
    #[SerializedName('ticket_group')]
    public ?string $group = null;
    #[SerializedName('support_agent_name')]
    public ?string $expertName = null;
}
