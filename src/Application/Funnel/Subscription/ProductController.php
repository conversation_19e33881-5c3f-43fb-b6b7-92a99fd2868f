<?php

declare(strict_types=1);

namespace App\Application\Funnel\Subscription;

use App\Domain\Enum\UserLocaleEnum;
use App\Application\Content\DeviceAndBreedContent;
use App\Application\Content\SubscriptionUpsellContent;
use App\Application\Messenger\Event\User\LoggedInEvent;
use App\Domain\Entity\Upsell;
use App\Domain\Entity\User;
use App\Domain\Enum\FunnelNameEnum;
use App\Infrastructure\ExternalService\LMS\Exception\LMSException;
use App\Infrastructure\ExternalService\LMS\LearnWorldsApiV2Adapter;
use App\Infrastructure\Repository\UpsellRepository;
use App\Infrastructure\Repository\UserQuizRepository;
use App\Infrastructure\Tool\RequestUserAgentChecker;
use DateTimeImmutable;
use Psr\Log\LoggerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Messenger\MessageBusInterface;
use Symfony\Component\Routing\Annotation\Route;
use Symfony\Component\Translation\LocaleSwitcher;

#[Route(path: 'questionary', name: 'subscription_product')]
class ProductController extends AbstractController
{
    public function __construct(
        private RequestUserAgentChecker $checker,
        private UpsellRepository $upsellRepository,
        private UserQuizRepository $userQuizRepository,
    ) {
    }

    #[Route(path: '/error')]
    public function error(): Response
    {
        return $this->render('subscription/pages/product/error.html.twig');
    }

    #[Route(path: '/landing-discount-80/{externalId}', name: '_landing_discount_80')]
    public function landingDiscount80(User $user, Request $request): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        return $this->render(
            'subscription/pages/product/landings/sub-discount-80.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => true,
                'funnelName' => FunnelNameEnum::DISCOUNT_80,
                'locale' => UserLocaleEnum::EN,
            ]
        );
    }

    #[Route(path: '/landing-1/{externalId}', name: '_landing_1')]
    public function landing1(User $user, Request $request): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        return $this->render(
            'subscription/pages/product/landings/sub-1.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => false,
                'funnelName' => FunnelNameEnum::MASTER,
                'locale' => UserLocaleEnum::EN,
            ]
        );
    }

    #[Route(path: '/landing-1-discount/{externalId}', name: '_landing_1_discount')]
    public function landing1Discount(User $user, Request $request): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        return $this->render(
            'subscription/pages/product/landings/sub-1-discount.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => true,
                'funnelName' => FunnelNameEnum::MASTER,
                'locale' => UserLocaleEnum::EN,
            ]
        );
    }

    #[Route(path: '/landing-2/{externalId}', name: '_landing_2')]
    public function landing2(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::EN->value);

        return $this->render(
            'subscription/pages/product/landings/love-landing.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => false,
                'funnelName' => FunnelNameEnum::LOVE,
                'locale' => UserLocaleEnum::EN,
            ]
        );
    }

    #[Route(path: '/landing-2-discount/{externalId}', name: '_landing_2_discount')]
    public function landing2Discount(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::EN->value);

        return $this->render(
            'subscription/pages/product/landings/love-landing.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => true,
                'funnelName' => FunnelNameEnum::LOVE,
                'locale' => UserLocaleEnum::EN,
            ]
        );
    }

    #[Route(path: '/landing-3/{externalId}', name: '_landing_3')]
    public function landing3(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::FR->value);

        return $this->render(
            'subscription/pages/product/landings/sub-3.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => false,
                'funnelName' => FunnelNameEnum::OBEDIENCE,
                'locale' => UserLocaleEnum::FR,
            ]
        );
    }

    #[Route(path: '/landing-3-discount/{externalId}', name: '_landing_3_discount')]
    public function landing3Discount(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::FR->value);

        return $this->render(
            'subscription/pages/product/landings/sub-3-discount.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => true,
                'funnelName' => FunnelNameEnum::OBEDIENCE,
                'locale' => UserLocaleEnum::FR,
            ]
        );
    }

    #[Route(path: '/landing-4/{externalId}', name: '_landing_4')]
    public function landing4(User $user, Request $request): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        return $this->render(
            'subscription/pages/product/landings/sub-4.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => false,
                'funnelName' => FunnelNameEnum::OBEDIENCE,
                'locale' => UserLocaleEnum::EN,
            ]
        );
    }

    #[Route(path: '/landing-4-discount/{externalId}', name: '_landing_4_discount')]
    public function landing4Discount(User $user, Request $request): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        return $this->render(
            'subscription/pages/product/landings/sub-4-discount.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => true,
                'funnelName' => FunnelNameEnum::OBEDIENCE,
                'locale' => UserLocaleEnum::EN,
            ]
        );
    }

    #[Route(path: '/landing-7/{externalId}', name: '_landing_7')]
    public function landing7(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::DE->value);

        return $this->render(
            'subscription/pages/product/landings/sub-7.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => false,
                'funnelName' => FunnelNameEnum::OBEDIENCE,
                'locale' => UserLocaleEnum::DE,
            ]
        );
    }

    #[Route(path: '/landing-7-discount/{externalId}', name: '_landing_7_discount')]
    public function landing7Discount(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::DE->value);

        return $this->render(
            'subscription/pages/product/landings/sub-7-discount.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => true,
                'funnelName' => FunnelNameEnum::OBEDIENCE,
                'locale' => UserLocaleEnum::DE,
            ]
        );
    }

    #[Route(path: '/landing-8/{externalId}', name: '_landing_8')]
    public function landing8(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::PT->value);

        return $this->render(
            'subscription/pages/product/landings/sub-8.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => false,
                'funnelName' => FunnelNameEnum::OBEDIENCE,
                'locale' => UserLocaleEnum::PT,
            ]
        );
    }

    #[Route(path: '/landing-8-discount/{externalId}', name: '_landing_8_discount')]
    public function landing8Discount(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::PT->value);

        return $this->render(
            'subscription/pages/product/landings/sub-8-discount.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => true,
                'funnelName' => FunnelNameEnum::OBEDIENCE,
                'locale' => UserLocaleEnum::PT,
            ]
        );
    }

    #[Route(path: '/landing-5/{externalId}', name: '_landing_5')]
    public function landing5(User $user, Request $request): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        return $this->render(
            'compliance/pages/product/landings/sub-5.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => false,
                'funnelName' => FunnelNameEnum::COMPLIANCE,
                'locale' => UserLocaleEnum::EN,
            ]
        );
    }

    #[Route(path: '/landing-5-discount/{externalId}', name: '_landing_5_discount')]
    public function landing5Discount(User $user, Request $request): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        return $this->render(
            'compliance/pages/product/landings/sub-5-discount.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => true,
                'funnelName' => FunnelNameEnum::COMPLIANCE,
                'locale' => UserLocaleEnum::EN,
            ]
        );
    }

    #[Route(path: '/landing-hi/{externalId}', name: '_landing_hi')]
    public function landingHi(User $user): Response
    {
        return $this->render('subscription/pages/product/landings/sub-hi.html.twig', ['user' => $user]);
    }

    #[Route(path: '/landing-10/{externalId}', name: '_landing_10')]
    public function landing10(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::ES->value);

        return $this->render(
            'subscription/pages/product/landings/sub-10.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => false,
                'funnelName' => FunnelNameEnum::OBEDIENCE,
                'locale' => UserLocaleEnum::ES,
            ]
        );
    }

    #[Route(path: '/landing-10-discount/{externalId}', name: '_landing_10_discount')]
    public function landing10discount(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::ES->value);

        return $this->render(
            'subscription/pages/product/landings/sub-10-discount.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => true,
                'funnelName' => FunnelNameEnum::OBEDIENCE,
                'locale' => UserLocaleEnum::ES,
            ]
        );
    }

    #[Route(path: '/landing-11/{externalId}', name: '_landing_11')]
    public function landing11(User $user, Request $request): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        return $this->render(
            'subscription/pages/product/landings/sub-11.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => false,
                'funnelName' => FunnelNameEnum::ANXIETY,
                'locale' => UserLocaleEnum::EN,
            ]
        );
    }

    #[Route(path: '/landing-11-discount/{externalId}', name: '_landing_11_discount')]
    public function landing11Discount(User $user, Request $request): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        return $this->render(
            'subscription/pages/product/landings/sub-11-discount.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => true,
                'funnelName' => FunnelNameEnum::ANXIETY,
                'locale' => UserLocaleEnum::EN,
            ]
        );
    }

    #[Route(path: '/landing-13/{externalId}', name: '_landing_13')]
    public function landing13(User $user, Request $request): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        return $this->render(
            'subscription/pages/product/landings/sub-13.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => false,
                'funnelName' => FunnelNameEnum::PUNISHMENT,
                'locale' => UserLocaleEnum::EN,
            ]
        );
    }

    #[Route(path: '/landing-13-discount/{externalId}', name: '_landing_13_discount')]
    public function landing13Discount(User $user, Request $request): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        return $this->render(
            'subscription/pages/product/landings/sub-13-discount.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => true,
                'funnelName' => FunnelNameEnum::PUNISHMENT,
                'locale' => UserLocaleEnum::EN,
            ]
        );
    }

    #[Route(path: '/landing-17/{externalId}', name: '_landing_17')]
    public function landing17(User $user, Request $request): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        return $this->render(
            'subscription/pages/product/landings/sub-17.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => false,
                'funnelName' => FunnelNameEnum::REACTIVITY,
                'locale' => UserLocaleEnum::EN,
            ]
        );
    }

    #[Route(path: '/landing-17-discount/{externalId}', name: '_landing_17_discount')]
    public function landing17Discount(User $user, Request $request): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        return $this->render(
            'subscription/pages/product/landings/sub-17-discount.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => true,
                'funnelName' => FunnelNameEnum::REACTIVITY,
                'locale' => UserLocaleEnum::EN,
            ]
        );
    }

    #[Route(path: '/landing-18/{externalId}', name: '_landing_18')]
    public function landing18(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::DE->value);

        return $this->render(
            'subscription/pages/product/landings/sub-18.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => false,
                'funnelName' => FunnelNameEnum::REACTIVITY,
                'locale' => UserLocaleEnum::DE,
            ]
        );
    }

    #[Route(path: '/landing-18-discount/{externalId}', name: '_landing_18_discount')]
    public function landing18Discount(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::DE->value);

        return $this->render(
            'subscription/pages/product/landings/sub-18-discount.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => true,
                'funnelName' => FunnelNameEnum::REACTIVITY,
                'locale' => UserLocaleEnum::DE,
            ]
        );
    }

    #[Route(path: '/landing-19/{externalId}', name: '_landing_19')]
    public function landing19(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::FR->value);

        return $this->render(
            'subscription/pages/product/landings/sub-19.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => false,
                'funnelName' => FunnelNameEnum::REACTIVITY,
                'locale' => UserLocaleEnum::FR,
            ]
        );
    }

    #[Route(path: '/landing-19-discount/{externalId}', name: '_landing_19_discount')]
    public function landing19Discount(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::FR->value);

        return $this->render(
            'subscription/pages/product/landings/sub-19-discount.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => true,
                'funnelName' => FunnelNameEnum::REACTIVITY,
                'locale' => UserLocaleEnum::FR,
            ]
        );
    }

    #[Route(path: '/landing-20/{externalId}', name: '_landing_20')]
    public function landing20(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::ES->value);

        return $this->render(
            'subscription/pages/product/landings/sub-20.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => false,
                'funnelName' => FunnelNameEnum::REACTIVITY,
                'locale' => UserLocaleEnum::ES,
            ]
        );
    }

    #[Route(path: '/landing-20-discount/{externalId}', name: '_landing_20_discount')]
    public function landing20Discount(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::ES->value);

        return $this->render(
            'subscription/pages/product/landings/sub-20-discount.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => true,
                'funnelName' => FunnelNameEnum::REACTIVITY,
                'locale' => UserLocaleEnum::ES,
            ]
        );
    }

    #[Route(path: '/landing-21/{externalId}', name: '_landing_21')]
    public function landing21(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::PT->value);

        return $this->render(
            'subscription/pages/product/landings/sub-21.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => false,
                'funnelName' => FunnelNameEnum::REACTIVITY,
                'locale' => UserLocaleEnum::PT,
            ]
        );
    }

    #[Route(path: '/landing-21-discount/{externalId}', name: '_landing_21_discount')]
    public function landing21Discount(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::PT->value);

        return $this->render(
            'subscription/pages/product/landings/sub-21-discount.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => true,
                'funnelName' => FunnelNameEnum::REACTIVITY,
                'locale' => UserLocaleEnum::PT,
            ]
        );
    }

    #[Route(path: '/landing-22/{externalId}', name: '_landing_22')]
    public function landing22(User $user, Request $request): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        return $this->render(
            'subscription/pages/product/landings/sub-22.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => false,
                'funnelName' => FunnelNameEnum::SEPARATION,
                'locale' => UserLocaleEnum::EN,
            ]
        );
    }

    #[Route(path: '/landing-22-discount/{externalId}', name: '_landing_22_discount')]
    public function landing22Discount(User $user, Request $request): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        return $this->render(
            'subscription/pages/product/landings/sub-22-discount.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => true,
                'funnelName' => FunnelNameEnum::SEPARATION,
                'locale' => UserLocaleEnum::EN,
            ]
        );
    }

    #[Route(path: '/landing-23/{externalId}', name: '_landing_23')]
    public function landing23(User $user, Request $request): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        return $this->render(
            'subscription/pages/product/landings/sub-23.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => false,
                'funnelName' => FunnelNameEnum::AGGRESSION,
                'locale' => UserLocaleEnum::EN,
            ]
        );
    }

    #[Route(path: '/landing-23-discount/{externalId}', name: '_landing_23_discount')]
    public function landing23Discount(User $user, Request $request): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        return $this->render(
            'subscription/pages/product/landings/sub-23-discount.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => true,
                'funnelName' => FunnelNameEnum::AGGRESSION,
                'locale' => UserLocaleEnum::EN,
            ]
        );
    }

    #[Route(path: '/landing-24/{externalId}', name: '_landing_24')]
    public function landing24(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::IT->value);

        return $this->render(
            'subscription/pages/product/landings/sub-24.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => false,
                'funnelName' => FunnelNameEnum::PUNISHMENT,
                'locale' => UserLocaleEnum::IT,
            ]
        );
    }

    #[Route(path: '/landing-24-discount/{externalId}', name: '_landing_24_discount')]
    public function landing24Discount(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::IT->value);

        return $this->render(
            'subscription/pages/product/landings/sub-24-discount.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => true,
                'funnelName' => FunnelNameEnum::PUNISHMENT,
                'locale' => UserLocaleEnum::IT,
            ]
        );
    }

    #[Route(path: '/landing-25/{externalId}', name: '_landing_25')]
    public function landing25(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::IT->value);

        return $this->render(
            'subscription/pages/product/landings/sub-25.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => false,
                'funnelName' => FunnelNameEnum::REACTIVITY,
                'locale' => UserLocaleEnum::IT,
            ]
        );
    }

    #[Route(path: '/landing-25-discount/{externalId}', name: '_landing_25_discount')]
    public function landing25Discount(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::IT->value);

        return $this->render(
            'subscription/pages/product/landings/sub-25-discount.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => true,
                'funnelName' => FunnelNameEnum::REACTIVITY,
                'locale' => UserLocaleEnum::IT,
            ]
        );
    }

    #[Route(path: '/landing-26/{externalId}', name: '_landing_26')]
    public function landing26(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::IT->value);

        return $this->render(
            'subscription/pages/product/landings/sub-26.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => false,
                'funnelName' => FunnelNameEnum::OBEDIENCE,
                'locale' => UserLocaleEnum::IT,
            ]
        );
    }

    #[Route(path: '/landing-26-discount/{externalId}', name: '_landing_26_discount')]
    public function landing26Discount(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::IT->value);

        return $this->render(
            'subscription/pages/product/landings/sub-26-discount.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => true,
                'funnelName' => FunnelNameEnum::OBEDIENCE,
                'locale' => UserLocaleEnum::IT,
            ]
        );
    }

    #[Route(path: '/landing-27/{externalId}', name: '_landing_27')]
    public function landing27(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::EN->value);

        return $this->render(
            'subscription/pages/product/landings/challenge-landing.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => false,
                'funnelName' => FunnelNameEnum::CHALLENGE,
                'locale' => UserLocaleEnum::EN,
            ]
        );
    }

    #[Route(path: '/landing-27-discount/{externalId}', name: '_landing_27_discount')]
    public function landing27Discount(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::EN->value);

        return $this->render(
            'subscription/pages/product/landings/challenge-landing.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => true,
                'funnelName' => FunnelNameEnum::CHALLENGE,
                'locale' => UserLocaleEnum::EN,
            ]
        );
    }

    #[Route(path: '/landing-28/{externalId}', name: '_landing_28')]
    public function landing28(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::DE->value);

        return $this->render(
            'subscription/pages/product/landings/challenge-landing.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => false,
                'funnelName' => FunnelNameEnum::CHALLENGE,
                'locale' => UserLocaleEnum::DE,
            ]
        );
    }

    #[Route(path: '/landing-28-discount/{externalId}', name: '_landing_28_discount')]
    public function landing28Discount(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::DE->value);

        return $this->render(
            'subscription/pages/product/landings/challenge-landing.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => true,
                'funnelName' => FunnelNameEnum::CHALLENGE,
                'locale' => UserLocaleEnum::DE,
            ]
        );
    }

    #[Route(path: '/landing-29/{externalId}', name: '_landing_29')]
    public function landing29(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::FR->value);

        return $this->render(
            'subscription/pages/product/landings/challenge-landing.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => false,
                'funnelName' => FunnelNameEnum::CHALLENGE,
                'locale' => UserLocaleEnum::FR,
            ]
        );
    }

    #[Route(path: '/landing-29-discount/{externalId}', name: '_landing_29_discount')]
    public function landing29Discount(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::FR->value);

        return $this->render(
            'subscription/pages/product/landings/challenge-landing.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => true,
                'funnelName' => FunnelNameEnum::CHALLENGE,
                'locale' => UserLocaleEnum::FR,
            ]
        );
    }

    #[Route(path: '/landing-30/{externalId}', name: '_landing_30')]
    public function landing30(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::ES_LA->value);

        return $this->render(
            'subscription/pages/product/landings/challenge-landing.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => false,
                'funnelName' => FunnelNameEnum::CHALLENGE,
                'locale' => UserLocaleEnum::ES_LA,
            ]
        );
    }

    #[Route(path: '/landing-30-discount/{externalId}', name: '_landing_30_discount')]
    public function landing30Discount(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::ES_LA->value);

        return $this->render(
            'subscription/pages/product/landings/challenge-landing.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => true,
                'funnelName' => FunnelNameEnum::CHALLENGE,
                'locale' => UserLocaleEnum::ES_LA,
            ]
        );
    }

    #[Route(path: '/landing-31/{externalId}', name: '_landing_31')]
    public function landing31(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::PT_BR->value);

        return $this->render(
            'subscription/pages/product/landings/challenge-landing.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => false,
                'funnelName' => FunnelNameEnum::CHALLENGE,
                'locale' => UserLocaleEnum::PT_BR,
            ]
        );
    }

    #[Route(path: '/landing-31-discount/{externalId}', name: '_landing_31_discount')]
    public function landing31Discount(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::PT_BR->value);

        return $this->render(
            'subscription/pages/product/landings/challenge-landing.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => true,
                'funnelName' => FunnelNameEnum::CHALLENGE,
                'locale' => UserLocaleEnum::PT_BR,
            ]
        );
    }

    #[Route(path: '/landing-32/{externalId}', name: '_landing_32')]
    public function landing32(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::IT->value);

        return $this->render(
            'subscription/pages/product/landings/challenge-landing.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => false,
                'funnelName' => FunnelNameEnum::CHALLENGE,
                'locale' => UserLocaleEnum::IT,
            ]
        );
    }

    #[Route(path: '/landing-32-discount/{externalId}', name: '_landing_32_discount')]
    public function landing32Discount(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::IT->value);

        return $this->render(
            'subscription/pages/product/landings/challenge-landing.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => true,
                'funnelName' => FunnelNameEnum::CHALLENGE,
                'locale' => UserLocaleEnum::IT,
            ]
        );
    }

    #[Route(path: '/landing-34/{externalId}', name: '_landing_34')]
    public function landing34(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::EN->value);

        return $this->render(
            'subscription/pages/product/landings/vagus-landing.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => false,
                'funnelName' => FunnelNameEnum::VAGUS,
                'locale' => UserLocaleEnum::EN,
            ]
        );
    }

    #[Route(path: '/landing-34-discount/{externalId}', name: '_landing_34_discount')]
    public function landing34Discount(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::EN->value);

        return $this->render(
            'subscription/pages/product/landings/vagus-landing.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => true,
                'funnelName' => FunnelNameEnum::VAGUS,
                'locale' => UserLocaleEnum::EN,
            ]
        );
    }

    #[Route(path: '/landing-35/{externalId}', name: '_landing_35')]
    public function landing35(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::DE->value);

        return $this->render(
            'subscription/pages/product/landings/love-landing.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => false,
                'funnelName' => FunnelNameEnum::LOVE,
                'locale' => UserLocaleEnum::DE,
            ]
        );
    }

    #[Route(path: '/landing-35-discount/{externalId}', name: '_landing_35_discount')]
    public function landing35Discount(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::DE->value);

        return $this->render(
            'subscription/pages/product/landings/love-landing.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => true,
                'funnelName' => FunnelNameEnum::LOVE,
                'locale' => UserLocaleEnum::DE,
            ]
        );
    }

    #[Route(path: '/landing-36/{externalId}', name: '_landing_36')]
    public function landing36(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::FR->value);

        return $this->render(
            'subscription/pages/product/landings/love-landing.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => false,
                'funnelName' => FunnelNameEnum::LOVE,
                'locale' => UserLocaleEnum::FR,
            ]
        );
    }

    #[Route(path: '/landing-36-discount/{externalId}', name: '_landing_36_discount')]
    public function landing36Discount(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::FR->value);

        return $this->render(
            'subscription/pages/product/landings/love-landing.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => true,
                'funnelName' => FunnelNameEnum::LOVE,
                'locale' => UserLocaleEnum::FR,
            ]
        );
    }

    #[Route(path: '/landing-37/{externalId}', name: '_landing_37')]
    public function landing37(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::ES_LA->value);

        return $this->render(
            'subscription/pages/product/landings/love-landing.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => false,
                'funnelName' => FunnelNameEnum::LOVE,
                'locale' => UserLocaleEnum::ES_LA,
            ]
        );
    }

    #[Route(path: '/landing-37-discount/{externalId}', name: '_landing_37_discount')]
    public function landing37Discount(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::ES_LA->value);

        return $this->render(
            'subscription/pages/product/landings/love-landing.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => true,
                'funnelName' => FunnelNameEnum::LOVE,
                'locale' => UserLocaleEnum::ES_LA,
            ]
        );
    }

    #[Route(path: '/landing-38/{externalId}', name: '_landing_38')]
    public function landing38(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::PT_BR->value);

        return $this->render(
            'subscription/pages/product/landings/love-landing.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => false,
                'funnelName' => FunnelNameEnum::LOVE,
                'locale' => UserLocaleEnum::PT_BR,
            ]
        );
    }

    #[Route(path: '/landing-38-discount/{externalId}', name: '_landing_38_discount')]
    public function landing38Discount(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::PT_BR->value);

        return $this->render(
            'subscription/pages/product/landings/love-landing.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => true,
                'funnelName' => FunnelNameEnum::LOVE,
                'locale' => UserLocaleEnum::PT_BR,
            ]
        );
    }

    #[Route(path: '/landing-39/{externalId}', name: '_landing_39')]
    public function landing39(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::IT->value);

        return $this->render(
            'subscription/pages/product/landings/love-landing.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => false,
                'funnelName' => FunnelNameEnum::LOVE,
                'locale' => UserLocaleEnum::IT,
            ]
        );
    }

    #[Route(path: '/landing-39-discount/{externalId}', name: '_landing_39_discount')]
    public function landing39Discount(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::IT->value);

        return $this->render(
            'subscription/pages/product/landings/love-landing.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => true,
                'funnelName' => FunnelNameEnum::LOVE,
                'locale' => UserLocaleEnum::IT,
            ]
        );
    }

    #[Route(path: '/landing-40/{externalId}', name: '_landing_40')]
    public function landing40(User $user, Request $request): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        return $this->render(
            'subscription/pages/product/landings/sub-40.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => false,
                'funnelName' => FunnelNameEnum::VAGUS_ANXIETY,
                'locale' => UserLocaleEnum::EN,
            ]
        );
    }

    #[Route(path: '/landing-40-discount/{externalId}', name: '_landing_40_discount')]
    public function landing40Discount(User $user, Request $request): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        return $this->render(
            'subscription/pages/product/landings/sub-40-discount.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => true,
                'funnelName' => FunnelNameEnum::VAGUS_ANXIETY,
                'locale' => UserLocaleEnum::EN,
            ]
        );
    }

    #[Route(path: '/landing-41/{externalId}', name: '_landing_41')]
    public function landing41(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        // $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::EN->value);

        return $this->render(
            'subscription/pages/product/landings/nutrition-landing.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => null,
                'isDiscountLanding' => false,
                'funnelName' => FunnelNameEnum::NUTRITION,
                'locale' => UserLocaleEnum::EN,
            ]
        );
    }

    #[Route(path: '/landing-41-discount/{externalId}', name: '_landing_41_discount')]
    public function landing41Discount(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::EN->value);

        return $this->render(
            'subscription/pages/product/landings/nutrition-landing.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => true,
                'funnelName' => FunnelNameEnum::NUTRITION,
                'locale' => UserLocaleEnum::EN,
            ]
        );
    }

    #[Route(path: '/landing-42/{externalId}', name: '_landing_42')]
    public function landing42(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        // $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::DE->value);

        return $this->render(
            'subscription/pages/product/landings/nutrition-landing.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => null,
                'isDiscountLanding' => false,
                'funnelName' => FunnelNameEnum::NUTRITION,
                'locale' => UserLocaleEnum::DE,
            ]
        );
    }

    #[Route(path: '/landing-42-discount/{externalId}', name: '_landing_42_discount')]
    public function landing42Discount(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::DE->value);

        return $this->render(
            'subscription/pages/product/landings/nutrition-landing.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => true,
                'funnelName' => FunnelNameEnum::NUTRITION,
                'locale' => UserLocaleEnum::DE,
            ]
        );
    }

    #[Route(path: '/landing-43/{externalId}', name: '_landing_43')]
    public function landing43(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        // $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::FR->value);

        return $this->render(
            'subscription/pages/product/landings/nutrition-landing.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => null,
                'isDiscountLanding' => false,
                'funnelName' => FunnelNameEnum::NUTRITION,
                'locale' => UserLocaleEnum::FR,
            ]
        );
    }

    #[Route(path: '/landing-43-discount/{externalId}', name: '_landing_43_discount')]
    public function landing43Discount(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::FR->value);

        return $this->render(
            'subscription/pages/product/landings/nutrition-landing.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => true,
                'funnelName' => FunnelNameEnum::NUTRITION,
                'locale' => UserLocaleEnum::FR,
            ]
        );
    }

    #[Route(path: '/landing-44/{externalId}', name: '_landing_44')]
    public function landing44(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        // $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::ES_LA->value);

        return $this->render(
            'subscription/pages/product/landings/nutrition-landing.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => null,
                'isDiscountLanding' => false,
                'funnelName' => FunnelNameEnum::NUTRITION,
                'locale' => UserLocaleEnum::ES_LA,
            ]
        );
    }

    #[Route(path: '/landing-44-discount/{externalId}', name: '_landing_44_discount')]
    public function landing44Discount(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::ES_LA->value);

        return $this->render(
            'subscription/pages/product/landings/nutrition-landing.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => true,
                'funnelName' => FunnelNameEnum::NUTRITION,
                'locale' => UserLocaleEnum::ES_LA,
            ]
        );
    }

    #[Route(path: '/landing-45/{externalId}', name: '_landing_45')]
    public function landing45(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        // $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::PT_BR->value);

        return $this->render(
            'subscription/pages/product/landings/nutrition-landing.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => null,
                'isDiscountLanding' => false,
                'funnelName' => FunnelNameEnum::NUTRITION,
                'locale' => UserLocaleEnum::PT_BR,
            ]
        );
    }

    #[Route(path: '/landing-45-discount/{externalId}', name: '_landing_45_discount')]
    public function landing45Discount(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::PT_BR->value);

        return $this->render(
            'subscription/pages/product/landings/nutrition-landing.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => true,
                'funnelName' => FunnelNameEnum::NUTRITION,
                'locale' => UserLocaleEnum::PT_BR,
            ]
        );
    }

    #[Route(path: '/landing-46/{externalId}', name: '_landing_46')]
    public function landing46(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        // $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::IT->value);

        return $this->render(
            'subscription/pages/product/landings/nutrition-landing.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => null,
                'isDiscountLanding' => false,
                'funnelName' => FunnelNameEnum::NUTRITION,
                'locale' => UserLocaleEnum::IT,
            ]
        );
    }

    #[Route(path: '/landing-46-discount/{externalId}', name: '_landing_46_discount')]
    public function landing46Discount(User $user, Request $request, LocaleSwitcher $localeSwitcher): Response
    {
        $deviceAndBreedContent = $this->getPersonalizationData($request, $user);
        $upsellNames = $this->getUpsellNames();

        $localeSwitcher->setLocale(UserLocaleEnum::IT->value);

        return $this->render(
            'subscription/pages/product/landings/nutrition-landing.html.twig',
            [
                'user' => $user,
                'deviceContent' => $deviceAndBreedContent,
                'upsellNames' => $upsellNames,
                'isDiscountLanding' => true,
                'funnelName' => FunnelNameEnum::NUTRITION,
                'locale' => UserLocaleEnum::IT,
            ]
        );
    }

    #[Route(path: '/upsell-cascade/{externalId}', name: '_upsell_cascade')]
    public function upsellCascade(
        User $user,
        UpsellRepository $upsellRepository,
        LearnWorldsApiV2Adapter $learnWorldsApiV2Adapter,
        LoggerInterface $logger,
        Request $request,
        MessageBusInterface $eventBus,
    ): Response {
        $enrolledUpsellsIds = [];

        foreach ($user->getOrders() as $order) {
            foreach ($order->getUpsells() as $orderUpsell) {
                $enrolledUpsellsIds[] = $orderUpsell->getId();
            }
        }

        $upsells = $upsellRepository->findAll();

        $firstUpsell = 'upsell_home';
        if ($request->query->has('first')) {
            $firstUpsell = $request->query->get('first');
        }

        $notEnrolledUpsellsNames = $this->filterAndSortUpsells($upsells, $enrolledUpsellsIds, $firstUpsell);

        if (count($notEnrolledUpsellsNames) === 0) {
            try {
                $link = $learnWorldsApiV2Adapter->getUserSSOLink($user);
            } catch (LMSException $exception) {
                $logger->error($exception->getMessage(), $exception->getContext());

                return new JsonResponse(
                    ['error' => 'Something went wrong'],
                    Response::HTTP_INTERNAL_SERVER_ERROR,
                );
            }

            $loggedInEvent = new LoggedInEvent($user->getId(), (new DateTimeImmutable())->getTimestamp());
            $eventBus->dispatch($loggedInEvent);

            return $this->redirect($link);
        }

        return $this->render(
            'subscription/pages/product/landings/sub-upsell-cascade.html.twig',
            [
                'userId' => $user->getExternalId(),
                'activeUpsell' => true,
                'upsellNames' => $notEnrolledUpsellsNames,
                'fromLMS' => true,
            ]
        );
    }

    /**
     * Contains static logic for rendering front-end UI. Change only on purpose.
     *
     * @param array|Upsell[] $upsells
     */
    private function filterAndSortUpsells(array $upsells, array $enrolledIds, string $firstUpsell): array
    {
        $order = SubscriptionUpsellContent::SORTED_UPSELLS;

        // Move to the first position given upsell if it exists in the upsell list.
        $key = array_search($firstUpsell, $order, true);
        if ($key !== false) {
            array_splice($order, (int) $key, 1);
            array_unshift($order, $firstUpsell);
        }

        $notEnrolled = [];
        foreach ($upsells as $upsell) {
            if (!in_array($upsell->getId(), $enrolledIds, true)) {
                $notEnrolled[] = $upsell->getName();
            }
        }

        $sortFn = static fn ($u1, $u2) => array_search($u1, $order, true) <=> array_search($u2, $order, true);
        usort($notEnrolled, $sortFn);

        return $notEnrolled;
    }

    private function getUpsellNames(): array
    {
        $upsellNames = [];
        $upsells = $this->upsellRepository->findAll();
        foreach ($upsells as $upsell) {
            $upsellNames[] = $upsell->getName();
        }

        return $upsellNames;
    }

    private function getPersonalizationData(Request $request, User $user): DeviceAndBreedContent
    {
        $breed = $request->get('breed');
        $deviceData = $this->checker->getRequestDeviceNameAndModel();

        if (is_null($breed)) {
            $quizData = $this->userQuizRepository->findOneBy(['user' => $user]);

            if (isset($quizData, $quizData->getResponses()['breed'])) {
                $breed = $quizData->getResponses()['breed'];
            }
        }

        return DeviceAndBreedContent::createWithParams(
            $breed,
            $deviceData['brand'] ?? null,
            $deviceData['model'] ?? null,
        );
    }
}
