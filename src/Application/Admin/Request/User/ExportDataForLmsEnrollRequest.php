<?php

namespace App\Application\Admin\Request\User;

use Symfony\Component\Serializer\Annotation\SerializedName;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\Validator\Constraints\Regex;
use Symfony\Component\Validator\Constraints\Type;

class ExportDataForLmsEnrollRequest
{
    #[NotBlank(message: 'Course IDs cannot be blank.')]
    #[Type(type: 'string', message: 'Course IDs must be a string.')]
    #[Regex(
        pattern: '/^[^,;]+(?:;[^,;]+)*$/',
        message: 'Invalid Course IDs format. Ensure tokens are separated by semicolons, there is no trailing semicolon, no empty tokens between semicolons, and no commas are included.'
    )]
    #[SerializedName('course_ids')]
    public string $courseIds;

    public array $context = [];
}
